# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a multi-component AI 3D Avatar Interview Tool with the following key parts:

### Frontend Applications
- **Candidate App** (`src/candidate/`): Next.js + TypeScript app for job candidates
  - Real-time 3D avatar display using Azure Avatar Service  
  - WebRTC-based video streaming with AvatarSynthesizer
  - Audio recording and speech synthesis integration
  - Emotion analysis display and feedback components
- **Management App** (`src/management/`): Next.js admin interface
  - Interview script generation and management
  - Meeting link generation and feedback viewing

### Backend
- **FastAPI Server** (`src/fastapi/`): Full-featured API implementation
  - Authentication endpoints under `/api/auth`
  - Interview management under `/api/interviews`
  - Health check at `/api/health/health`

### Infrastructure
- **pnpm workspaces**: モノレポ依存関係管理
- Docker Compose setup for local development
- VoiceVox engine for speech synthesis
- Containerized backend services

## Development Commands

### Root Level (Monorepo - pnpm workspaces)
```bash
# Install all dependencies using pnpm workspaces
pnpm install

# Start all services (backend + candidate + management)
npm run dev:all

# Start backend + candidate only (legacy)
npm run dev

# Start backend only
npm run dev:backend

# Start candidate frontend only  
npm run dev:frontend

# Start management frontend only
npm run dev:management

# pnpm workspace commands (alternative)
pnpm --filter mensetsu-kun-candidate dev
pnpm --filter mensetsu-kun-management dev

# Stop all running services
npm run stop
```

### Candidate App (`src/candidate/`)
```bash
# Development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

### Management App (`src/management/`)
```bash
# Development server
npm run dev

# Build for production  
npm run build

# Start production server
npm start
```

### Backend (`src/backend/`)
```bash
# Start server
npm start
```

### Infrastructure
```bash
# Start Docker services (from infra/ directory)
docker-compose up -d

# Stop Docker services
docker-compose down
```

## Key Technologies & Dependencies

### Azure Integration
- **Azure Speech Service**: Core speech synthesis and avatar functionality
- **AvatarSynthesizer**: Real-time 3D avatar rendering with lip-sync
- **WebRTC**: Video streaming for avatar display
- ICE server configuration with relay tokens

### Frontend Stack
- **Next.js 14**: React framework for both apps
- **TypeScript**: Type safety across frontend components
- **Chakra UI**: Component library for consistent UI
- **Framer Motion**: Animations and transitions
- **microsoft-cognitiveservices-speech-sdk**: Azure Speech SDK integration

### Backend Stack
- **FastAPI**: API server framework
- **SQLAlchemy**: Database ORM
- **Redis**: Caching layer
- **Azure SDK**: Various Azure service integrations

## Environment Configuration

Required environment variables in `.env` at project root:
```
NEXT_PUBLIC_AZURE_SPEECH_KEY=your_azure_speech_key
NEXT_PUBLIC_AZURE_SPEECH_REGION=your_azure_speech_region
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region
OPENAI_API_KEY=your_openai_api_key
GOOGLE_API_KEY=your_google_api_key
```

## WebRTC Avatar Connection Flow

The avatar system uses a complex WebRTC setup:

1. **SpeechConfig Setup**: Initialize with Azure Speech credentials
2. **AvatarConfig**: Configure avatar ID, style, and video format
3. **Relay Token Acquisition**: Get ICE server info from Azure relay endpoint
4. **RTCPeerConnection**: Create connection with ICE servers
5. **AvatarSynthesizer**: Connect synthesizer to peer connection
6. **Event Handling**: Monitor connection states and video streams
7. **Text-to-Speech**: Use `speakTextAsync()` for avatar speech

## Local Development Setup

1. Clone repository 
2. Install pnpm: `npm install -g pnpm`
3. Install all dependencies using pnpm workspaces: `pnpm install`
4. Set up environment variables in `.env`
5. Start Docker services: `cd infra && docker-compose up -d`
6. Start development: `npm run dev:all` (from project root)

Access points:
- Candidate interface: http://localhost:3000
- Management interface: http://localhost:3001
- Backend API: http://localhost:8080

## Common Issues & Debugging

### Avatar Display Problems
- Check Azure Speech Service credentials in environment variables
- Monitor WebRTC connection states in browser console
- Verify ICE server configuration and relay token acquisition
- Use manual reconnection if automatic connection fails

### Audio/Speech Issues
- Confirm browser audio permissions
- Check network connectivity to Azure services  
- Monitor speech synthesis logs in browser console
- Verify VoiceVox engine is running (Docker container)

### Development Workflow
- Use browser dev tools console for real-time logging
- Check Network tab for WebRTC connection status
- Monitor Docker container logs for backend issues
- Verify all required environment variables are set

### Shared Module Development
- Shared code is located in `src/shared/` as `@mensetsu-kun/shared` package
- Uses pnpm workspaces for dependency management
- Import shared modules: `import { ... } from '@mensetsu-kun/shared/...'`
- TypeScript paths are configured via `tsconfig-base.json` inheritance