project: AI_3D_Avatar_Interview_Tool
version: 1.2
date: 2024-03-26
# MVP開始フェーズ：ローカルデモ環境の実装要件を最優先
priority_tasks:
  - id: local_demo_implementation
    title: ローカルデモ環境実装
    description: >
      ゼロベースの開発者が何も準備せずに、
      リポジトリクローン後すぐローカルで動作するスタブデモを確認できること。
      以下のスタブ要件を実装する。
    requirements:
      - Lambdaエミュレータ（LocalStack/SAM）による `/signal`, `/orchestrate`, `/analyze` スタブ
      - Azure Speech SDK の AvatarSynthesizer を使用したリアルタイム3Dアバター表示と音声合成 (WebRTC接続、リレートークン利用)
      - 管理画面／求職者画面のプレースホルダーUIと固定ダミーデータ連携
      - 3Dアバター Idle モーション、音声再生、表情スコア表示のスタブ実装
      - README.md にスタブ設定と `docker-compose up` + `npm run dev` コマンド例を記載
      - 詳細なログ表示と状態管理UIの実装
      - エラーハンドリングと自動再接続ロジックの実装

current_issues:
  - id: avatar_display_issues
    title: アバター表示の問題
    description: >
      アバターが表示されない問題が発生している。
      以下の原因が考えられ、対策を実施中。
    causes:
      - ICEサーバー設定の不備
      - WebRTC接続の確立失敗
      - リレートークンの取得失敗
      - ビデオトラックの受信失敗
    countermeasures:
      - ICEサーバー設定の確認とフォールバック
      - WebRTC接続状態の詳細なログ出力
      - リレートークン取得のエラーハンドリング強化
      - ビデオトラック受信のタイムアウト処理
      - 手動再接続機能の実装

  - id: tts_delay
    title: 音声合成の遅延
    description: >
      音声合成に遅延が発生する問題。
    causes:
      - ネットワーク遅延
      - リソース競合
    countermeasures:
      - 音声合成のキューイング処理
      - 状態管理の改善

components:
  management_ui:
    tech_stack:
      - Next.js (TypeScript)
      - Chakra UI
    features:
      - 企業情報入力フォーム (スタブ)
      - 質問スクリプト生成ボタン (ダミー呼び出し)
      - MTGリンク発行 (プレースホルダー)
      - フィードバック閲覧 (スタブ表示)

  candidate_ui:
    tech_stack:
      - Next.js (TypeScript)
      - Chakra UI
      - Azure Speech SDK
      - Azure Avatar Service
    features:
      - 3Dアバター表示と制御
      - 音声合成とリップシンク
      - 質問表示と回答入力
      - フィードバック表示
      - エラーハンドリングとローディング状態管理
      - リアルタイムログ表示機能
      - 手動再接続機能

  backend:
    tech_stack:
      - AWS Lambda (Node.js)
      - AWS API Gateway (REST & WebSocket)
      - LocalStack (ローカルエミュレータ)
    functions:
      signaling:
        endpoint: /signal
        behavior: スタブOffer/Answer/ICE JSON返却
      orchestration:
        endpoint: /orchestrate
        behavior: 固定質問リスト+フィードバックJSON返却
      tts:
        endpoint: /synthesize
        behavior: 固定音声バイナリ返却
      emotion_analysis:
        endpoint: /analyze
        behavior: 固定表情スコアJSON返却

# 本番環境向け追加実装ステップ
production_tasks:
  - id: integrate_llm_dynamic_questions
    title: LLM 動的質問生成
    description: >
      /orchestrate を Azure OpenAI GPT-4 連携に置き換え、
      企業情報入力→質問＋意図を DynamoDB 保存

  - id: web_rtc_full_implementation
    title: WebRTC 本番シグナリング
    description: >
      LocalStack シグナリングを API Gateway＋Lambda／TURN サーバ構成に移行

  - id: deploy_tts_service
    title: Azure Speech Service 本番設定
    description: >
      Azure Speech Service の本番環境設定と
      動的テキスト合成を実現

  - id: enable_emotion_analysis
    title: 表情分析モデル強化
    description: >
      MediaPipe＋ファインチューニング済みFERモデルに切替、
      スコア蓄積と可視化追加

  - id: add_authentication
    title: 認証・認可実装
    description: >
      管理画面／面接セッションで Cognito/Auth0 等による認証導入

  - id: setup_cicd_and_infra_as_code
    title: CI/CD＆IaC構築
    description: >
      GitHub Actions/AWS CodePipeline で自動デプロイ、
      Terraform/CloudFormation で環境構築

ai_services:
  stt: Web Speech API
  llm: Azure OpenAI GPT-4
  tts: Azure Speech Service
  emotion_model: TensorFlow Lite (FER)

infrastructure:
  cloud:
    provider: AWS + Azure
    services:
      - API Gateway (REST & WebSocket)
      - Lambda (シグナリング/オーケストレーション/分析)
      - DynamoDB
      - Azure Speech Service
      - LocalStack (ローカル開発用)
  networking:
    stun: stun.l.google.com:19302
    turn: Optional Coturn on EC2

nonfunctional_requirements:
  scalability: "同時100セッション"
  performance:
    max_response_time: "2s"
    tts_latency: "<1s"
  availability: "99.9%"
  security:
    - IAM最小権限
    - JWT検証

deliverables:
  - requirements.yaml (本ファイル)
  - README.md (ローカルデモ手順 + 本番移行ガイド)
  - src/management/
  - src/candidate/
  - src/backend/
  - infra/docker-compose.yml
  - docs/architecture.png