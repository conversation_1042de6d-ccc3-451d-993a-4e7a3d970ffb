## **面接君アプリケーション UXアーキテクチャ設計書【V4.0 - The Constitution】**

### **改訂履歴**

| バージョン | 改訂日 | 改訂内容 | 重要な変更点 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024/06/10 | 初版作成 | 基本設計書の作成 |
| 2.0 | 2024/06/11 | 堅牢性・網羅性向上 | 面接官向けシステム拡充、リスク対策追加 |
| 3.0 | 2024/06/12 | UIUXの包括的強化 | デザインシステム、アクセシビリティ、マイクロインタラクション、レスポンシブ設計の大幅拡充 |
| **4.0** | **2025/06/11** | **思想・戦略的昇華** | **UX憲法の制定、アダプティブUI、フロー理論の導入、未来構想の明記** |

### **目次**

0.  **設計思想とプロダクト憲法**
1.  プロジェクト概要
2.  **デザインシステム 2.0（モーション＆サウンド含む）**
3.  ペルソナとユーザージャーニー（マイクロモーメント分析）
4.  情報アーキテクチャ
5.  **詳細UI/UX設計（アダプティブ＆パーソナライズド仕様）**
6.  **インタラクション＆フローデザイン**
7.  アクセシビリティ設計
8.  レスポンシブ設計
9.  技術要件とUX設計
10. 品質保証とテスト
11. **グロース＆フューチャーデザイン（リリース後の進化戦略）**

-----

### **0. 設計思想とプロダクト憲法**

#### **0.1 プロダクト哲学**

我々は、テクノロジーが人間の可能性を評価し、ランク付けする未来を望まない。我々が創造するのは、テクノロジーが一人ひとりの内に秘めた輝きを見出し、自信という名の光を灯す未来である。`面接君`は、AIを評価者から「究極の伴走者」へと再定義する挑戦である。

#### **0.2 UXの北極星（North Star Metric）**

ビジネスKPIを超えて我々が追求する唯一の指標、それは「**ユーザーの自己効力感の向上**」である。すべての機能、すべてのデザインは、ユーザーが「自分ならできる」「自分は成長している」と心から感じられる体験を創造するために存在する。

#### **0.3 プロダクト憲法**

`面接君`の開発と運用に関わるすべての者は、以下の原則を遵守する。

  * **第一条（心理的安全性の絶対的保障）:** 我々は、いかなる機能においてもユーザーを断罪、批判、あるいは能力を否定する表現を用いない。すべてのフィードバックは、未来への可能性を示唆するものでなければならない。
  * **第二条（個人の尊重と個別化）:** 我々は、すべてのユーザーがユニークな存在であることを理解し、画一的な体験を強要しない。パーソナライゼーションとアダプティブな設計を通じて、個々の成長経路を尊重する。
  * **第三条（透明性と信頼）:** 我々は、AIの判断プロセスを可能な限り透明にし、ユーザーが「なぜ」そのフィードバックがなされたのかを理解できるよう努める。ユーザーのデータは、ユーザー自身の成長のためにのみ用いる。

-----

### **1. プロジェクト概要**

#### **1.1 プロダクトビジョン**

「AIとの対話を通じて、すべての人が自信を持ってキャリアの岐路に立てる世界を創造する」

#### **1.2 コアバリュープロポジション**

| ユーザー | 現在の課題 | 提供価値 | 差別化要因 |
| :--- | :--- | :--- | :--- |
| **求職者** | 面接練習の機会不足\<br\>客観的フィードバック欠如 | いつでも実践的練習\<br\>AIによる多角的分析 | リアル3Dアバター\<br\>感情分析技術 |
| **面接官** | 面接スキルの属人化\<br\>内定辞退率の高さ | スキルの標準化\<br\>データドリブン改善 | AI求職者シミュレーション\<br\>セルフコーチング機能 |
| **エージェント** | 面接対策の非効率性\<br\>フィードバックの主観性 | 業務効率化30%\<br\>客観的分析レポート | ワンクリック設定\<br\>カスタムブランディング |

#### **1.3 成功指標（KPI）**

  * **ビジネスKPI**
      * 月間アクティブユーザー: 5,000名（6ヶ月目標）
      * ユーザー継続率: 80%（3ヶ月継続）
      * 顧客満足度: NPS 50以上
  * **品質KPI**
      * システム稼働率: 99.9%
      * 面接完了率: 95%以上
      * 技術エラー率: 1%未満

-----

### **2. デザインシステム 2.0（モーション＆サウンド含む）**

#### **2.1 ブランドアイデンティティ**

  * **ブランドパーソナリティ:** 信頼できる、親しみやすい、革新的、成長志向
  * **トーン&マナー:**
      * ✅ **Do:** 「一緒に成長していきましょう」「ここが素晴らしいポイントです」「次のステップをご提案します」
      * ❌ **Don't:** 「ダメです」「間違っています」「あなたは〜できていません」「これでは合格できません」

#### **2.2 カラーシステム**

```css
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;  /* メインブランドカラー */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* Secondary Colors */
  --secondary-50: #f0fdf4;
  --secondary-500: #22c55e;  /* 成功・ポジティブ */
  --secondary-600: #16a34a;
  
  /* Alert Colors */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;   /* 注意・改善点 */
  --error-50: #fef2f2;
  --error-500: #ef4444;     /* エラー・重要 */
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-900: #111827;
}
```

#### **2.3 タイポグラフィ**

```css
:root {
  --font-primary: 'Inter', 'Hiragino Sans', 'Yu Gothic UI', sans-serif;
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
}
```

#### **2.4 コンポーネントライブラリ**

  * **ボタンシステム:** Primary (CTA), Secondary (通常操作), Icon (補助操作) の3種を定義。ホバー、アクティブ時のインタラクションも規定。
  * **カードシステム:** 情報を整理するための基本コンポーネント。ホバー時の浮遊エフェクトでインタラクティブ性を表現。
  * **入力フィールドシステム:** ラベル、フィールド、ヘルパーテキスト、エラーメッセージを一体として定義。フォーカス時のスタイルで視覚的フィードバックを強化。

#### **2.5 アイコンシステム**

  * **ライブラリ:** Lucide React アイコンセットを使用し、一貫性を担保。
  * **ガイドライン:** サイズ（4段階）、ストローク（1.5px）、カラー、配置ルールを明確化。

#### **2.6 モーションデザインシステム**

  * **原則:** モーションは、ただの装飾ではなく、プロダクトの「性格」を表現し、ユーザーの認知を助けるための機能である。
  * **トーン:** `Calm & Responsive`（穏やかで、反応が良い）
  * **速度トークン:**
      * `--motion-duration-quick (150ms):` ツールチップ、ホバーエフェクト
      * `--motion-duration-moderate (300ms):` モーダル表示、アコーディオン開閉
      * `--motion-duration-slow (500ms):` 画面全体のトランジション
  * **イージングトークン:**
      * `--motion-easing-natural (ease-in-out):` 基本的なUIアニメーション
      * `--motion-easing-emphasized (cubic-bezier(0.4, 0, 0.2, 1)):` 重要なアクション（CTAクリック後など）

#### **2.7 サウンドデザインシステム（構想）**

  * **原則:** 聴覚情報は、視覚情報を補強し、没入感を高める。ミニマルで心地よいサウンドスケープを構築する。
  * **UIサウンド:**
      * **成功音:** 短く、明るい上昇音（例: 面接完了時）
      * **通知音:** 柔らかく、注意を引くチャイム音
  * **環境音（オプション）:** 面接中に集中力を高めるための、微かな環境音（例: 静かなカフェ、図書館）を提供。

-----

### **3. ペルソナとユーザージャーニー（マイクロモーメント分析）**

#### **3.1 詳細ペルソナ設計**

  * **プライマリペルソナ: 田中 美咲（26歳、求職者）**
      * **感情の変遷:** 不安 → 期待 → 緊張 → 集中 → 安堵 → 納得 → 自信
  * **セカンダリペルソナ: 佐藤 健太（32歳、エージェント）**
      * **システム期待値:** 設定作業5分以内、分析レポートの詳細度、対策時間30%削減
  * **ターシャリペルソナ: 鈴木 雅子（38歳、面接官）**
      * **改善ニーズ:** 自身の面接スキル客観視、部下のスキル標準化、内定承諾率の向上

#### **3.2 包括的ユーザージャーニー**

（業務フロー最適化、感情とタッチポイントの詳細なジャーニーマップを定義）

#### **3.3 マイクロモーメントと感情デザイン**

ユーザー体験が劇的に変化する決定的瞬間（マイクロモーメント）を特定し、最適な介入を設計する。

| マイクロモーメント | ユーザーの感情・思考 | UX介入戦略 |
| :--- | :--- | :--- |
| **面接開始ボタンを押す直前** | 緊張、不安、ためらい\<br\>「本当に大丈夫かな？」 | **「最後の深呼吸」プロンプト:** ボタンが微かに脈動し、「準備ができたら、深呼吸をして始めましょう」という優しいメッセージを表示。クリックへの心理的障壁を下げる。 |
| **最初の質問が表示される瞬間** | 固唾を飲む、身構える | **アバターの優しい微笑み:** 質問テキストの表示と同時に、AIアバターが微笑み、頷くモーションを入れる。「さあ、聞かせてください」という無言のメッセージを送る。 |
| **自分の回答音声を初めて再生する時** | 羞恥心、自己嫌悪\<br\>「自分の声、聞きたくない…」 | **ポジティブ・ハイライト機能:** 再生前に、AIが「この回答の、特に素晴らしかった部分をハイライトしました」と表示。ポジティブな側面に焦点を当てさせることで、自己評価のバイアスを緩和する。 |

-----

### **4. 情報アーキテクチャ**

#### **4.1 サイトマップ（完全版）**

求職者、エージェント、面接官の各システムについて、認証、ダッシュボード、主要機能、サポートに至るまで、全ページの階層構造を網羅的に定義。

#### **4.2 画面遷移フロー図**

求職者とエージェントの主要タスク（面接実施、面接設定）について、正常系・異常系を含めた詳細な画面遷移フローをグラフィカルに図示。

-----

### **5. 詳細UI/UX設計（アダプティブ＆パーソナライズド仕様）**

#### **5.1 求職者向け画面設計**

##### **5.1.1 面接待機画面（完全版）**

  * **レイアウト:** 企業情報ヘッダー、デバイスチェック、準備ガイド、アクションフッターの4段構成。
  * **インタラクション:** 必須のプリフライトチェックが完了すると、「面接を開始する」ボタンがアクティブになり、脈動アニメーションでユーザーのアクションを促す。
  * **アダプティブ仕様:**
      * **動的ウォームアップ:** ユーザーの利用履歴に基づき、「前回は話すスピードが速めでした。今回は『、』で一呼吸おくことを意識してみましょう」といったパーソナライズされた目標を提示。

##### **5.1.2 面接実施画面（完全版）**

  * **レイアウト:** プログレスヘッダー、アバター＆質問エリア、録音インターフェース、サイドパネル（自己映像・メモ）で構成。
  * **インタラクション:** 録音状態はインジケーターの色とテキストで明確に表示。一時停止、スキップ、ヘルプなどの補助機能も完備。
  * **アダプティブ仕様:**
      * **リアルタイム・アシスト（Subtle Nudge）:** ユーザーの緊張度（表情分析から推定）が高い場合、AIアバターが「少しリラックスしましょうか。深呼吸を一度どうぞ」と優しく声をかける。

##### **5.1.3 フィードバック表示画面（インタラクティブ版）**

  * **レイアウト:** 総合評価ヘッダーと、概要、詳細分析、質問別、改善プランのタブで構成。
  * **インタラクション:** レーダーチャートや感情タイムラインはインタラクティブで、詳細なデータポイントをホバーで確認可能。質問別フィードバックでは、回答音声と文字起こし、フィードバックが連動。
  * **アダプティブ仕様:**
      * **感情に配慮した情報開示:** 総合スコアが著しく低かった場合、「今回は難しい挑戦でしたね。でも、すべての経験が次への一歩です。まずは、最も輝いていた『企業理解度』の項目から見てみましょう」と、スコアの衝撃を和らげ、ポジティブな側面からレビューを開始する。

#### **5.2 エージェント向け画面設計**

（ダッシュボード、新規面接設定画面、結果分析画面の詳細なUI/UX設計を定義）

#### **5.3 面接官向け画面設計**

（パフォーマンスダッシュボード、セルフコーチング画面、AI求職者シミュレーション画面のUI/UX設計を定義）

-----

### **6. インタラクション＆フローデザイン**

#### **6.1 フロー理論に基づくエンゲージメント設計**

  * **目的:** ユーザーを「やらなければならない」という義務感から解放し、「もっとやりたい」という内発的動機付けによる熱中状態（フロー）へと導く。
  * **実践:**
      * **明確な目標設定:** 各セッション前に小さな目標（例:「フィラーワードを3回以内に」）を設定。
      * **挑戦とスキルの最適バランス:** AIがユーザーのレベルに応じ、常に「少し難しいが達成可能」な質問を自動生成。
      * **即時フィードバック:** セッション完了直後に、ポジティブな点を速報で伝える。

#### **6.2 ゲーミフィケーション2.0（自己決定理論に基づく）**

  * **目的:** 外的な報酬だけでなく、ユーザーの根源的な心理的欲求（自律性、有能感、関係性）を満たし、持続的なモチベーションを喚起する。
  * **実践:**
      * **自律性（Autonomy）:** ユーザー自身が練習プランをカスタマイズできる機能。
      * **有能感（Competence）:** スキルツリーや過去の自分との比較で、成長を可視化。
      * **関係性（Relatedness）:** 匿名でのエール交換や、チーム内でのベストプラクティス共有機能。

-----

### **7. アクセシビリティ設計**

  * **WCAG 2.1 AA準拠:** コントラスト比、キーボード操作、スクリーンリーダー対応など、公的なガイドラインを遵守。
  * **ARIA属性の適切な使用:** すべてのインタラクティブな要素に、役割（role）や状態（state）を明記。
  * **ユーザー設定:** フォントサイズやコントラストをユーザーが調整できる機能を提供。

### **8. レスポンシブ設計**

  * **モバイルファースト:** スマートフォンの狭い画面を基準に設計し、徐々にタブレット、デスクトップへとレイアウトを拡張。
  * **ブレークポイント:** スマートフォン (`~767px`), タブレット (`768px~1199px`), デスクトップ (`1200px~`) の3段階で定義。
  * **タッチフレンドリー:** ボタンやリンクなどのタップ領域は、最低でも 44x44px を確保。

-----

### **9. 技術要件とUX設計**

  * **パフォーマンス:** WebRTCの最適化、AI処理の非同期化により、遅延を感じさせない体験を実現。
  * **セキュリティ:** 個人データはすべて暗号化。RBAC（役割ベースのアクセス制御）を徹底。
  * **データ設計:** 収集するデータ項目と、その分析・活用方法、プライバシー保護方針を明確化。

-----

### **10. 品質保証とテスト**

  * **ユーザビリティテスト:** 各ペルソナに基づいたシナリオを用意し、タスク完了率やSUSスコアを測定。
  * **技術テスト:** パフォーマンステスト、セキュリティテスト（脆弱性診断）を定期的に実施。
  * **A/Bテスト:** オンボーディングフローやフィードバックの表現方法など、重要なUX要素について、データに基づいた改善を行う。

-----

### **11. グロース＆フューチャーデザイン（リリース後の進化戦略）**

#### **11.1 運用・改善計画**

  * **HEARTフレームワークの導入:** Happiness, Engagement, Adoption, Retention, Task Success の5つの側面からUXを継続的に測定・評価。
  * **改善ループ:** データ分析とユーザーフィードバックに基づき、2週間単位のスプリントで継続的な改善サイクルを回す。

#### **11.2 ビジョナリー・ロードマップ（プロダクトの未来）**

  * **V4.0 (2026): AIパーソナリティの選択とカスタマイズ**
      * ユーザーは面接官AIの性格（例：「共感的なカウンセラータイプ」「鋭い戦略コンサルタントタイプ」）を選択可能に。究極のロールプレイング環境を実現。
  * **V5.0 (2027): 複合的コミュニケーションシミュレーション**
      * **グループディスカッション:** 複数のAIアバターを相手に、協調性やリーダーシップを訓練。
      * **プレゼンテーション:** 特定のテーマについてAI役員にプレゼンし、質疑応答スキルを磨く。
  * **V6.0 (2028〜): ライフキャリア・プラットフォームへの進化**
      * `面接君`から`キャリア君`へ。面接だけでなく、昇進交渉、部下へのフィードバック、投資家へのピッチなど、キャリアにおけるあらゆる重要な対話シーンをシミュレーションできるプラットフォームへと進化する。

-----

この【V4.0 - The Constitution】は、プロダクトの仕様を定義するだけでなく、その魂と未来への道を照らすものです。この憲法に基づき、`面接君`は単なるアプリケーションを超え、ユーザーの人生に寄り添う不可欠なパートナーとして成長を続けます。