#!/usr/bin/env python3
"""
Script xóa toàn bộ DỮ LIỆU trong database và ChromaDB cho testing
CẢNH BÁO: Script này sẽ xóa TẤT CẢ dữ liệu nhưng giữ lại cấu trúc tables!
"""

import os
import sys
import shutil
import chromadb
from sqlalchemy import create_engine, text
from app.core.config import settings
from app.models.base import Base, engine
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_database_data():
    """Clear toàn bộ DỮ LIỆU trong database (giữ lại tables)"""
    try:
        logger.info("🗄️ Clearing database data...")
        
        with engine.connect() as conn:
            # Lấy tất cả tables
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """))
            
            tables = result.fetchall()
            logger.info(f"📋 Found {len(tables)} tables to clear")
            
            # Disable foreign key checks tạm thời
            conn.execute(text("SET session_replication_role = replica"))
            
            # Truncate từng table
            for table in tables:
                table_name = table[0]
                try:
                    logger.info(f"🧹 Clearing data from: {table_name}")
                    conn.execute(text(f"TRUNCATE TABLE {table_name} RESTART IDENTITY CASCADE"))
                except Exception as e:
                    logger.warning(f"⚠️ Could not truncate {table_name}: {e}")
                    # Fallback to DELETE
                    try:
                        conn.execute(text(f"DELETE FROM {table_name}"))
                        logger.info(f"🧹 Deleted data from: {table_name}")
                    except Exception as e2:
                        logger.error(f"❌ Failed to clear {table_name}: {e2}")
            
            # Re-enable foreign key checks
            conn.execute(text("SET session_replication_role = DEFAULT"))
            
            # Reset sequences
            seq_result = conn.execute(text("""
                SELECT sequence_name 
                FROM information_schema.sequences 
                WHERE sequence_schema = 'public'
            """))
            
            sequences = seq_result.fetchall()
            for seq in sequences:
                sequence_name = seq[0]
                logger.info(f"🔄 Resetting sequence: {sequence_name}")
                conn.execute(text(f"ALTER SEQUENCE {sequence_name} RESTART WITH 1"))
            
            conn.commit()
        
        logger.info("✅ Database data cleared successfully (tables preserved)!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error clearing database data: {e}")
        return False

def clear_chromadb():
    """Clear toàn bộ ChromaDB collections"""
    try:
        logger.info("🔍 Clearing ChromaDB...")
        
        client = chromadb.HttpClient(host="chroma", port=8000)
        collections = client.list_collections()
        
        logger.info(f"📂 Deleting {len(collections)} collections...")
        for collection in collections:
            logger.info(f"🗑️ Deleting: {collection.name}")
            client.delete_collection(collection.name)
        
        logger.info("✅ ChromaDB collections cleared!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error clearing ChromaDB: {e}")
        return False

def clear_chroma_data_folder():
    """Xóa data files trong folder chroma (giữ lại chroma.sqlite3)"""
    try:
        possible_paths = [
            "/app/data/chroma",
            "/data/chroma", 
            "./data/chroma",
            "/home/<USER>/Documents/mensetsu-kun/src/fastapi/data/chroma"
        ]
        
        # Files được bảo vệ (không xóa)
        protected_files = {
            "chroma.sqlite3",
            "chroma.sqlite3-wal", 
            "chroma.sqlite3-shm",
            ".gitkeep",
            "README.md"
        }
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"📁 Clearing folder: {path}")
                
                for filename in os.listdir(path):
                    if filename in protected_files:
                        logger.info(f"⚡ Skipping protected: {filename}")
                        continue
                        
                    file_path = os.path.join(path, filename)
                    try:
                        if os.path.isfile(file_path):
                            os.unlink(file_path)
                            logger.info(f"🗑️ Deleted file: {filename}")
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            logger.info(f"🗑️ Deleted folder: {filename}")
                    except Exception as e:
                        logger.error(f"❌ Error deleting {file_path}: {e}")
                
                logger.info(f"✅ Folder {path} cleared (protected files preserved)!")
                return True
        
        logger.warning("⚠️ No chroma data folder found")
        return True
            
    except Exception as e:
        logger.error(f"❌ Error clearing chroma folder: {e}")
        return False

def verify_database_structure():
    """Xác minh cấu trúc database vẫn còn"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
            """))
            
            tables = result.fetchall()
            logger.info(f"✅ Database structure verified: {len(tables)} tables preserved")
            for table in tables:
                # Check if table is empty
                count_result = conn.execute(text(f"SELECT COUNT(*) FROM {table[0]}"))
                count = count_result.fetchone()[0]
                logger.info(f"  📊 {table[0]}: {count} records")
            
        return True
    except Exception as e:
        logger.error(f"❌ Error verifying database: {e}")
        return False

def main():
    """Main function - XÓA DỮ LIỆU (giữ lại cấu trúc)"""
    logger.info("🚀 Starting DATA WIPE (preserving structure)...")
    
    results = []
    
    # 1. Clear database data
    results.append(("Database Data", clear_database_data()))
    
    # 2. Clear ChromaDB
    results.append(("ChromaDB", clear_chromadb()))
    
    # 3. Clear chroma files  
    results.append(("Chroma Files", clear_chroma_data_folder()))
    
    # 4. Verify structure
    results.append(("Structure Check", verify_database_structure()))
    
    # Summary
    logger.info(f"\n📊 SUMMARY:")
    success_count = 0
    for name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {name}: {status}")
        if success:
            success_count += 1
    
    if success_count == len(results):
        logger.info("🎉 ALL DATA CLEARED! Database structure preserved!")
    else:
        logger.warning(f"⚠️ {len(results) - success_count} operations failed")
    
    logger.info("🔄 Ready for fresh testing with existing schema!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n❌ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1) 