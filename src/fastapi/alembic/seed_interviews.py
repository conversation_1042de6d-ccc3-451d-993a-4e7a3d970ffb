from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.models.interview import InterviewSession
from app.models.company import Company
from app.models.base import Base
from app.core.config import settings
from datetime import datetime, timedelta
import uuid
from app.models.candidate import Candidate

engine = create_engine(settings.DATABASE_URL)
Base.metadata.bind = engine
session = Session(bind=engine)

# Lấy 1 company để gán cho interview (hoặc tạo mới nếu chưa có)
company = session.query(Company).first()
if not company:
    company = Company(
        id=uuid.uuid4(),
        name="Demo Company",
        industry="IT",
        description="A demo company",
        core_values={},
        vision_mission="Demo mission",
        created_at=datetime.utcnow()
    )
    session.add(company)
    session.commit()

# Lấy 1 candidate để gán cho interview (hoặc tạo mới nếu chưa có)
candidate = session.query(Candidate).first()
if not candidate:
    candidate = Candidate(
        id=uuid.uuid4(),
        name_kana="Demo Candidate",
        age=25,
        employment_status="employed",
        company="Demo Company",
        position="Developer",
        address="Tokyo",
        has_resume=True,
        has_career_history=True,
        profile={},
        main_skill="Python",
        other_skill="JavaScript",
        experience="2 years",
        education="Tokyo University",
        language="Japanese",
        other_info="Demo info"
    )
    session.add(candidate)
    session.commit()

for i in range(5):
    interview = InterviewSession(
        id=uuid.uuid4(),
        company_id=company.id,
        candidate_id=candidate.id,
        status="created",
        transcript=[],
        feedback=[],
        overall_score=0,
        session_token=str(uuid.uuid4()),
        expires_at=datetime.utcnow() + timedelta(hours=1),
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    session.add(interview)

session.commit()
session.close()
print("✅ Seeded interviews successfully!")