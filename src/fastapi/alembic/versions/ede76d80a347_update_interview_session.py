"""update interview session

Revision ID: ede76d80a347
Revises: 8a926935cc97
Create Date: 2025-07-08 07:07:34.206742

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ede76d80a347'
down_revision: Union[str, None] = '8a926935cc97'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('interview_sessions', sa.Column('candidate_id', sa.UUID(), nullable=False))
    op.drop_index(op.f('ix_interview_sessions_interview_url'), table_name='interview_sessions')
    op.create_foreign_key(None, 'interview_sessions', 'candidates', ['candidate_id'], ['id'], ondelete='CASCADE')
    op.drop_column('interview_sessions', 'interview_url')
    op.drop_column('interview_sessions', 'candidate_name')
    op.drop_column('interview_sessions', 'candidate_profile')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('interview_sessions', sa.Column('candidate_profile', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.add_column('interview_sessions', sa.Column('candidate_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('interview_sessions', sa.Column('interview_url', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'interview_sessions', type_='foreignkey')
    op.create_index(op.f('ix_interview_sessions_interview_url'), 'interview_sessions', ['interview_url'], unique=True)
    op.drop_column('interview_sessions', 'candidate_id')
    # ### end Alembic commands ###
