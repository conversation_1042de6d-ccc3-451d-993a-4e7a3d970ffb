"""update user table to add password, name, role, last_login_at, updated_at columns

Revision ID: 1994b04c5165
Revises: 47cc9d50f40f
Create Date: 2025-06-19 04:02:00.929535
"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '1994b04c5165'
down_revision: Union[str, None] = '47cc9d50f40f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('users', sa.Column('password', sa.String(length=255), nullable=True))
    op.add_column('users', sa.Column('name', sa.String(length=255), nullable=True))
    op.add_column('users', sa.Column('role', sa.String(length=50), nullable=True))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('updated_at', sa.DateTime(), nullable=True))

    op.execute("UPDATE users SET name = full_name, password = hashed_password")
    op.execute("ALTER TABLE users ALTER COLUMN password SET NOT NULL")
    op.drop_column('users', 'hashed_password')
    op.drop_column('users', 'full_name')


def downgrade() -> None:
    op.add_column('users', sa.Column('hashed_password', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('full_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True))

    op.execute("UPDATE users SET full_name = name, hashed_password = password")
    op.execute("ALTER TABLE users ALTER COLUMN hashed_password SET NOT NULL")

    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'role')
    op.drop_column('users', 'name')
    op.drop_column('users', 'password')

