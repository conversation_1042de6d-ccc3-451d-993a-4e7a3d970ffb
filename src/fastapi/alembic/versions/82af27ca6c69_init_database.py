"""init database

Revision ID: 82af27ca6c69
Revises: 9b3393d2e4be
Create Date: 2025-06-11 07:45:01.545297

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '82af27ca6c69'
down_revision: Union[str, None] = '9b3393d2e4be'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
