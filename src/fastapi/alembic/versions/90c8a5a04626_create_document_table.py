"""create document table

Revision ID: 90c8a5a04626
Revises: 6e3d9694e580
Create Date: 2025-06-20 09:55:29.490579

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '90c8a5a04626'
down_revision: Union[str, None] = '6e3d9694e580'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('company_documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=True),
    sa.Column('file_name', sa.String(), nullable=True),
    sa.Column('file_path', sa.String(), nullable=True),
    sa.Column('document_type', sa.String(), nullable=True),
    sa.Column('upload_date', sa.DateTime(), nullable=True),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('candidate_documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('file_name', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('document_type', sa.String(length=50), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('mime_type', sa.String(length=100), nullable=True),
    sa.Column('upload_date', sa.DateTime(), nullable=True),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.Column('text_chunks_count', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_index(op.f('ix_candidates_id'), table_name='candidates')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_candidates_id'), 'candidates', ['id'], unique=False)
    op.drop_table('candidate_documents')
    op.drop_table('company_documents')
    # ### end Alembic commands ###
