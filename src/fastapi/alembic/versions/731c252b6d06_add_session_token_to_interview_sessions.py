"""Add session_token to interview_sessions

Revision ID: 731c252b6d06
Revises: 82af27ca6c69
Create Date: 2025-06-11 07:53:30.159334

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '731c252b6d06'
down_revision: Union[str, None] = '82af27ca6c69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('interview_sessions', sa.Column('session_token', sa.String(length=255), nullable=False))
    op.add_column('interview_sessions', sa.Column('expires_at', sa.DateTime(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('interview_sessions', 'expires_at')
    op.drop_column('interview_sessions', 'session_token')
    # ### end Alembic commands ###
