"""add_interviewer_role_to_questions

Revision ID: c91921417503
Revises: cc7b9d613397
Create Date: 2025-06-25 03:31:31.195978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c91921417503'
down_revision: Union[str, None] = 'cc7b9d613397'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('questions', sa.Column('interviewer_role', sa.String(length=50), nullable=False))
    op.add_column('questions', sa.Column('interviewer_perspective', sa.Text(), nullable=True))
    op.add_column('questions', sa.Column('role_specific_context', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('questions', 'role_specific_context')
    op.drop_column('questions', 'interviewer_perspective')
    op.drop_column('questions', 'interviewer_role')
    # ### end Alembic commands ###
