"""update flow keypoint database

Revision ID: cc7b9d613397
Revises: 5903102563f2
Create Date: 2025-06-24 07:54:33.863996

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cc7b9d613397'
down_revision: Union[str, None] = '5903102563f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('keypoint_templates', sa.Column('company_id', sa.UUID(), nullable=False))
    op.create_foreign_key(None, 'keypoint_templates', 'companies', ['company_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'keypoint_templates', type_='foreignkey')
    op.drop_column('keypoint_templates', 'company_id')
    # ### end Alembic commands ###
