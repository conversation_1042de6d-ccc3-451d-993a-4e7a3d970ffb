"""create agents table

Revision ID: 47cc9d50f40f
Revises: 731c252b6d06
Create Date: 2025-06-21 10:00:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision: str = '47cc9d50f40f'
down_revision: Union[str, None] = '731c252b6d06'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Tạo bảng agents"""
    op.create_table('agents',
        sa.Column('id', UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('profile_name', sa.String(length=255), nullable=False),
        sa.Column('profile_organization', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=True, default=sa.func.now(), onupdate=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_agents_user_id'), 'agents', ['user_id'], unique=True)


def downgrade() -> None:
    """Xóa bảng agents"""
    op.drop_table('agents')
