"""add table questions

Revision ID: 5903102563f2
Revises: 99cbc461c727
Create Date: 2025-06-24 07:23:29.912347

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5903102563f2'
down_revision: Union[str, None] = '99cbc461c727'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('questions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('company_keypoint_id', sa.UUID(), nullable=False),
    sa.Column('question_text', sa.Text(), nullable=False),
    sa.Column('question_type', sa.String(length=50), nullable=False),
    sa.Column('difficulty_level', sa.Integer(), nullable=True),
    sa.Column('expected_answer_points', sa.JSON(), nullable=True),
    sa.Column('follow_up_suggestions', sa.JSON(), nullable=True),
    sa.Column('evaluation_criteria', sa.JSON(), nullable=True),
    sa.Column('order_index', sa.Integer(), nullable=True),
    sa.Column('estimated_time_minutes', sa.Integer(), nullable=True),
    sa.Column('generation_prompt', sa.Text(), nullable=True),
    sa.Column('generation_metadata', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('effectiveness_score', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_keypoint_id'], ['company_keypoints.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('questions')
    # ### end Alembic commands ###
