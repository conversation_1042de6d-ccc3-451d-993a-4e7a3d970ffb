"""add_candidates_table

Revision ID: 6e3d9694e580
Revises: 1994b04c5165
Create Date: 2025-06-20 03:14:35.420476

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import enum
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision: str = '6e3d9694e580'
down_revision: Union[str, None] = '1994b04c5165'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade():
    op.create_table('candidates',
        sa.Column('id', UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('name_kana', sa.String(), nullable=False),
        sa.Column('age', sa.Integer(), nullable=False),
        sa.Column('employment_status', sa.String(), nullable=False),
        sa.Column('company', sa.String(), nullable=True),
        sa.Column('position', sa.String(), nullable=True),
        sa.Column('address', sa.String(), nullable=True),
        sa.Column('has_resume', sa.Boolean(), default=False),
        sa.Column('has_career_history', sa.Boolean(), default=False),
        sa.Column('profile', sa.JSON(), nullable=True),
        sa.Column('main_skill', sa.String(), nullable=True),
        sa.Column('other_skill', sa.String(), nullable=True),
        sa.Column('experience', sa.String(), nullable=True),
        sa.Column('education', sa.String(), nullable=True),
        sa.Column('language', sa.String(), nullable=True),
        sa.Column('other_info', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.Index('ix_candidates_id', 'id'),
        sa.Index('ix_candidates_user_id', 'user_id', unique=True)
    )

def downgrade():
    op.drop_table('candidates')
