"""update

Revision ID: 8a926935cc97
Revises: c91921417503
Create Date: 2025-07-01 04:18:56.218313

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8a926935cc97'
down_revision: Union[str, None] = 'c91921417503'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('video_segments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('interview_session_id', sa.UUID(), nullable=False),
    sa.Column('question_id', sa.UUID(), nullable=False),
    sa.Column('start_time', sa.DateTime(), nullable=False),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('video_file_path', sa.String(), nullable=True),
    sa.Column('video_size_bytes', sa.BigInteger(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('segment_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['interview_session_id'], ['interview_sessions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('video_analyses',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('interview_session_id', sa.UUID(), nullable=False),
    sa.Column('question_id', sa.UUID(), nullable=False),
    sa.Column('video_segment_id', sa.UUID(), nullable=False),
    sa.Column('gemini_analysis_result', sa.JSON(), nullable=False),
    sa.Column('scores', sa.JSON(), nullable=False),
    sa.Column('behavioral_insights', sa.JSON(), nullable=True),
    sa.Column('recommendations', sa.JSON(), nullable=True),
    sa.Column('video_duration_seconds', sa.Float(), nullable=False),
    sa.Column('analysis_status', sa.String(length=50), nullable=False),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('model_version', sa.String(length=100), nullable=True),
    sa.Column('analyzed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['interview_session_id'], ['interview_sessions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], ),
    sa.ForeignKeyConstraint(['video_segment_id'], ['video_segments.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('video_analyses')
    op.drop_table('video_segments')
    # ### end Alembic commands ###
