"""update_company_documents_add_missing_fields

Revision ID: f9b2f74ae8a7
Revises: 90c8a5a04626
Create Date: 2025-06-23 07:24:31.914093

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f9b2f74ae8a7'
down_revision: Union[str, None] = '90c8a5a04626'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_agents_user_id'), table_name='agents')
    op.add_column('company_documents', sa.Column('file_size', sa.Integer(), nullable=True))
    op.add_column('company_documents', sa.Column('mime_type', sa.String(length=100), nullable=True))
    op.add_column('company_documents', sa.Column('text_chunks_count', sa.Integer(), nullable=True))
    op.alter_column('company_documents', 'company_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.alter_column('company_documents', 'file_name',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('company_documents', 'file_path',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('company_documents', 'document_type',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('company_documents', 'document_type',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('company_documents', 'file_path',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('company_documents', 'file_name',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('company_documents', 'company_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.drop_column('company_documents', 'text_chunks_count')
    op.drop_column('company_documents', 'mime_type')
    op.drop_column('company_documents', 'file_size')
    op.create_index(op.f('ix_agents_user_id'), 'agents', ['user_id'], unique=True)
    # ### end Alembic commands ###
