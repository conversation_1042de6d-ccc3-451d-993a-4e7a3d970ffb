Tôi sẽ tạo file tutorial ngắn gọn về cách sử dụng Alembic trong dự án này:
Read file: src/fastapi/alembic/versions
Tôi sẽ tạo file tutorial.md trong thư mục alembic:

```markdown:src/fastapi/alembic/tutorial.md
# Alembic Migration Tutorial - Mensetsu-kun

## 🚀 Giới thiệu
Alembic là công cụ quản lý database migration cho SQLAlchemy. Tutorial này hướng dẫn cách sử dụng trong dự án Mensetsu-kun.

## 📁 Cấu trúc thư mục
```
alembic/
├── versions/           # Chứa các file migration
├── env.py             # Cấu hình môi trường Alembic  
├── alembic.ini        # File cấu hình chính
├── seed_users.py      # Script seed dữ liệu mẫu
└── tutorial.md        # File này
```

## 🛠️ <PERSON><PERSON><PERSON> lệnh cơ bản

### 1. Tạo migration mới
```bash
alembic revision -m "tên_migration"
```

### 2. Tạo migration tự động từ model changes
```bash
alembic revision --autogenerate -m "tên_migration"
```

### 3. Chạy migration (upgrade)
```bash
# Chạy tất cả migration
alembic upgrade head

# Chạy migration cụ thể
alembic upgrade <revision_id>
```

### 4. Rollback migration (downgrade)
```bash
# Rollback 1 bước
alembic downgrade -1

# Rollback về revision cụ thể
alembic downgrade <revision_id>
```

### 5. Xem trạng thái migration
```bash
# Xem revision hiện tại
alembic current

# Xem lịch sử migration
alembic history
```

## 🗄️ Seed dữ liệu

### Chạy script seed users
```bash
python alembic/seed_users.py
```

## ⚠️ Troubleshooting

### Lỗi "relation không tồn tại"
- Kiểm tra thứ tự migration
- Đảm bảo migration tạo bảng được chạy trước

### Reset database hoàn toàn
```bash
# Xóa tất cả migration
alembic downgrade base

# Chạy lại từ đầu
alembic upgrade head
```

### Kiểm tra SQL sẽ được execute
```bash
alembic upgrade head --sql
```

## 📝 Quy tắc đặt tên Migration

- `create_<table_name>` - Tạo bảng mới
- `add_<column_name>_to_<table>` - Thêm cột
- `remove_<column_name>_from_<table>` - Xóa cột  
- `update_<table_name>` - Cập nhật bảng

## 🔧 Best Practices

1. **Luôn backup database** trước khi chạy migration
2. **Test migration** trên môi trường dev trước
3. **Viết downgrade function** cho mọi migration
4. **Không sửa migration đã deployed** - tạo migration mới thay thế
5. **Sử dụng --autogenerate** nhưng luôn review code được tạo

## 📚 Tài liệu tham khảo

- [Alembic Documentation](https://alembic.sqlalchemy.org/)
- [SQLAlchemy Models](../app/models/)
```

File này cung cấp hướng dẫn ngắn gọn và thực tế cho việc sử dụng Alembic trong dự án Mensetsu-kun, bao gồm các lệnh cơ bản, troubleshooting và best practices.