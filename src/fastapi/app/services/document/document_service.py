# app/services/document_service.py
import aiofiles
import pdfplumber
from app.models.candidate import CandidateDocument
from app.models.company import CompanyDocument
from sqlalchemy.orm import Session
from app.mixin.file_upload_mixin import DocumentUploadMixin
from fastapi import UploadFile, HTTPException
from uuid import UUID
from typing import Union, Optional
import docx
import io
from pathlib import Path
from app.services.rag.candidate_rag_service import CandidateRAGService
from app.services.rag.company_rag_service import CompanyRAGService
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    """Service xử lý upload và extract text từ documents"""
    
    def __init__(self):
        self.candidate_rag_service = CandidateRAGService()
        self.company_rag_service = CompanyRAGService()

    async def save_pdf_file(
        self, 
        file: UploadFile, 
        entity_id: UUID, 
        entity_type: str, 
        db: Session
    ) -> Union[CandidateDocument, CompanyDocument]:
        """
        Save PDF file and create document record
        
        Args:
            file: UploadFile object
            entity_id: ID of candidate or company
            entity_type: 'candidate' or 'company' 
            db: Database session
            
        Returns:
            Document record (CandidateDocument or CompanyDocument)
        """
        if entity_type not in ['candidate', 'company']:
            raise HTTPException(
                status_code=400,
                detail=f"Not supported entity_type: {entity_type}"
            )

        try:
            if entity_type == 'candidate':
                file_path, file_content, mime_type = await DocumentUploadMixin.save_candidate_cv(
                    file=file,
                    candidate_id=entity_id
                )
                
                document = CandidateDocument(
                    candidate_id=entity_id,
                    file_name=file.filename,
                    file_path=str(file_path),
                    document_type="cv",
                    file_size=len(file_content),
                    mime_type=mime_type
                )
            else:  # company
                file_path, file_content, mime_type = await DocumentUploadMixin.save_company_document(
                    file=file,
                    company_id=entity_id,
                    upload_base_dir="uploads/companies"
                )
                
                document = CompanyDocument(
                    company_id=entity_id,
                    file_name=file.filename,
                    file_path=str(file_path),
                    document_type="document",
                    file_size=len(file_content),
                    mime_type=mime_type
                )
                
            db.add(document)
            db.commit()
            db.refresh(document)
            
            logger.info(f"Saved {entity_type} document: {file_path}")
            return document

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error saving PDF file for {entity_type} {entity_id}: {e}")
            db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Error saving file {entity_type}"
            )

    async def save_candidate_cv(
        self, 
        file: UploadFile, 
        candidate_id: UUID, 
        db: Session
    ) -> tuple[CandidateDocument, Optional[str]]:
        """
        Save candidate CV and extract text content
        
        Args:
            file: CV file to upload
            candidate_id: ID of candidate
            db: Database session
            
        Returns:
            Tuple of (document_record, extracted_text)
        """
        document = await self.save_pdf_file(file, candidate_id, 'candidate', db)
        extracted_text = await self.extract_text_from_file(document.file_path)
        
        # Update document processing status
        if extracted_text:
            document.processed = True
            db.commit()
            logger.info(f"Extracted text from CV: {len(extracted_text)} characters")
        
        return document, extracted_text

    async def extract_text_from_file(self, file_path: str) -> Optional[str]:
        """
        Extract text from PDF, DOC, DOCX files
        
        Args:
            file_path: Path to file
            
        Returns:
            Extracted text or None if failed
        """
        if not file_path or not Path(file_path).exists():
            logger.warning(f"File not found: {file_path}")
            return None

        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            return await self._extract_text_from_pdf(file_path)
        elif file_extension in ['.doc', '.docx']:
            return await self._extract_text_from_docx(file_path)
        else:
            logger.warning(f"Unsupported file type for text extraction: {file_extension}")
            return None

    async def _extract_text_from_pdf(self, file_path: str) -> Optional[str]:
        """
        Extract text from PDF using pdfplumber (better than PyPDF2)
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Extracted text or None if failed
        """
        try:
            async with aiofiles.open(file_path, 'rb') as file:
                content = await file.read()
            
            # Use pdfplumber for better text extraction
            with pdfplumber.open(io.BytesIO(content)) as pdf:
                text_content = []
                
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(page_text)
            
            extracted_text = '\n'.join(text_content).strip()
            return extracted_text if extracted_text else None
            
        except Exception as e:
            logger.error(f"Error extracting PDF text from {file_path}: {e}")
            return None

    async def _extract_text_from_docx(self, file_path: str) -> Optional[str]:
        """
        Extract text from DOCX using python-docx
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            Extracted text or None if failed
        """
        try:
            doc = docx.Document(file_path)
            text_content = [
                paragraph.text.strip() 
                for paragraph in doc.paragraphs 
                if paragraph.text.strip()
            ]
            
            extracted_text = '\n'.join(text_content).strip()
            return extracted_text if extracted_text else None
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text from {file_path}: {e}")
            return None

    async def save_company_document(
        self, 
        file: UploadFile, 
        company_id: UUID, 
        db: Session
    ) -> tuple[CompanyDocument, Optional[str]]:
        """
        Save company document and extract text content
        
        Args:
            file: Document file to upload
            company_id: ID of company
            db: Database session
            
        Returns:
            Tuple of (document_record, extracted_text)
        """
        document = await self.save_pdf_file(file, company_id, 'company', db)
        extracted_text = await self.extract_text_from_file(document.file_path)
        
        if extracted_text:
            document.processed = True
            db.commit()
            logger.info(f"Extracted text from company document: {len(extracted_text)} characters")
        
        return document, extracted_text

    async def save_company_document_with_rag_sync(
        self, 
        file: UploadFile, 
        company_id: UUID, 
        company_data: dict,
        db: Session,
        sync_to_rag: bool = True
    ) -> tuple[CompanyDocument, Optional[str], Optional[dict]]:
        """
        Save company document, extract text and sync with RAG service
        
        Args:
            file: Document file to upload (PDF/DOCX)
            company_id: ID of company
            company_data: Company profile data (name, description, core_values, etc.)
            db: Database session
            sync_to_rag: Whether to sync with RAG service
            
        Returns:
            Tuple of (document_record, extracted_text, rag_sync_result)
        """
        document, extracted_text = await self.save_company_document(
            file=file,
            company_id=company_id,
            db=db
        )
        
        rag_sync_result = None
        
        if sync_to_rag and extracted_text:
            try:
                enhanced_company_data = {
                    **company_data,
                    "document_content": extracted_text,
                    "document_type": document.document_type,
                    "document_name": document.file_name
                }
                
                rag_sync_success = self.company_rag_service.load_company_data(
                    company_id=str(company_id),
                    company_data=enhanced_company_data
                )
                
                if rag_sync_success:
                    ai_summary, key_insights = self.company_rag_service.analyze_company_with_ai(
                        company_data=enhanced_company_data,
                        company_id=str(company_id)
                    )
                    
                    rag_sync_result = {
                        "sync_status": "success",
                        "chunks_processed": len(extracted_text) // 500,
                        "ai_summary": ai_summary,
                        "key_insights": key_insights,
                        "document_indexed": True
                    }
                else:
                    rag_sync_result = {"sync_status": "failed", "error": "Failed to load company data"}
                    
            except Exception as e:
                rag_sync_result = {"sync_status": "error", "error": str(e)}
        
        return document, extracted_text, rag_sync_result

    async def sync_company_documents_with_rag(
        self,
        company_id: UUID,
        company_data: dict,
        db: Session
    ) -> dict:
        """
        Sync existing company documents with RAG service
        
        Args:
            company_id: ID of company
            company_data: Company profile data
            db: Database session
            
        Returns:
            Sync result summary
        """
        from app.models.company import Company
        
        company = Company.get_company_by_id(db, company_id)
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        documents = company.documents
        total_documents = len(documents)
        processed_documents = 0
        all_document_content = []
        
        for doc in documents:
            if doc.file_path and Path(doc.file_path).exists():
                extracted_text = await self.extract_text_from_file(doc.file_path)
                if extracted_text:
                    all_document_content.append({
                        "content": extracted_text,
                        "document_type": doc.document_type,
                        "file_name": doc.file_name
                    })
                    processed_documents += 1
        
        enhanced_company_data = {
            **company_data,
            "documents": all_document_content
        }
        
        try:
            rag_sync_success = self.company_rag_service.load_company_data(
                company_id=str(company_id),
                company_data=enhanced_company_data
            )
            
            if rag_sync_success:
                ai_summary, key_insights = self.company_rag_service.analyze_company_with_ai(
                    company_data=enhanced_company_data,
                    company_id=str(company_id)
                )
                
                return {
                    "sync_status": "success",
                    "total_documents": total_documents,
                    "processed_documents": processed_documents,
                    "ai_summary": ai_summary,
                    "key_insights": key_insights
                }
            else:
                return {
                    "sync_status": "failed",
                    "total_documents": total_documents,
                    "processed_documents": processed_documents,
                    "error": "Failed to load company data to RAG"
                }
                
        except Exception as e:
            logger.error(f"Error syncing company documents with RAG: {e}")
            return {
                "sync_status": "error",
                "total_documents": total_documents,
                "processed_documents": processed_documents,
                "error": str(e)
            }

    async def save_candidate_cv_with_rag_sync(
        self, 
        file: UploadFile, 
        candidate_id: UUID, 
        candidate_data: dict,
        db: Session,
        sync_to_rag: bool = True
    ) -> tuple[CandidateDocument, Optional[str], Optional[dict]]:
        """
        Save candidate CV, extract text and sync with RAG service
        
        Args:
            file: CV file to upload
            candidate_id: ID of candidate
            candidate_data: Candidate profile data
            db: Database session
            sync_to_rag: Whether to sync with RAG service
            
        Returns:
            Tuple of (document_record, extracted_text, rag_sync_result)
        """
        document, extracted_text = await self.save_candidate_cv(
            file=file,
            candidate_id=candidate_id,
            db=db
        )
        
        rag_sync_result = None
        
        if sync_to_rag and extracted_text:
            user_id = candidate_data.get("user_id")
            if not user_id:
                logger.warning(f"⚠️ No user_id found, skipping RAG sync")
                return document, extracted_text, rag_sync_result

            try:
                rag_sync_result = await self.candidate_rag_service.sync_candidate_profile_with_cv(
                    user_id=user_id,
                    candidate_data=candidate_data,
                    cv_content=extracted_text
                )
                    
            except Exception as e:
                logger.error(f"RAG sync failed for candidate {candidate_id}: {e}")
        
        return document, extracted_text, rag_sync_result