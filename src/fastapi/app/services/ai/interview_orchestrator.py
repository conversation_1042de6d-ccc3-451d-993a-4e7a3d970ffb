# app/services/ai/interview_orchestrator.py

import json
from typing import Dict, List
from langchain_google_genai import ChatGoogleGenerativeAI
from app.core.config import settings


class InterviewOrchestrator:
    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model=settings.GEMINI_MODEL,
            google_api_key=settings.GOOGLE_API_KEY,
            temperature=0.7,
            max_tokens=500
        )
        
        self.interview_stages = {
            "introduction": {
                "questions_range": (1, 2),
                "focus": "warm-up, basic background",
                "next_stage": "experience"
            },
            "experience": {
                "questions_range": (2, 4),
                "focus": "work experience, achievements",
                "next_stage": "behavioral"
            },
            "behavioral": {
                "questions_range": (2, 3),
                "focus": "soft skills, teamwork, leadership",
                "next_stage": "technical"
            },
            "technical": {
                "questions_range": (2, 4),
                "focus": "technical skills, problem-solving",
                "next_stage": "company_fit"
            },
            "company_fit": {
                "questions_range": (1, 2),
                "focus": "cultural fit, motivation",
                "next_stage": "wrap_up"
            },
            "wrap_up": {
                "questions_range": (1, 1),
                "focus": "final thoughts, questions from candidate",
                "next_stage": "completed"
            }
        }
    
    async def generate_next_question(
        self,
        session_data: Dict,
        classification_result: Dict,
        company_context: List[Dict],
        current_message: str
    ) -> Dict:
        """
        Generate contextual next question
        """
        current_stage = session_data["current_stage"]
        conversation_history = session_data["conversation_history"]
        candidate_profile = session_data["candidate_profile"]
        
        should_advance_stage = self._should_advance_stage(
            current_stage, 
            session_data["question_count"], 
            classification_result
        )
        
        if should_advance_stage:
            current_stage = self._get_next_stage(current_stage)
            session_data["current_stage"] = current_stage
        
        question_prompt = self._build_question_prompt(
            current_stage=current_stage,
            company_context=company_context,
            candidate_profile=candidate_profile,
            conversation_history=conversation_history[-5:],  # Last 5 exchanges
            classification_result=classification_result,
            current_message=current_message
        )
        
        response = await self.llm.ainvoke(question_prompt)
        
        question_data = self._parse_question_response(response.content, current_stage)
        
        return question_data
    def _build_question_prompt(
        self,
        current_stage: str,
        company_context: List[Dict],
        candidate_profile: Dict,
        conversation_history: List[Dict],
        classification_result: Dict,
        current_message: str
    ) -> str:
        
        company_info = "\n".join([ctx["content"] for ctx in company_context[:3]])
        
        history_text = "\n".join(
            f"Q: {item.get('question', '')}\nA: {item.get('answer', '')}"
            for item in conversation_history
        )
        
        profile_text = f"""
            Skills: {list(candidate_profile.get('skills_detected', set()))}
            Experience: {candidate_profile.get('experience_years', 'Not specified')} years
            Industries: {candidate_profile.get('industries', [])}
            Soft Skills: {candidate_profile.get('soft_skills', [])}
        """
        
        stage_config = self.interview_stages.get(current_stage, {})
        
        prompt = f"""
        You are a professional AI interviewer.
        
        ### COMPANY CONTEXT
        {company_info}
        
        ### INTERVIEW STAGE
        Stage: {current_stage.upper()}
        Focus: {stage_config.get('focus', '')}
        
        ### CANDIDATE PROFILE
        {profile_text}
        
        ### RECENT CONVERSATION
        {history_text}
        
        ### LATEST RESPONSE FROM CANDIDATE
        "{current_message}"
        
        ### RESPONSE CLASSIFICATION
        - Type: {classification_result.get('message_type')}
        - Clarity: {classification_result.get('clarity_score')}
        - Skills Mentioned: {classification_result.get('extracted_info', {}).get('skills_mentioned', [])}
        
        --- 
        ### TASK
        Ask the **next interview question** that:
        1. Follows naturally from the candidate's last answer
        2. Matches this interview stage's focus
        3. Tests relevant skills or knowledge
        4. Fits the candidate's experience
        5. Keeps the conversation smooth and engaging
        
        Follow-up if:
        - The answer was vague/brief
        - New topics were introduced
        - More examples/details are needed
        
        ---
        
        ### OUTPUT FORMAT (Valid JSON)
        {{
            "question": "Your interview question here (in Japanese)",
            "type": "behavioral|technical|experience|cultural_fit|follow_up",
            "reasoning": "Why this question is appropriate",
            "expected_elements": ["element1", "element2"],
            "stage": "{current_stage}",
            "estimated_remaining": "number of questions left"
        }}

        Only output the JSON. Do not add commentary.
        """
        
        return prompt
    
    def _parse_question_response(self, response: str, current_stage: str) -> Dict:
        """
        Parse LLM response into structured format
        """
        try:
            cleaned_response = response.strip()
            if cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[7:-3]
            elif cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[3:-3]
            
            question_data = json.loads(cleaned_response)
            
            if "question" not in question_data:
                raise ValueError("Missing question field")
            
            question_data.setdefault("type", "general")
            question_data.setdefault("stage", current_stage)
            question_data.setdefault("estimated_remaining", 3)
            
            return question_data
            
        except Exception as e:  
            return self._generate_fallback_question(current_stage)
    
    def _generate_fallback_question(self, current_stage: str) -> Dict:
        """
        Generate fallback question if parsing fails
        """
        fallback_questions = {
            "introduction": "Bạn có thể chia sẻ về bản thân và kinh nghiệm làm việc của mình?",
            "experience": "Hãy kể về một dự án hoặc thành tích mà bạn tự hào nhất trong công việc?",
            "behavioral": "Bạn xử lý như thế nào khi phải làm việc dưới áp lực cao?",
            "technical": "Bạn có thể mô tả quy trình làm việc của mình khi giải quyết một vấn đề kỹ thuật phức tạp?",
            "company_fit": "Điều gì khiến bạn quan tâm đến công ty chúng tôi?",
            "wrap_up": "Bạn có câu hỏi nào về công ty hoặc vị trí này không?"
        }
        
        return {
            "question": fallback_questions.get(current_stage, "Bạn có thể chia sẻ thêm về kinh nghiệm của mình?"),
            "type": "general",
            "stage": current_stage,
            "estimated_remaining": 3
        }
    
    def _should_advance_stage(self, current_stage: str, question_count: int, classification: Dict) -> bool:
        """
        Determine if interview should advance to next stage
        """
        stage_config = self.interview_stages.get(current_stage, {})
        min_questions, max_questions = stage_config.get("questions_range", (1, 3))
        
        # Check if we've asked enough questions for this stage
        stage_questions = self._count_stage_questions(current_stage, question_count)
        
        # Advance if we've reached minimum and response quality is good
        if stage_questions >= min_questions:
            clarity_score = classification.get("clarity_score", 0.5)
            relevance_score = classification.get("relevance_score", 0.5)
            
            # Advance if good response quality or reached max questions
            return (clarity_score > 0.7 and relevance_score > 0.6) or stage_questions >= max_questions
        
        return False
    
    def _get_next_stage(self, current_stage: str) -> str:
        """
        Get next interview stage
        """
        return self.interview_stages.get(current_stage, {}).get("next_stage", current_stage)
    
    def _count_stage_questions(self, stage: str, total_questions: int) -> int:
        """
        Count questions asked in current stage (simplified)
        """
        # In practice, you'd track this more precisely
        return min(total_questions, 3)