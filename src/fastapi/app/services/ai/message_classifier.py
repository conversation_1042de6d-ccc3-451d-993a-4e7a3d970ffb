# app/services/ai/message_classifier.py

import json
import hashlib
from typing import Dict, List
from langchain_google_genai import ChatGoogleGenerativeAI
from app.core.config import settings
import redis
import logging

logger = logging.getLogger(__name__)

class GeminiMessageClassifier:
    def __init__(self):
        self.model = ChatGoogleGenerativeAI(
            model=settings.GEMINI_MODEL,
            google_api_key=settings.GOOGLE_API_KEY,
            temperature=0.1,
            max_tokens=150
        )
        self.redis_client = redis.from_url(settings.REDIS_URL)
        
    async def classify_message(self, message: str, conversation_history: List[Dict], session_id: str) -> Dict:
        """
        Classify message from candidate with comprehensive context
        """
        context_str = json.dumps(conversation_history[-3:] if conversation_history else [])
        cache_key = f"classify:{hashlib.md5((message + context_str).encode()).hexdigest()}"
        
        cached_result = self.redis_client.get(cache_key)
        if cached_result:
            return json.loads(cached_result)
        
        classification_prompt = self._build_classification_prompt(message, conversation_history)
        
        try:
            response = await self.model.ainvoke(classification_prompt)
            result = self._parse_classification_response(response.content)
            
            await self.redis_client.setex(cache_key, settings.CACHE_TTL, json.dumps(result))
            return result
            
        except Exception as e:
            return self._fallback_classification(message)
    
    def _build_classification_prompt(self, message: str, context: List[Dict]) -> str:
        context_text = ""
        if context:
            context_text = "\n".join([
                f"Q: {item.get('question', '')}\nA: {item.get('answer', '')}" 
                for item in context[-3:]
            ])
        
        return f"""
        You are an AI assistant helping to analyze interview conversations.
        
        ### Previous Context
        {context_text}
        
        ### Current Message
        "{message}"
        
        Classify the message and extract detailed information in **valid JSON** with the following structure:
        {{
            "message_type": "introduction | experience_answer | behavioral_answer | technical_answer | question_back | clarification | ready_next | off_topic",
            "confidence": 0.0 - 1.0,
            "extracted_info": {{
                "skills_mentioned": [ "..." ],
                "experience_years": number | null,
                "industries_mentioned": [ "..." ],
                "soft_skills": [ "..." ],
                "technical_details": [ "..." ],
                "achievements": [ "..." ],
                "challenges_mentioned": [ "..." ]
            }},
            "sentiment": "positive | neutral | negative",
            "clarity_score": 0.0 - 1.0,
            "relevance_score": 0.0 - 1.0
        }}
        
        Only return a valid JSON object. Do not include any explanations.
        """
    
    def _parse_classification_response(self, response: str) -> Dict:
        try:
            cleaned_response = response.strip()

            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:].strip("` \n")
            elif cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[3:].strip("` \n")
            elif cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3].strip("` \n")

            return json.loads(cleaned_response)

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse classification response: {e}\nResponse was: {response}")
            return self._fallback_classification("")
    
    def _fallback_classification(self, message: str) -> Dict:
        return {
            "message_type": "general_answer",
            "confidence": 0.5,
            "extracted_info": {
                "skills_mentioned": [],
                "experience_years": None,
                "industries_mentioned": [],
                "soft_skills": [],
                "technical_details": [],
                "achievements": [],
                "challenges_mentioned": []
            },
            "sentiment": "neutral",
            "clarity_score": 0.7,
            "relevance_score": 0.6
        }
        