import base64
import json
import logging
import tempfile
import os
from typing import Dict, Optional
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from app.core.config import settings
from app.models.question import Question
from app.models.video_analysis import VideoSegment

logger = logging.getLogger(__name__)

class GeminiVideoAnalyzer:
    def __init__(self):
        try:
            # Sử dụng LangChain với OpenAI thay vì Google Generative AI
            self.llm = ChatOpenAI(
                model="gpt-4-vision-preview",
                api_key=settings.OPENAI_API_KEY,
                temperature=0.1,
                max_tokens=2000
            )
            self.is_available = True
            logger.info("Video Analyzer initialized successfully with <PERSON><PERSON><PERSON><PERSON>")
        except Exception as e:
            logger.error(f"Failed to initialize Video Analyzer: {e}")
            self.llm = None
            self.is_available = False
    
    async def analyze_question_segment(
        self,
        video_data: bytes,
        question: Question,
        segment: VideoSegment
    ) -> Dict:
        """Analyze video segment for a specific question"""
        if not self.is_available:
            logger.warning("Video analyzer not available, returning fallback analysis")
            return self._create_fallback_analysis(question, segment)
        
        try:
            # Create analysis prompt
            prompt = self._create_question_specific_prompt(question, segment)
            
            # Prepare video for analysis
            video_part = await self._prepare_video_for_analysis(video_data)
            if not video_part:
                return self._create_fallback_analysis(question, segment)
            
            # Send to AI for analysis
            logger.info(f"Sending video for analysis (Question: {question.question_type})")
            
            response = await self._call_ai_api(video_part, prompt)
            
            if response:
                # Parse and validate response
                analysis_result = self._parse_and_validate_response(response, question, segment)
                logger.info(f"Video analysis completed for question {question.id}")
                return analysis_result
            else:
                return self._create_fallback_analysis(question, segment)
                
        except Exception as e:
            logger.error(f"Video analysis failed: {e}")
            return self._create_fallback_analysis(question, segment)
    
    def _create_question_specific_prompt(self, question: Question, segment: VideoSegment) -> str:
        """Create analysis prompt tailored to question type and context"""
        
        duration_minutes = round(segment.duration_seconds / 60, 1) if segment.duration_seconds else 0
        
        base_prompt = f"""
You are an expert HR interviewer analyzing a video interview response.

**QUESTION CONTEXT:**
- Question: "{question.question_text}"
- Type: {question.question_type}
- Interviewer Role: {question.interviewer_role}
- Expected Duration: {question.estimated_time_minutes} minutes
- Actual Duration: {duration_minutes} minutes

**EVALUATION CRITERIA:**
{json.dumps(question.evaluation_criteria, indent=2) if question.evaluation_criteria else "Standard interview criteria"}

**EXPECTED ANSWER POINTS:**
{json.dumps(question.expected_answer_points, indent=2) if question.expected_answer_points else "Not specified"}

**ANALYSIS REQUEST:**
Analyze this video response focusing on the following aspects:
"""

        # Customize analysis based on question type
        if question.question_type == "behavioral":
            specific_analysis = """
1. **Communication Effectiveness** (0-10):
   - Clarity and articulation of speech
   - Logical flow and structure (STAR method usage)
   - Storytelling ability and engagement
   - Confidence in delivery

2. **Non-verbal Communication** (0-10):
   - Professional body language and posture
   - Appropriate eye contact and facial expressions
   - Hand gestures that support communication
   - Overall presence and composure

3. **Behavioral Competencies** (0-10):
   - Emotional intelligence demonstration
   - Leadership qualities or potential
   - Problem-solving approach in examples
   - Team collaboration indicators

4. **Response Quality** (0-10):
   - Relevance to the question asked
   - Use of specific, concrete examples
   - Demonstration of self-awareness
   - Learning and growth mindset
"""
        elif question.question_type == "technical":
            specific_analysis = """
1. **Technical Communication** (0-10):
   - Ability to explain complex concepts clearly
   - Use of appropriate technical terminology
   - Logical progression in explanations
   - Confidence in technical knowledge

2. **Problem-Solving Demonstration** (0-10):
   - Systematic approach to problems
   - Creative thinking and innovation
   - Handling of uncertainty or unknowns
   - Methodology and process explanation

3. **Professional Competence** (0-10):
   - Depth of technical knowledge shown
   - Practical experience evidence
   - Continuous learning attitude
   - Quality standards awareness

4. **Communication Style** (0-10):
   - Ability to simplify complex topics
   - Active listening and responsiveness
   - Professional presentation
   - Enthusiasm for technical challenges
"""
        else:  # introduction or general
            specific_analysis = """
1. **First Impression** (0-10):
   - Professional appearance and grooming
   - Initial confidence and energy
   - Positive attitude and enthusiasm
   - Professional communication style

2. **Self-Presentation** (0-10):
   - Clear and engaging introduction
   - Relevant background highlights
   - Personal branding consistency
   - Authentic and genuine delivery

3. **Communication Skills** (0-10):
   - Clear articulation and pacing
   - Appropriate volume and tone
   - Structured response organization
   - Active engagement with question

4. **Cultural Fit Indicators** (0-10):
   - Values alignment demonstration
   - Motivation and interest level
   - Professional maturity
   - Interpersonal skills preview
"""

        return base_prompt + specific_analysis + """

**OUTPUT FORMAT (JSON ONLY):**
```json
{
    "scores": {
        "communication_effectiveness": 0-10,
        "non_verbal_communication": 0-10,
        "content_quality": 0-10,
        "overall_impression": 0-10,
        "question_specific_score": 0-10
    },
    "detailed_analysis": {
        "strengths": ["specific strength 1", "specific strength 2", "specific strength 3"],
        "areas_for_improvement": ["area 1", "area 2"],
        "key_observations": ["observation 1", "observation 2", "observation 3"],
        "professionalism_notes": "Professional presentation assessment"
    },
    "behavioral_insights": {
        "confidence_level": "high|medium|low",
        "engagement_quality": "excellent|good|average|poor",
        "authenticity": "genuine|somewhat_genuine|rehearsed",
        "stress_indicators": "none|minimal|moderate|high",
        "communication_style": "confident|conversational|formal|hesitant"
    },
    "recommendations": {
        "immediate_feedback": "Positive, constructive feedback for the candidate",
        "development_suggestions": ["suggestion 1", "suggestion 2"],
        "interviewer_notes": "Private notes for the interviewer's reference",
        "follow_up_questions": ["follow-up question 1", "follow-up question 2"]
    }
}
```

Please analyze the video and provide your assessment in the exact JSON format above.
"""

    async def _prepare_video_for_analysis(self, video_data: bytes) -> Optional[Dict]:
        """Prepare video data for AI analysis"""
        try:
            # Save video to temporary file
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                temp_file.write(video_data)
                temp_file_path = temp_file.name
            
            # Convert to base64 for LangChain
            video_base64 = base64.b64encode(video_data).decode('utf-8')
            
            return {
                "type": "image_url",
                "image_url": {
                    "url": f"data:video/mp4;base64,{video_base64}",
                    "detail": "high"
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to prepare video for analysis: {e}")
            return None
        finally:
            # Clean up temp file
            if 'temp_file_path' in locals():
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

    async def _call_ai_api(self, video_part: Dict, prompt: str) -> Optional[str]:
        """Call AI API using LangChain"""
        try:
            messages = [
                SystemMessage(content="You are an expert HR interviewer analyzing video interview responses. Provide detailed, professional analysis in JSON format."),
                HumanMessage(content=[
                    {"type": "text", "text": prompt},
                    video_part
                ])
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"AI API call failed: {e}")
            return None

    def _parse_and_validate_response(
        self, 
        response_text: str, 
        question: Question, 
        segment: VideoSegment
    ) -> Dict:
        """Parse and validate AI response"""
        try:
            # Extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                logger.warning("No JSON found in response")
                return self._create_fallback_analysis(question, segment)
            
            json_str = response_text[json_start:json_end]
            analysis_data = json.loads(json_str)
            
            # Validate required fields
            required_fields = ["scores", "detailed_analysis", "behavioral_insights", "recommendations"]
            for field in required_fields:
                if field not in analysis_data:
                    logger.warning(f"Missing required field: {field}")
                    return self._create_fallback_analysis(question, segment)
            
            # Add metadata
            analysis_data["metadata"] = {
                "analyzed_at": datetime.utcnow().isoformat(),
                "question_id": str(question.id),
                "segment_id": str(segment.id),
                "model_used": "gpt-4-vision-preview",
                "analysis_duration_seconds": segment.duration_seconds
            }
            
            return analysis_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return self._create_fallback_analysis(question, segment)
        except Exception as e:
            logger.error(f"Failed to validate response: {e}")
            return self._create_fallback_analysis(question, segment)

    def _create_fallback_analysis(self, question: Question, segment: VideoSegment) -> Dict:
        """Create fallback analysis when AI is unavailable"""
        duration_minutes = round(segment.duration_seconds / 60, 1) if segment.duration_seconds else 0
        
        return {
            "scores": {
                "communication_effectiveness": 7,
                "non_verbal_communication": 7,
                "content_quality": 7,
                "overall_impression": 7,
                "question_specific_score": 7
            },
            "detailed_analysis": {
                "strengths": ["Video analysis temporarily unavailable"],
                "areas_for_improvement": ["Please try again later"],
                "key_observations": ["Video duration: {} minutes".format(duration_minutes)],
                "professionalism_notes": "Analysis service temporarily unavailable"
            },
            "behavioral_insights": {
                "confidence_level": "medium",
                "engagement_quality": "good",
                "authenticity": "genuine",
                "stress_indicators": "minimal",
                "communication_style": "conversational"
            },
            "recommendations": {
                "immediate_feedback": "Video analysis is currently unavailable. Please try again later.",
                "development_suggestions": ["Retry video analysis when service is available"],
                "interviewer_notes": "Fallback analysis used due to service unavailability",
                "follow_up_questions": ["Please retry the video analysis"]
            },
            "metadata": {
                "analyzed_at": datetime.utcnow().isoformat(),
                "question_id": str(question.id),
                "segment_id": str(segment.id),
                "model_used": "fallback",
                "analysis_duration_seconds": segment.duration_seconds,
                "fallback_used": True
            }
        }
