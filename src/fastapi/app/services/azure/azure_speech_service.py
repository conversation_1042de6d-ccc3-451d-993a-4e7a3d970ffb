import azure.cognitiveservices.speech as speechsdk
import asyncio
import base64
import hashlib
import json
import logging
from typing import Optional, Dict, List, Tuple
from app.core.config import settings
import time
import threading

logger = logging.getLogger(__name__)

class AzureSpeechService:
    def __init__(self):
        self.speech_key = settings.AZURE_SPEECH_KEY
        self.speech_region = settings.AZURE_SPEECH_REGION
        self.cache = {}
        self.cache_lock = threading.Lock()
        self.max_cache_size = 100
        self.cache_ttl = 3600
        self.is_available = self._check_availability()
        
    def _check_availability(self) -> bool:
        """Check if Azure Speech Service is properly configured"""
        if not self.speech_key or not self.speech_region:
            logger.warning("Azure Speech Service not configured - TTS will use fallback")
            return False
        if self.speech_key in ["", "your_key", "your_azure_speech_key"]:
            logger.warning("Azure Speech Service has placeholder key - TTS will use fallback")
            return False
        
        try:
            test_config = speechsdk.SpeechConfig(
                subscription=self.speech_key, 
                region=self.speech_region
            )
            test_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm
            )
            logger.info(f"Azure Speech Service configured successfully for region: {self.speech_region}")
            return True
        except Exception as e:
            logger.error(f"Azure Speech Service configuration failed: {str(e)}")
            return False
        
    def _get_cache_key(self, text: str, voice: str) -> str:
        """Generate cache key from text and voice"""
        content = f"{text}_{voice}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _clean_cache(self):
        """Clean expired cache entries"""
        with self.cache_lock:
            current_time = time.time()
            expired_keys = []
            
            for key, (data, timestamp) in self.cache.items():
                if current_time - timestamp > self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                
            if len(self.cache) > self.max_cache_size:
                sorted_items = sorted(self.cache.items(), key=lambda x: x[1][1])
                items_to_remove = len(self.cache) - self.max_cache_size
                for i in range(items_to_remove):
                    del self.cache[sorted_items[i][0]]
    
    def _get_speech_config(self, voice: str) -> speechsdk.SpeechConfig:
        """Create and configure speech config"""
        try:
            speech_config = speechsdk.SpeechConfig(
                subscription=self.speech_key, 
                region=self.speech_region
            )
            
            speech_config.speech_synthesis_voice_name = voice
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm
            )
            
            return speech_config
            
        except Exception as e:
            logger.error(f"Failed to create speech config: {str(e)}")
            raise
    
    async def text_to_speech(self, text: str, voice: str = "ja-JP-NanamiNeural") -> bytes:
        """Convert text to speech using Azure Speech Service"""
        if not self.is_available:
            logger.info("Azure Speech Service not available, using fallback")
            return await self._generate_fallback_audio(text)
        
        self._clean_cache()
        
        cache_key = self._get_cache_key(text, voice)
        with self.cache_lock:
            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    logger.info(f"TTS cache hit for: {text[:50]}...")
                    return cached_data
                else:
                    del self.cache[cache_key]
        
        try:
            speech_config = self._get_speech_config(voice)
            
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=None
            )
            
            def synthesize():
                return synthesizer.speak_text_async(text).get()
            
            loop = asyncio.get_event_loop()
            result = await asyncio.wait_for(
                loop.run_in_executor(None, synthesize),
                timeout=30.0
            )
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                audio_data = result.audio_data
                
                with self.cache_lock:
                    self.cache[cache_key] = (audio_data, time.time())
                
                logger.info(f"TTS success: {len(audio_data)} bytes generated")
                return audio_data
                
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = speechsdk.CancellationDetails(result)
                logger.error(f"TTS cancelled: {cancellation_details.reason}")
                if cancellation_details.reason == speechsdk.CancellationReason.Error:
                    logger.error(f"TTS error details: {cancellation_details.error_details}")
                return await self._generate_fallback_audio(text)
            else:
                logger.error(f"TTS failed with reason: {result.reason}")
                return await self._generate_fallback_audio(text)
                
        except asyncio.TimeoutError:
            logger.error("TTS request timed out")
            return await self._generate_fallback_audio(text)
        except Exception as e:
            logger.error(f"TTS Exception: {str(e)}")
            return await self._generate_fallback_audio(text)
    
    async def text_to_speech_with_ssml(self, ssml: str) -> bytes:
        """Convert SSML to speech"""
        if not self.is_available:
            raise Exception("Azure Speech Service not available")
            
        try:
            speech_config = speechsdk.SpeechConfig(
                subscription=self.speech_key, 
                region=self.speech_region
            )
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm
            )
            
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=None
            )
            
            def synthesize():
                return synthesizer.speak_ssml_async(ssml).get()
            
            loop = asyncio.get_event_loop()
            result = await asyncio.wait_for(
                loop.run_in_executor(None, synthesize),
                timeout=30.0
            )
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return result.audio_data
            else:
                raise Exception(f"SSML synthesis failed: {result.reason}")
                
        except asyncio.TimeoutError:
            raise Exception("SSML TTS request timed out")
        except Exception as e:
            raise Exception(f"SSML TTS Error: {str(e)}")
    
    async def _generate_fallback_audio(self, text: str) -> bytes:
        """Generate fallback audio when Azure TTS is not available"""
        try:
            sample_rate = 16000
            duration = max(0.5, min(len(text) * 0.1, 10.0))
            samples = int(sample_rate * duration)
            
            import math
            audio_data = bytearray()
            for i in range(samples): 
                sample = int(16383 * math.sin(2 * math.pi * 440 * i / sample_rate))
                audio_data.extend(sample.to_bytes(2, byteorder='little', signed=True))
            
            logger.info(f"Generated fallback audio: {len(audio_data)} bytes")
            return bytes(audio_data)
            
        except Exception as e:
            logger.error(f"Failed to generate fallback audio: {str(e)}")
            return b'\x00' * 1024
    
    def get_available_voices(self) -> List[Dict[str, str]]:
        """Get list of available voices"""
        return [
            {"id": "ja-JP-NanamiNeural", "name": "Nanami (Female)", "language": "ja"},
            {"id": "ja-JP-KeitaNeural", "name": "Keita (Male)", "language": "ja"},
            {"id": "en-US-JennyNeural", "name": "Jenny (Female)", "language": "en"},
            {"id": "vi-VN-HoaiMyNeural", "name": "Hoai My (Female)", "language": "vi"},
            {"id": "zh-CN-YunyiMultilingualNeural", "name": "Yunyi (Multilingual)", "language": "zh"},
        ]
    
    def get_status(self) -> Dict[str, any]:
        """Get service status"""
        # Test available formats
        available_formats = []
        try:
            for format_name in dir(speechsdk.SpeechSynthesisOutputFormat):
                if not format_name.startswith('_'):
                    available_formats.append(format_name)
        except Exception as e:
            logger.error(f"Error getting formats: {e}")
        
        return {
            "available": self.is_available,
            "speech_key_configured": bool(self.speech_key and self.speech_key not in ["", "your_key"]),
            "speech_region": self.speech_region,
            "cache_size": len(self.cache),
            "cache_max_size": self.max_cache_size,
            "cache_ttl": self.cache_ttl,
            "available_formats": available_formats[:10],
            "speech_key_length": len(self.speech_key) if self.speech_key else 0
        }

    async def handle_tts_request(self, text: str, voice: str = "ja-JP-NanamiNeural") -> bytes:
        """Handle TTS request with input validation"""
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")

        # Limit text length to prevent abuse
        if len(text) > 5000:
            raise ValueError("Text too long (max 5000 characters)")

        # Validate voice
        available_voices = [v["id"] for v in self.get_available_voices()]
        if voice not in available_voices:
            logger.warning(f"Invalid voice {voice}, using default")
            voice = "ja-JP-NanamiNeural"

        return await self.text_to_speech(text, voice)

    async def speech_to_text(self, audio_data: bytes, language: str = "vi-VN") -> dict:
        """Convert speech to text using Azure Speech Service"""
        if not self.is_available:
            logger.warning("Azure Speech Service not available, cannot perform speech-to-text")
            return {"text": "", "confidence": 0.0, "error": "Service not available"}

        try:
            # Create speech config
            speech_config = speechsdk.SpeechConfig(
                subscription=self.speech_key,
                region=self.speech_region
            )
            speech_config.speech_recognition_language = language

            # Create audio stream from bytes
            push_stream = speechsdk.audio.PushAudioInputStream()
            push_stream.write(audio_data)
            push_stream.close()

            audio_config = speechsdk.audio.AudioConfig(stream=push_stream)

            # Create speech recognizer
            recognizer = speechsdk.SpeechRecognizer(
                speech_config=speech_config,
                audio_config=audio_config
            )

            # Recognize speech
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: recognizer.recognize_once_async().get()
            )

            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                # Try to extract confidence if available
                confidence = 0.8  # Default confidence
                try:
                    json_result = result.properties.get(speechsdk.PropertyId.SpeechServiceResponse_JsonResult)
                    if json_result:
                        json_data = json.loads(json_result)
                        confidence = json_data.get("NBest", [{}])[0].get("Confidence", 0.8)
                except Exception as e:
                    logger.warning(f"Failed to extract confidence: {e}")

                return {
                    "text": result.text,
                    "confidence": confidence,
                    "duration": result.duration / 10000000  # Convert to seconds
                }
            elif result.reason == speechsdk.ResultReason.NoMatch:
                logger.warning("Speech could not be recognized")
                return {"text": "", "confidence": 0.0, "error": "Speech could not be recognized"}
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation = speechsdk.CancellationDetails(result)
                if cancellation.reason == speechsdk.CancellationReason.Error:
                    logger.error(f"Speech recognition error: {cancellation.error_details}")
                    return {"text": "", "confidence": 0.0, "error": cancellation.error_details}
                else:
                    logger.warning(f"Speech recognition canceled: {cancellation.reason}")
                    return {"text": "", "confidence": 0.0, "error": f"Canceled: {cancellation.reason}"}
            else:
                logger.warning(f"Unexpected recognition result reason: {result.reason}")
                return {"text": "", "confidence": 0.0, "error": f"Unexpected result: {result.reason}"}

        except Exception as e:
            logger.error(f"Speech-to-text error: {str(e)}")
            return {"text": "", "confidence": 0.0, "error": str(e)}