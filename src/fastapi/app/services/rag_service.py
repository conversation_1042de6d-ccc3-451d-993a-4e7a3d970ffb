# backend/app/services/rag_service.py
from langchain_google_genai import GoogleGenerativeAIEmbeddings 
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from app.core.config import settings
import chromadb
from typing import List, Dict
import logging
import time
from chromadb.utils import embedding_functions

logger = logging.getLogger(__name__)

class RAGService:
    def __init__(self):
        self.use_google_embeddings = True
        
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(
                google_api_key=settings.GOOGLE_API_KEY,
                model="models/gemini-embedding-exp-03-07",
                task_type="RETRIEVAL_DOCUMENT"
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Google embeddings: {e}")
            self.use_google_embeddings = False
            self.embeddings = embedding_functions.DefaultEmbeddingFunction()
        
        self.client = chromadb.HttpClient(
            host="chroma",
            port=8000
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100
        )
    
    async def create_company_collection(self, company_id: str):
        """Create a dedicated collection for each company"""
        try:
            collection = self.client.get_or_create_collection(
                name=company_id,
                metadata={"company_id": company_id, "type": "interview_context"}
            )
            logger.info(f"Collection {company_id} created/retrieved successfully")
            return collection
        except Exception as e:
            logger.error(f"Error creating collection {company_id}: {e}")
            return None
    
    async def load_company_data(self, company_id: str, company_data: Dict) -> bool:
        """Load and index company data into vector database"""
        try:
            collection = await self.create_company_collection(company_id)
            if not collection:
                return False
            documents = []
            
            if company_data.get("description"):
                documents.append(Document(
                    page_content=f"Company Description: {company_data['description']}",
                    metadata={"type": "description", "company_id": company_id}
                ))
            
            if company_data.get("core_values"):
                core_values_text = "\n".join([
                    f"- {value}" for value in company_data["core_values"]
                ]) if isinstance(company_data["core_values"], list) else str(company_data["core_values"])
                
                documents.append(Document(
                    page_content=f"Core Values:\n{core_values_text}",
                    metadata={"type": "core_values", "company_id": company_id}
                ))
            
            if company_data.get("vision_mission"):
                documents.append(Document(
                    page_content=f"Vision & Mission: {company_data['vision_mission']}",
                    metadata={"type": "vision_mission", "company_id": company_id}
                ))
            
            all_chunks = []
            for doc in documents:
                chunks = self.text_splitter.split_documents([doc])
                all_chunks.extend(chunks)
            
            if all_chunks:
                texts = [chunk.page_content for chunk in all_chunks]
                metadatas = [chunk.metadata for chunk in all_chunks]
                ids = [f"{company_id}_{i}" for i in range(len(texts))]
                
                if self.use_google_embeddings:
                    embeddings = self.embeddings.embed_documents(texts)
                    collection.add(
                        documents=texts,
                        metadatas=metadatas,
                        ids=ids,
                        embeddings=embeddings
                    )
                else:
                    collection.add(
                        documents=texts,
                        metadatas=metadatas,
                        ids=ids
                    )
                
                logger.info(f"Successfully indexed {len(texts)} chunks for company {company_id}")
                return True
        
        except Exception as e:
            logger.error(f"Failed to load company data for {company_id}: {e}")
            return False
    
    async def _add_documents_with_google_embeddings(self, collection, chunks, max_retries=2):
        for attempt in range(max_retries):
            try:
                batch_size = 3
                for i in range(0, len(chunks), batch_size):
                    batch = chunks[i:i + batch_size]
                    
                    texts = [doc.page_content for doc in batch]
                    metadatas = [doc.metadata for doc in batch]
                    ids = [f"{doc.metadata['company_id']}_{doc.metadata['type']}_{i+j}" 
                          for j, doc in enumerate(batch)]
                    
                    try:
                        embeddings = self.embeddings.embed_documents(texts)
                        
                        collection.add(
                            documents=texts,
                            embeddings=embeddings,
                            metadatas=metadatas,
                            ids=ids
                        )
                        
                        if i + batch_size < len(chunks):
                            time.sleep(2)
                            
                    except Exception as embed_error:
                        if "429" in str(embed_error) or "quota" in str(embed_error).lower():
                            logger.warning(f"Google API quota exceeded, switching to default embeddings")
                            self.use_google_embeddings = False
                            self.embeddings = embedding_functions.DefaultEmbeddingFunction()
                            return await self._add_documents_with_default_embeddings(collection, chunks)
                        else:
                            raise embed_error
                
                return  # Success
                
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 30
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time} seconds: {e}")
                    time.sleep(wait_time)
                else:
                    logger.error(f"All Google embedding attempts failed: {e}")
                    self.use_google_embeddings = False
                    self.embeddings = embedding_functions.DefaultEmbeddingFunction()
                    return await self._add_documents_with_default_embeddings(collection, chunks)
    
    async def _add_documents_with_default_embeddings(self, collection, chunks):
        """Add documents using default ChromaDB embeddings"""
        try:
            texts = [doc.page_content for doc in chunks]
            metadatas = [doc.metadata for doc in chunks]
            ids = [f"{doc.metadata['company_id']}_{doc.metadata['type']}_{i}" 
                  for i, doc in enumerate(chunks)]
            
            # ChromaDB sẽ tự động generate embeddings
            collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            logger.info("Successfully added documents with default embeddings")
            
        except Exception as e:
            logger.error(f"Error adding documents with default embeddings: {e}")
            raise e
    
    async def search_company_context(
        self, 
        company_id: str, 
        query: str, 
        k: int = 3
    ) -> List[str]:
        """Search for relevant context from company data"""
        try:
            collection = self.client.get_collection(company_id)
            
            if self.use_google_embeddings:
                query_embedding = self.embeddings.embed_query(query)
                results = collection.query(
                    query_embeddings=[query_embedding],
                    n_results=k
                )
            else:
                results = collection.query(
                    query_texts=[query],
                    n_results=k
                )
                
            contexts = []
            
            if results["documents"]:
                for doc_list in results["documents"]:
                    contexts.extend(doc_list)
            
            return contexts[:k]
        except Exception as e:
            logger.error(f"Failed to search context for company {company_id}: {e}")
            return []
        
    async def retrieve_contextual_information(
        self, 
        company_id: str, 
        query_context: Dict,
        max_results: int = 5
    ) -> List[Dict]:
        """ Truy xuất thông tin contextual dựa trên trạng thái phỏng vấn """
        
        try:
            collection = self.client.get_collection(name=company_id)
        except:
            return []
        
        search_query = self._build_search_query(query_context)
        
        results = collection.query(
            query_texts=[search_query],
            n_results=max_results,
            include=["documents", "metadatas", "distances"]
        )
        
        formatted_results = []
        if results["documents"] and results["documents"][0]:
            for i, doc in enumerate(results["documents"][0]):
                formatted_results.append({
                    "content": doc,
                    "metadata": results["metadatas"][0][i] if results["metadatas"][0] else {},
                    "relevance_score": 1 - (results["distances"][0][i] if results["distances"][0] else 0.5)
                })
        
        return formatted_results
