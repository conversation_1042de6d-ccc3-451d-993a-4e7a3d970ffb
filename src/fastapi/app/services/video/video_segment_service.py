import logging
import base64
from datetime import datetime
from typing import Dict, <PERSON>tional, Tu<PERSON>
from uuid import UUID
from sqlalchemy.orm import Session

from app.models.video_analysis import VideoSegment
from app.models.question import Question

logger = logging.getLogger(__name__)

class VideoSegmentService:
    def __init__(self):
        self.active_segments = {}  # session_id -> segment_info
        self.video_chunks = {}     # session_id -> list of chunks
        
    async def start_question_segment(
        self,
        db: Session,
        session_id: str,
        question_id: UUID,
        interview_session_id: UUID
    ) -> VideoSegment:
        """Start new video segment for a question"""
        try:
            # Create new video segment
            segment = VideoSegment.create_segment(
                db=db,
                interview_session_id=interview_session_id,
                question_id=question_id,
                start_time=datetime.utcnow()
            )
            
            # Store segment info
            self.active_segments[session_id] = {
                "segment_id": str(segment.id),
                "question_id": str(question_id),
                "start_time": segment.start_time,
                "chunk_count": 0,
                "total_size": 0
            }
            
            # Initialize video chunks storage
            self.video_chunks[session_id] = []
            
            logger.info(f"Started video segment {segment.id} for session {session_id}")
            return segment
            
        except Exception as e:
            logger.error(f"Failed to start video segment: {e}")
            raise
    
    async def add_video_chunk(
        self,
        session_id: str,
        chunk_data: str,  # base64 encoded
        timestamp: datetime
    ) -> bool:
        """Add video chunk to current segment"""
        try:
            if session_id not in self.active_segments:
                logger.warning(f"No active segment for session {session_id}")
                return False
            
            # Decode base64 chunk
            video_bytes = base64.b64decode(chunk_data)
            
            # Store chunk
            self.video_chunks[session_id].append({
                "data": video_bytes,
                "timestamp": timestamp,
                "size": len(video_bytes)
            })
            
            # Update segment info
            self.active_segments[session_id]["chunk_count"] += 1
            self.active_segments[session_id]["total_size"] += len(video_bytes)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add video chunk: {e}")
            return False
    
    async def end_current_segment(
        self,
        db: Session,
        session_id: str
    ) -> Optional[Tuple[VideoSegment, bytes]]:
        """End current video segment and return video data"""
        try:
            if session_id not in self.active_segments:
                logger.warning(f"No active segment for session {session_id}")
                return None
            
            # Get segment info
            segment_info = self.active_segments[session_id]
            segment_id = UUID(segment_info["segment_id"])
            
            # Get segment from database
            segment = db.query(VideoSegment).filter(VideoSegment.id == segment_id).first()
            if not segment:
                logger.error(f"Segment not found: {segment_id}")
                return None
            
            # Combine all video chunks
            video_data = b""
            for chunk in self.video_chunks.get(session_id, []):
                video_data += chunk["data"]
            
            # Complete segment
            segment.complete_segment(
                db=db,
                end_time=datetime.utcnow(),
                video_data=video_data
            )
            
            # Clean up
            del self.active_segments[session_id]
            del self.video_chunks[session_id]
            
            logger.info(f"Completed video segment {segment_id} for session {session_id}")
            return segment, video_data
            
        except Exception as e:
            logger.error(f"Failed to end video segment: {e}")
            return None
    
    def get_segment_info(self, session_id: str) -> Optional[Dict]:
        """Get current segment info"""
        return self.active_segments.get(session_id) 