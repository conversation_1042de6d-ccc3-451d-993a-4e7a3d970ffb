# app/services/websocket/protocols.py
from typing import Protocol, Dict, Any, runtime_checkable
from fastapi import WebSocket

@runtime_checkable
class WebSocketHandlerProtocol(Protocol):
    """Protocol interface cho WebSocket handler operations"""
    
    active_connections: Dict[str, WebSocket]
    connection_metadata: Dict[str, Dict[str, Any]]
    
    async def disconnect(self, session_id: str) -> None:
        """Disconnect and cleanup session"""
        ...
    
    async def update_connection_metadata(self, session_id: str, data: Dict[str, Any]) -> None:
        """Update connection metadata"""
        ...
        
    async def start_recording(self, session_id: str) -> bool:
        pass
    
    async def stop_recording(self, session_id: str) -> str:
        pass


@runtime_checkable 
class MessageUtilsProtocol(Protocol):
    """Protocol interface cho message utilities"""

    async def send_message(self, session_id: str, message: Dict[str, Any]) -> None:
        """Send message to session"""
        ...
    
    async def send_error(self, session_id: str, error_message: str) -> None:
        """Send error message"""
        ...
    
    async def heartbeat_monitor(self, session_id: str) -> None:
        """Monitor heartbeat for session"""
        ...

class VideoStreamHandlerProtocol:
    async def start_recording(self, session_id: str) -> bool:
        pass
    
    async def stop_recording(self, session_id: str) -> str:
        pass
    
    async def save_video_frame(self, session_id: str, frame_data: bytes) -> bool:
        pass
