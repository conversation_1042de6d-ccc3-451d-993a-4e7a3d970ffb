# app/services/websocket/manager.py
from typing import Optional
from app.services.websocket.handle_websocket import WebSocketHandler

class WebSocketManager:
    """Singleton manager for WebSocket connections"""
    
    _instance: Optional[WebSocketHandler] = None
    
    @classmethod
    def get_instance(cls) -> WebSocketHandler:
        if cls._instance is None:
            cls._instance = WebSocketHandler()
        return cls._instance
    
    @classmethod
    def reset_instance(cls) -> None:
        cls._instance = None

def get_websocket_handler() -> WebSocketHandler:
    return WebSocketManager.get_instance()
