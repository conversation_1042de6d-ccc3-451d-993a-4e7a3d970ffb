# app/services/websocket/message_dispatcher.py
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass
from enum import Enum

from fastapi import WebSocket

from .config import config
from app.core.error_handling import (
    websocket_error_handler, <PERSON>rror<PERSON><PERSON>, <PERSON>rrorContext, ErrorSeverity
)
from app.core.logging_config import log_performance

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Enumeration of message types"""
    CONNECTION_REQUEST = "connection_request"
    GENERATE_SPEECH = "generate_speech"
    PONG = "pong"
    WEBRTC_OFFER = "webrtc_offer"
    WEBRTC_ANSWER = "webrtc_answer"
    WEBRTC_CANDIDATE = "webrtc_candidate"
    WEBRTC_READY = "webrtc_ready"
    START_VIDEO_RECORDING = "start_video_recording"
    VIDEO_CHUNK = "video_chunk"
    END_VIDEO_RECORDING = "end_video_recording"
    ANSWER_COMPLETED = "answer_completed"
    CANDIDATE_MESSAGE = "candidate_message"
    UNKNOWN = "unknown"


@dataclass
class MessageHandler:
    """Represents a message handler"""
    handler_func: Callable
    description: str
    requires_session: bool = True
    requires_websocket: bool = True


class MessageDispatcher:
    """Centralized message dispatcher for WebSocket messages"""
    
    def __init__(self):
        self.handlers: Dict[MessageType, MessageHandler] = {}
        self.middleware: List[Callable] = []
        self._setup_default_handlers()
    
    def _setup_default_handlers(self):
        """Set up default message handlers"""
        # These will be registered by the main WebSocket handler
        pass
    
    def register_handler(
        self, 
        message_type: MessageType, 
        handler_func: Callable,
        description: str = "",
        requires_session: bool = True,
        requires_websocket: bool = True
    ):
        """Register a message handler"""
        self.handlers[message_type] = MessageHandler(
            handler_func=handler_func,
            description=description,
            requires_session=requires_session,
            requires_websocket=requires_websocket
        )
        logger.debug(f"Registered handler for {message_type.value}: {description}")
    
    def register_middleware(self, middleware_func: Callable):
        """Register middleware function"""
        self.middleware.append(middleware_func)
        logger.debug(f"Registered middleware: {middleware_func.__name__}")
    
    def _get_message_type(self, message_data: Dict[str, Any]) -> MessageType:
        """Determine message type from message data"""
        message_type_str = message_data.get("type", "")
        
        try:
            return MessageType(message_type_str)
        except ValueError:
            # Check if it's a WebRTC message
            if message_type_str in config.websocket.webrtc_message_types:
                return MessageType(message_type_str)
            
            # Check if it's a video message
            if message_type_str in config.websocket.video_message_types:
                return MessageType(message_type_str)
            
            # Default to candidate message if no type specified
            if not message_type_str:
                return MessageType.CANDIDATE_MESSAGE
            
            logger.warning(f"Unknown message type: {message_type_str}")
            return MessageType.UNKNOWN
    
    @log_performance("message_dispatch", "websocket")
    async def dispatch(
        self,
        websocket: WebSocket,
        session_id: str,
        message_data: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Dispatch message to appropriate handler with enhanced error handling"""
        error_context = ErrorContext(
            session_id=session_id,
            operation="message_dispatch",
            component="message_dispatcher",
            additional_data={"message_type": message_data.get("type", "unknown")}
        )

        try:
            message_type = self._get_message_type(message_data)
            error_context.additional_data["parsed_message_type"] = message_type.value

            # Run middleware with error handling
            for i, middleware in enumerate(self.middleware):
                try:
                    result = await middleware(websocket, session_id, message_data, context)
                    if result is False:  # Middleware can block processing
                        logger.info(f"Message processing blocked by middleware {i} for session {session_id}")
                        return False
                except Exception as e:
                    error_info = await websocket_error_handler.handle_message_error(
                        websocket, session_id, message_data, e, f"middleware_{i}"
                    )
                    return False

            # Get handler
            handler = self.handlers.get(message_type)
            if not handler:
                error_info = websocket_error_handler.error_handler.create_error(
                    code=ErrorCode.WS_MESSAGE_INVALID,
                    message=f"No handler registered for message type: {message_type.value}",
                    context=error_context,
                    severity=ErrorSeverity.MEDIUM,
                    recovery_suggestions=[
                        "Check message type spelling",
                        "Verify supported message types",
                        "Update client to use supported message types"
                    ]
                )
                await websocket_error_handler.error_handler.send_error_to_websocket(websocket, error_info)
                return False

            # Prepare handler arguments
            handler_kwargs = {
                "message_data": message_data,
                "session_id": session_id if handler.requires_session else None,
                "websocket": websocket if handler.requires_websocket else None,
                "context": context or {}
            }

            # Remove None values
            handler_kwargs = {k: v for k, v in handler_kwargs.items() if v is not None}

            # Call handler with error handling
            try:
                result = await handler.handler_func(**handler_kwargs)

                logger.debug(f"Message {message_type.value} processed successfully for session {session_id}")
                return result if isinstance(result, bool) else True

            except Exception as handler_error:
                error_info = await websocket_error_handler.handle_message_error(
                    websocket, session_id, message_data, handler_error, f"handler_{message_type.value}"
                )
                return False

        except Exception as e:
            # Handle unexpected errors in message dispatch
            error_info = await websocket_error_handler.handle_message_error(
                websocket, session_id, message_data, e, "message_dispatch"
            )
            return False
    
    async def _send_error(self, websocket: WebSocket, error_message: str):
        """Send error message to client"""
        try:
            error_response = {
                "type": "error",
                "error": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send_text(json.dumps(error_response))
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")
    
    def get_registered_handlers(self) -> Dict[str, str]:
        """Get list of registered handlers"""
        return {
            message_type.value: handler.description 
            for message_type, handler in self.handlers.items()
        }
    
    def is_handler_registered(self, message_type: MessageType) -> bool:
        """Check if handler is registered for message type"""
        return message_type in self.handlers


class MessageValidator:
    """Validates incoming messages"""
    
    @staticmethod
    def validate_json(data: str) -> Optional[Dict[str, Any]]:
        """Validate and parse JSON data"""
        try:
            return json.loads(data)
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON: {e}")
            return None
    
    @staticmethod
    def validate_message_structure(message: Dict[str, Any]) -> bool:
        """Validate basic message structure"""
        if not isinstance(message, dict):
            return False
        
        # Check for required fields
        if "type" not in message:
            logger.warning("Message missing 'type' field")
            return False
        
        return True
    
    @staticmethod
    def validate_webrtc_offer(message: Dict[str, Any]) -> bool:
        """Validate WebRTC offer message"""
        offer = message.get("offer")
        if not offer:
            return False
        
        required_fields = ["sdp", "type"]
        return all(field in offer for field in required_fields)
    
    @staticmethod
    def validate_webrtc_candidate(message: Dict[str, Any]) -> bool:
        """Validate WebRTC candidate message"""
        candidate = message.get("candidate")
        if not candidate:
            return False
        
        required_fields = ["candidate", "sdpMid"]
        return all(field in candidate for field in required_fields)
    
    @staticmethod
    def validate_tts_request(message: Dict[str, Any]) -> bool:
        """Validate TTS request message"""
        text = message.get("text", "")
        if not text or not text.strip():
            return False
        
        # Check text length
        if len(text) > 5000:
            logger.warning("TTS text too long")
            return False
        
        return True


class RateLimiter:
    """Rate limiting for message processing"""
    
    def __init__(self):
        self.request_counts: Dict[str, List[float]] = {}
    
    async def check_rate_limit(self, session_id: str) -> bool:
        """Check if session is within rate limits"""
        import time
        
        current_time = time.time()
        window_start = current_time - config.websocket.rate_limit_window
        
        # Initialize or clean old requests
        if session_id not in self.request_counts:
            self.request_counts[session_id] = []
        
        # Remove old requests
        self.request_counts[session_id] = [
            req_time for req_time in self.request_counts[session_id] 
            if req_time > window_start
        ]
        
        # Check limit
        if len(self.request_counts[session_id]) >= config.websocket.max_requests_per_window:
            logger.warning(f"Rate limit exceeded for session {session_id}")
            return False
        
        # Add current request
        self.request_counts[session_id].append(current_time)
        return True
    
    def cleanup_session(self, session_id: str):
        """Clean up rate limiting data for session"""
        self.request_counts.pop(session_id, None)


# Global instances
message_dispatcher = MessageDispatcher()
message_validator = MessageValidator()
rate_limiter = RateLimiter()
