# app/services/websocket/utils.py
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from .protocols import WebSocketHandlerProtocol
from fastapi import WebSocket
from app.core.error_handling import error_handler, <PERSON>rrorCode, ErrorContext, ErrorSeverity
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class WebSocketUtils:
    def __init__(self, handler: Optional[WebSocketHandlerProtocol] = None):
        self._handler = handler
        self._heartbeat_tasks: Dict[str, asyncio.Task] = {}
    
    @property
    def handler(self) -> WebSocketHandlerProtocol:
        if self._handler is None:
            raise ValueError("Handler is not set")
        return self._handler
    
    async def send_message(self, session_id: str, message: Dict[str, Any], 
        message_type: str = "message", **kwargs) -> bool:
        
        if session_id not in self.handler.active_connections:
            logger.warning(f"Session {session_id} not found in active connections")
            return False
            
        try:
            websocket = self.handler.active_connections[session_id]
            message_json = json.dumps({
                "type": message_type,
                "message": message,
                "timestamp": datetime.utcnow().isoformat(),
                **kwargs
            })
            await websocket.send_text(message_json)
            
            await self.handler.update_connection_metadata(session_id, {
                "bytes_sent": len(message_json.encode('utf-8')),
                "connection_quality": "excellent"
            })
            
            if session_id in self.handler.connection_metadata:
                self.handler.connection_metadata[session_id]["message_count"] += 1
            
            logger.debug(f"Message sent successfully to {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to {session_id}: {e}")
            await self.handler.disconnect(session_id)
            return False
    
    async def send_system_message(self, session_id: str, message: str, **kwargs) -> bool:
        
        system_payload = {
            "type": "system_message",
            "message": message,
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs
        }
        return await self.send_message(session_id, system_payload)
    
    async def start_heartbeat_monitor(self, session_id: str, interval: int = 30) -> None:
        
        if session_id in self._heartbeat_tasks:
            logger.warning(f"Heartbeat already running for {session_id}")
            return
            
        task = asyncio.create_task(
            self._heartbeat_loop(session_id, interval)
        )
        self._heartbeat_tasks[session_id] = task
        logger.info(f"Started heartbeat monitor for {session_id}")
    
    async def stop_heartbeat_monitor(self, session_id: str) -> None:
        
        if session_id in self._heartbeat_tasks:
            self._heartbeat_tasks[session_id].cancel()
            del self._heartbeat_tasks[session_id]
            logger.info(f"Stopped heartbeat monitor for {session_id}")
    
    async def _heartbeat_loop(self, session_id: str, interval: int) -> None:
        
        while session_id in self.handler.active_connections:
            try:
                await asyncio.sleep(interval)
                
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                success = await self.send_message(session_id, ping_message)
                if not success:
                    logger.warning(f"Heartbeat ping failed for {session_id}")
                    break
                    
            except asyncio.CancelledError:
                logger.info(f"Heartbeat cancelled for {session_id}")
                break
            except Exception as e:
                logger.error(f"Heartbeat error for {session_id}: {e}")
                break
        
        await self.stop_heartbeat_monitor(session_id)
    
    async def broadcast_message(self, message: Dict[str, Any], exclude_sessions: Optional[list] = None) -> int:
        exclude_sessions = exclude_sessions or []
        success_count = 0
        
        for session_id in list(self.handler.active_connections.keys()):
            if session_id not in exclude_sessions:
                if await self.send_message(session_id, message):
                    success_count += 1

        return success_count

    async def send_error(self,
        websocket: WebSocket,
        error_message: str,
        error_code: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> None:
        try:
            context = ErrorContext(
                session_id=session_id,
                operation="send_error",
                component="websocket_endpoint"
            )

            error_info = error_handler.create_error(
                code=ErrorCode.WS_MESSAGE_INVALID if not error_code else ErrorCode(error_code),
                message=error_message,
                context=context,
                severity=ErrorSeverity.MEDIUM
            )

            await error_handler.send_error_to_websocket(websocket, error_info)
        except Exception as e:
            try:
                fallback_response = {
                    "type": "error",
                    "error": error_message,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                await websocket.send_text(json.dumps(fallback_response))
            except:
                pass

    async def send_ack(self, websocket: WebSocket, session_id: str, original_type: str) -> None:
        try:
            ack_response = {
                "type": "webrtc_ack",
                "sessionId": session_id,
                "originalType": original_type,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send_text(json.dumps(ack_response))
        except Exception as e:
            logger.error(f"Failed to send ack message: {e}")
