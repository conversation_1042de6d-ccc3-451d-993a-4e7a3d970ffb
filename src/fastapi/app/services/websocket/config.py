# app/services/websocket/config.py
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
import os
import json
import logging
from pathlib import Path
from functools import lru_cache

logger = logging.getLogger(__name__)


@dataclass
class WebSocketConfig:
    """Configuration for WebSocket connections"""

    connection_timeout: float = 180.0
    activity_timeout: float = 300.0
    ping_interval: float = 30.0

    rate_limit_window: int = 60
    max_requests_per_window: int = 30

    max_video_duration: float = 600.0
    video_frame_timeout: float = 30.0
    max_consecutive_errors: int = 10
    video_fps: float = 30.0
    retry_delay: float = 0.5
    connection_check_interval: float = 10.0

    ice_servers: List[Dict[str, Any]] = field(default_factory=lambda: [
        {"urls": "stun:stun.l.google.com:19302"}
    ])

    video_storage_paths: List[Path] = field(default_factory=lambda: [
        Path("/tmp/interview_videos"),
    ])

    webrtc_message_types: List[str] = field(default_factory=lambda: [
        "webrtc_offer",
        "webrtc_answer",
        "webrtc_candidate",
        "webrtc_ready"
    ])

    video_message_types: List[str] = field(default_factory=lambda: [
        "start_video_recording",
        "video_chunk",
        "end_video_recording",
        "answer_completed"
    ])

    cleanup_timeout: float = 60.0
    max_reconnect_attempts: int = 3
    reconnect_delay: float = 2.0

    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like access to configuration values"""
        return getattr(self, key, default)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for serialization"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Path):
                result[key] = str(value)
            elif isinstance(value, list) and value and isinstance(value[0], Path):
                result[key] = [str(p) for p in value]
            else:
                result[key] = value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "WebSocketConfig":
        """Create configuration from dictionary"""
        if "video_storage_paths" in data:
            data["video_storage_paths"] = [Path(p) for p in data["video_storage_paths"]]
        
        return cls(**data)


@dataclass
class WebRTCConfig:
    """Configuration specific to WebRTC functionality"""

    ice_servers: List[Dict[str, Any]] = field(default_factory=lambda: [
        {"urls": "stun:stun.l.google.com:19302"}
    ])

    ice_candidate_timeout: float = 5.0
    max_ice_candidates: int = 50

    video_codec: str = "XVID"
    video_quality: int = 30

    valid_connection_states: List[str] = field(default_factory=lambda: [
        "new", "connecting", "connected", "disconnected", "failed", "closed"
    ])

    offer_timeout: float = 10.0
    answer_timeout: float = 10.0
    candidate_timeout: float = 5.0

    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like access to configuration values"""
        return getattr(self, key, default)

    def is_valid_connection_state(self, state: str) -> bool:
        """Check if connection state is valid"""
        return state in self.valid_connection_states


@dataclass
class VideoConfig:
    """Configuration for video processing"""
    
    candidates_dir_paths: List[str] = field(default_factory=lambda: [
        "/tmp/interview_videos"
    ])
    
    video_codec: str = "XVID"
    filename_template: str = "interview_{session_id}_{timestamp}.avi"
    
    max_file_size_mb: int = 500
    compression_quality: int = 30
    frame_buffer_size: int = 100

    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like access to configuration values"""
        return getattr(self, key, default)


@dataclass
class MessageConfig:
    """Configuration for message handling"""
    
    webrtc_message_types: List[str] = field(default_factory=lambda: [
        "webrtc_offer", 
        "webrtc_answer", 
        "webrtc_candidate", 
        "webrtc_ready"
    ])
    
    video_message_types: List[str] = field(default_factory=lambda: [
        "start_video_recording", 
        "video_chunk", 
        "end_video_recording", 
        "answer_completed"
    ])

    max_message_size: int = 1024 * 1024
    message_timeout: float = 30.0

    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like access to configuration values"""
        return getattr(self, key, default)

    def is_webrtc_message(self, message_type: str) -> bool:
        """Check if message type is WebRTC related"""
        return message_type in self.webrtc_message_types

    def is_video_message(self, message_type: str) -> bool:
        """Check if message type is video related"""
        return message_type in self.video_message_types


class ConfigManager:
    """Centralized configuration manager with environment variable support"""

    def __init__(self):
        self.websocket = WebSocketConfig()
        self.webrtc = WebRTCConfig()
        self.video = VideoConfig()
        self.message = MessageConfig()
        self._load_from_environment()

    def _safe_cast(self, value: str, cast_type: type, default: Any) -> Any:
        """Safely cast environment variable to target type"""
        try:
            if cast_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif cast_type == list:
                return json.loads(value)
            else:
                return cast_type(value)
        except (ValueError, TypeError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to cast '{value}' to {cast_type.__name__}: {e}")
            return default

    def _load_from_environment(self):
        """Load configuration from environment variables with validation"""
        env_mappings = {
            "WS_CONNECTION_TIMEOUT": ("websocket", "connection_timeout", float),
            "WS_ACTIVITY_TIMEOUT": ("websocket", "activity_timeout", float),
            "WS_PING_INTERVAL": ("websocket", "ping_interval", float),
            "WS_RATE_LIMIT_WINDOW": ("websocket", "rate_limit_window", int),
            "WS_MAX_REQUESTS_PER_WINDOW": ("websocket", "max_requests_per_window", int),
            
            "VIDEO_MAX_DURATION": ("websocket", "max_video_duration", float),
            "VIDEO_FRAME_TIMEOUT": ("websocket", "video_frame_timeout", float),
            "VIDEO_MAX_CONSECUTIVE_ERRORS": ("websocket", "max_consecutive_errors", int),
            "VIDEO_FPS": ("websocket", "video_fps", float),
            "VIDEO_RETRY_DELAY": ("websocket", "retry_delay", float),
            "VIDEO_CONNECTION_CHECK_INTERVAL": ("websocket", "connection_check_interval", float),
            
            "WEBRTC_ICE_SERVERS": ("webrtc", "ice_servers", list),
            "WEBRTC_ICE_CANDIDATE_TIMEOUT": ("webrtc", "ice_candidate_timeout", float),
            "WEBRTC_MAX_ICE_CANDIDATES": ("webrtc", "max_ice_candidates", int),
            "WEBRTC_VIDEO_CODEC": ("webrtc", "video_codec", str),
            "WEBRTC_VIDEO_QUALITY": ("webrtc", "video_quality", int),
            
            "MSG_MAX_MESSAGE_SIZE": ("message", "max_message_size", int),
            "MSG_MESSAGE_TIMEOUT": ("message", "message_timeout", float),
        }

        for env_var, (config_section, attr_name, cast_type) in env_mappings.items():
            if env_value := os.getenv(env_var):
                config_obj = getattr(self, config_section)
                current_value = getattr(config_obj, attr_name)
                new_value = self._safe_cast(env_value, cast_type, current_value)
                setattr(config_obj, attr_name, new_value)
                logger.info(f"Loaded {env_var}: {new_value}")

    @lru_cache(maxsize=1)
    def get_video_storage_path(self) -> Optional[Path]:
        """Get the first available video storage path with caching"""
        for path in self.websocket.video_storage_paths:
            try:
                path.mkdir(parents=True, exist_ok=True)
                test_file = path / ".write_test"
                test_file.write_text("test")
                test_file.unlink()
                logger.info(f"Using video storage path: {path}")
                return path
            except (PermissionError, OSError) as e:
                logger.warning(f"Cannot use video storage path {path}: {e}")
                continue
        
        logger.error("No writable video storage path found")
        return None

    def is_webrtc_message(self, message_type: str) -> bool:
        """Check if message type is WebRTC related"""
        return self.message.is_webrtc_message(message_type)

    def is_video_message(self, message_type: str) -> bool:
        """Check if message type is video related"""
        return self.message.is_video_message(message_type)

    def validate_configuration(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        if self.websocket.connection_timeout <= 0:
            issues.append("connection_timeout must be positive")
        
        if self.websocket.video_frame_timeout <= 0:
            issues.append("video_frame_timeout must be positive")
        
        if not self.get_video_storage_path():
            issues.append("No writable video storage path available")
        
        if not self.webrtc.ice_servers:
            issues.append("At least one ICE server must be configured")
        
        if self.websocket.max_requests_per_window <= 0:
            issues.append("max_requests_per_window must be positive")
        
        return issues

    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration for debugging"""
        return {
            "websocket": {
                "connection_timeout": self.websocket.connection_timeout,
                "activity_timeout": self.websocket.activity_timeout,
                "video_frame_timeout": self.websocket.video_frame_timeout,
                "max_consecutive_errors": self.websocket.max_consecutive_errors,
            },
            "webrtc": {
                "ice_servers_count": len(self.webrtc.ice_servers),
                "video_codec": self.webrtc.video_codec,
                "video_quality": self.webrtc.video_quality,
            },
            "video": {
                "storage_paths_count": len(self.video.candidates_dir_paths),
                "current_storage_path": str(self.get_video_storage_path()),
            },
            "validation_issues": self.validate_configuration(),
        }

    def reload_from_environment(self):
        """Reload configuration from environment variables"""
        self.get_video_storage_path.cache_clear()
        
        self._load_from_environment()
        
        issues = self.validate_configuration()
        if issues:
            logger.warning(f"Configuration validation issues after reload: {issues}")
        
        logger.info("Configuration reloaded from environment")


config = ConfigManager()

validation_issues = config.validate_configuration()
if validation_issues:
    logger.warning(f"Configuration validation issues: {validation_issues}")
else:
    logger.info("Configuration validated successfully")


websocket_config = config.websocket
webrtc_config = config.webrtc
video_config = config.video
message_config = config.message