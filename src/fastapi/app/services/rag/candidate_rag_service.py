# backend/app/services/rag/candidate_rag_service.py

from langchain_google_genai import GoogleGenerativeAIEmbeddings 
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from app.core.config import settings
import chromadb
from typing import List, Optional
from chromadb.utils import embedding_functions
from uuid import UUID
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class CandidateRAGService:
    def __init__(self):
        self.use_google_embeddings = self._setup_embeddings()
        self.client = self._setup_chromadb_client()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100
        )
    
    def _setup_embeddings(self) -> bool:
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(
                google_api_key=settings.GOOGLE_API_KEY,
                model="models/gemini-embedding-exp-03-07",
                task_type="RETRIEVAL_DOCUMENT"
            )
            return True
        except Exception as e:
            logger.warning(f"Failed to initialize Google embeddings, using default: {e}")
            self.embeddings = embedding_functions.DefaultEmbeddingFunction()
            return False
    
    def _setup_chromadb_client(self) -> chromadb.HttpClient:
        return chromadb.HttpClient(host="chroma", port=8000)
    
    def _get_collection_name(self, user_id: str) -> str:
        return user_id
    
    def _get_or_create_collection(self, collection_name: str):
        try:
            return self.client.get_collection(collection_name)
        except Exception:
            return self.client.create_collection(
                name=collection_name,
                metadata={"type": "candidate_profile", "user_id": collection_name}
            )
    
    def _validate_collection_exists(self, collection_name: str) -> bool:
        try:
            self.client.get_collection(collection_name)
            return True
        except Exception:
            return False
    
    async def create_collection_candidate(self, collection_name: str, candidate_data: dict) -> dict:
        """
        
        Args:
            collection_name: collection name (user_id)
            candidate_data: candidate data
            
        Returns:
            dict: result
        """
        full_collection_name = self._get_collection_name(collection_name)
        collection = self._get_or_create_collection(full_collection_name)
        
        if not collection:
            return {
                "status": "error",
                "message": "Failed to create collection"
            }
        
        documents = []
        documents.extend(self._process_personal_info(candidate_data))
        documents.extend(self._process_skills_experience(candidate_data))
        documents.extend(self._process_documents(candidate_data))
        
        if documents:
            await self._upsert_to_collection(full_collection_name, documents)
        
        return {
            "collection_name": full_collection_name,
            "documents_count": len(documents),
            "status": "success"
        }

    def _process_personal_info(self, candidate_data: dict) -> List[Document]:
        personal_fields = ["name_kana", "age", "employment_status", "company", "position", "address"]
        
        content_parts = [
            f"{field}: {candidate_data[field]}" 
            for field in personal_fields 
            if candidate_data.get(field)
        ]
        
        if not content_parts:
            return []
        
        content = "Personal Information\n" + "\n".join(content_parts)
        
        return [self._create_document(
            content=content,
            user_id=candidate_data["user_id"],
            data_type="personal"
        )]

    def _process_skills_experience(self, candidate_data: dict) -> List[Document]:
        skill_fields = ["main_skill", "other_skill", "experience", "education", "language"]
        documents = []
        
        for field in skill_fields:
            value = candidate_data.get(field)
            if not value:
                continue
                
            content = f"{field.replace('_', ' ').title()}\n{value}"
            chunks = self.text_splitter.split_text(content)
            
            for i, chunk in enumerate(chunks):
                documents.append(self._create_document(
                    content=chunk,
                    user_id=candidate_data["user_id"],
                    data_type=field,
                    chunk_index=i
                ))
        
        return documents

    def _process_documents(self, candidate_data: dict) -> List[Document]:
        doc_fields = ["profile", "other_info"]
        documents = []
        
        for field in doc_fields:
            value = candidate_data.get(field)
            if not value:
                continue
                
            content = f"{field.replace('_', ' ').title()}\n{str(value)}"
            chunks = self.text_splitter.split_text(content)
            
            for i, chunk in enumerate(chunks):
                documents.append(self._create_document(
                    content=chunk,
                    user_id=candidate_data["user_id"],
                    data_type=field,
                    chunk_index=i
                ))
        
        return documents
    
    def _create_document(self, content: str, user_id: str, data_type: str, chunk_index: int = 0) -> Document:
        return Document(
            page_content=content,
            metadata={
                "user_id": str(user_id),
                "data_type": data_type,
                "chunk_index": chunk_index,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
           
    async def upload_resume(self, user_id: UUID, resume: str) -> dict:
        """
        
        Args:
            user_id: ID candidate
            resume: resume
            
        Returns:
            dict: result
        """
        collection_name = self._get_collection_name(str(user_id))
        
        chunks = self.text_splitter.split_text(f"Resume\n{resume}")
        documents = [
            self._create_document(
                content=chunk,
                user_id=str(user_id),
                data_type="resume",
                chunk_index=i
            )
            for i, chunk in enumerate(chunks)
        ]
        
        await self._upsert_to_collection(collection_name, documents)
        return {"status": "success", "chunks": len(documents)}
    
    async def _upsert_to_collection(self, collection_name: str, documents: List[Document]):
        """
        
        Args:
            collection_name: collection name
            documents: documents to save
        """
        if not documents:
            return
        
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        ids = [
            f"{doc.metadata['user_id']}_{doc.metadata['data_type']}_{doc.metadata['chunk_index']}" 
            for doc in documents
        ]
        
        if self.use_google_embeddings:
            embeddings = await self.embeddings.aembed_documents(texts)
        else:
            embeddings = [self.embeddings([text]) for text in texts]
        
        collection = self.client.get_collection(collection_name)
        collection.upsert(
            embeddings=embeddings,
            documents=texts,
            metadatas=metadatas,
            ids=ids
        )

    async def search_candidate_info(
        self, 
        user_id: UUID, 
        query: str, 
        limit: int = 5, 
        relevance_threshold: float = 0.5
    ) -> dict:
        """
        
        Args:
            user_id: ID candidate
            query: query
            limit: limit
            relevance_threshold: relevance_threshold
            
        Returns:
            dict: result
        """
        collection_name = self._get_collection_name(str(user_id))
        collection = self.client.get_collection(collection_name)

        if self.use_google_embeddings:
            query_embedding = await self.embeddings.aembed_query(query)
        else:
            query_embedding = self.embeddings([query])[0]
        
        search_limit = max(limit * 3, 15)
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=search_limit,
            include=["documents", "metadatas", "distances"]
        )
        
        return self._organize_search_results(
            user_id=user_id,
            query=query,
            results=results,
            limit=limit,
            relevance_threshold=relevance_threshold
        )
    
    async def _organize_search_results(
        self, 
        user_id: UUID, 
        query: str, 
        results: dict, 
        limit: int, 
        relevance_threshold: float
    ) -> dict:
        organized_results = {
            "candidate_info": {
                "user_id": str(user_id),
                "search_query": query,
                "relevance_threshold": relevance_threshold,
                "total_results": 0,
                "filtered_results": 0
            },
            "personal_information": {},
            "skills_and_experience": {},
            "education_and_language": {},
            "other_information": {},
            "search_ranking": []
        }
        
        await self._add_personal_info_to_results(user_id, organized_results)
        
        filtered_results = self._filter_and_categorize_results(
            results, relevance_threshold, organized_results
        )
        
        filtered_results.sort(key=lambda x: x["relevance_score"], reverse=True)
        organized_results["search_ranking"] = filtered_results[:limit]
        
        original_count = len(results["documents"][0]) if results["documents"] else 0
        organized_results["candidate_info"]["total_results"] = original_count
        organized_results["candidate_info"]["filtered_results"] = len(filtered_results)
        
        organized_results["summary"] = self._create_search_summary(organized_results, filtered_results)
        
        return organized_results
    
    async def _add_personal_info_to_results(self, user_id: UUID, organized_results: dict):
        personal_data = await self.get_candidate_data_by_type(user_id, "personal")
        
        if personal_data.get("status") != "success":
            return
            
        personal_content = personal_data.get("content", "")
        personal_info = {}
        
        for line in personal_content.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                personal_info[key.strip()] = value.strip()
        
        organized_results["personal_information"] = {
            "name_kana": personal_info.get("name_kana", "No information"),
            "age": personal_info.get("age", "No information"),
            "employment_status": personal_info.get("employment_status", "No information"),
            "company": personal_info.get("company", "No information"),
            "position": personal_info.get("position", "No information"),
            "address": personal_info.get("address", "No information"),
            "raw_content": personal_content
        }
    
    def _filter_and_categorize_results(
        self, 
        results: dict, 
        relevance_threshold: float, 
        organized_results: dict
    ) -> List[dict]:
        filtered_results = []
        
        if not results["documents"] or not results["metadatas"]:
            return filtered_results
        
        for i, (doc, metadata) in enumerate(zip(results["documents"][0], results["metadatas"][0])):
            data_type = metadata.get("data_type", "unknown")
            distance = results["distances"][0][i] if results["distances"] and results["distances"][0] else 1.0
            relevance_score = 1 - distance
            
            if relevance_score < relevance_threshold:
                continue
                
            result_item = {
                "content": doc,
                "data_type": data_type,
                "relevance_score": relevance_score,
                "metadata": metadata
            }
            
            filtered_results.append(result_item)
            self._categorize_result_item(result_item, organized_results)
        
        return filtered_results
    
    def _categorize_result_item(self, result_item: dict, organized_results: dict):  
        data_type = result_item["data_type"]
        
        if data_type in ["main_skill", "other_skill", "experience"]:
            category = "skills_and_experience"
        elif data_type in ["education", "language"]:
            category = "education_and_language"
        elif data_type in ["profile", "other_info"]:
            category = "other_information"
        else:
            return
        
        if data_type not in organized_results[category]:
            organized_results[category][data_type] = []
        organized_results[category][data_type].append(result_item)
    
    def _create_search_summary(self, organized_results: dict, filtered_results: List[dict]) -> dict:
        summary = {
            "candidate_name": organized_results["personal_information"].get("name_kana", "No information"),
            "top_skills": [],
            "experience_summary": "",
            "best_matches": [],
            "has_relevant_results": len(filtered_results) > 0,
            "search_quality": self._assess_search_quality(filtered_results)
        }
        
        skills_exp = organized_results["skills_and_experience"]
        for skill_type in ["main_skill", "other_skill"]:
            if skill_type in skills_exp:
                summary["top_skills"].extend([
                    item["content"] for item in skills_exp[skill_type]
                ])
        
        if "experience" in skills_exp:
            summary["experience_summary"] = " ".join([
                item["content"] for item in skills_exp["experience"]
            ])
        
        summary["best_matches"] = [
            {
                "content": item["content"],
                "type": item["data_type"],
                "score": item["relevance_score"]
            }
            for item in filtered_results[:3]
        ]
        
        return summary
    
    def _assess_search_quality(self, filtered_results: List[dict]) -> str:
        if not filtered_results:
            return "low"
        
        highest_score = filtered_results[0]["relevance_score"]
        
        if highest_score > 0.7:
            return "high"
        elif len(filtered_results) > 0:
            return "medium"
        else:
            return "low"
   
    async def update_candidate_data(self, user_id: UUID, data_type: str, content: str) -> dict:
        """
        
        Args:
            user_id: ID candidate
            data_type: data type (resume, experience, etc.)
            content: new content
            
        Returns:
            dict: result
        """
        collection_name = self._get_collection_name(str(user_id))
        
        if not self._validate_collection_exists(collection_name):
            raise Exception("Candidate collection not found")
        
        collection = self.client.get_collection(collection_name)
        
        existing_results = collection.get(
            where={"data_type": data_type},
            include=["ids"]
        )
        
        if existing_results["ids"]:
            collection.delete(ids=existing_results["ids"])
        
        chunks = self.text_splitter.split_text(f"{data_type.replace('_', ' ').title()}\n{content}")
        documents = [
            self._create_document(
                content=chunk,
                user_id=str(user_id),
                data_type=data_type,
                chunk_index=i
            )
            for i, chunk in enumerate(chunks)
        ]
        
        await self._upsert_to_collection(collection_name, documents)
        
        return {"status": "success", "updated_chunks": len(documents)}
    
    async def get_candidate_data_by_type(self, user_id: UUID, data_type: str) -> dict:
        """
        
        Args:
            user_id: ID candidate
            data_type: data type to get
            
        Returns:
            dict: result
        """
        collection_name = self._get_collection_name(str(user_id))
        
        if not self._validate_collection_exists(collection_name):
            return {"status": "error", "message": "Collection not found"}
        
        collection = self.client.get_collection(collection_name)
        results = collection.get(
            where={"data_type": data_type},
            include=["documents", "metadatas"]
        )
        
        if not results["documents"]:
            return {"status": "error", "message": f"No data found for type: {data_type}"}
        
        combined_content = " ".join(results["documents"])
        
        return {
            "status": "success",
            "data_type": data_type,
            "content": combined_content,
            "chunk_count": len(results["documents"]),
            "metadatas": results["metadatas"]
        }
        
    async def sync_candidate_profile_with_cv(
        self, 
        user_id: UUID, 
        candidate_data: dict, 
        cv_content: Optional[str] = None
    ) -> dict:
        """
        
        Args:
            user_id: ID candidate
            candidate_data: candidate data
            cv_content: resume content (optional)
            
        Returns:
            dict: result
        """
        results = {"profile_sync": None, "cv_sync": None}
        
        profile_result = await self.create_collection_candidate(
            collection_name=self._get_collection_name(str(user_id)),
            candidate_data=candidate_data
        )
        results["profile_sync"] = profile_result
        
        if cv_content and cv_content.strip():
            cv_result = await self.upload_resume(user_id, cv_content)
            results["cv_sync"] = cv_result
        
        return {
            "status": "success",
            "user_id": str(user_id),
            "results": results,
            "has_cv": cv_content is not None
        }

    async def get_candidate_summary(self, user_id: UUID) -> dict:
        """
        Get candidate summary including CV
        
        Args:
            user_id: ID of candidate
            
        Returns:
            dict: summary
        """
        collection_name = self._get_collection_name(str(user_id))
        print(f"Collection name: {collection_name}")

        if not self._validate_collection_exists(collection_name):
            return {
                "status": "error", 
                "message": "Candidate information not found in RAG system"
            }
        
        summary = {
            "user_id": str(user_id),
            "status": "success",
            "personal_information": {},
            "skills_and_experience": {},
            "education_and_language": {},
            "documents_info": {},
            "cv_content": {},
            "summary_text": ""
        }
        
        personal_data = await self.get_candidate_data_by_type(user_id, "personal")
        if personal_data.get("status") == "success":
            summary["personal_information"] = self._parse_personal_info(personal_data["content"])
        
        skills_data = await self.get_candidate_data_by_type(user_id, "main_skill")
        if skills_data.get("status") == "success":
            summary["skills_and_experience"]["main_skill"] = skills_data["content"]
        
        other_skills_data = await self.get_candidate_data_by_type(user_id, "other_skill")
        if other_skills_data.get("status") == "success":
            summary["skills_and_experience"]["other_skill"] = other_skills_data["content"]
        
        experience_data = await self.get_candidate_data_by_type(user_id, "experience")
        if experience_data.get("status") == "success":
            summary["skills_and_experience"]["experience"] = experience_data["content"]
        
        education_data = await self.get_candidate_data_by_type(user_id, "education")
        if education_data.get("status") == "success":
            summary["education_and_language"]["education"] = education_data["content"]
        
        language_data = await self.get_candidate_data_by_type(user_id, "language")
        if language_data.get("status") == "success":
            summary["education_and_language"]["language"] = language_data["content"]
        
        resume_data = await self.get_candidate_data_by_type(user_id, "resume")
        if resume_data.get("status") == "success":
            summary["cv_content"] = {
                "content": resume_data["content"],
                "chunk_count": resume_data["chunk_count"],
                "summary": self._extract_cv_highlights(resume_data["content"])
            }
        
        profile_data = await self.get_candidate_data_by_type(user_id, "profile")
        if profile_data.get("status") == "success":
            summary["documents_info"]["profile"] = profile_data["content"]
        
        other_info_data = await self.get_candidate_data_by_type(user_id, "other_info")
        if other_info_data.get("status") == "success":
            summary["documents_info"]["other_info"] = other_info_data["content"]
        
        summary["summary_text"] = self._generate_comprehensive_summary(summary)
        
        return summary

    def _parse_personal_info(self, content: str) -> dict:
        """Parse personal information from content"""
        personal_info = {}
        for line in content.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                personal_info[key.strip().lower().replace(' ', '_')] = value.strip()
        return personal_info

    def _extract_cv_highlights(self, cv_content: str) -> dict:
        """Extract highlights from CV"""
        lines = cv_content.split('\n')
        
        highlights = {
            "key_skills": [],
            "work_experience": [],
            "achievements": [],
            "certifications": []
        }
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            lower_line = line.lower()

            if any(keyword in lower_line for keyword in ['skill', 'technology', 'programming', 'framework']):
                highlights["key_skills"].append(line)
            
            elif any(keyword in lower_line for keyword in ['experience', 'worked', 'company', 'position', 'role']):
                highlights["work_experience"].append(line)
            
            elif any(keyword in lower_line for keyword in ['achievement', 'award', 'project', 'success']):
                highlights["achievements"].append(line)
            
            elif any(keyword in lower_line for keyword in ['certificate', 'certification', 'degree', 'diploma']):
                highlights["certifications"].append(line)
        
        return highlights

    def _generate_comprehensive_summary(self, summary_data: dict) -> str:
        """Generate comprehensive summary"""
        summary_parts = []
        
        personal = summary_data.get("personal_information", {})
        if personal:
            name = personal.get("name_kana", "Candidate")
            age = personal.get("age", "")
            position = personal.get("position", "")
            company = personal.get("company", "")
            
            intro = f"{name}"
            if age:
                intro += f", {age} years old"
            if position and company:
                intro += f", working as {position} at {company}"
            elif position:
                intro += f", position {position}"
            
            summary_parts.append(intro)
        
        skills = summary_data.get("skills_and_experience", {})
        if skills.get("main_skill"):
            summary_parts.append(f"Main skills: {skills['main_skill']}")
        
        if skills.get("experience"):
            exp_preview = skills["experience"][:200] + "..." if len(skills["experience"]) > 200 else skills["experience"]
            summary_parts.append(f"Experience: {exp_preview}")
        
        cv_info = summary_data.get("cv_content", {})
        if cv_info and cv_info.get("summary"):
            cv_summary = cv_info["summary"]
            if cv_summary.get("key_skills"):
                summary_parts.append(f"Key skills from CV: {', '.join(cv_summary['key_skills'][:3])}")
        
        return " | ".join(summary_parts)
