# app/services/rag/company_rag_service.py
from typing import List, Dict, Optional, Tuple, Any
from app.services.rag_service import RAGService
from app.core.config import settings
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from chromadb.utils import embedding_functions
import chromadb
import logging
import json
from datetime import datetime
from app.models.company import Company
from app.models.keypoint_template import KeypointTemplate
from app.models.company import CompanyKeypoint

logger = logging.getLogger(__name__)

class CompanyRAGService(RAGService):
    """
    Service for handling RAG and AI analysis for company data
    
    Main functions:
    - Create and manage company collections in ChromaDB
    - Load company data into vector database
    - Perform semantic search on company context
    - Analyze company data using Gemini AI
    """
    
    def __init__(self):
        self.use_google_embeddings = self._setup_embeddings()
        self.client = self._setup_chromadb_client()
        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)
        self._setup_gemini_ai()
    
    def _setup_embeddings(self) -> bool:
        """Initialize Google embeddings"""
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(
                google_api_key=settings.GOOGLE_API_KEY,
                model="models/gemini-embedding-exp-03-07",
                task_type="RETRIEVAL_DOCUMENT"
            )
            return True
        except Exception:
            self.embeddings = embedding_functions.DefaultEmbeddingFunction()
            return False
    
    def _setup_chromadb_client(self) -> chromadb.HttpClient:
        return chromadb.HttpClient(host="chroma", port=8000)
    
    def _setup_gemini_ai(self):
        try:
            self.gemini_llm = ChatGoogleGenerativeAI(
                model=settings.GEMINI_MODEL,
                google_api_key=settings.GOOGLE_API_KEY,
                temperature=0.3,
                max_tokens=1000
            )
            self.ai_available = True
        except Exception:
            self.gemini_llm = None
            self.ai_available = False
    
    # === CORE RAG OPERATIONS ===
    
    def create_company_collection(self, company_id: str):
        try:
            collection = self.client.get_collection(company_id)
            return collection
        except Exception:
            collection = self.client.create_collection(
                name=company_id,
                metadata={"company_id": company_id, "type": "company_context"}
            )

        return collection
    
    def load_company_data(self, company_id: str, company_data: Dict) -> bool:
        """Load company data into vector database"""
        collection = self.create_company_collection(company_id)
        if not collection:
            return False
        
        documents = self._build_company_documents(company_id, company_data)
        if not documents:
            return False
        
        chunks = self._split_documents_to_chunks(documents)
        if not chunks:
            return False
        
        return self._add_chunks_to_collection(collection, chunks, company_id)
    
    def _build_company_documents(self, company_id: str, company_data: Dict) -> List[Document]:
        documents = []
        
        if company_data.get("description"):
            documents.append(Document(
                page_content=f"Company Description: {company_data['description']}",
                metadata={"type": "description", "company_id": company_id}
            ))
        
        if company_data.get("core_values"):
            core_values_text = self._format_core_values(company_data["core_values"])
            documents.append(Document(
                page_content=f"Core Values:\n{core_values_text}",
                metadata={"type": "core_values", "company_id": company_id}
            ))
        
        if company_data.get("vision_mission"):
            documents.append(Document(
                page_content=f"Vision & Mission: {company_data['vision_mission']}",
                metadata={"type": "vision_mission", "company_id": company_id}
            ))
        
        if company_data.get("document_content"):
            documents.append(Document(
                page_content=f"Company Document ({company_data.get('document_name', 'Unknown')}):\n{company_data['document_content']}",
                metadata={
                    "type": "uploaded_document", 
                    "company_id": company_id,
                    "document_type": company_data.get('document_type', 'document'),
                    "file_name": company_data.get('document_name', 'Unknown')
                }
            ))
        
        if company_data.get("documents"):
            for i, doc in enumerate(company_data["documents"]):
                documents.append(Document(
                    page_content=f"Company Document ({doc.get('file_name', f'Document {i+1}')}):\n{doc['content']}",
                    metadata={
                        "type": "uploaded_document",
                        "company_id": company_id, 
                        "document_type": doc.get('document_type', 'document'),
                        "file_name": doc.get('file_name', f'Document {i+1}')
                    }
                ))
        
        return documents
    
    def _format_core_values(self, core_values) -> str:
        if isinstance(core_values, list):
            return "\n".join([f"- {value}" for value in core_values])
        return str(core_values)
    
    def _split_documents_to_chunks(self, documents: List[Document]) -> List[Document]:
        all_chunks = []
        for doc in documents:
            chunks = self.text_splitter.split_documents([doc])
            all_chunks.extend(chunks)
        return all_chunks
    
    def _add_chunks_to_collection(self, collection, chunks: List[Document], company_id: str) -> bool:
        if not chunks:
            return False
        
        texts = [chunk.page_content for chunk in chunks]
        metadatas = [chunk.metadata for chunk in chunks]
        ids = [f"{company_id}_{i}" for i in range(len(texts))]
        
        try:
            if self.use_google_embeddings:
                embeddings = self.embeddings.embed_documents(texts)
                collection.add(documents=texts, metadatas=metadatas, ids=ids, embeddings=embeddings)
            else:
                collection.add(documents=texts, metadatas=metadatas, ids=ids)
            
            return True
        except Exception as e:
            logger.error(f"Error adding chunks to collection: {e}")
            return False
    
    # === SEARCH OPERATIONS ===
    def search_company_context(self, company_id: str, query: str, k: int = 3) -> List[Dict]:
        try:
            collection = self.client.get_collection(company_id)
        except Exception:
            return []

        if self.use_google_embeddings:
            query_embedding = self.embeddings.embed_query(query)
            results = collection.query(
                query_embeddings=[query_embedding], 
                n_results=k
            )
        else:
            results = collection.query(query_texts=[query], n_results=k)
        
        return self._format_search_results(results)
    
    def _format_search_results(self, results) -> List[Dict]:
        context_data = []
        
        if not (results and 'documents' in results and results['documents']):
            return context_data
        
        documents = results['documents'][0] or []
        metadatas = results.get('metadatas', [[]])[0] or []
        distances = results.get('distances', [[]])[0] or []
        
        for i, doc in enumerate(documents):
            context_data.append({
                "content": doc,
                "metadata": metadatas[i] if i < len(metadatas) else {},
                "distance": distances[i] if i < len(distances) else 0
            })
        
        return context_data
    
    # === AI ANALYSIS ===
    
    def analyze_company_with_ai(self, company_data: Dict, company_id: str) -> Tuple[Optional[str], List[str]]:
        """Analyze company data using RAG + Gemini AI"""
        self._ensure_company_data_loaded(company_id, company_data)
        
        if not self.ai_available:
            return self._create_fallback_analysis(company_data)
        
        query_context = self._build_company_query_context(company_data)
        rag_context = self.search_company_context(company_id, query_context, k=5)
        
        ai_analysis = self._generate_ai_analysis(company_data, rag_context)
        
        if ai_analysis:
            return ai_analysis.get("summary"), ai_analysis.get("key_insights", [])
        
        return self._create_fallback_analysis(company_data)
    
    def _ensure_company_data_loaded(self, company_id: str, company_data: Dict):
        try:
            self.client.get_collection(company_id)
        except Exception:
            self.load_company_data(company_id, company_data)
    
    def _build_company_query_context(self, company_data: Dict) -> str:
        return f"""
        Company information for {company_data.get('name', 'Unknown')}:
        - Industry: {company_data.get('industry') or 'Not specified'}
        - Description: {company_data.get('description') or 'No information available'} 
        - Core values: {company_data.get('core_values') or []}
        - Vision & Mission: {company_data.get('vision_mission') or 'No information available'}
        """
    
    def _generate_ai_analysis(self, company_data: Dict, rag_context: List[Dict]) -> Optional[Dict]:
        prompt = self._build_ai_analysis_prompt(company_data, rag_context)
        
        try:
            ai_response = self.gemini_llm.invoke(prompt)
            return self._parse_ai_response(ai_response.content)
        except Exception:
            return None
    
    def _build_ai_analysis_prompt(self, company_data: Dict, rag_context: List[Dict]) -> str:
        return f"""
        You are a company analysis expert (like a company founder or CEO). Analyze the following information and create (In Vietnamese):
        1. Brief company summary (2-3 sentences)
        2. List of 3-5 important key highlights
        
        Company information:
        {self._build_company_query_context(company_data)}
        
        Additional information:
        {json.dumps(rag_context, ensure_ascii=False) if rag_context else 'None available'}
        
        Return JSON format:
        {{
            "summary": "Summary...",
            "key_insights": ["Point 1", "Point 2", "Point 3"]
        }}
        
        Return only JSON.
        """
    
    def _parse_ai_response(self, response_content: str) -> Optional[Dict]:
        cleaned_response = response_content.strip()
        
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:].strip("` \n")
        elif cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[3:].strip("` \n")
        elif cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3].strip("` \n")
        
        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            return None
    
    def _create_fallback_analysis(self, company_data: Dict) -> Tuple[str, List[str]]:
        """Create fallback analysis when AI is not available"""
        company_name = company_data.get('name', 'Unknown Company')
        industry = company_data.get('industry')
        
        summary = f"Company {company_name}"
        if industry:
            summary += f" operates in {industry} industry"
        summary += "."
        
        key_insights = []
        if industry:
            key_insights.append(f"Specializes in {industry} field")
        if company_data.get('core_values'):
            key_insights.append("Has clearly defined core values")
        if company_data.get('vision_mission'):
            key_insights.append("Has clear vision and mission")
        
        if not key_insights:
            key_insights = ["Detailed information is being updated"]
        
        return summary, key_insights
    

    def generate_multiple_keypoints_with_context(
        self, 
        company_id: str,
        company: 'Company',
        template: 'KeypointTemplate', 
        position_type: str,
        keypoint_index: int,
        total_keypoints: int
    ) -> Dict[str, Any]:
        """
        Generate multiple variations of keypoints based on same template
        """
        from datetime import datetime
        
        # Build company info
        company_info = {
            "name": company.name,
            "industry": company.industry,
            "description": company.description or "",
            "core_values": company.core_values or [],
            "vision_mission": company.vision_mission or ""
        }
        
        # Get RAG context
        rag_context = []
        try:
            rag_results = self.search_company_context(
                company_id=company_id,
                query=f"company culture values requirements {position_type} skills",
                k=8
            )
            rag_context = [r["content"][:250] for r in rag_results[:4]]
        except Exception as e:
            logger.warning(f"RAG context failed: {e}")
        
        # Define keypoint focuses based on index
        keypoint_focuses = [
            "technical skills and problem-solving abilities",
            "communication and teamwork capabilities", 
            "learning ability and adaptability",
            "cultural fit and company values alignment"
        ]
        
        current_focus = keypoint_focuses[keypoint_index - 1] if keypoint_index <= len(keypoint_focuses) else "general competencies"
        
        # AI generate với specific focus
        if self.ai_available:
            try:
                prompt = f"""
                    Bạn là chuyên gia HR. Tạo keypoint phỏng vấn số {keypoint_index}/{total_keypoints} cho công ty dựa trên template.

                    ### TEMPLATE GỐC
                    - Tên: {template.name}
                    - Mô tả: {template.description}
                    - Độ khó: {template.difficulty_level}/5

                    ### THÔNG TIN CÔNG TY
                    - Tên: {company_info["name"]}
                    - Ngành: {company_info["industry"]}
                    - Mô tả: {company_info["description"]}
                    - Giá trị: {company_info["core_values"]}
                    - Tầm nhìn: {company_info["vision_mission"]}

                    ### VỊ TRÍ TUYỂN DỤNG
                    {position_type}

                    ### FOCUS CHO KEYPOINT NÀY
                    {current_focus}

                    ### NGỮ CẢNH TỪ TÀI LIỆU
                    {chr(10).join(rag_context) if rag_context else "Không có thông tin bổ sung"}

                    ### YÊU CẦU
                    Tạo keypoint phỏng vấn số {keypoint_index} tập trung vào "{current_focus}". 
                    Đảm bảo keypoint này khác biệt với các keypoint khác và phù hợp với văn hóa công ty.

                    ### OUTPUT JSON
                    {{
                        "custom_name": "Tên keypoint tùy chỉnh cho focus này (tối đa 80 ký tự)",
                        "custom_description": "Mô tả chi tiết keypoint đánh giá {current_focus}, tích hợp thông tin công ty (150-300 ký tự)"
                    }}

                    Chỉ trả về JSON.
                    """
                
                ai_response = self.gemini_llm.invoke(prompt)
                parsed = self._parse_ai_response(ai_response.content)
                
                if parsed and "custom_name" in parsed:
                    return {
                        "custom_name": parsed["custom_name"],
                        "custom_description": parsed["custom_description"],
                        "source_data": {
                            "template": {"id": str(template.id), "name": template.name},
                            "company_info": company_info,
                            "keypoint_focus": current_focus,
                            "keypoint_index": keypoint_index,
                            "total_keypoints": total_keypoints,
                            "rag_context_used": len(rag_context)
                        },
                        "generation_metadata": {
                            "ai_model": "gemini",
                            "generated_at": datetime.utcnow().isoformat(),
                            "keypoint_focus": current_focus,
                            "context_sources": ["template", "company_data", "rag_docs"]
                        }
                    }
            except Exception as e:
                logger.error(f"AI generation failed: {e}")
        
        # Fallback generation với variation
        focus_names = {
            "technical skills and problem-solving abilities": "Kỹ năng Kỹ thuật",
            "communication and teamwork capabilities": "Giao tiếp & Làm việc nhóm", 
            "learning ability and adaptability": "Học hỏi & Thích ứng",
            "cultural fit and company values alignment": "Phù hợp Văn hóa"
        }
        
        return {
            "custom_name": f"{focus_names.get(current_focus, 'Năng lực Chung')} - {company.name}",
            "custom_description": f"Đánh giá {current_focus} của ứng viên phù hợp với {company.name} trong lĩnh vực {company.industry}. Keypoint {keypoint_index}/{total_keypoints} tập trung đặc biệt vào khả năng {current_focus}.",
            "source_data": {
                "template": {"id": str(template.id), "name": template.name},
                "company_info": company_info,
                "generation_method": "fallback",
                "keypoint_focus": current_focus
            },
            "generation_metadata": {
                "ai_model": "fallback",
                "generated_at": datetime.utcnow().isoformat(),
                "keypoint_focus": current_focus
            }
        }

    # ===== THÊM PHẦN MỚI CHO QUESTION GENERATION =====

    def get_company_context_for_questions(
        self, 
        company_id: str, 
        company: 'Company', 
        keypoint: CompanyKeypoint
    ) -> Dict:
        """
        Get comprehensive company context specifically for question generation
        """
        # Basic company info
        company_info = self._build_company_info_dict(company)
        
        # RAG context focused on keypoint
        rag_context = self._get_keypoint_focused_rag_context(company_id, keypoint)
        
        return {
            "company_info": company_info,
            "rag_context": rag_context,
            "keypoint": {
                "id": str(keypoint.id),
                "name": keypoint.custom_name,
                "description": keypoint.custom_description,
                "position_type": keypoint.position_type
            }
        }

    def _build_company_info_dict(self, company: 'Company') -> Dict:
        """Build standardized company info dict"""
        return {
            "id": str(company.id),
            "name": company.name,
            "industry": company.industry,
            "description": company.description or "",
            "core_values": company.core_values or [],
            "vision_mission": company.vision_mission or ""
        }

    def _get_keypoint_focused_rag_context(self, company_id: str, keypoint: 'CompanyKeypoint') -> List[str]:
        """Get RAG context focused on specific keypoint"""
        rag_query = f"""
        {keypoint.custom_description or keypoint.template.description}
        {keypoint.position_type} requirements skills
        company culture values interview questions
        """
        
        rag_results = self.search_company_context(
            company_id=company_id,
            query=rag_query,
            k=6
        )
        
        return [r["content"][:250] for r in rag_results[:4]]

    def search_context_for_question_generation(
        self,
        company_id: str,
        keypoint_description: str,
        position_type: str,
        interviewer_focus: List[str] = None
    ) -> List[str]:
        """
        Specialized search for question generation context
        """
        # Build search query
        search_terms = [keypoint_description, f"{position_type} skills"]
        
        if interviewer_focus:
            search_terms.extend(interviewer_focus)
        
        query = " ".join(search_terms)
        
        # Search và format results
        results = self.search_company_context(company_id, query, k=5)
        return [r["content"][:200] for r in results[:3]]

