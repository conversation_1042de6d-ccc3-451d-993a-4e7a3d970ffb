# Service riêng để generate questions
from typing import List, Dict, Optional, Any
from app.services.rag.company_rag_service import CompanyRAGService
from app.services.question.interviewer_role_service import Interviewer<PERSON>oleService, InterviewerRole
from app.core.config import settings
from langchain_google_genai import ChatGoogleGenerativeAI
import json
import logging
from datetime import datetime
from app.models.company import CompanyKeypoint, Company

logger = logging.getLogger(__name__)

class QuestionGenerationService:
    """Service chuyên generate questions với AI - simplified"""
    
    def __init__(self):
        self.company_rag_service = CompanyRAGService()
        self.interviewer_service = InterviewerRoleService()
        self._setup_ai()
    
    def _setup_ai(self):
        """Setup AI model"""
        try:
            self.gemini_llm = ChatGoogleGenerativeAI(
                model=settings.GEMINI_MODEL,
                google_api_key=settings.GOOGLE_API_KEY,
                temperature=0.4,
                max_tokens=1200
            ) if settings.GOOGLE_API_KEY else None
            
            self.ai_available = self.gemini_llm is not None
        except Exception as e:
            logger.warning(f"AI setup failed: {e}")
            self.gemini_llm = None
            self.ai_available = False
    
    def generate_questions_for_keypoint(
        self,
        company_id: str,
        keypoint: CompanyKeypoint,
        company: Company,
        interviewer_roles: Optional[List[str]] = None,
        questions_per_role: int = 1
    ) -> List[Dict[str, Any]]:
        """Main method để generate questions cho keypoint"""
        
        # Get default roles nếu không provided
        if not interviewer_roles:
            interviewer_roles = self.interviewer_service.get_default_roles_for_position(
                keypoint.position_type
            )
        
        company_context = self.company_rag_service.get_company_context_for_questions(
            company_id, company, keypoint
        )
        all_questions = []
        
        for role in interviewer_roles:
            role_questions = self._generate_questions_for_role(
                company_context=company_context,
                keypoint=keypoint,
                interviewer_role=role,
                question_count=questions_per_role
            )
            all_questions.extend(role_questions)
        
        return all_questions
    
    def _generate_questions_for_role(
        self,
        company_context: Dict,
        keypoint: CompanyKeypoint,
        interviewer_role: str,
        question_count: int = 1
    ) -> List[Dict]:
        """Generate questions cho specific interviewer role"""
        
        questions = []
        role_profile = self.interviewer_service.get_role_profile(interviewer_role)
        
        for i in range(question_count):
            difficulty_levels = role_profile.get("difficulty_preference", [1, 2])
            difficulty = difficulty_levels[i % len(difficulty_levels)]
            
            question_data = self._generate_single_question(
                company_context=company_context,
                keypoint=keypoint,
                interviewer_role=interviewer_role,
                role_profile=role_profile,
                difficulty_level=difficulty,
                question_index=i
            )
            
            if question_data:
                questions.append(question_data)
        
        return questions
    
    def _generate_single_question(
        self,
        company_context: Dict,
        keypoint: CompanyKeypoint,
        interviewer_role: str,
        role_profile: Dict,
        difficulty_level: int,
        question_index: int
    ) -> Optional[Dict]:
        """Generate single question với AI"""
        
        if not self.ai_available:
            return self._create_fallback_question(
                keypoint, interviewer_role, role_profile, difficulty_level
            )
        
        prompt = self._build_generation_prompt(
            company_context, keypoint, interviewer_role, role_profile, difficulty_level
        )
        
        try:
            ai_response = self.gemini_llm.invoke(prompt)
            parsed_response = self._parse_ai_response(ai_response.content)
            
            if parsed_response and "question_text" in parsed_response:
                parsed_response.update({
                    "interviewer_role": interviewer_role,
                    "generation_metadata": {
                        "ai_model": "gemini",
                        "interviewer_role": interviewer_role,
                        "difficulty_level": difficulty_level,
                        "generated_at": datetime.utcnow().isoformat(),
                        "keypoint_id": str(keypoint.id),
                        "company_id": company_context["company_info"]["id"]
                    }
                })
                return parsed_response
        except Exception as e:
            logger.warning(f"AI generation failed: {e}")
        
        return self._create_fallback_question(
            keypoint, interviewer_role, role_profile, difficulty_level
        )
    
    def _build_generation_prompt(
        self,
        company_context: Dict,
        keypoint: CompanyKeypoint,
        interviewer_role: str,
        role_profile: Dict,
        difficulty_level: int
    ) -> str:
        """Build AI prompt cho question generation"""
        
        company_info = company_context["company_info"]
        rag_context = company_context["rag_context"]
        
        role_context = self.interviewer_service.build_role_context(interviewer_role, company_info)
        
        return f"""
        Bạn đang đóng vai {role_profile["name"]} phỏng vấn cho công ty {company_info["name"]}.

        {role_context}

        ### KEYPOINT CẦN ĐÁNH GIÁ
        - Tên: {keypoint.custom_name}
        - Mô tả: {keypoint.custom_description}
        - Vị trí: {keypoint.position_type}

        ### THÔNG TIN CÔNG TY
        - Tên: {company_info["name"]}
        - Ngành: {company_info["industry"]}
        - Mô tả: {company_info["description"]}
        - Giá trị: {company_info["core_values"]}

        ### CONTEXT TỪ TÀI LIỆU CÔNG TY
        {chr(10).join(rag_context) if rag_context else "Không có context bổ sung"}

        ### YÊU CẦU
        Tạo câu hỏi phỏng vấn level {difficulty_level}/3 từ góc nhìn {role_profile["name"]}:
        - Phù hợp với {role_profile["question_style"]}
        - Tone: {role_profile["tone"]}
        - Tập trung: {', '.join(role_profile["focus_areas"])}

        ### OUTPUT JSON
        {{
            "question_text": "Câu hỏi chi tiết từ góc nhìn {role_profile['name']}...",
            "question_type": "behavioral|technical|scenario|strategic",
            "difficulty_level": {difficulty_level},
            "interviewer_perspective": "Tại sao {role_profile['name']} hỏi câu này...",
            "expected_answer_points": ["Điểm mong đợi 1", "Điểm mong đợi 2"],
            "follow_up_suggestions": ["Follow-up 1", "Follow-up 2"],
            "evaluation_criteria": {{"technical": 30, "behavioral": 40, "cultural_fit": 30}},
            "estimated_time_minutes": 3-5,
            "role_specific_context": {{
                "focus_areas": {role_profile["focus_areas"]},
                "evaluation_weight": "phân bổ điểm"
            }}
        }}

        Chỉ trả về JSON hợp lệ.
        """
    
    def _parse_ai_response(self, response_content: str) -> Optional[Dict]:
        """Parse AI response to JSON"""
        cleaned = response_content.strip()
        
        if cleaned.startswith("```json"):
            cleaned = cleaned[7:].strip("` \n")
        elif cleaned.startswith("```"):
            cleaned = cleaned[3:].strip("` \n")
        elif cleaned.endswith("```"):
            cleaned = cleaned[:-3].strip("` \n")
        
        try:
            return json.loads(cleaned)
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON")
            return None
    
    def _create_fallback_question(
        self,
        keypoint: CompanyKeypoint,
        interviewer_role: str,
        role_profile: Dict,
        difficulty_level: int
    ) -> Dict:
        """Create fallback question when AI fails"""
        
        fallback_templates = {
            InterviewerRole.HR_MANAGER: {
                1: f"Hãy chia sẻ về kinh nghiệm của bạn liên quan đến {keypoint.custom_name}?",
                2: f"Mô tả một tình huống khó khăn bạn đã gặp trong {keypoint.custom_name} và cách giải quyết?",
                3: f"Bạn sẽ xây dựng team culture như thế nào để cải thiện {keypoint.custom_name}?"
            },
            InterviewerRole.TECHNICAL_LEAD: {
                1: f"Giải thích approach của bạn cho {keypoint.custom_name}?",
                2: f"Thiết kế solution cho một challenge phức tạp trong {keypoint.custom_name}?",
                3: f"Làm thế nào bạn scale và optimize {keypoint.custom_name} cho production?"
            }
        }
        
        templates = fallback_templates.get(
            interviewer_role, 
            fallback_templates[InterviewerRole.HR_MANAGER]
        )
        
        question_text = templates.get(difficulty_level, templates[1])
        
        return {
            "question_text": question_text,
            "question_type": "behavioral" if difficulty_level <= 2 else "scenario",
            "difficulty_level": difficulty_level,
            "interviewer_role": interviewer_role,
            "interviewer_perspective": f"Câu hỏi fallback từ {role_profile['name']} để đánh giá {', '.join(role_profile['focus_areas'])}",
            "expected_answer_points": ["Kinh nghiệm cụ thể", "Phương pháp tiếp cận", "Kết quả đạt được"],
            "follow_up_suggestions": ["Bạn có thể chia sẻ thêm chi tiết?", "Bạn sẽ làm gì khác biệt lần sau?"],
            "evaluation_criteria": {"experience": 40, "method": 35, "communication": 25},
            "estimated_time_minutes": 4,
            "role_specific_context": {
                "focus_areas": role_profile["focus_areas"],
                "fallback_used": True
            },
            "generation_metadata": {
                "ai_model": "fallback",
                "interviewer_role": interviewer_role,
                "difficulty_level": difficulty_level,
                "generated_at": datetime.utcnow().isoformat()
            }
        }
