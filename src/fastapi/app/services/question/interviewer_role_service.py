from typing import Dict, List, Optional
from enum import Enum

class InterviewerRole(str, Enum):
    HR_MANAGER = "hr_manager"
    TECHNICAL_LEAD = "technical_lead" 
    SENIOR_DEVELOPER = "senior_developer"
    PROJECT_MANAGER = "project_manager"
    CEO_FOUNDER = "ceo_founder"
    TEAM_LEAD = "team_lead"

class InterviewerRoleService:
    """Service để handle interviewer roles và profiles"""
    
    ROLE_PROFILES = {
        InterviewerRole.HR_MANAGER: {
            "name": "HR Manager",
            "focus_areas": ["cultural_fit", "communication", "teamwork", "motivation"],
            "question_style": "behavioral_situational",
            "tone": "friendly_professional",
            "typical_questions": ["behavioral", "cultural_fit"],
            "difficulty_preference": [1, 2]  # Prefer easier questions
        },
        
        InterviewerRole.TECHNICAL_LEAD: {
            "name": "Technical Leader", 
            "focus_areas": ["technical_skills", "problem_solving", "architecture", "best_practices"],
            "question_style": "technical_deep_dive",
            "tone": "analytical_challenging", 
            "typical_questions": ["technical", "scenario", "problem_solving"],
            "difficulty_preference": [2, 3]  # Prefer harder questions
        },
        
        InterviewerRole.SENIOR_DEVELOPER: {
            "name": "Senior Developer",
            "focus_areas": ["coding_skills", "debugging", "collaboration", "mentoring"],
            "question_style": "practical_hands_on",
            "tone": "peer_to_peer",
            "typical_questions": ["technical", "practical"],
            "difficulty_preference": [2, 3]
        },
        
        InterviewerRole.PROJECT_MANAGER: {
            "name": "Project Manager", 
            "focus_areas": ["project_management", "communication", "deadlines", "stakeholder_management"],
            "question_style": "process_oriented",
            "tone": "structured_methodical",
            "typical_questions": ["behavioral", "scenario"],
            "difficulty_preference": [1, 2]
        },
        
        InterviewerRole.CEO_FOUNDER: {
            "name": "CEO/Founder",
            "focus_areas": ["vision", "leadership", "company_growth", "innovation"],
            "question_style": "strategic_visionary", 
            "tone": "inspirational_challenging",
            "typical_questions": ["strategic", "behavioral"],
            "difficulty_preference": [2, 3]
        },
        
        InterviewerRole.TEAM_LEAD: {
            "name": "Team Leader",
            "focus_areas": ["team_collaboration", "leadership", "conflict_resolution", "mentoring"],
            "question_style": "leadership_focused",
            "tone": "collaborative_supportive",
            "typical_questions": ["behavioral", "leadership"],
            "difficulty_preference": [1, 2]
        }
    }
    
    @classmethod
    def get_role_profile(cls, role: InterviewerRole) -> Dict:
        """Get profile cho interviewer role"""
        return cls.ROLE_PROFILES.get(role, cls.ROLE_PROFILES[InterviewerRole.HR_MANAGER])
    
    @classmethod
    def get_default_roles_for_position(cls, position_type: str) -> List[InterviewerRole]:
        """Get default interviewer roles based on position"""
        role_mapping = {
            "developer": [InterviewerRole.HR_MANAGER, InterviewerRole.TECHNICAL_LEAD, InterviewerRole.SENIOR_DEVELOPER],
            "manager": [InterviewerRole.HR_MANAGER, InterviewerRole.PROJECT_MANAGER, InterviewerRole.CEO_FOUNDER],
            "designer": [InterviewerRole.HR_MANAGER, InterviewerRole.PROJECT_MANAGER, InterviewerRole.TEAM_LEAD],
            "default": [InterviewerRole.HR_MANAGER, InterviewerRole.TEAM_LEAD]
        }
        return role_mapping.get(position_type.lower(), role_mapping["default"])
    
    @classmethod
    def build_role_context(cls, role: InterviewerRole, company_info: Dict) -> str:
        """Build context string cho AI prompt"""
        profile = cls.get_role_profile(role)
        
        return f"""
        VẬI TRÒ: {profile["name"]}
        PHONG CÁCH: {profile["question_style"]}
        TONE: {profile["tone"]}
        TẬP TRUNG: {', '.join(profile["focus_areas"])}
        LOẠI CÂU HỎI: {', '.join(profile["typical_questions"])}
        """
