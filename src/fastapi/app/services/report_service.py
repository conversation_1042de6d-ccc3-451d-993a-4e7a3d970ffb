# app/services/report_service.py
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from io import BytesIO
import seaborn as sns
import base64
from typing import Dict, List
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class ReportGenerationService:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom report styles"""
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            textColor=colors.HexColor('#2C3E50')
        )
        
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            textColor=colors.HexColor('#34495E')
        )
    
    async def generate_company_analytics_report(
        self, 
        analytics_data: Dict, 
        company_info: Dict
    ) -> bytes:
        """Generate comprehensive PDF analytics report"""
        try:
            buffer = BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            story = []
            
            # Report title
            title = f"Interview Analytics Report - {company_info.get('name', 'Company')}"
            story.append(Paragraph(title, self.title_style))
            story.append(Spacer(1, 12))
            
            # Executive Summary
            story.append(Paragraph("Executive Summary", self.subtitle_style))
            summary_data = analytics_data.get("overview_metrics", {})
            
            summary_text = f"""
            This report provides a comprehensive analysis of interview performance for the period 
            {analytics_data.get('date_range', {}).get('start', 'N/A')} to 
            {analytics_data.get('date_range', {}).get('end', 'N/A')}.
            
            Key Highlights:
            • Total Interviews Conducted: {summary_data.get('total_interviews', 0)}
            • Completion Rate: {summary_data.get('completion_rate', 0)}%
            • Average Candidate Score: {summary_data.get('average_score', 0)}/10
            • Average Interview Duration: {summary_data.get('duration_stats', {}).get('average_minutes', 0)} minutes
            """
            
            story.append(Paragraph(summary_text, self.styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Overview Metrics Table
            story.append(Paragraph("Performance Overview", self.subtitle_style))
            overview_table_data = [
                ['Metric', 'Value'],
                ['Total Interviews', summary_data.get('total_interviews', 0)],
                ['Completed Interviews', summary_data.get('completed_interviews', 0)],
                ['Completion Rate', f"{summary_data.get('completion_rate', 0)}%"],
                ['Average Score', f"{summary_data.get('average_score', 0)}/10"],
                ['Average Duration', f"{summary_data.get('duration_stats', {}).get('average_minutes', 0)} min"]
            ]
            
            overview_table = Table(overview_table_data)
            overview_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498DB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(overview_table)
            story.append(Spacer(1, 30))
            
            # Performance Trends Chart
            story.append(Paragraph("Performance Trends", self.subtitle_style))
            trends_chart = self._create_trends_chart(analytics_data.get("performance_trends", {}))
            if trends_chart:
                story.append(trends_chart)
            story.append(Spacer(1, 20))
            
            # Candidate Insights
            story.append(Paragraph("Candidate Performance Insights", self.subtitle_style))
            candidate_insights = analytics_data.get("candidate_insights", {})
            
            # Top Candidates Table
            top_candidates = candidate_insights.get("top_candidates", [])[:5]  # Top 5
            if top_candidates:
                story.append(Paragraph("Top Performing Candidates", self.styles['Heading3']))
                
                candidates_table_data = [['Candidate Name', 'Position', 'Score', 'Interview Date']]
                for candidate in top_candidates:
                    candidates_table_data.append([
                        candidate.get('candidate_name', 'N/A'),
                        candidate.get('position', 'N/A'),
                        f"{candidate.get('overall_score', 0)}/10",
                        candidate.get('interview_date', 'N/A')[:10]  # Date only
                    ])
                
                candidates_table = Table(candidates_table_data)
                candidates_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#27AE60')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(candidates_table)
                story.append(Spacer(1, 20))
            
            # Technical Performance Analysis
            technical_analysis = analytics_data.get("technical_analysis", {})
            if technical_analysis:
                story.append(Paragraph("Technical Performance Analysis", self.subtitle_style))
                
                technical_scores = technical_analysis.get("average_technical_scores", {})
                technical_table_data = [['Technical Skill', 'Average Score']]
                
                for skill, score in technical_scores.items():
                    skill_name = skill.replace('_', ' ').title()
                    technical_table_data.append([skill_name, f"{score}/10"])
                
                technical_table = Table(technical_table_data)
                technical_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#E74C3C')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(technical_table)
                story.append(Spacer(1, 20))
            
            # Hiring Recommendations
            hiring_recommendations = analytics_data.get("hiring_recommendations", {})
            if hiring_recommendations:
                story.append(Paragraph("Hiring Recommendations", self.subtitle_style))
                
                recommended_candidates = hiring_recommendations.get("recommended_candidates", [])
                if recommended_candidates:
                    for i, candidate in enumerate(recommended_candidates[:3]):  # Top 3 recommendations
                        recommendation_text = f"""
                        {i+1}. {candidate.get('candidate_name', 'N/A')} - {candidate.get('position', 'N/A')}
                        Overall Score: {candidate.get('overall_score', 0)}/10
                        Recommendation Level: {candidate.get('recommendation_level', 'N/A')}
                        Key Strengths: {', '.join(candidate.get('key_strengths', [])[:3])}
                        """
                        story.append(Paragraph(recommendation_text, self.styles['Normal']))
                        story.append(Spacer(1, 10))
            
            # Build PDF
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            raise