# app/services/analytics_service.py
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.models.interview import InterviewSession
from app.models.company import Company
from app.models.base import get_db
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)

class AdvancedAnalyticsService:
    def __init__(self):
        self.metrics_cache = {}
        self.cache_expiry = timedelta(minutes=15)
        
    async def generate_company_analytics(
        self, 
        company_id: str, 
        date_range: Tuple[datetime, datetime] = None
    ) -> Dict:
        """Generate comprehensive company analytics"""
        try:
            db: Session = next(get_db())
            
            # Set default date range if not provided
            if not date_range:
                end_date = datetime.utcnow()
                start_date = end_date - timedelta(days=30)
                date_range = (start_date, end_date)
            
            # Get interview data
            interviews = self._get_company_interviews(db, company_id, date_range)
            
            if not interviews:
                return self._empty_analytics_response(company_id)
            
            # Calculate comprehensive metrics
            analytics = {
                "company_id": company_id,
                "date_range": {
                    "start": date_range[0].isoformat(),
                    "end": date_range[1].isoformat()
                },
                "overview_metrics": await self._calculate_overview_metrics(interviews),
                "performance_trends": await self._calculate_performance_trends(interviews),
                "candidate_insights": await self._analyze_candidate_insights(interviews),
                "technical_analysis": await self._analyze_technical_performance(interviews),
                "behavioral_analysis": await self._analyze_behavioral_performance(interviews),
                "hiring_recommendations": await self._generate_hiring_recommendations(interviews),
                "comparative_analysis": await self._generate_comparative_analysis(db, company_id, interviews)
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error generating company analytics: {e}")
            return {"error": "Failed to generate analytics", "company_id": company_id}
        finally:
            db.close()
    
    def _get_company_interviews(
        self, 
        db: Session, 
        company_id: str, 
        date_range: Tuple[datetime, datetime]
    ) -> List[InterviewSession]:
        """Get company interviews within date range"""
        return db.query(InterviewSession).filter(
            and_(
                InterviewSession.company_id == company_id,
                InterviewSession.created_at >= date_range[0],
                InterviewSession.created_at <= date_range[1],
                InterviewSession.status.in_(["completed", "in_progress"])
            )
        ).all()
    
    async def _calculate_overview_metrics(self, interviews: List[InterviewSession]) -> Dict:
        """Calculate overview metrics"""
        total_interviews = len(interviews)
        completed_interviews = len([i for i in interviews if i.status == "completed"])
        
        # Calculate average scores
        scores = [i.overall_score for i in interviews if i.overall_score > 0]
        avg_score = np.mean(scores) if scores else 0
        
        # Calculate completion rate
        completion_rate = (completed_interviews / total_interviews) * 100 if total_interviews > 0 else 0
        
        # Calculate interview duration stats
        durations = self._calculate_interview_durations(interviews)
        
        # Calculate response quality metrics
        quality_metrics = self._calculate_response_quality(interviews)
        
        return {
            "total_interviews": total_interviews,
            "completed_interviews": completed_interviews,
            "completion_rate": round(completion_rate, 2),
            "average_score": round(avg_score, 2),
            "score_distribution": self._calculate_score_distribution(scores),
            "duration_stats": {
                "average_minutes": round(np.mean(durations), 2) if durations else 0,
                "median_minutes": round(np.median(durations), 2) if durations else 0,
                "min_minutes": round(min(durations), 2) if durations else 0,
                "max_minutes": round(max(durations), 2) if durations else 0
            },
            "quality_metrics": quality_metrics
        }
    
    async def _calculate_performance_trends(self, interviews: List[InterviewSession]) -> Dict:
        """Calculate performance trends over time"""
        # Group interviews by week
        weekly_data = {}
        
        for interview in interviews:
            week_key = interview.created_at.strftime("%Y-W%U")
            
            if week_key not in weekly_data:
                weekly_data[week_key] = {
                    "interviews": [],
                    "scores": [],
                    "completion_count": 0
                }
            
            weekly_data[week_key]["interviews"].append(interview)
            if interview.overall_score > 0:
                weekly_data[week_key]["scores"].append(interview.overall_score)
            if interview.status == "completed":
                weekly_data[week_key]["completion_count"] += 1
        
        # Calculate trends
        trends = []
        for week, data in sorted(weekly_data.items()):
            avg_score = np.mean(data["scores"]) if data["scores"] else 0
            completion_rate = (data["completion_count"] / len(data["interviews"])) * 100
            
            trends.append({
                "week": week,
                "interview_count": len(data["interviews"]),
                "average_score": round(avg_score, 2),
                "completion_rate": round(completion_rate, 2)
            })
        
        # Calculate trend direction
        if len(trends) >= 2:
            recent_scores = [t["average_score"] for t in trends[-3:]]  # Last 3 weeks
            trend_direction = "improving" if recent_scores[-1] > recent_scores[0] else "declining"
        else:
            trend_direction = "stable"
        
        return {
            "weekly_trends": trends,
            "trend_direction": trend_direction,
            "performance_volatility": self._calculate_volatility(trends)
        }
    
    async def _analyze_candidate_insights(self, interviews: List[InterviewSession]) -> Dict:
        """Analyze candidate performance insights"""
        
        # Analyze by experience level
        experience_analysis = {}
        position_analysis = {}
        
        for interview in interviews:
            profile = interview.candidate_profile or {}
            experience = profile.get("experience_level", "Unknown")
            position = profile.get("position", "Unknown")
            
            # Experience level analysis
            if experience not in experience_analysis:
                experience_analysis[experience] = {
                    "count": 0,
                    "scores": [],
                    "completion_rate": 0
                }
            
            experience_analysis[experience]["count"] += 1
            if interview.overall_score > 0:
                experience_analysis[experience]["scores"].append(interview.overall_score)
            
            # Position analysis
            if position not in position_analysis:
                position_analysis[position] = {
                    "count": 0,
                    "scores": [],
                    "top_performers": []
                }
            
            position_analysis[position]["count"] += 1
            if interview.overall_score > 0:
                position_analysis[position]["scores"].append(interview.overall_score)
                
                if interview.overall_score >= 8:  # High performer threshold
                    position_analysis[position]["top_performers"].append({
                        "candidate_name": interview.candidate_name,
                        "score": interview.overall_score,
                        "interview_date": interview.created_at.isoformat()
                    })
        
        # Calculate averages
        for exp_level, data in experience_analysis.items():
            data["average_score"] = round(np.mean(data["scores"]), 2) if data["scores"] else 0
        
        for position, data in position_analysis.items():
            data["average_score"] = round(np.mean(data["scores"]), 2) if data["scores"] else 0
        
        return {
            "experience_level_performance": experience_analysis,
            "position_performance": position_analysis,
            "top_candidates": self._get_top_candidates(interviews, limit=10),
            "improvement_candidates": self._get_improvement_candidates(interviews, limit=5)
        }
    
    async def _analyze_technical_performance(self, interviews: List[InterviewSession]) -> Dict:
        """Analyze technical interview performance"""
        technical_metrics = {
            "technical_accuracy": [],
            "problem_solving": [],
            "implementation_skills": [],
            "technical_communication": []
        }
        
        common_strengths = {}
        common_weaknesses = {}
        
        for interview in interviews:
            if not interview.feedback:
                continue
                
            for feedback_item in interview.feedback:
                if feedback_item.get("question_type") == "technical":
                    analysis = feedback_item.get("analysis", {})
                    
                    # Collect technical scores
                    for metric in technical_metrics.keys():
                        score = analysis.get(metric, 0)
                        if score > 0:
                            technical_metrics[metric].append(score)
                    
                    # Collect strengths and weaknesses
                    strengths = analysis.get("strengths", [])
                    weaknesses = analysis.get("improvements", [])
                    
                    for strength in strengths:
                        common_strengths[strength] = common_strengths.get(strength, 0) + 1
                    
                    for weakness in weaknesses:
                        common_weaknesses[weakness] = common_weaknesses.get(weakness, 0) + 1
        
        # Calculate averages
        avg_technical_metrics = {}
        for metric, scores in technical_metrics.items():
            avg_technical_metrics[metric] = round(np.mean(scores), 2) if scores else 0
        
        # Get top common items
        top_strengths = sorted(common_strengths.items(), key=lambda x: x[1], reverse=True)[:5]
        top_weaknesses = sorted(common_weaknesses.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "average_technical_scores": avg_technical_metrics,
            "common_technical_strengths": [{"skill": s[0], "frequency": s[1]} for s in top_strengths],
            "common_technical_weaknesses": [{"area": w[0], "frequency": w[1]} for w in top_weaknesses],
            "technical_skill_gaps": self._identify_skill_gaps(technical_metrics)
        }
    
    async def _generate_hiring_recommendations(self, interviews: List[InterviewSession]) -> Dict:
        """Generate intelligent hiring recommendations"""
        
        # Analyze high-performing candidates
        high_performers = [i for i in interviews if i.overall_score >= 8]
        recommended_candidates = []
        
        for interview in high_performers:
            profile = interview.candidate_profile or {}
            
            recommendation = {
                "candidate_name": interview.candidate_name,
                "overall_score": interview.overall_score,
                "position": profile.get("position", "Unknown"),
                "key_strengths": self._extract_key_strengths(interview),
                "recommendation_level": self._determine_recommendation_level(interview.overall_score),
                "next_steps": self._suggest_next_steps(interview)
            }
            
            recommended_candidates.append(recommendation)
        
        # Sort by score
        recommended_candidates.sort(key=lambda x: x["overall_score"], reverse=True)
        
        # Generate hiring insights
        hiring_insights = {
            "total_recommended": len(recommended_candidates),
            "recommendation_rate": (len(recommended_candidates) / len(interviews)) * 100 if interviews else 0,
            "average_recommended_score": np.mean([c["overall_score"] for c in recommended_candidates]) if recommended_candidates else 0,
            "position_recommendations": self._group_recommendations_by_position(recommended_candidates)
        }
        
        return {
            "recommended_candidates": recommended_candidates[:10],  # Top 10
            "hiring_insights": hiring_insights,
            "recruitment_suggestions": self._generate_recruitment_suggestions(interviews)
        }
