from datetime import datetime, timedelta
from typing import Optional, Dict, Any, <PERSON><PERSON>, Callable
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.config import settings
from app.models.user import User, UserRole
from app.models.agent import Agent
from app.models.base import get_db
import uuid
import redis
import bcrypt

redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
security = HTTPBearer()

class AuthService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(user_id: str, agent_id: str = None) -> str:
        refresh_token = str(uuid.uuid4())
        token_data = {
            "user_id": user_id,
            "agent_id": agent_id,
            "created_at": datetime.utcnow().isoformat()
        }
        redis_client.setex(
            f"refresh_token:{refresh_token}",
            timedelta(days=7),
            str(token_data)
        )
        return refresh_token
    
    @staticmethod
    def verify_refresh_token(refresh_token: str) -> Optional[Dict[str, str]]:
        token_data = redis_client.get(f"refresh_token:{refresh_token}")
        if token_data:
            return eval(token_data)  
        return None
    
    @staticmethod
    def revoke_refresh_token(refresh_token: str) -> bool:
        return redis_client.delete(f"refresh_token:{refresh_token}") > 0
    
    @staticmethod
    def revoke_all_tokens(user_id: str) -> int:
        pattern = f"refresh_token:*"
        tokens = redis_client.keys(pattern)
        revoked_count = 0
        
        for token_key in tokens:
            token_data = redis_client.get(token_key)
            if token_data and user_id in str(token_data):
                redis_client.delete(token_key)
                revoked_count += 1
        
        return revoked_count
    
    @staticmethod
    def decode_access_token(token: str) -> Dict[str, Any]:
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    @staticmethod
    def authenticate_agent(db: Session, email: str, password: str) -> Optional[tuple]:  
        user = db.query(User).filter(User.email == email).first()

        if not user or not AuthService.verify_password(password, user.password):
            return None
        
        if not user.role or not user.is_active:
            return None
        
        agent = db.query(Agent).filter(Agent.user_id == user.id).first()
        if not agent:
            agent = None   
        
        return user, agent
    
    @staticmethod
    def create_agent_account(
        db: Session, 
        email: str, 
        password: str, 
        role: UserRole,
        profile_name: str,
        profile_organization: str = None
    ) -> tuple:
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        user = User(
            email=email,
            password=hashed_password,
            name=profile_name,
            role=role,
            is_active=True
        )
        db.add(user)
        db.flush()
        
        agent = Agent.create_agent(
            db=db,
            user_id=user.id,
            profile_name=profile_name,
            profile_organization=profile_organization
        )
        
        return user, agent
    
    @staticmethod
    async def get_current_user_agent(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db: Session = Depends(get_db)
    ) -> Tuple[User, Agent]:
        token = credentials.credentials
        
        try:
            payload = AuthService.decode_access_token(token)
            
            user_id: str = payload.get("sub")
            agent_id: str = payload.get("agent_id")
            
            if user_id is None or agent_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token format"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
        
        # Lấy user và agent từ database
        user = db.query(User).filter(User.id == user_id).first()
        agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        if agent is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Agent not found"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User is not active"
            )
        
        return user, agent
    
    @staticmethod
    async def get_current_agent(
        user_agent: Tuple[User, Agent] = Depends(get_current_user_agent)
    ) -> Agent:
        """Dependency để chỉ lấy current agent"""
        return user_agent[1]
    
    @staticmethod
    async def get_current_user(
        user_agent: Tuple[User, Agent] = Depends(get_current_user_agent)
    ) -> User:
        """Dependency để chỉ lấy current user"""
        return user_agent[0]
    
    @staticmethod
    def require_role(required_role: str) -> Callable:
        """
        Decorator để yêu cầu role cụ thể
        Usage: @require_role("agent") hoặc @require_role("interviewer")
        """
        def role_checker(
            agent: Agent = Depends(AuthService.get_current_agent)
        ) -> Agent:
            if agent.role.value != required_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied. Required role: {required_role}"
                )
            return agent
        
        return role_checker