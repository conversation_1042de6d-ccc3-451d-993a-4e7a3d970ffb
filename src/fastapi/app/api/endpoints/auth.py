from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.models.base import get_db
from app.models.schemas.agent import (
    LoginRequest, TokenResponse, RefreshTokenRequest, 
    AgentProfileResponse, MessageResponse
)
from app.services.auth.auth_service import AuthService
from app.models.agent import Agent
from app.models.user import User
from typing import <PERSON>ple
from datetime import datetime
router = APIRouter()

@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    result = AuthService.authenticate_agent(db, login_data.email, login_data.password)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    user, agent = result
    user.last_login_at = datetime.now()
    db.commit()
    
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role,
        "agent_id": str(agent.id) if agent else None
    }
    
    access_token = AuthService.create_access_token(token_data)
    refresh_token = AuthService.create_refresh_token(str(user.id))
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in= 24 * 60 * 60
    )

@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    token_data = AuthService.verify_refresh_token(refresh_data.refresh_token)
    
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    user = db.query(User).filter(User.id == token_data["user_id"]).first()

    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User or agent not found or inactive"
        )
    
    AuthService.revoke_refresh_token(refresh_data.refresh_token)
    
    new_token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role,
        "agent_id": str(user.id) if user else None
    }
    
    new_access_token = AuthService.create_access_token(new_token_data)
    new_refresh_token = AuthService.create_refresh_token(str(user.id))
    
    return TokenResponse(
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        expires_in= 24 * 60 * 60
    )

@router.post("/logout", response_model=MessageResponse)
async def logout(
    refresh_data: RefreshTokenRequest,
    current_agent: Agent = Depends(AuthService.get_current_agent)
):
    revoked = AuthService.revoke_refresh_token(refresh_data.refresh_token)
    
    if not revoked:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid refresh token"
        )
    
    return MessageResponse(message="Successfully logged out")

@router.post("/logout-all", response_model=MessageResponse)
async def logout_all(
    user_agent: Tuple[User, Agent] = Depends(AuthService.get_current_user_agent)
):
    user, agent = user_agent
    revoked_count = AuthService.revoke_all_tokens(str(user.id))
    
    return MessageResponse(
        message="Successfully logged out from all devices",
        detail=f"Revoked {revoked_count} tokens"
    )

@router.get("/me", response_model=AgentProfileResponse)
async def get_current_profile(
    user_agent: Tuple[User, Agent] = Depends(AuthService.get_current_user_agent)
):
    user, agent = user_agent
    
    return AgentProfileResponse(
        id=agent.id,
        email=user.email,
        role=user.role,
        profile_name=agent.profile_name,
        profile_organization=agent.profile_organization,
        created_at=agent.created_at,
        updated_at=agent.updated_at,
        last_login_at=user.last_login_at,
        is_active=user.is_active
    ) 