from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session, joinedload
from app.models.base import get_db
from app.models.interview import InterviewSession
from app.models.company import Company
from app.models.candidate import Candidate
from pydantic import BaseModel
from typing import Dict, List
from uuid import UUID
from app.models.schemas.interview import InterviewResponse as FullInterviewResponse, InterviewLinkCreate, InterviewLinkResponse
from datetime import datetime, timedelta
import os

router = APIRouter()

class InterviewStart(BaseModel):
    interview_id: str

class InterviewResponse(BaseModel):
    id: str
    company_id: str
    candidate_name: str
    status: str
    created_at: str

class InterviewListResponse(BaseModel):
    total_interviews: int
    expired_interviews_count: int
    interviews: List[FullInterviewResponse]

@router.post("/generate-link", response_model=InterviewLinkResponse)
async def generate_interview_link(
    interview_data: InterviewLinkCreate,
    db: Session = Depends(get_db)
):
    """Generate a new interview link for a candidate"""
    # Kiểm tra candidate có tồn tại không
    candidate = db.query(Candidate).options(joinedload(Candidate.user)).filter(Candidate.id == interview_data.candidate_id).first()
    if not candidate:
        raise HTTPException(status_code=404, detail="Candidate not found")

    # Kiểm tra company có tồn tại không
    company = db.query(Company).filter(Company.id == interview_data.company_id).first()
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")

    # Tạo phiên phỏng vấn mới
    expires_at = datetime.utcnow() + timedelta(days=interview_data.expiration_days)
    session_token = InterviewSession.generate_session_token()

    interview_session = InterviewSession(
        candidate_id=interview_data.candidate_id,
        company_id=interview_data.company_id,
        status="created",
        session_token=session_token,
        expires_at=expires_at,
        transcript=[],
        feedback=[],
        overall_score=0
    )

    # Thêm metadata nếu có
    metadata = {
        "agent_notes": interview_data.agent_notes,
        "interviewer_role": interview_data.interviewer_role,
        "interview_type": interview_data.interview_type
    }

    # Lưu vào database
    db.add(interview_session)
    db.commit()
    db.refresh(interview_session)

    # Tạo URL phỏng vấn
    candidate_url = os.getenv("NEXT_PUBLIC_CANDIDATE_URL", "http://localhost:3000")
    interview_url = f"{candidate_url}/interview-token?token={session_token}"

    # Lấy tên từ user nếu có, nếu không thì dùng name_kana
    candidate_name = candidate.user.name if candidate.user and candidate.user.name else candidate.name_kana

    return InterviewLinkResponse(
        id=str(interview_session.id),
        session_token=session_token,
        expires_at=expires_at,
        candidate_name=candidate_name,
        interview_url=interview_url
    )

@router.post("/join", response_model=InterviewResponse)
async def join_interview(
    interview_data: InterviewStart, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_url(db, interview_data.interview_url)    

    if not interview:
        raise HTTPException(status_code=404, detail="Company not found")

    if interview.status != "created":
        raise HTTPException(status_code=400, detail="Interview already started")

    interview.status = "joined"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )

@router.post("/{interview_id}/start", response_model=InterviewResponse)
async def start_interview(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Start a new interview session"""
    interview = InterviewSession.get_interview_by_id(db, UUID(interview_id))

    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")

    if interview.status != "joined":
        raise HTTPException(status_code=400, detail="Interview not joined")

    interview.status = "started"
    db.commit()

    return InterviewResponse(
        id=str(interview.id),
        company_id=str(interview.company_id),
        candidate_name=interview.candidate_name,
        status=interview.status,
        created_at=interview.created_at.isoformat()
    )

@router.get("/", response_model=InterviewListResponse)
async def list_interviews(db: Session = Depends(get_db)):
    """Get list of all interviews, total, and expired count"""
    interviews = db.query(InterviewSession).all()
    now = datetime.utcnow()
    expired_count = sum(1 for interview in interviews if interview.expires_at < now)
    return InterviewListResponse(
        total_interviews=len(interviews),
        expired_interviews_count=expired_count,
        interviews=[
            FullInterviewResponse(
                id=str(interview.id),
                candidate_name=interview.candidate.name_kana,
                candidate_profile=interview.candidate.profile,
                status=interview.status,
                created_at=interview.created_at,
                updated_at=interview.updated_at,
                transcript=interview.transcript,
                feedback=interview.feedback,
                overall_score=interview.overall_score
            )
            for interview in interviews
        ]
    )

@router.get("/{interview_id}/feedback")
async def get_interview_feedback(
    interview_id: str, 
    db: Session = Depends(get_db)
):
    """Get interview feedback"""

    session = db.query(InterviewSession).filter(
        InterviewSession.id == interview_id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Interview session not found")

    if not session.feedback:
        raise HTTPException(status_code=400, detail="Interview not completed yet")

    return {
        "session_id": str(session.id),
        "feedback": session.feedback,
        "overall_score": session.overall_score,
        "status": session.status
    }

@router.get("/token/{token}")
async def get_interview_by_token(
    token: str,
    db: Session = Depends(get_db)
):
    """Get interview by token"""
    interview = db.query(InterviewSession).filter(
        InterviewSession.session_token == token
    ).first()

    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found")

    # Kiểm tra xem token có hết hạn không
    if interview.expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="Interview token has expired")

    return {
        "id": str(interview.id),
        "candidate_id": str(interview.candidate_id),
        "company_id": str(interview.company_id),
        "status": interview.status,
        "expires_at": interview.expires_at.isoformat()
    }
