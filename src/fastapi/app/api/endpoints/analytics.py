# app/api/endpoints/analytics.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime, timedelta
from app.services.analytics_service import AdvancedAnalyticsService
from app.services.report_service import ReportGenerationService
from app.models.base import get_db
from app.services.auth.auth_service import AuthService
import uuid
import io

router = APIRouter()
analytics_service = AdvancedAnalyticsService()
report_service = ReportGenerationService()

@router.get("/companies/{company_id}/analytics")
async def get_company_analytics(
    company_id: uuid.UUID,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: dict = Depends(AuthService.get_current_user_agent)
):
    """Get comprehensive company analytics"""
    try:
        # Set default date range
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Generate analytics
        analytics = await analytics_service.generate_company_analytics(
            str(company_id), (start_date, end_date)
        )
        
        return {
            "success": True,
            "analytics": analytics,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate analytics: {str(e)}"
        )

@router.get("/companies/{company_id}/reports/pdf")
async def download_analytics_report(
    company_id: uuid.UUID,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_db),
    current_user: dict = Depends(AuthService.get_current_user_agent)
):
    """Download PDF analytics report"""
    try:
        # Get analytics data
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        analytics = await analytics_service.generate_company_analytics(
            str(company_id), (start_date, end_date)
        )
        
        # Get company info
        company = db.query(Company).filter(Company.id == company_id).first()
        if not company:
            raise HTTPException(status_code=404, detail="Company not found")
        
        company_info = {
            "name": company.name,
            "industry": company.industry
        }
        
        # Generate PDF report
        pdf_content = await report_service.generate_company_analytics_report(
            analytics, company_info
        )
        
        # Return as streaming response
        pdf_stream = io.BytesIO(pdf_content)
        
        return StreamingResponse(
            io.BytesIO(pdf_content),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=analytics_report_{company.name}_{start_date.strftime('%Y%m%d')}.pdf"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate PDF report: {str(e)}"
        )
