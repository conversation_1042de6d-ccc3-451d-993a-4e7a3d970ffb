# app/api/endpoints/websocket.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from app.services.interview_service import InterviewService
from app.services.websocket.handlers.tts_handle import TTSHandler
from app.services.websocketv2.video_enhanced_handler import VideoEnhancedWebSocketHandler
from app.services.websocket.webrtc_manager import WebRTCManager
from app.services.websocket.video_manager import VideoManager
from app.services.websocket.message_dispatcher import (
    message_dispatcher, message_validator, rate_limiter, MessageType
)
from app.services.websocket.config import config
from app.services.websocket.utils import WebSocketUtils
from app.core.error_handling import (
    error_handler, ErrorCode, ErrorContext, ErrorSeverity
)
import json
import logging
import asyncio
from datetime import datetime, timezone
from app.services.websocket.handlers.video_handle import VideoHandler
from app.services.websocket.handle_session import InterviewSessionManager

router = APIRouter()
logger = logging.getLogger(__name__)

# Global managers
webrtc_manager = WebRTCManager()
video_manager = VideoManager()
utils = WebSocketUtils()
tts_handler = TTSHandler()
video_handler = VideoHandler()

def get_interview_service() -> InterviewService:
    return InterviewService()

def get_video_websocket_handler() -> VideoEnhancedWebSocketHandler:
    return VideoEnhancedWebSocketHandler()

def _register_message_handlers(handler, interview_service):
    async def handle_connection_request(websocket, session_id, message_data, context):
        await handler.utils.send_message(
            session_id,
            {
                "message": "Hẹ hẹ",
                "session_id": session_id,
                "status": "connected"
            },
            message_type="question_ready"
        )
        return True

    async def handle_tts_request(websocket, session_id, message_data, context):
        return await tts_handler.handle_tts_request_impl(websocket, message_data, session_id)

    async def handle_pong(websocket, session_id, message_data, context):
        return True

    async def handle_webrtc_offer(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_offer(message_data):
            await utils.send_error(websocket, "Invalid WebRTC offer")
            return False

        if session_id not in webrtc_manager.connections:
            await webrtc_manager.create_connection(
                session_id,
                on_track_callback=video_handler.handle_video_track
            )

        return await webrtc_manager.handle_offer(
            session_id, message_data.get("offer"), websocket
        )

    async def handle_webrtc_candidate(websocket, session_id, message_data, context):
        if not message_validator.validate_webrtc_candidate(message_data):
            await utils.send_error(websocket, "Invalid WebRTC candidate")
            return False

        return await webrtc_manager.handle_ice_candidate(
            session_id, message_data.get("candidate")
        )

    async def handle_webrtc_ready(websocket, session_id, message_data, context):
        logger.info(f"WebRTC ready signal from {session_id}")
        await utils.send_ack(websocket, session_id, "webrtc_ready")
        return True

    async def handle_video_message(websocket, session_id, message_data, context):
        return await handler.handle_video_message(session_id, message_data)

    async def handle_candidate_message(websocket, session_id, message_data, context):
        return await interview_service.communicate(session_id, message_data)

    message_dispatcher.register_handler(
        MessageType.CONNECTION_REQUEST, handle_connection_request,
        "Handle connection request"
    )
    message_dispatcher.register_handler(
        MessageType.GENERATE_SPEECH, handle_tts_request,
        "Handle text-to-speech request"
    )
    message_dispatcher.register_handler(
        MessageType.PONG, handle_pong,  
        "Handle pong message"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_OFFER, handle_webrtc_offer,
        "Handle WebRTC offer"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_CANDIDATE, handle_webrtc_candidate,
        "Handle WebRTC ICE candidate"
    )
    message_dispatcher.register_handler(
        MessageType.WEBRTC_READY, handle_webrtc_ready,
        "Handle WebRTC ready signal"
    )

    for msg_type in [MessageType.START_VIDEO_RECORDING, MessageType.VIDEO_CHUNK,
                     MessageType.END_VIDEO_RECORDING, MessageType.ANSWER_COMPLETED]:
        message_dispatcher.register_handler(
            msg_type, handle_video_message,
            f"Handle {msg_type.value}"
        )

    message_dispatcher.register_handler(
        MessageType.CANDIDATE_MESSAGE, handle_candidate_message,
        "Handle candidate message"
    )

@router.websocket("/ws/interview/{session_id}")
async def websocket_interview_endpoint(
    websocket: WebSocket,
    session_id: str,
    interview_service: InterviewService = Depends(get_interview_service),
):
    """Refactored WebSocket endpoint with improved architecture"""

    handler = get_video_websocket_handler()

    _register_message_handlers(handler, interview_service)

    connection_success = await handler.connect(websocket, session_id)
    if not connection_success:
        return

    keep_alive_task = asyncio.create_task(keep_alive_loop(websocket, session_id))
    last_activity = datetime.now(timezone.utc)

    try:
        while True:
            try:
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=config.websocket.connection_timeout
                )
                last_activity = datetime.now(timezone.utc)

                message = message_validator.validate_json(data)
                if not message:
                    await utils.send_error(websocket, "Invalid JSON format", ErrorCode.VALIDATION_MESSAGE_FORMAT.value, session_id)
                    continue

                if not message_validator.validate_message_structure(message):
                    await utils.send_error(websocket, "Invalid message structure", ErrorCode.VALIDATION_MESSAGE_FORMAT.value, session_id)
                    continue

                if not await rate_limiter.check_rate_limit(session_id):
                    context = ErrorContext(
                        session_id=session_id,
                        operation="rate_limiting",
                        component="websocket_endpoint"
                    )
                    error_info = error_handler.create_error(
                        code=ErrorCode.WS_RATE_LIMIT_EXCEEDED,
                        message=f"Rate limit exceeded for session {session_id}",
                        context=context,
                        severity=ErrorSeverity.MEDIUM
                    )
                    await error_handler.send_error_to_websocket(websocket, error_info)
                    continue

                # Dispatch message
                context = {
                    "handler": handler,
                    "interview_service": interview_service,
                    "last_activity": last_activity
                }

                success = await message_dispatcher.dispatch(
                    websocket, session_id, message, context
                )

                if not success:
                    logger.warning(f"Failed to process message for {session_id}")

            except asyncio.TimeoutError:
                # Check for activity timeout
                if (datetime.now(timezone.utc) - last_activity).total_seconds() > config.websocket.activity_timeout:
                    context = ErrorContext(
                        session_id=session_id,
                        operation="activity_timeout",
                        component="websocket_endpoint"
                    )
                    error_info = error_handler.create_error(
                        code=ErrorCode.WS_TIMEOUT,
                        message=f"Activity timeout for session {session_id}",
                        context=context,
                        severity=ErrorSeverity.MEDIUM
                    )
                    logger.warning(f"Connection timeout for {session_id}")
                    break
                continue

            except WebSocketDisconnect:
                logger.info(f"Client {session_id} disconnected normally")
                break

            except Exception as e:
                # Handle unexpected WebSocket errors
                context = ErrorContext(
                    session_id=session_id,
                    operation="websocket_receive",
                    component="websocket_endpoint",
                    additional_data={"exception_type": type(e).__name__}
                )
                error_info = error_handler.create_error(
                    code=ErrorCode.WS_DISCONNECT_UNEXPECTED,
                    message=f"WebSocket receive error for {session_id}: {str(e)}",
                    context=context,
                    exception=e,
                    severity=ErrorSeverity.HIGH
                )
                logger.error(f"WebSocket receive error for {session_id}: {e}")
                break

    except Exception as e:
        logger.error(f"WebSocket error for {session_id}: {e}")
    finally:
        try:
            if keep_alive_task and not keep_alive_task.done():
                keep_alive_task.cancel()
                try:
                    await asyncio.wait_for(keep_alive_task, timeout=5.0)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    pass

            await video_manager.stop_recording(session_id)
            await webrtc_manager.close_connection(session_id)
            await handler.disconnect(session_id)
            rate_limiter.cleanup_session(session_id)
            
            session_manager = InterviewSessionManager()
            session_manager.redis_client.delete(f"session:{session_id}")
            
            logger.info(f"Session cleanup completed for {session_id}")
            
        except Exception as e:
            logger.error(f"Error during session cleanup for {session_id}: {e}")



async def keep_alive_loop(websocket: WebSocket, session_id: str):
    """Keep WebSocket connection alive with periodic pings"""
    try:
        while True:
            await asyncio.sleep(config.websocket.ping_interval)
            if websocket.client_state.value == 1:
                try:
                    await websocket.send_text(json.dumps({
                        "type": "ping",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }))
                    logger.debug(f"Sent ping to {session_id}")
                except Exception as e:
                    logger.warning(f"Failed to send ping to {session_id}: {e}")
                    break
            else:
                logger.info(f"WebSocket not connected for {session_id}, stopping keep-alive")
                break
    except asyncio.CancelledError:
        logger.debug(f"Keep-alive task cancelled for {session_id}")
    except Exception as e:
        logger.error(f"Keep-alive error for {session_id}: {e}")
