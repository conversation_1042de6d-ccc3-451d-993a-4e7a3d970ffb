# app/api/endpoints/health.py
from fastapi import APIRouter, status
from app.models.base import engine
from sqlalchemy import text
import redis
from app.core.config import settings
from datetime import datetime
from app.services.websocket import handle_websocket

router = APIRouter()

@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    """Comprehensive health check endpoint"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {}
    }
    
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        health_status["services"]["database"] = "healthy"
    except Exception as e:
        health_status["services"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    try:
        redis_client = redis.from_url(settings.REDIS_URL)
        redis_client.ping()
        health_status["services"]["redis"] = "healthy"
    except Exception as e:
        health_status["services"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    health_status["services"]["websocket"] = {
        "active_connections": len(handle_websocket.active_connections),
        "status": "healthy"
    }
    
    return health_status

@router.get("/metrics", status_code=status.HTTP_200_OK)
async def get_metrics():
    """Get system performance metrics"""
    return {
        "active_websocket_connections": len(handle_websocket.active_connections),
        "connection_metadata": {
            session_id: {
                "connected_duration": (
                    datetime.utcnow() - metadata["connected_at"]
                ).total_seconds(),
                "message_count": metadata["message_count"]
            }
            for session_id, metadata in handle_websocket.connection_metadata.items()
        }
    }
