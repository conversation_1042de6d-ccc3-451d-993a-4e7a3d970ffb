from app.services.question.question_generation_service import QuestionGenerationService
from app.services.question.interviewer_role_service import Interview<PERSON><PERSON><PERSON>
from app.models.company import CompanyKeypoint, Company
from app.models.question import Question
from app.models.schemas.question import QuestionResponse
from app.models.base import get_db
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import uuid
from typing import List

router = APIRouter()

@router.post("/keypoints/{keypoint_id}/generate-questions")
def generate_questions_for_keypoint(
    keypoint_id: uuid.UUID,
    interviewer_roles: List[str] = Query(default=None),
    questions_per_role: int = Query(default=1, ge=1, le=2),
    db: Session = Depends(get_db)
):
    """Generate questions với separated services"""
    
    # Validate keypoint exists
    keypoint = db.query(CompanyKeypoint).filter(
        CompanyKeypoint.id == keypoint_id,
        CompanyKeypoint.is_active == True
    ).first()
    
    if not keypoint:
        raise HTTPException(status_code=404, detail="Keypoint not found")
    
    # Get company
    company = Company.get_company_by_id(db, keypoint.company_id)
    if not company:
        raise HTTPException(status_code=404, detail="Company not found")
    
    # Generate questions using separated service
    question_service = QuestionGenerationService()
    questions_data = question_service.generate_questions_for_keypoint(
        company_id=str(keypoint.company_id),
        keypoint=keypoint,
        company=company,
        interviewer_roles=interviewer_roles,
        questions_per_role=questions_per_role
    )
    
    # Save to database
    created_questions = Question.create_questions_for_keypoint(
        db=db,
        keypoint_id=keypoint_id,
        questions_data=questions_data
    )
    
    return {
        "keypoint_id": keypoint_id,
        "total_questions": len(created_questions),
        "questions": [QuestionResponse.from_orm(q) for q in created_questions],
        "services_used": ["QuestionGenerationService", "CompanyRAGService", "InterviewerRoleService"]
    }
