# backend/app/api/api.py
from fastapi import APIRouter
from app.api.endpoints import companies, interviews, health, websocket, analytics, auth, candidates, questions

api_router = APIRouter()

api_router.include_router(
    auth.router, 
    prefix="/auth", 
    tags=["authentication"]
)

api_router.include_router(
    companies.router, 
    prefix="/companies", 
    tags=["companies"]
)

api_router.include_router(
    interviews.router, 
    prefix="/interviews", 
    tags=["interviews"]
)

api_router.include_router(
    health.router, 
    prefix="/health", 
    tags=["health"]
)

api_router.include_router(
    websocket.router, 
    prefix="/ws", 
    tags=["websocket"]
)

api_router.include_router(
    analytics.router, 
    prefix="/analytics", 
    tags=["analytics"]
)

api_router.include_router(
    candidates.router, 
    prefix="/candidates", 
    tags=["candidates"]
)

api_router.include_router(
    questions.router, 
    prefix="/questions", 
    tags=["questions"]
)