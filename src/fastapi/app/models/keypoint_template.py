# src/fastapi/app/models/keypoint_template.py
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.base import Base
import uuid
from datetime import datetime

class KeypointTemplate(Base):
    __tablename__ = "keypoint_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey("companies.id"), nullable=False)
    name = Column(String(255), nullable=False)  # "Technical Problem Solving"
    description = Column(Text)
    position_type = Column(String(100))  # "developer", "manager", "designer"
    industry = Column(String(100))  # "IT", "Finance", "Healthcare"
    
    difficulty_level = Column(Integer, default=1)
    
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = relationship("Company", back_populates="keypoint_templates")
    company_keypoints = relationship("CompanyKeypoint", back_populates="template")