# src/fastapi/app/models/video_analysis.py
from sqlalchemy import Column, String, DateTime, JSON, Float, BigInteger, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Session
from app.models.base import Base
import uuid
from datetime import datetime
from typing import List, Dict, Optional

class VideoSegment(Base):
    __tablename__ = "video_segments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    interview_session_id = Column(UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.id"), nullable=False)
    
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    video_file_path = Column(String, nullable=True)
    video_size_bytes = Column(BigInteger, nullable=True)
    status = Column(String(50), default="recording", nullable=False)  # recording, completed, processing, failed
    segment_metadata = Column(JSON, default=dict)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    interview_session = relationship("InterviewSession", back_populates="video_segments")
    question = relationship("Question")
    video_analysis = relationship("VideoAnalysis", back_populates="video_segment", uselist=False)
    
    @classmethod
    def create_segment(
        cls,
        db: Session,
        interview_session_id: uuid.UUID,
        question_id: uuid.UUID,
        start_time: datetime
    ) -> 'VideoSegment':
        """Create new video segment"""
        segment = cls(
            interview_session_id=interview_session_id,
            question_id=question_id,
            start_time=start_time,
            status="recording"
        )
        db.add(segment)
        db.commit()
        db.refresh(segment)
        return segment
    
    def complete_segment(self, db: Session, end_time: datetime, video_data: bytes = None) -> None:
        """Mark segment as completed"""
        self.end_time = end_time
        self.duration_seconds = (end_time - self.start_time).total_seconds()
        self.status = "completed"
        
        if video_data:
            self.video_size_bytes = len(video_data)
        
        db.commit()
    
    @classmethod
    def get_by_session_and_question(
        cls,
        db: Session,
        session_id: uuid.UUID,
        question_id: uuid.UUID
    ) -> Optional['VideoSegment']:
        """Get segment by session and question"""
        return db.query(cls).filter(
            cls.interview_session_id == session_id,
            cls.question_id == question_id
        ).first()

class VideoAnalysis(Base):
    __tablename__ = "video_analyses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    interview_session_id = Column(UUID(as_uuid=True), ForeignKey("interview_sessions.id", ondelete="CASCADE"), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.id"), nullable=False)
    video_segment_id = Column(UUID(as_uuid=True), ForeignKey("video_segments.id", ondelete="CASCADE"), nullable=False)
    
    # Analysis Results
    gemini_analysis_result = Column(JSON, nullable=False)
    scores = Column(JSON, nullable=False)
    behavioral_insights = Column(JSON, nullable=True)
    recommendations = Column(JSON, nullable=True)
    
    # Metadata
    video_duration_seconds = Column(Float, nullable=False)
    analysis_status = Column(String(50), default="pending", nullable=False)  # pending, completed, failed
    confidence_score = Column(Float, default=0.0)
    model_version = Column(String(100), default="gemini-2.0-flash-exp")
    
    analyzed_at = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    interview_session = relationship("InterviewSession", back_populates="video_analyses")
    question = relationship("Question")
    video_segment = relationship("VideoSegment", back_populates="video_analysis")
    
    @classmethod
    def create_analysis(
        cls,
        db: Session,
        video_segment: VideoSegment,
        gemini_result: Dict,
        scores: Dict,
        behavioral_insights: Dict = None,
        recommendations: Dict = None
    ) -> 'VideoAnalysis':
        """Create new video analysis"""
        analysis = cls(
            interview_session_id=video_segment.interview_session_id,
            question_id=video_segment.question_id,
            video_segment_id=video_segment.id,
            gemini_analysis_result=gemini_result,
            scores=scores,
            behavioral_insights=behavioral_insights or {},
            recommendations=recommendations or {},
            video_duration_seconds=video_segment.duration_seconds or 0,
            analysis_status="completed",
            confidence_score=scores.get("overall_confidence", 0.0)
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        return analysis
    
    @classmethod
    def get_by_session(cls, db: Session, session_id: uuid.UUID) -> List['VideoAnalysis']:
        """Get all analyses for a session"""
        return db.query(cls).filter(
            cls.interview_session_id == session_id,
            cls.analysis_status == "completed"
        ).order_by(cls.created_at).all()
