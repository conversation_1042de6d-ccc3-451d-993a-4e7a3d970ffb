# app/models/user.py
from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Session
from app.models.base import Base
import uuid
from datetime import datetime
from app.models.schemas.user import UserCreate
from enum import Enum as PyEnum
import bcrypt

class UserRole(PyEnum):
    AGENT = "agent"
    CANDIDATE = "candidate"

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, index=True, nullable=False)
    password = Column(String(255), nullable=False)
    name = Column(String(255))
    role = Column(String(50), nullable=True)
    last_login_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    agent = relationship("Agent", back_populates="user", uselist=False)
    candidate_profile = relationship("Candidate", back_populates="user", uselist=False)
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, name={self.name}, role={self.role})>"
    
    def create_user(self, db: Session, user: UserCreate):
        db_user = User(
            email=user.email, 
            password=self.hash_password(user.password), 
            name=user.name,
            role=user.role if hasattr(user, 'role') else None
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    def get_user_by_id(self, db: Session, user_id: UUID):
        return db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, db: Session, email: str):
        return db.query(User).filter(User.email == email).first()
    
    def update_user(self, db: Session, user_id: UUID, user: UserCreate):
        db_user = self.get_user_by_id(db, user_id)
        if db_user:
            db_user.email = user.email
            db_user.password = self.hash_password(user.password)
            db_user.name = user.name
            if hasattr(user, 'role'):
                db_user.role = user.role
            db.commit()
            db.refresh(db_user)
            return db_user
    
    def update_last_login(self, db: Session, user_id: UUID):
        user = self.get_user_by_id(db, user_id)
        if user:
            user.last_login_at = datetime.utcnow()
            user.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(user)
            return user
        return None

    @property
    def role_enum(self):
        if self.role:
            return UserRole(self.role)
        return None

    @role_enum.setter
    def role_enum(self, value):
        if value is not None:
            self.role = value.value
        else:
            self.role = None
            
    def hash_password(self, password: str):
        password_bytes = password.encode('utf-8')
        salt = bcrypt.gensalt()
        hashed_password = bcrypt.hashpw(password_bytes, salt)
        return hashed_password.decode('utf-8')
            
