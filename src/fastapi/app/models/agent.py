from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Session
from app.models.base import Base
import uuid
from datetime import datetime

class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    profile_name = Column(String(255), nullable=False)
    profile_organization = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    user = relationship("User", back_populates="agent")
    
    def __repr__(self):
        return f"<Agent(id={self.id}, profile_name={self.profile_name})>"
    
    @staticmethod
    def create_agent(db: Session, user_id: uuid.UUID, profile_name: str, profile_organization: str = None):
        """Tạo agent mới"""
        db_agent = Agent(
            user_id=user_id,
            profile_name=profile_name,
            profile_organization=profile_organization
        )
        db.add(db_agent)
        db.commit()
        db.refresh(db_agent)
        return db_agent
    
    @staticmethod
    def get_agent_by_user_id(db: Session, user_id: uuid.UUID):
        """Lấy agent theo user_id"""
        return db.query(Agent).filter(Agent.user_id == user_id).first()
    
    @staticmethod
    def get_agent_by_id(db: Session, agent_id: uuid.UUID):
        """Lấy agent theo id"""
        return db.query(Agent).filter(Agent.id == agent_id).first() 