# src/fastapi/app/models/question.py  
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, ForeignKey, JSON, Float, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Session
from app.models.base import Base
import uuid
from datetime import datetime
from app.models.company import CompanyKeypoint
from typing import List, Dict

class Question(Base):
    __tablename__ = "questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_keypoint_id = Column(UUID(as_uuid=True), ForeignKey("company_keypoints.id"), nullable=False)
    
    # Question content
    question_text = Column(Text, nullable=False)
    question_type = Column(String(50), nullable=False)  # "behavioral", "technical", "scenario"
    difficulty_level = Column(Integer, default=1)  # 1-5 scale
    
    # ✅ THÊM INTERVIEWER ROLE FIELD
    interviewer_role = Column(String(50), nullable=False, default="hr_manager")
    interviewer_perspective = Column(Text)  # Gó<PERSON> nh<PERSON>n của interviewer
    
    # Metadata for interviewer
    expected_answer_points = Column(JSON)  # C<PERSON>c đi<PERSON>m cần có trong câu trả lời
    follow_up_suggestions = Column(JSON)  # Gợi ý câu hỏi follow-up
    evaluation_criteria = Column(JSON)  # Tiêu chí đánh giá
    role_specific_context = Column(JSON)  # Context specific cho role
    
    # Ordering và priority
    order_index = Column(Integer, default=0)  # Thứ tự trong keypoint
    estimated_time_minutes = Column(Integer, default=3)  # Thời gian dự kiến
    
    # AI generation info
    generation_prompt = Column(Text)  # Prompt đã dùng để generate
    generation_metadata = Column(JSON)  # AI model, temperature, etc.
    
    # Status và usage
    is_active = Column(Boolean, default=True)
    usage_count = Column(Integer, default=0)  # Số lần đã sử dụng
    effectiveness_score = Column(Float, default=0.0)  # Điểm hiệu quả từ feedback
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company_keypoint = relationship("CompanyKeypoint", back_populates="questions")
    
    @classmethod
    def get_questions_by_keypoint(cls, db: Session, keypoint_id: uuid.UUID):
        return db.query(cls).filter(
            cls.company_keypoint_id == keypoint_id,
            cls.is_active == True
        ).order_by(cls.order_index).all()
    
    @classmethod
    def get_questions_by_company(cls, db: Session, company_id: uuid.UUID):
        return db.query(cls).join(CompanyKeypoint).filter(
            CompanyKeypoint.company_id == company_id,
            CompanyKeypoint.is_active == True,
            cls.is_active == True
        ).order_by(CompanyKeypoint.order_index, cls.order_index).all()
    
    # ✅ THÊM MISSING METHODS
    @classmethod
    def create_questions_for_keypoint(
        cls, 
        db: Session, 
        keypoint_id: uuid.UUID,
        questions_data: List[Dict]
    ) -> List['Question']:
        """Create multiple questions for a keypoint"""
        created_questions = []
        
        for i, q_data in enumerate(questions_data):
            # Get next order index
            next_order = cls.get_next_order_index(db, keypoint_id)
            
            db_question = cls(
                company_keypoint_id=keypoint_id,
                question_text=q_data["question_text"],
                question_type=q_data["question_type"],
                difficulty_level=q_data["difficulty_level"],
                interviewer_role=q_data.get("interviewer_role", "hr_manager"),
                interviewer_perspective=q_data.get("interviewer_perspective"),
                expected_answer_points=q_data.get("expected_answer_points"),
                follow_up_suggestions=q_data.get("follow_up_suggestions"),
                evaluation_criteria=q_data.get("evaluation_criteria"),
                role_specific_context=q_data.get("role_specific_context"),
                order_index=next_order + i,
                estimated_time_minutes=q_data.get("estimated_time_minutes", 3),
                generation_metadata=q_data.get("generation_metadata"),
                is_active=True
            )
            
            db.add(db_question)
            created_questions.append(db_question)
        
        db.commit()
        
        for q in created_questions:
            db.refresh(q)
        
        return created_questions
    
    @classmethod  
    def get_next_order_index(cls, db: Session, keypoint_id: uuid.UUID) -> int:
        """Get next order index for questions in keypoint"""
        max_order = db.query(func.max(cls.order_index)).filter(
            cls.company_keypoint_id == keypoint_id,
            cls.is_active == True
        ).scalar() or 0
        return max_order + 1
    
    @classmethod
    def create_single_question(
        cls,
        db: Session,
        keypoint_id: uuid.UUID,
        question_data: Dict
    ) -> 'Question':
        """Create single question"""
        next_order = cls.get_next_order_index(db, keypoint_id)
        
        db_question = cls(
            company_keypoint_id=keypoint_id,
            question_text=question_data["question_text"],
            question_type=question_data["question_type"],
            difficulty_level=question_data["difficulty_level"],
            interviewer_role=question_data.get("interviewer_role", "hr_manager"),
            interviewer_perspective=question_data.get("interviewer_perspective"),
            expected_answer_points=question_data.get("expected_answer_points"),
            follow_up_suggestions=question_data.get("follow_up_suggestions"),
            evaluation_criteria=question_data.get("evaluation_criteria"),
            role_specific_context=question_data.get("role_specific_context"),
            order_index=next_order,
            estimated_time_minutes=question_data.get("estimated_time_minutes", 3),
            generation_metadata=question_data.get("generation_metadata"),
            is_active=True
        )
        
        db.add(db_question)
        db.commit()
        db.refresh(db_question)
        
        return db_question