# fastapi/app/models/company.py
from sqlalchemy import Column, String, Text, DateTime, JSON, Boolean, ForeignKey, Integer, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.base import Base
import uuid
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc, asc
from typing import Dict


class Company(Base):
    __tablename__ = "companies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    industry = Column(String(100))
    description = Column(Text)
    core_values = Column(JSON)
    vision_mission = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    documents = relationship(
        "CompanyDocument", back_populates="company", cascade="all, delete-orphan", lazy="select")
    
    interviews = relationship(
        "InterviewSession", 
        back_populates="company", 
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    keypoint_templates = relationship(
        "KeypointTemplate", 
        back_populates="company", 
        cascade="all, delete-orphan",
        order_by="KeypointTemplate.created_at"
    )
    
    keypoints = relationship(
        "CompanyKeypoint", 
        back_populates="company", 
        cascade="all, delete-orphan",
        order_by="CompanyKeypoint.order_index"
    )
    
    @classmethod
    def create_company(cls, db: Session, company_data):
        """Create new company"""
        db_company = cls(
            name=company_data.name,
            industry=company_data.industry,
            description=company_data.description,
            core_values=company_data.core_values,
            vision_mission=company_data.vision_mission
        )
        db.add(db_company)
        db.commit()
        db.refresh(db_company)
        return db_company
    
    @classmethod
    def get_company_by_id(cls, db: Session, company_id: uuid.UUID):
        return db.query(cls).filter(cls.id == company_id).first()
    
    @classmethod
    def get_all_companies(cls, db: Session, skip: int = 0, limit: int = 100):
        return db.query(cls).offset(skip).limit(limit).all()

    @classmethod
    def get_companies_with_filters(
        cls,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        industry: str = None,
        search: str = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ):
        """Get companies with filtering, searching, and sorting"""
        query = db.query(cls)

        # Apply filters
        if industry:
            query = query.filter(cls.industry.ilike(f"%{industry}%"))

        if search:
            query = query.filter(
                or_(
                    cls.name.ilike(f"%{search}%"),
                    cls.description.ilike(f"%{search}%")
                )
            )

        # Apply sorting
        if sort_by == "name":
            sort_column = cls.name
        elif sort_by == "industry":
            sort_column = cls.industry
        else:  # default to created_at
            sort_column = cls.created_at

        if sort_order == "asc":
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))

        return query.offset(skip).limit(limit).all()

    @classmethod
    def get_companies_count(
        cls,
        db: Session,
        industry: str = None,
        search: str = None
    ) -> int:
        """Get total count of companies with filters"""
        query = db.query(func.count(cls.id))

        # Apply same filters as get_companies_with_filters
        if industry:
            query = query.filter(cls.industry.ilike(f"%{industry}%"))

        if search:
            query = query.filter(
                or_(
                    cls.name.ilike(f"%{search}%"),
                    cls.description.ilike(f"%{search}%")
                )
            )

        return query.scalar()

class CompanyDocument(Base):
    __tablename__ = "company_documents"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey("companies.id"), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    document_type = Column(String(50), nullable=False)
    file_size = Column(Integer)
    mime_type = Column(String(100))
    upload_date = Column(DateTime, default=datetime.utcnow)
    processed = Column(Boolean, default=False)
    text_chunks_count = Column(Integer, default=0)

    company = relationship("Company", back_populates="documents")

class CompanyKeypoint(Base):
    __tablename__ = "company_keypoints"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey("companies.id"), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("keypoint_templates.id"), nullable=False)
    
    # ✅ AI-generated content dựa trên template + company data
    custom_name = Column(String(255))  # AI có thể customize name
    custom_description = Column(Text)  # AI-generated description
    position_type = Column(String(100), nullable=False)
    
    priority = Column(Integer, default=1)
    order_index = Column(Integer, default=0)
    
    # ✅ Metadata về AI generation
    source_data = Column(JSON)  # Template + company docs đã dùng
    generation_metadata = Column(JSON)  # AI model, prompt, timestamp
    
    status = Column(String(50), default="draft")  # draft/approved/active
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = relationship("Company", back_populates="keypoints")
    template = relationship("KeypointTemplate", back_populates="company_keypoints")  # ✅ Từ template gốc
    questions = relationship("Question", back_populates="company_keypoint", cascade="all, delete-orphan")  # ✅ Generate questions
    
    @classmethod
    def get_next_order_index(cls, db: Session, company_id: uuid.UUID, position_type: str) -> int:
        """Get next order index for company keypoint"""
        max_order = db.query(func.max(cls.order_index)).filter(
            cls.company_id == company_id,
            cls.position_type == position_type,
            cls.is_active == True
        ).scalar() or 0
        return max_order + 1
    
    @classmethod
    def create_keypoint(cls, db: Session, keypoint_data: Dict) -> 'CompanyKeypoint':
        """Create new company keypoint with auto order index"""
        next_order = cls.get_next_order_index(
            db, 
            keypoint_data["company_id"], 
            keypoint_data["position_type"]
        )
        
        db_keypoint = cls(
            company_id=keypoint_data["company_id"],
            template_id=keypoint_data["template_id"],
            custom_name=keypoint_data["custom_name"],
            custom_description=keypoint_data["custom_description"],
            position_type=keypoint_data["position_type"],
            priority=keypoint_data.get("priority", 1),
            order_index=next_order,
            source_data=keypoint_data.get("source_data"),
            generation_metadata=keypoint_data.get("generation_metadata"),
            status="draft",
            is_active=True
        )
        
        db.add(db_keypoint)
        db.commit()
        db.refresh(db_keypoint)
        return db_keypoint

