# src/fastapi/app/models/schemas/question.py

from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional, List, Dict, Any

class QuestionCreate(BaseModel):
    company_keypoint_id: UUID
    question_text: str
    question_type: str
    difficulty_level: int = 1
    interviewer_role: str = "hr_manager"
    interviewer_perspective: Optional[str] = None
    expected_answer_points: Optional[List[str]] = None
    follow_up_suggestions: Optional[List[str]] = None
    evaluation_criteria: Optional[Dict[str, Any]] = None
    role_specific_context: Optional[Dict[str, Any]] = None
    estimated_time_minutes: int = 3

class QuestionResponse(BaseModel):
    id: UUID
    company_keypoint_id: UUID
    question_text: str
    question_type: str
    difficulty_level: int
    interviewer_role: str
    interviewer_perspective: Optional[str]
    expected_answer_points: Optional[List[str]]
    follow_up_suggestions: Optional[List[str]]
    evaluation_criteria: Optional[Dict[str, Any]]
    role_specific_context: Optional[Dict[str, Any]]
    order_index: int
    estimated_time_minutes: int
    is_active: bool
    usage_count: int
    effectiveness_score: float
    created_at: datetime

    class Config:
        from_attributes = True

class QuestionBatchCreate(BaseModel):
    keypoint_id: UUID
    questions: List[QuestionCreate]

class QuestionGenerationRequest(BaseModel):
    keypoint_id: UUID
    interviewer_roles: Optional[List[str]] = None
    questions_per_role: int = 1