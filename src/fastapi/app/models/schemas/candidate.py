from pydantic import BaseModel, Field
from typing import Optional, Any, List
from datetime import datetime
from enum import Enum
from uuid import UUID

class EmploymentStatus(str, Enum):
    employed = "employed"
    unemployed = "unemployed"
    student = "student"

class CandidateRegisterRequest(BaseModel):
    candidate_name_kana: str = Field(..., alias="candidate<PERSON><PERSON><PERSON><PERSON>", min_length=1)
    candidate_age: int = Field(..., alias="candidateAge", ge=18, le=70)
    candidate_employment_status: EmploymentStatus = Field(..., alias="candidateEmploymentStatus")
    candidate_company: Optional[str] = Field(None, alias="candidateCompany")
    candidate_position: Optional[str] = Field(None, alias="candidatePosition")
    candidate_address: Optional[str] = Field(None, alias="candidateAddress")
    candidate_has_resume: bool = Field(..., alias="candidateHasResume")
    candidate_has_career_history: bool = Field(..., alias="candidateHasCareerHistory")
    candidate_profile: Optional[Any] = Field(None, alias="candidateProfile")
    user_id: UUID = Field(None, alias="userId")
    candidate_main_skill: Optional[str] = Field(None, alias="candidateMainSkill")
    candidate_other_skill: Optional[str] = Field(None, alias="candidateOtherSkill")
    candidate_experience: Optional[str] = Field(None, alias="candidateExperience")
    candidate_education: Optional[str] = Field(None, alias="candidateEducation")
    candidate_language: Optional[str] = Field(None, alias="candidateLanguage")
    candidate_other_info: Optional[str] = Field(None, alias="candidateOtherInfo")
    candidate_email: str = Field(..., alias="candidateEmail")

    model_config = {
        "populate_by_name": True
    }


class CandidateResponse(BaseModel):
    id: UUID = Field(..., alias="id")
    user_id: UUID = Field(..., alias="userId")
    name_kana: str = Field(..., alias="nameKana")
    age: int
    employment_status: EmploymentStatus
    company: Optional[str]
    position: Optional[str]
    address: Optional[str]
    has_resume: bool = Field(..., alias="hasResume")
    has_career_history: bool = Field(..., alias="hasCareerHistory")
    profile: Optional[Any]
    main_skill: Optional[str]
    other_skill: Optional[str]
    experience: Optional[str]
    education: Optional[str]
    language: Optional[str]
    other_info: Optional[str]
    created_at: datetime = Field(..., alias="createdAt")
    updated_at: datetime = Field(..., alias="updatedAt")


    model_config = {
        "populate_by_name": True,
        "from_attributes": True
    }
    
class CandidateDocumentResponse(BaseModel):
    id: UUID = Field(..., alias="id")
    candidate_id: UUID = Field(..., alias="candidateId")
    file_name: str = Field(..., alias="fileName")
    file_path: str = Field(..., alias="filePath")
    document_type: str = Field(..., alias="documentType")
    file_size: int = Field(..., alias="fileSize")
    
class CandidateRAGSummary(BaseModel):
    user_id: str
    status: str
    personal_information: dict = Field(default_factory=dict)
    skills_and_experience: dict = Field(default_factory=dict)
    education_and_language: dict = Field(default_factory=dict)
    documents_info: dict = Field(default_factory=dict)
    cv_content: dict = Field(default_factory=dict)
    summary_text: str = ""

    model_config = {
        "populate_by_name": True
    }
    
class CandidateDetailResponse(CandidateResponse):
    documents: List[CandidateDocumentResponse] = Field(default_factory=list)
    rag_summary: Optional[CandidateRAGSummary] = Field(None, alias="ragSummary")

    model_config = {
        "populate_by_name": True,
        "from_attributes": True
    }