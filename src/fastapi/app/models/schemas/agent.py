from pydantic import BaseModel, EmailStr
from datetime import datetime
from uuid import UUID
from typing import Optional
from enum import Enum
from app.models.user import UserRole

class AgentRole(str, Enum):
    AGENT = "agent"
    INTERVIEWER = "interviewer"

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class AgentProfileResponse(BaseModel):
    id: UUID
    email: str
    role: UserRole
    profile_name: str
    profile_organization: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None
    is_active: bool

    class Config:
        from_attributes = True

class AgentCreateRequest(BaseModel):
    email: EmailStr
    password: str
    role: AgentRole
    profile_name: str
    profile_organization: Optional[str] = None

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str

class MessageResponse(BaseModel):
    message: str
    detail: Optional[str] = None 