from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class QuestionRequest(BaseModel):
    session_id: str
    question_type: str
    question_text: str
    question_options: Optional[List[str]] = None
    question_answer: Optional[str] = None
    question_explanation: Optional[str] = None
    question_score: Optional[int] = None
    question_feedback: Optional[str] = None
    question_status: Optional[str] = None
    question_created_at: Optional[datetime] = None
    question_updated_at: Optional[datetime] = None
    
class QuestionResponse(BaseModel):
    question_id: str
    question_text: str
    question_options: Optional[List[str]] = None
    question_answer: Optional[str] = None
    question_explanation: Optional[str] = None
    question_score: Optional[int] = None
    question_feedback: Optional[str] = None
    question_status: Optional[str] = None
    question_created_at: Optional[datetime] = None
    question_updated_at: Optional[datetime] = None
    
class AnalysisRequest(BaseModel):
    session_id: str
    question_id: str
    question_text: str
    question_options: Optional[List[str]] = None
    question_answer: Optional[str] = None
    question_explanation: Optional[str] = None  