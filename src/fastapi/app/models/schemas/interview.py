# app/models/schemas/interview.py
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional, List, Dict, Any

class InterviewCreate(BaseModel):
    candidate_name: str

class InterviewLinkCreate(BaseModel):
    candidate_id: UUID
    company_id: UUID
    expiration_days: int = 7
    agent_notes: Optional[str] = None
    interviewer_role: Optional[str] = "hr"
    interview_type: Optional[str] = "first"
    candidate_email: Optional[str] = None

class InterviewLinkResponse(BaseModel):
    id: str
    session_token: str
    expires_at: datetime
    candidate_name: str
    interview_url: Optional[str] = None

class InterviewResponse(BaseModel):
    id: str
    candidate_name: str
    candidate_profile: dict
    status: str
    created_at: datetime
    updated_at: datetime
    transcript: list
    feedback: list
    overall_score: int