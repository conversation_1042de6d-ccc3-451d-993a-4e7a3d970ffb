# src/fastapi/app/models/schemas/company_keypoint.py

from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional, List
from .question import QuestionResponse
from .keypoint_template import KeypointTemplateResponse

class CompanyKeypointCreate(BaseModel):
    company_id: UUID
    template_id: UUID
    position_type: str
    custom_name: Optional[str] = None
    custom_description: Optional[str] = None
    priority: int = 1

class CompanyKeypointResponse(BaseModel):
    id: UUID
    company_id: UUID
    template_id: UUID
    custom_name: Optional[str]
    custom_description: Optional[str]
    position_type: str
    priority: int
    order_index: int
    status: str
    is_active: bool
    created_at: datetime
    
    # Related data
    template: Optional[KeypointTemplateResponse] = None
    questions: List[QuestionResponse] = []
    total_questions: int = 0
    estimated_total_time: int = 0

    class Config:
        from_attributes = True

class KeypointGenerationRequest(BaseModel):
    company_id: UUID
    template_id: Optional[UUID] = None
    custom_requirements: Optional[str] = None