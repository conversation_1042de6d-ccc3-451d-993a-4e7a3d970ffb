# app/models/schemas/document.py
from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

class DocumentUploadRequest(BaseModel):
    document_type: str
    description: Optional[str] = None

class DocumentResponse(BaseModel):
    id: UUID
    file_name: str
    document_type: str
    upload_date: datetime
    processed: bool
    text_chunks_count: Optional[int] = None

class CompanyDocumentResponse(DocumentResponse):
    company_id: UUID

class CandidateDocumentResponse(DocumentResponse):
    candidate_id: UUID