# src/fastapi/app/models/schemas/keypoint_template.py
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional

class KeypointTemplateCreate(BaseModel):
    name: str
    description: Optional[str] = None
    position_type: Optional[str] = None
    industry: Optional[str] = None
    difficulty_level: int = 1
    estimated_time_minutes: int = 5
    is_default: bool = False

class KeypointTemplateResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str]
    position_type: Optional[str]
    industry: Optional[str]
    difficulty_level: int
    is_default: bool
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True