# app/models/schemas/user.py
from pydantic import BaseModel, EmailStr
from datetime import datetime
from uuid import UUID
from typing import Optional
from enum import Enum

class UserRole(str, Enum):
    AGENT = "agent"
    CANDIDATE = "candidate"

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    name: str
    role: Optional[UserRole] = None

class UserUpdate(BaseModel):
    email: EmailStr
    password: str
    name: str
    role: Optional[UserRole] = None

class UserResponse(BaseModel):
    id: UUID
    email: str
    name: str
    role: Optional[UserRole] = None
    last_login_at: Optional[datetime] = None
    is_active: bool
    is_admin: bool
    created_at: datetime