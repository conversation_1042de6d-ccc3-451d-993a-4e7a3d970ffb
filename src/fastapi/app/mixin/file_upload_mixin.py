from fastapi import HTTPException, UploadFile
from pathlib import Path
from typing import List, Optional, <PERSON>ple
import aiofiles
import mimetypes
from uuid import UUID
import logging

logger = logging.getLogger(__name__)

class FileUploadMixin:
    """Mixin class to handle file upload with common validation"""
    
    ALLOWED_EXTENSIONS = {
        'document': ['.pdf', '.doc', '.docx'],
        'image': ['.jpg', '.jpeg', '.png', '.gif'],
        'archive': ['.zip', '.rar']
    }
    
    MAX_FILE_SIZES = {
        'document': 10 * 1024 * 1024,  # 10MB
        'image': 5 * 1024 * 1024,      # 5MB
        'archive': 50 * 1024 * 1024    # 50MB
    }
    
    @staticmethod
    async def validate_file(
        file: UploadFile, 
        file_type: str = 'document',
        max_size: Optional[int] = None,
        allowed_extensions: Optional[List[str]] = None
    ) -> Tuple[bytes, str]:
        """
        Validate file upload
        
        Args:
            file: UploadFile object
            file_type: Type of file ('document', 'image', 'archive')
            max_size: Maximum file size in bytes (optional)
            allowed_extensions: List of allowed extensions (optional)
            
        Returns:
            Tuple of (file_content, mime_type)
            
        Raises:
            HTTPException: If validation fails
        """
        if not file or not file.filename:
            raise HTTPException(
                status_code=400,
                detail="No file uploaded"
            )
        
        file_extension = Path(file.filename).suffix.lower()
        extensions = allowed_extensions or FileUploadMixin.ALLOWED_EXTENSIONS.get(file_type, [])
        
        if file_extension not in extensions:
            raise HTTPException(
                status_code=400,
                detail=f"Only support files: {', '.join(extensions)}"
            )
        
        file_content = await file.read()
        
        max_allowed_size = max_size or FileUploadMixin.MAX_FILE_SIZES.get(file_type, 10 * 1024 * 1024)
        if len(file_content) > max_allowed_size:
            raise HTTPException(
                status_code=400,
                detail=f"File size must be less than {max_allowed_size // (1024*1024)}MB"
            )
        
        await file.seek(0)
        
        mime_type, _ = mimetypes.guess_type(file.filename)
        
        return file_content, mime_type or "application/octet-stream"
    
    @staticmethod
    async def save_file(
        file: UploadFile,
        upload_dir: str,
        filename: str,
        create_dir: bool = True
    ) -> Path:
        """
        Save file to disk
        
        Args:
            file: UploadFile object
            upload_dir: Directory to save file
            filename: Name of the file to save
            create_dir: Whether to create directory if not exists
            
        Returns:
            Path to saved file
        """
        dir_path = Path(upload_dir)
        
        if create_dir:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        file_path = dir_path / filename
        
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        logger.info(f"Saved file: {file_path}")
        return file_path
    
    @staticmethod
    def generate_unique_filename(
        user_id: UUID,
        original_filename: str,
        prefix: str = ""
    ) -> str:
        """
        Generate unique filename
        
        Args:
            user_id: User ID
            original_filename: Original filename
            prefix: Prefix for filename
            
        Returns:
            Unique filename
        """
        file_extension = Path(original_filename).suffix
        return f"{user_id}_{prefix}{file_extension}" if prefix else f"{user_id}{file_extension}"


class DocumentUploadMixin(FileUploadMixin):
    """Mixin for document upload"""
    
    @staticmethod
    async def validate_document(file: UploadFile) -> Tuple[bytes, str]:
        """Validate document file"""
        return await FileUploadMixin.validate_file(
            file=file,
            file_type='document',
            allowed_extensions=['.pdf', '.doc', '.docx']
        )
    
    @staticmethod
    async def save_candidate_cv(
        file: UploadFile,
        candidate_id: UUID,
        upload_base_dir: str = "uploads/candidates"
    ) -> Tuple[Path, bytes, str]:
        """
        Save candidate CV file
        
        Returns:
            Tuple of (file_path, file_content, mime_type)
        """
        file_content, mime_type = await DocumentUploadMixin.validate_document(file)
        
        unique_filename = FileUploadMixin.generate_unique_filename(
            candidate_id, file.filename, "cv_"
        )
        
        file_path = await FileUploadMixin.save_file(
            file=file,
            upload_dir=upload_base_dir,
            filename=unique_filename
        )
        
        return file_path, file_content, mime_type

    @staticmethod
    async def save_company_document(
        file: UploadFile,
        company_id: UUID,
        upload_base_dir: str = "uploads/companies"
    ) -> Tuple[Path, bytes, str]:
        """
        Save company document file
        
        Returns:
            Tuple of (file_path, file_content, mime_type)
        """
        file_content, mime_type = await DocumentUploadMixin.validate_document(file)
        
        unique_filename = FileUploadMixin.generate_unique_filename(
            company_id, file.filename, "doc_"
        )
        
        file_path = await FileUploadMixin.save_file(
            file=file,
            upload_dir=upload_base_dir,
            filename=unique_filename
        )
        
        return file_path, file_content, mime_type


class ImageUploadMixin(FileUploadMixin):
    """Mixin for image upload"""
    
    @staticmethod
    async def validate_image(file: UploadFile) -> Tuple[bytes, str]:
        """Validate image file"""
        return await FileUploadMixin.validate_file(
            file=file,
            file_type='image',
            allowed_extensions=['.jpg', '.jpeg', '.png', '.gif']
        )
    
    @staticmethod
    async def save_profile_image(
        file: UploadFile,
        user_id: UUID,
        upload_base_dir: str = "uploads/profiles"
    ) -> Tuple[Path, bytes, str]:
        """Save profile image"""
        file_content, mime_type = await ImageUploadMixin.validate_image(file)
        
        unique_filename = FileUploadMixin.generate_unique_filename(
            user_id, file.filename, "profile_"
        )
        
        file_path = await FileUploadMixin.save_file(
            file=file,
            upload_dir=upload_base_dir,
            filename=unique_filename
        )
        
        return file_path, file_content, mime_type