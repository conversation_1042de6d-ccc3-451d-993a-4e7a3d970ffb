# backend/app/core/middleware.py
from fastapi import Request
from fastapi.responses import JSONResponse
import json
import logging
import redis
from app.core.config import settings
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)
redis_client = redis.from_url(settings.REDIS_URL)


async def json_error_handler(request: Request, call_next):
    """Handle JSON decode errors"""
    try:
        response = await call_next(request)
        return response
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error: {e}")
        return JSONResponse(
            status_code=400,
            content={
                "detail": "Invalid JSON format",
                "error": str(e),
                "hint": "Please check your JSON syntax"
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )

class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
    
    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        key = f"rate_limit:{client_ip}"
        
        current = redis_client.get(key)
        if current is None:
            redis_client.setex(key, self.period, 1)
        else:
            current_count = int(current)
            if current_count >= self.calls:
                return JSONResponse(
                    status_code=429,
                    content={"detail": "Rate limit exceeded"}
                )
            redis_client.incr(key)
        
        response = await call_next(request)
        return response

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response