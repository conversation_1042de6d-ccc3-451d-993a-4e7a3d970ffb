# app/core/monitoring.py
from azure.monitor.opentelemetry import configure_azure_monitor
from opentelemetry import trace, metrics
from opentelemetry.instrumentation.fastapi import Fast<PERSON>IInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
import logging
import time
from typing import Dict, Any
from functools import wraps
import asyncio
import psutil

logger = logging.getLogger(__name__)

class ProductionMonitoring:
    def __init__(self, app_insights_connection_string: str):
        # Configure Azure Monitor
        configure_azure_monitor(
            connection_string=app_insights_connection_string
        )
        
        # Get tracer and meter
        self.tracer = trace.get_tracer(__name__)
        self.meter = metrics.get_meter(__name__)
        
        # Create custom metrics
        self.setup_custom_metrics()
        
        # Setup logging
        self.setup_logging()
    
    def setup_custom_metrics(self):
        """Setup custom metrics for monitoring"""
        # Interview metrics
        self.interview_counter = self.meter.create_counter(
            name="mensetsu_interviews_total",
            description="Total number of interviews conducted",
            unit="1"
        )
        
        self.interview_duration_histogram = self.meter.create_histogram(
            name="mensetsu_interview_duration_seconds",
            description="Interview duration in seconds",
            unit="s"
        )
        
        self.avatar_synthesis_duration = self.meter.create_histogram(
            name="mensetsu_avatar_synthesis_duration_seconds",
            description="Avatar synthesis duration in seconds",
            unit="s"
        )
        
        # WebSocket metrics
        self.websocket_connections_gauge = self.meter.create_up_down_counter(
            name="mensetsu_websocket_connections_active",
            description="Number of active WebSocket connections",
            unit="1"
        )
        
        # AI service metrics
        self.ai_question_generation_duration = self.meter.create_histogram(
            name="mensetsu_ai_question_generation_duration_seconds",
            description="AI question generation duration in seconds",
            unit="s"
        )
        
        self.ai_response_analysis_duration = self.meter.create_histogram(
            name="mensetsu_ai_response_analysis_duration_seconds",
            description="AI response analysis duration in seconds",
            unit="s"
        )
    
    def setup_logging(self):
        """Setup structured logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Create specialized loggers
        self.performance_logger = logging.getLogger("performance")
        self.security_logger = logging.getLogger("security")
        self.business_logger = logging.getLogger("business")
    
    def instrument_fastapi(self, app):
        """Instrument FastAPI application"""
        FastAPIInstrumentor.instrument_app(app)
        SQLAlchemyInstrumentor().instrument()
        RedisInstrumentor().instrument()
    
    def track_interview_metrics(self, session_id: str, duration: float, questions_count: int):
        """Track interview completion metrics"""
        with self.tracer.start_as_current_span("interview_completed") as span:
            span.set_attributes({
                "session_id": session_id,
                "duration_seconds": duration,
                "questions_count": questions_count
            })
            
            # Record metrics
            self.interview_counter.add(1, {"status": "completed"})
            self.interview_duration_histogram.record(duration)
            
            # Business logging
            self.business_logger.info(
                f"Interview completed - Session: {session_id}, "
                f"Duration: {duration:.2f}s, Questions: {questions_count}"
            )
    
    def track_avatar_synthesis(self, text_length: int, synthesis_time: float, success: bool):
        """Track avatar synthesis performance"""
        with self.tracer.start_as_current_span("avatar_synthesis") as span:
            span.set_attributes({
                "text_length": text_length,
                "synthesis_time_seconds": synthesis_time,
                "success": success
            })
            
            # Record metrics
            self.avatar_synthesis_duration.record(synthesis_time)
            
            status = "success" if success else "failure"
            self.performance_logger.info(
                f"Avatar synthesis {status} - "
                f"Text length: {text_length}, Time: {synthesis_time:.2f}s"
            )
    
    def track_websocket_connection(self, session_id: str, action: str):
        """Track WebSocket connections"""
        if action == "connect":
            self.websocket_connections_gauge.add(1)
        elif action == "disconnect":
            self.websocket_connections_gauge.add(-1)
        
        self.performance_logger.info(f"WebSocket {action} - Session: {session_id}")
    
    def track_ai_performance(self, operation: str, duration: float, success: bool, context: Dict[str, Any] = None):
        """Track AI service performance"""
        with self.tracer.start_as_current_span(f"ai_{operation}") as span:
            span.set_attributes({
                "operation": operation,
                "duration_seconds": duration,
                "success": success,
                **(context or {})
            })
            
            # Record appropriate metrics
            if operation == "question_generation":
                self.ai_question_generation_duration.record(duration)
            elif operation == "response_analysis":
                self.ai_response_analysis_duration.record(duration)
            
            status = "success" if success else "failure"
            self.performance_logger.info(
                f"AI {operation} {status} - Duration: {duration:.2f}s"
            )
    
    def track_security_event(self, event_type: str, details: Dict[str, Any]):
        """Track security-related events"""
        with self.tracer.start_as_current_span("security_event") as span:
            span.set_attributes({
                "event_type": event_type,
                **details
            })
            
            self.security_logger.warning(
                f"Security event: {event_type} - Details: {details}"
            )

# Performance monitoring decorators
def monitor_performance(operation_name: str):
    """Decorator to monitor function performance"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration = time.time() - start_time
                
                # Log performance
                logger = logging.getLogger("performance")
                status = "success" if success else "failure"
                
                log_message = f"{operation_name} {status} - Duration: {duration:.2f}s"
                if error:
                    log_message += f" - Error: {error}"
                
                if success:
                    logger.info(log_message)
                else:
                    logger.error(log_message)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration = time.time() - start_time
                
                # Log performance
                logger = logging.getLogger("performance")
                status = "success" if success else "failure"
                
                log_message = f"{operation_name} {status} - Duration: {duration:.2f}s"
                if error:
                    log_message += f" - Error: {error}"
                
                if success:
                    logger.info(log_message)
                else:
                    logger.error(log_message)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# Health check endpoint với detailed monitoring
class HealthCheckMonitor:
    def __init__(self):
        self.dependencies = {
            "database": self._check_database,
            "redis": self._check_redis,
            "azure_speech": self._check_azure_speech,
            "azure_openai": self._check_azure_openai,
            "chroma": self._check_chroma
        }
    
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "checks": {},
            "performance": {}
        }
        
        # Check all dependencies
        for service_name, check_func in self.dependencies.items():
            try:
                start_time = time.time()
                check_result = await check_func()
                check_duration = time.time() - start_time
                
                health_status["checks"][service_name] = {
                    "status": "healthy" if check_result["healthy"] else "unhealthy",
                    "response_time_ms": round(check_duration * 1000, 2),
                    "details": check_result.get("details", {})
                }
                
                if not check_result["healthy"]:
                    health_status["status"] = "degraded"
                    
            except Exception as e:
                health_status["checks"][service_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health_status["status"] = "unhealthy"
        
        # Add performance metrics
        health_status["performance"] = await self._get_performance_metrics()
        
        return health_status
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        from app.models.base import engine
        
        try:
            start_time = time.time()
            with engine.connect() as conn:
                result = conn.execute("SELECT 1")
                query_time = time.time() - start_time
                
                return {
                    "healthy": True,
                    "details": {
                        "query_time_ms": round(query_time * 1000, 2),
                        "pool_size": engine.pool.size(),
                        "checked_out_connections": engine.pool.checkedout()
                    }
                }
        except Exception as e:
            return {
                "healthy": False,
                "details": {"error": str(e)}
            }
    
    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        import psutil
        
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage_percent": psutil.disk_usage("/").percent
        }
