# app/core/error_handling.py
import logging
import traceback
import time
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from fastapi import WebSocket
import json


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for better classification"""
    WEBSOCKET = "websocket"
    WEBRTC = "webrtc"
    VIDEO_PROCESSING = "video_processing"
    AUDIO_PROCESSING = "audio_processing"
    NETWORK = "network"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    EXTERNAL_SERVICE = "external_service"
    UNKNOWN = "unknown"


class ErrorCode(Enum):
    """Standardized error codes"""
    # WebSocket errors
    WS_CONNECTION_FAILED = "WS_001"
    WS_MESSAGE_INVALID = "WS_002"
    WS_RATE_LIMIT_EXCEEDED = "WS_003"
    WS_TIMEOUT = "WS_004"
    WS_DISCONNECT_UNEXPECTED = "WS_005"
    
    # WebRTC errors
    WEBRTC_OFFER_FAILED = "RTC_001"
    WEBRTC_ICE_FAILED = "RTC_002"
    WEBRTC_CONNECTION_FAILED = "RTC_003"
    WEBRTC_MEDIA_FAILED = "RTC_004"
    
    # Video processing errors
    VIDEO_RECORDING_FAILED = "VID_001"
    VIDEO_FRAME_INVALID = "VID_002"
    VIDEO_CODEC_ERROR = "VID_003"
    VIDEO_STORAGE_FAILED = "VID_004"
    VIDEO_QUALITY_DEGRADED = "VID_005"
    
    # Audio processing errors
    AUDIO_TTS_FAILED = "AUD_001"
    AUDIO_PROCESSING_FAILED = "AUD_002"
    
    # Network errors
    NETWORK_TIMEOUT = "NET_001"
    NETWORK_CONNECTION_LOST = "NET_002"
    NETWORK_BANDWIDTH_LOW = "NET_003"
    
    # Validation errors
    VALIDATION_MESSAGE_FORMAT = "VAL_001"
    VALIDATION_PARAMETER_MISSING = "VAL_002"
    VALIDATION_PARAMETER_INVALID = "VAL_003"
    
    # Resource errors
    RESOURCE_MEMORY_EXCEEDED = "RES_001"
    RESOURCE_DISK_FULL = "RES_002"
    RESOURCE_CPU_HIGH = "RES_003"
    
    # External service errors
    EXTERNAL_AZURE_SPEECH_FAILED = "EXT_001"
    EXTERNAL_DATABASE_FAILED = "EXT_002"
    
    # Generic errors
    INTERNAL_SERVER_ERROR = "GEN_001"
    CONFIGURATION_ERROR = "GEN_002"
    UNKNOWN_ERROR = "GEN_999"


@dataclass
class ErrorContext:
    """Context information for errors"""
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    operation: Optional[str] = None
    component: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorInfo:
    """Comprehensive error information"""
    code: ErrorCode
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    context: ErrorContext
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    stack_trace: Optional[str] = None
    recovery_suggestions: List[str] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3


class ErrorHandler:
    """Centralized error handling system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_counts: Dict[str, int] = {}
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        
        # User-friendly error messages
        self.user_messages = {
            ErrorCode.WS_CONNECTION_FAILED: "接続に問題が発生しました。ページを再読み込みしてください。",
            ErrorCode.WS_RATE_LIMIT_EXCEEDED: "操作が多すぎます。少し待ってから再試行してください。",
            ErrorCode.WEBRTC_CONNECTION_FAILED: "ビデオ通話の接続に失敗しました。ブラウザの設定を確認してください。",
            ErrorCode.VIDEO_RECORDING_FAILED: "録画に失敗しました。しばらく待ってから再試行してください。",
            ErrorCode.AUDIO_TTS_FAILED: "音声生成に失敗しました。ネットワーク接続を確認してください。",
            ErrorCode.NETWORK_TIMEOUT: "ネットワークタイムアウトが発生しました。接続を確認してください。",
            ErrorCode.VALIDATION_MESSAGE_FORMAT: "メッセージの形式が正しくありません。",
            ErrorCode.INTERNAL_SERVER_ERROR: "サーバーエラーが発生しました。しばらく待ってから再試行してください。",
        }
    
    def create_error(
        self,
        code: ErrorCode,
        message: str,
        context: Optional[ErrorContext] = None,
        exception: Optional[Exception] = None,
        severity: Optional[ErrorSeverity] = None,
        recovery_suggestions: Optional[List[str]] = None
    ) -> ErrorInfo:
        """Create a comprehensive error object"""
        
        # Determine category from error code
        category = self._get_category_from_code(code)
        
        # Determine severity if not provided
        if severity is None:
            severity = self._get_default_severity(code)
        
        # Get user-friendly message
        user_message = self.user_messages.get(code, "予期しないエラーが発生しました。")
        
        # Create error info
        error_info = ErrorInfo(
            code=code,
            category=category,
            severity=severity,
            message=message,
            user_message=user_message,
            context=context or ErrorContext(),
            stack_trace=traceback.format_exc() if exception else None,
            recovery_suggestions=recovery_suggestions or []
        )
        
        # Track error
        self._track_error(error_info)
        
        return error_info
    
    def _get_category_from_code(self, code: ErrorCode) -> ErrorCategory:
        """Determine error category from error code"""
        code_str = code.value
        if code_str.startswith("WS_"):
            return ErrorCategory.WEBSOCKET
        elif code_str.startswith("RTC_"):
            return ErrorCategory.WEBRTC
        elif code_str.startswith("VID_"):
            return ErrorCategory.VIDEO_PROCESSING
        elif code_str.startswith("AUD_"):
            return ErrorCategory.AUDIO_PROCESSING
        elif code_str.startswith("NET_"):
            return ErrorCategory.NETWORK
        elif code_str.startswith("VAL_"):
            return ErrorCategory.VALIDATION
        elif code_str.startswith("RES_"):
            return ErrorCategory.RESOURCE
        elif code_str.startswith("EXT_"):
            return ErrorCategory.EXTERNAL_SERVICE
        else:
            return ErrorCategory.UNKNOWN
    
    def _get_default_severity(self, code: ErrorCode) -> ErrorSeverity:
        """Get default severity for error code"""
        critical_codes = [
            ErrorCode.RESOURCE_MEMORY_EXCEEDED,
            ErrorCode.RESOURCE_DISK_FULL,
            ErrorCode.EXTERNAL_DATABASE_FAILED
        ]
        
        high_codes = [
            ErrorCode.WS_CONNECTION_FAILED,
            ErrorCode.WEBRTC_CONNECTION_FAILED,
            ErrorCode.VIDEO_RECORDING_FAILED,
            ErrorCode.INTERNAL_SERVER_ERROR
        ]
        
        if code in critical_codes:
            return ErrorSeverity.CRITICAL
        elif code in high_codes:
            return ErrorSeverity.HIGH
        elif code.value.endswith("_FAILED"):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _track_error(self, error_info: ErrorInfo):
        """Track error occurrence"""
        error_key = f"{error_info.code.value}_{error_info.context.session_id or 'global'}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Add to history
        self.error_history.append(error_info)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
        
        # Log error
        self._log_error(error_info)
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level"""
        log_data = {
            "error_code": error_info.code.value,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "session_id": error_info.context.session_id,
            "component": error_info.context.component,
            "operation": error_info.context.operation,
            "retry_count": error_info.retry_count,
            "additional_data": error_info.context.additional_data
        }
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"{error_info.message}", extra=log_data)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(f"{error_info.message}", extra=log_data)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"{error_info.message}", extra=log_data)
        else:
            self.logger.info(f"{error_info.message}", extra=log_data)
        
        # Log stack trace for high severity errors
        if error_info.stack_trace and error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.error(f"Stack trace for {error_info.code.value}: {error_info.stack_trace}")


    async def send_error_to_websocket(
        self,
        websocket: WebSocket,
        error_info: ErrorInfo,
        include_details: bool = False
    ) -> bool:
        """Send error information to WebSocket client"""
        try:
            error_response = {
                "type": "error",
                "error_code": error_info.code.value,
                "message": error_info.user_message,
                "timestamp": error_info.timestamp.isoformat(),
                "severity": error_info.severity.value,
                "category": error_info.category.value
            }

            # Include additional details for debugging if requested
            if include_details:
                error_response.update({
                    "technical_message": error_info.message,
                    "recovery_suggestions": error_info.recovery_suggestions,
                    "retry_count": error_info.retry_count,
                    "context": {
                        "session_id": error_info.context.session_id,
                        "operation": error_info.context.operation,
                        "component": error_info.context.component
                    }
                })

            await websocket.send_text(json.dumps(error_response))
            return True

        except Exception as e:
            self.logger.error(f"Failed to send error to WebSocket: {e}")
            return False

    def should_retry(self, error_info: ErrorInfo) -> bool:
        """Determine if an operation should be retried"""
        if error_info.retry_count >= error_info.max_retries:
            return False

        # Don't retry validation errors
        if error_info.category == ErrorCategory.VALIDATION:
            return False

        # Don't retry authentication errors
        if error_info.category == ErrorCategory.AUTHENTICATION:
            return False

        # Retry network and external service errors
        retryable_categories = [
            ErrorCategory.NETWORK,
            ErrorCategory.EXTERNAL_SERVICE,
            ErrorCategory.RESOURCE
        ]

        return error_info.category in retryable_categories

    def get_retry_delay(self, error_info: ErrorInfo) -> float:
        """Get retry delay in seconds with exponential backoff"""
        base_delay = 1.0
        max_delay = 30.0

        delay = base_delay * (2 ** error_info.retry_count)
        return min(delay, max_delay)

    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        total_errors = len(self.error_history)
        if total_errors == 0:
            return {"total_errors": 0}

        # Count by category
        category_counts = {}
        severity_counts = {}
        recent_errors = []

        # Get recent errors (last hour)
        one_hour_ago = datetime.now(timezone.utc).timestamp() - 3600

        for error in self.error_history:
            # Category counts
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1

            # Severity counts
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

            # Recent errors
            if error.timestamp.timestamp() > one_hour_ago:
                recent_errors.append({
                    "code": error.code.value,
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "timestamp": error.timestamp.isoformat(),
                    "session_id": error.context.session_id
                })

        return {
            "total_errors": total_errors,
            "recent_errors_count": len(recent_errors),
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "recent_errors": recent_errors[-10:],  # Last 10 recent errors
            "error_rate_per_hour": len(recent_errors)
        }


class WebSocketErrorHandler:
    """Specialized error handler for WebSocket operations"""

    def __init__(self, error_handler: ErrorHandler):
        self.error_handler = error_handler
        self.logger = logging.getLogger(f"{__name__}.websocket")

    async def handle_connection_error(
        self,
        websocket: WebSocket,
        session_id: str,
        exception: Exception,
        operation: str = "connection"
    ) -> ErrorInfo:
        """Handle WebSocket connection errors"""
        context = ErrorContext(
            session_id=session_id,
            operation=operation,
            component="websocket",
            additional_data={"exception_type": type(exception).__name__}
        )

        error_info = self.error_handler.create_error(
            code=ErrorCode.WS_CONNECTION_FAILED,
            message=f"WebSocket connection failed for session {session_id}: {str(exception)}",
            context=context,
            exception=exception,
            recovery_suggestions=[
                "Check network connection",
                "Refresh the page",
                "Clear browser cache"
            ]
        )

        await self.error_handler.send_error_to_websocket(websocket, error_info)
        return error_info

    async def handle_message_error(
        self,
        websocket: WebSocket,
        session_id: str,
        message_data: Any,
        exception: Exception,
        operation: str = "message_processing"
    ) -> ErrorInfo:
        """Handle message processing errors"""
        context = ErrorContext(
            session_id=session_id,
            operation=operation,
            component="message_handler",
            additional_data={
                "message_type": message_data.get("type") if isinstance(message_data, dict) else "unknown",
                "exception_type": type(exception).__name__
            }
        )

        # Determine appropriate error code
        if "json" in str(exception).lower() or "decode" in str(exception).lower():
            code = ErrorCode.VALIDATION_MESSAGE_FORMAT
        elif "timeout" in str(exception).lower():
            code = ErrorCode.WS_TIMEOUT
        else:
            code = ErrorCode.WS_MESSAGE_INVALID

        error_info = self.error_handler.create_error(
            code=code,
            message=f"Message processing failed for session {session_id}: {str(exception)}",
            context=context,
            exception=exception,
            recovery_suggestions=[
                "Check message format",
                "Verify required fields",
                "Reduce message frequency"
            ]
        )

        await self.error_handler.send_error_to_websocket(websocket, error_info)
        return error_info


# Global error handler instances
error_handler = ErrorHandler()
websocket_error_handler = WebSocketErrorHandler(error_handler)
