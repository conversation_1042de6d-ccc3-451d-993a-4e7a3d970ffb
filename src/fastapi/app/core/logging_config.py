# app/core/logging_config.py
import logging
import logging.handlers
import sys
import json
import os
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path
from enum import Enum


class LogLevel(Enum):
    """Log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(Enum):
    """Log format types"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    JSON = "json"


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging"""
    
    def __init__(self, format_type: LogFormat = LogFormat.DETAILED):
        self.format_type = format_type
        super().__init__()
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record"""
        if self.format_type == LogFormat.JSON:
            return self._format_json(record)
        elif self.format_type == LogFormat.DETAILED:
            return self._format_detailed(record)
        else:
            return self._format_simple(record)
    
    def _format_json(self, record: logging.LogRecord) -> str:
        """Format as JSON"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process
        }
        
        # Add extra fields if present
        if hasattr(record, 'session_id'):
            log_data['session_id'] = record.session_id
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        if hasattr(record, 'operation'):
            log_data['operation'] = record.operation
        if hasattr(record, 'component'):
            log_data['component'] = record.component
        if hasattr(record, 'error_code'):
            log_data['error_code'] = record.error_code
        if hasattr(record, 'category'):
            log_data['category'] = record.category
        if hasattr(record, 'severity'):
            log_data['severity'] = record.severity
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, ensure_ascii=False)
    
    def _format_detailed(self, record: logging.LogRecord) -> str:
        """Format with detailed information"""
        timestamp = datetime.fromtimestamp(record.created, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # Base format
        base_format = f"[{timestamp}] [{record.levelname:8}] [{record.name}] {record.getMessage()}"
        
        # Add context information
        context_parts = []
        if hasattr(record, 'session_id') and record.session_id:
            context_parts.append(f"session={record.session_id}")
        if hasattr(record, 'operation') and record.operation:
            context_parts.append(f"op={record.operation}")
        if hasattr(record, 'component') and record.component:
            context_parts.append(f"comp={record.component}")
        if hasattr(record, 'error_code') and record.error_code:
            context_parts.append(f"code={record.error_code}")
        
        if context_parts:
            base_format += f" [{', '.join(context_parts)}]"
        
        # Add location info for errors
        if record.levelno >= logging.ERROR:
            base_format += f" ({record.module}:{record.funcName}:{record.lineno})"
        
        # Add exception info if present
        if record.exc_info:
            base_format += f"\n{self.formatException(record.exc_info)}"
        
        return base_format
    
    def _format_simple(self, record: logging.LogRecord) -> str:
        """Simple format for console output"""
        timestamp = datetime.fromtimestamp(record.created, tz=timezone.utc).strftime('%H:%M:%S')
        return f"[{timestamp}] {record.levelname:8} | {record.getMessage()}"


class LoggingConfig:
    """Centralized logging configuration"""
    
    def __init__(
        self,
        log_level: LogLevel = LogLevel.INFO,
        log_format: LogFormat = LogFormat.DETAILED,
        log_dir: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        enable_rotation: bool = True,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        enable_structured_logging: bool = True
    ):
        self.log_level = log_level
        self.log_format = log_format
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.enable_console = enable_console
        self.enable_file = enable_file
        self.enable_rotation = enable_rotation
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_structured_logging = enable_structured_logging
        
        # Create log directory
        self.log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Clear existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set root level
        root_logger.setLevel(getattr(logging, self.log_level.value))
        
        # Create formatters
        if self.enable_structured_logging:
            console_formatter = StructuredFormatter(LogFormat.SIMPLE)
            file_formatter = StructuredFormatter(self.log_format)
        else:
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_formatter = console_formatter
        
        # Console handler
        if self.enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(getattr(logging, self.log_level.value))
            root_logger.addHandler(console_handler)
        
        # File handlers
        if self.enable_file:
            self._setup_file_handlers(file_formatter)
        
        # Setup specialized loggers
        self._setup_specialized_loggers()
    
    def _setup_file_handlers(self, formatter):
        """Setup file handlers for different log levels"""
        log_files = {
            'all': self.log_dir / 'application.log',
            'error': self.log_dir / 'error.log',
            'websocket': self.log_dir / 'websocket.log',
            'webrtc': self.log_dir / 'webrtc.log',
            'video': self.log_dir / 'video.log',
            'performance': self.log_dir / 'performance.log',
            'security': self.log_dir / 'security.log'
        }
        
        for log_type, log_file in log_files.items():
            if self.enable_rotation:
                handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=self.max_file_size,
                    backupCount=self.backup_count,
                    encoding='utf-8'
                )
            else:
                handler = logging.FileHandler(log_file, encoding='utf-8')
            
            handler.setFormatter(formatter)
            
            # Set appropriate log levels for different files
            if log_type == 'error':
                handler.setLevel(logging.ERROR)
                handler.addFilter(lambda record: record.levelno >= logging.ERROR)
            else:
                handler.setLevel(getattr(logging, self.log_level.value))
            
            # Add handler to appropriate logger
            if log_type == 'all':
                logging.getLogger().addHandler(handler)
            else:
                logging.getLogger(log_type).addHandler(handler)
    
    def _setup_specialized_loggers(self):
        """Setup specialized loggers for different components"""
        specialized_loggers = {
            'websocket': logging.getLogger('websocket'),
            'webrtc': logging.getLogger('webrtc'),
            'video': logging.getLogger('video'),
            'performance': logging.getLogger('performance'),
            'security': logging.getLogger('security'),
            'error_handler': logging.getLogger('error_handler')
        }
        
        for name, logger in specialized_loggers.items():
            logger.setLevel(getattr(logging, self.log_level.value))
            # Prevent propagation to avoid duplicate logs
            logger.propagate = False if self.enable_file else True
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the specified name"""
        return logging.getLogger(name)
    
    def add_context_filter(self, logger_name: str, context: Dict[str, Any]):
        """Add context filter to a logger"""
        logger = logging.getLogger(logger_name)
        
        class ContextFilter(logging.Filter):
            def filter(self, record):
                for key, value in context.items():
                    setattr(record, key, value)
                return True
        
        logger.addFilter(ContextFilter())
    
    def set_log_level(self, logger_name: str, level: LogLevel):
        """Set log level for a specific logger"""
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, level.value))


# Global logging configuration
def setup_logging(
    environment: str = "development",
    log_level: Optional[str] = None,
    log_dir: Optional[str] = None
) -> LoggingConfig:
    """Setup logging based on environment"""
    
    # Determine log level
    if log_level:
        level = LogLevel(log_level.upper())
    elif environment == "production":
        level = LogLevel.INFO
    elif environment == "development":
        level = LogLevel.DEBUG
    else:
        level = LogLevel.INFO
    
    # Determine log format
    log_format = LogFormat.JSON if environment == "production" else LogFormat.DETAILED
    
    # Setup logging config
    config = LoggingConfig(
        log_level=level,
        log_format=log_format,
        log_dir=log_dir or os.getenv("LOG_DIR", "logs"),
        enable_console=True,
        enable_file=True,
        enable_rotation=True,
        enable_structured_logging=True
    )
    
    return config


# Performance logging decorator
def log_performance(operation_name: str, logger_name: str = "performance"):
    """Decorator to log performance metrics"""
    def decorator(func):
        import functools
        import time
        import asyncio
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name)
            start_time = time.time()
            success = True
            error = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration = time.time() - start_time
                logger.info(
                    f"Performance: {operation_name}",
                    extra={
                        'operation': operation_name,
                        'duration_ms': round(duration * 1000, 2),
                        'success': success,
                        'error': error
                    }
                )
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name)
            start_time = time.time()
            success = True
            error = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                duration = time.time() - start_time
                logger.info(
                    f"Performance: {operation_name}",
                    extra={
                        'operation': operation_name,
                        'duration_ms': round(duration * 1000, 2),
                        'success': success,
                        'error': error
                    }
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
