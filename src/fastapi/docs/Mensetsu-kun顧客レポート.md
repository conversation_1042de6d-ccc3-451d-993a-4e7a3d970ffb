# Mensetsu-kun バックエンドシステムアーキテクチャ：顧客レポート

## システム概要

Mensetsu-kunは、3Dアバターを使用してAIによる自動面接システムです。このシステムは応募者とリアルタイムで対話し、回答を分析して、応募者のフィードバックに基づいて次の質問を調整するように設計されています。

# フロー: 会社セットアップAPI

このフローは、会社情報のセットアップから、関連ドキュメントの解析・ベクトルDBへの保存までの処理を示します。

```mermaid
sequenceDiagram
    participant クライアント as クライアント
    participant API as セットアップAPI
    participant クローラー as 会社情報クローラー
    participant パーサー as ドキュメントパーサー
    participant ベクトルDB as ベクトルデータベース

    クライアント->>API: 会社セットアップリクエスト送信（POST /setup-company）
    API->>クローラー: 会社情報を収集（Webサイト、プロフィールなど）
    クローラー-->>API: 収集した会社情報を返却（テキスト、メタデータ）

    API->>パーサー: PDFやWordなどの文書を解析・テキスト化
    パーサー-->>API: テキストデータやチャンクを返却

    API->>ベクトルDB: ベクトル埋め込みを保存
    ベクトルDB-->>API: 保存成功を返却

    API-->>クライアント: セットアップ完了レスポンス
```

# フロー: 応募者インタビュリンクセットアップAPI

このフローは、応募者の情報と応募ポジションに関するデータを処理し、ベクトルDBに保存するまでの流れを示します。

```mermaid
sequenceDiagram
    participant クライアント as クライアント
    participant API as インタビューリンクセットアップAPI
    participant 応募者パーサー as 応募者データパーサー
    participant ポジションパーサー as 応募ポジションパーサー
    participant ベクトルDB as ベクトルデータベース

    クライアント->>API: 応募者インタビューリンク作成リクエスト（POST /setup-interview-link）
    
    API->>応募者パーサー: 応募者の履歴書や資料（PDF, DOCX, TXT）を解析
    応募者パーサー-->>API: 応募者情報（テキスト、チャンク）

    API->>ベクトルDB: 応募者情報をベクトル保存
    ベクトルDB-->>API: 応募者情報の保存完了

    API->>ポジションパーサー: 応募ポジションに関する資料を解析
    ポジションパーサー-->>API: ポジション情報（テキスト、チャンク）

    API->>ベクトルDB: ポジション情報をベクトル保存
    ベクトルDB-->>API: ポジション情報の保存完了

    API-->>クライアント: セットアップ完了レスポンス（インタビューリンク含む）
```

## 全体アーキテクチャ

```mermaid
graph TD
    Client["フロントエンドクライアント"] <-->|WebSocketプロトコル| WebsocketAPI["WebSocket API"]
    Client <-->|HTTP REST| RestAPI["REST API"]
    
    WebsocketAPI -->|リアルタイム通信| InterviewFlow["面接フローサービス"]
    RestAPI --> Controllers["APIコントローラー"]
    
    InterviewFlow -->|テキスト分析| GeminiClassifier["Geminiメッセージ分類器"]
    InterviewFlow -->|コンテキスト検索| RAGService["RAGサービス"]
    InterviewFlow -->|質問生成| InterviewOrchestrator["面接オーケストレーター"]
    
    GeminiClassifier -->|埋め込み| GoogleAI["Google Generative AI API"]
    RAGService -->|ベクトル検索| ChromaDB["Chroma ベクトルデータベース"]
    InterviewOrchestrator -->|LLM処理| GoogleAI
    
    Controllers --> Database["PostgreSQLデータベース"]
    InterviewFlow --> SessionManager["セッションマネージャー"]
    SessionManager <-->|セッションデータ| RedisCache["Redisキャッシュ"]
    
    subgraph "バックエンドサービス"
        InterviewFlow
        GeminiClassifier
        RAGService
        InterviewOrchestrator
        SessionManager
    end
    
    subgraph "外部依存関係"
        GoogleAI
        ChromaDB
        RedisCache
        Database
    end
```

## 面接処理フロー

システムは以下の論理的プロセスに従って面接セッションを処理します：

```mermaid
sequenceDiagram
    participant Candidate as 応募者
    participant WebSocket as WebSocketサービス
    participant Flow as 面接フロー
    participant Classifier as メッセージ分類器
    participant RAG as RAGサービス
    participant Orchestrator as 面接オーケストレーター
    participant SessionMgr as セッションマネージャー

    Candidate->>WebSocket: WebSocket接続
    WebSocket->>SessionMgr: セッション作成
    SessionMgr-->>WebSocket: セッション確認
    WebSocket-->>Candidate: 接続成功
    
    Candidate->>WebSocket: メッセージ送信
    WebSocket->>Flow: 応募者メッセージ処理
    Flow->>SessionMgr: アクティブ時間更新
    Flow->>Classifier: メッセージ分析
    Classifier-->>Flow: 分析結果
    
    Flow->>RAG: 企業情報検索
    RAG-->>Flow: 企業コンテキスト情報
    
    Flow->>Orchestrator: 次の質問生成
    Orchestrator-->>Flow: 質問とフィードバック
    
    Flow->>SessionMgr: 会話履歴更新
    Flow-->>WebSocket: 面接応答
    WebSocket-->>Candidate: 次の質問+フィードバック
```

## Demo
<!-- link gif -->
![Mensetsu-kun Demo](demo-jp.gif)

## 主要コンポーネント

### 1. WebSocketハンドラー

応募者とのWebSocket接続を管理し、リアルタイム通信と接続イベントの処理を確保します。

**主な機能：**
- WebSocket接続の確立と維持
- 接続品質とハートビートの監視
- レート制限の制御
- 接続統計の提供

### 2. 面接フロー

面接プロセスを調整し、アプリケーションのメインロジックフローを処理します。

**主な機能：**
- 応募者からのメッセージ処理
- AI分析に基づく応募者プロファイルの更新
- 企業コンテキストを提供するRAGサービスとの統合
- 面接段階に適した次の質問の生成

### 3. メッセージ分類器

Geminiモデルを使用して応募者のメッセージを分析・分類します。

**主な機能：**
- メッセージタイプの識別（自己紹介、技術回答、行動など）
- 重要情報の抽出（スキル、経験、業界）
- 回答の明確さと適切さの評価
- パフォーマンス向上のためのRedisキャッシュの使用

### 4. 面接オーケストレーター

異なる段階を通して面接の進行を管理し、コンテキストに適した質問を生成します。

**主な機能：**
- 面接段階の定義と管理
- コンテキストに基づく次の質問の生成
- 次の段階に移行するタイミングの決定
- AIエラー時のフォールバックケースの処理

### 5. RAGサービス（Retrieval-Augmented Generation）

面接品質を高めるための企業コンテキスト情報を提供します。

**主な機能：**
- ベクトルデータベースでの企業データの保存と管理
- ドキュメントの埋め込み作成と管理
- 現在の面接コンテキストに基づく関連情報の検索
- 埋め込みAPIがクォータを超えた場合の適切な処理

### 6. セッションマネージャー

面接セッションのデータを管理します。

**主な機能：**
- 面接セッションの作成と管理
- Redisでのセッションデータの保存と更新
- セッションデータの一貫性と可用性の確保

## 面接段階

システムは6つの主要段階で面接を実施します：

```mermaid
graph LR
    Introduction["1. 自己紹介"] --> Experience["2. 経験"]
    Experience --> Behavioral["3. 行動"]
    Behavioral --> Technical["4. 技術"]
    Technical --> CompanyFit["5. 企業適合性"]
    CompanyFit --> WrapUp["6. 終了"]
    WrapUp --> Completed["完了"]
    
    style Introduction fill:#c3e6cb
    style Experience fill:#d4edda
    style Behavioral fill:#d1ecf1
    style Technical fill:#bee5eb
    style CompanyFit fill:#ffeeba
    style WrapUp fill:#f8d7da
    style Completed fill:#d6d8db
```

各段階の特徴：
1. **自己紹介**: アイスブレイク、応募者の基本情報
2. **経験**: 職歴、実績
3. **行動**: ソフトスキル、チームワーク、リーダーシップ
4. **技術**: 技術スキル、問題解決能力
5. **企業適合性**: 文化的適合性、モチベーション
6. **終了**: 最終意見、応募者からの質問

## APIエンドポイント

### REST API

| エンドポイント | メソッド | 説明 |
|----------|------------|-------|
| `/api/interviews/join` | POST | 面接への参加 |
| `/api/interviews/{interview_id}/start` | POST | 面接の開始 |
| `/api/interviews/{interview_id}/feedback` | GET | 面接フィードバックの取得 |
| `/api/companies/...` | Various | 企業情報の管理 |
| `/api/analytics/...` | Various | 分析データの取得 |

### WebSocket

| イベント | 方向 | 説明 |
|---------|-------|-------|
| `connect` | Client -> Server | WebSocket接続の確立 |
| `candidate_message` | Client -> Server | 応募者からのメッセージ |
| `interview_question` | Server -> Client | 次の面接質問 |
| `processing_status` | Server -> Client | 処理状況 |
| `system_message` | Server -> Client | システムメッセージ |

## データベースとキャッシング

### PostgreSQL

固定的・長期的データの保存：
- 企業情報
- 面接セッション
- 応募者プロファイル
- 分析データ

### ChromaDB

ベクトルデータベースの保存：
- 企業ドキュメントの埋め込み
- RAGのコンテキスト情報
- 企業ごとの個別コレクション

### Redis

キャッシュストレージ：
- 面接セッションデータ
- メッセージ分類結果
- 面接中の一時データ

## エラー処理と耐障害性

システムは高い耐障害性で設計されています：
- 埋め込みAPIがクォータを超えた場合の適切な処理
- Google APIにアクセスできない場合のデフォルト埋め込みの使用
- 接続損失を検出するハートビートメカニズム
- サービス乱用を防ぐレート制限
- セッションリカバリーメカニズム

## スケーラビリティ

### 水平スケーリング

システムは以下の方法で水平にスケールできます：
- 複数のバックエンドインスタンスのデプロイ
- ロードバランサーを通じたWebSocketの負荷分散
- AIサービスとメッセージ処理の分離

### 垂直スケーリング

垂直スケーリング能力：
- 埋め込み処理の最適化
- リソースを多く必要とするAIサービスのハードウェアアップグレード
- キャッシュメカニズムの改善

## 結論

Mensetsu-kunバックエンドシステムは、強力かつ柔軟な自動面接ソリューションを提供します：
- モジュール化されたアーキテクチャにより、保守と拡張が容易
- 面接コンテンツの分析と生成のための先進的なAI統合
- WebSocketを通じたリアルタイム処理
- 複数のフォールバックメカニズムによる高い耐障害性
- さまざまな面接要件に簡単にカスタマイズ可能

このシステムは初期面接プロセスを大幅に改善し、採用担当者の時間とリソースを節約しながら、応募者に一貫した体験を提供します。 