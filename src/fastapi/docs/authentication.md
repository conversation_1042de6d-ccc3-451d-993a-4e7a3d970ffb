# Mensetsu-kun Authentication System

## Overview

The Mensetsu-kun authentication system is designed to authenticate agents and interviewers using JWT tokens with a refresh token mechanism.

## Architecture

### Models
- **User**: Stores basic information (email, password, etc.)
- **Agent**: Extends User with role-specific information (1-1 relationship)
- **AgentRole**: Enum defining roles: `agent`, `interviewer`

### Relationships
```sql
users (1) ←→ (1) agents
```

### JWT Token Structure
```json
{
  "sub": "user_id",
  "agent_id": "agent_id", 
  "email": "<EMAIL>",
  "role": "agent|interviewer",
  "exp": "expiration_timestamp"
}
```

## API Endpoints - Detailed Usage Guide

### 1. Login

This endpoint authenticates a user and provides access and refresh tokens.

**Endpoint:** `POST /api/auth/login`

**Request Format:**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response Format:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "token_type": "bearer",
  "expires_in": 86400
}
```

**Step-by-step Process:**
1. The system receives email and password credentials
2. Authenticates the user against the database
3. If authentication succeeds:
   - Updates the user's last login timestamp
   - Creates a JWT access token containing user information
   - Generates a refresh token and stores it in Redis
   - Returns both tokens to the client
4. If authentication fails:
   - Returns a 401 Unauthorized error

**Usage Notes:**
- The access token is valid for 24 hours (86400 seconds)
- Store both tokens securely in your application
- Use the access token for all authenticated API requests
- Keep the refresh token for obtaining a new access token when needed

### 2. Refresh Token

This endpoint allows getting a new access token using a refresh token when the access token expires.

**Endpoint:** `POST /api/auth/refresh`

**Request Format:**
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refresh_token": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
}
```

**Response Format:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "f7g8h9i0-j1k2-3456-lmno-pq7890123456",
  "token_type": "bearer",
  "expires_in": 86400
}
```

**Step-by-step Process:**
1. The system verifies the provided refresh token against Redis storage
2. If the token is valid:
   - Retrieves the associated user from the database
   - Revokes the old refresh token (one-time use only)
   - Generates a new access token
   - Creates a new refresh token
   - Returns both new tokens to the client
3. If the token is invalid or expired:
   - Returns a 401 Unauthorized error

**Usage Notes:**
- Each refresh token can only be used once
- After using a refresh token, you'll receive a new refresh token
- Implement token refresh logic in your application to handle token expiration
- Typical implementation: when an API call returns 401, try refreshing the token and retry the request

### 3. Get Current Profile

This endpoint retrieves the profile information of the currently authenticated user.

**Endpoint:** `GET /api/auth/me`

**Request Format:**
```http
GET /api/auth/me
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response Format:**
```json
{
  "id": "2337e591-8168-49b8-bc53-a1f11dd30808",
  "email": "<EMAIL>",
  "role": "agent",
  "profile_name": "佐藤 健太",
  "profile_organization": "キャリアサポート株式会社",
  "created_at": "2025-06-17T23:57:01.940Z",
  "updated_at": "2025-06-18T00:18:41.756Z",
  "last_login_at": "2025-06-18T00:18:41.756Z",
  "is_active": true
}
```

**Step-by-step Process:**
1. The system extracts and validates the JWT from the Authorization header
2. If the token is valid:
   - Retrieves the user and associated agent information
   - Returns the profile data
3. If the token is invalid or expired:
   - Returns a 401 Unauthorized error

**Usage Notes:**
- Always include the access token in the Authorization header
- Use this endpoint to verify authentication status or get user information
- The format must be exactly: `Bearer {token}` with a space after "Bearer"

### 4. Logout

This endpoint invalidates a specific refresh token, effectively logging out from one device or session.

**Endpoint:** `POST /api/auth/logout`

**Request Format:**
```http
POST /api/auth/logout
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
  "refresh_token": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
}
```

**Response Format:**
```json
{
  "message": "Successfully logged out"
}
```

**Step-by-step Process:**
1. The system validates the access token in the Authorization header
2. If the token is valid:
   - Revokes the provided refresh token in Redis
   - Returns a success message
3. If the refresh token is invalid:
   - Returns a 400 Bad Request error
4. If the access token is invalid:
   - Returns a 401 Unauthorized error

**Usage Notes:**
- This endpoint requires both the access token and refresh token
- Use this when implementing a "Logout" feature in your application
- The access token will still be technically valid until it expires, but without a valid refresh token, the session cannot be extended

### 5. Logout All Devices

This endpoint invalidates all refresh tokens for the current user, effectively logging out from all devices and sessions.

**Endpoint:** `POST /api/auth/logout-all`

**Request Format:**
```http
POST /api/auth/logout-all
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response Format:**
```json
{
  "message": "Successfully logged out from all devices",
  "detail": "Revoked 3 tokens"
}
```

**Step-by-step Process:**
1. The system validates the access token in the Authorization header
2. If the token is valid:
   - Revokes all refresh tokens associated with the user in Redis
   - Returns a success message with the count of revoked tokens
3. If the access token is invalid:
   - Returns a 401 Unauthorized error

**Usage Notes:**
- Use this for implementing a "Logout from all devices" security feature
- This is useful when a user suspects unauthorized access to their account
- After calling this endpoint, all sessions will need to log in again with username and password

## Authentication Flow Implementation Guide

### Complete Authentication Flow

Here's a step-by-step guide to implementing the full authentication flow in your application:

1. **Initial Login:**
   ```javascript
   // Example using fetch API
   async function login(email, password) {
     const response = await fetch('/api/auth/login', {
       method: 'POST',
       headers: {
         'Content-Type': 'application/json'
       },
       body: JSON.stringify({ email, password })
     });
     
     if (!response.ok) {
       throw new Error('Login failed');
     }
     
     const tokenData = await response.json();
     
     // Store tokens securely
     localStorage.setItem('access_token', tokenData.access_token);
     localStorage.setItem('refresh_token', tokenData.refresh_token);
     localStorage.setItem('token_expiry', Date.now() + (tokenData.expires_in * 1000));
     
     return tokenData;
   }
   ```

2. **Making Authenticated API Calls:**
   ```javascript
   async function fetchUserProfile() {
     const accessToken = localStorage.getItem('access_token');
     
     const response = await fetch('/api/auth/me', {
       method: 'GET',
       headers: {
         'Authorization': `Bearer ${accessToken}`
       }
     });
     
     if (response.status === 401) {
       // Token expired, try to refresh
       await refreshAccessToken();
       return fetchUserProfile(); // Retry with new token
     }
     
     if (!response.ok) {
       throw new Error('Failed to fetch profile');
     }
     
     return response.json();
   }
   ```

3. **Refreshing Tokens:**
   ```javascript
   async function refreshAccessToken() {
     const refreshToken = localStorage.getItem('refresh_token');
     
     try {
       const response = await fetch('/api/auth/refresh', {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json'
         },
         body: JSON.stringify({ refresh_token: refreshToken })
       });
       
       if (!response.ok) {
         // If refresh fails, redirect to login
         localStorage.clear();
         window.location.href = '/login';
         throw new Error('Token refresh failed');
       }
       
       const tokenData = await response.json();
       
       // Update stored tokens
       localStorage.setItem('access_token', tokenData.access_token);
       localStorage.setItem('refresh_token', tokenData.refresh_token);
       localStorage.setItem('token_expiry', Date.now() + (tokenData.expires_in * 1000));
       
       return tokenData;
     } catch (error) {
       // If any error occurs during refresh, redirect to login
       localStorage.clear();
       window.location.href = '/login';
       throw error;
     }
   }
   ```

4. **Implementing Logout:**
   ```javascript
   async function logout() {
     const accessToken = localStorage.getItem('access_token');
     const refreshToken = localStorage.getItem('refresh_token');
     
     try {
       const response = await fetch('/api/auth/logout', {
         method: 'POST',
         headers: {
           'Authorization': `Bearer ${accessToken}`,
           'Content-Type': 'application/json'
         },
         body: JSON.stringify({ refresh_token: refreshToken })
       });
       
       // Clear local storage regardless of response
       localStorage.clear();
       
       return response.ok;
     } catch (error) {
       // Clear local storage even if request fails
       localStorage.clear();
       throw error;
     }
   }
   ```

5. **Logout from All Devices:**
   ```javascript
   async function logoutAllDevices() {
     const accessToken = localStorage.getItem('access_token');
     
     try {
       const response = await fetch('/api/auth/logout-all', {
         method: 'POST',
         headers: {
           'Authorization': `Bearer ${accessToken}`
         }
       });
       
       // Clear local storage regardless of response
       localStorage.clear();
       
       return response.ok;
     } catch (error) {
       // Clear local storage even if request fails
       localStorage.clear();
       throw error;
     }
   }
   ```

### Token Management Best Practices

1. **Secure Storage:**
   - In browser applications, consider using `sessionStorage` instead of `localStorage` for better security
   - For mobile applications, use secure storage options provided by the platform
   - Never store tokens in cookies without proper security flags

2. **Token Expiration Handling:**
   - Implement proactive token refresh before expiration
   - Example: Refresh when token is 5 minutes from expiring

   ```javascript
   function isTokenExpiringSoon() {
     const expiry = localStorage.getItem('token_expiry');
     const fiveMinutes = 5 * 60 * 1000;
     
     return expiry - Date.now() < fiveMinutes;
   }
   
   async function getValidAccessToken() {
     if (isTokenExpiringSoon()) {
       await refreshAccessToken();
     }
     return localStorage.getItem('access_token');
   }
   ```

3. **Error Handling:**
   - Implement consistent handling for authentication errors
   - Redirect to login page when authentication cannot be recovered

4. **Security Considerations:**
   - Implement HTTPS for all API communications
   - Consider adding fingerprinting to tokens for additional security
   - Implement rate limiting for authentication endpoints

## Security Features

### Password Hashing
- Uses bcrypt with automatic salt rounds
- Passwords are never stored as plain text

### JWT Security
- Access tokens have a short lifespan (24 hours)
- Refresh tokens are stored in Redis with a TTL of 7 days
- Secret key is configured from environment variables

### Role-based Access Control
```python
# Require specific role
@router.get("/agent-only")
async def agent_only_endpoint(
    agent: Agent = Depends(require_agent_role)
):
    return {"message": "Only agents can access this"}

@router.get("/interviewer-only") 
async def interviewer_only_endpoint(
    agent: Agent = Depends(require_interviewer_role)
):
    return {"message": "Only interviewers can access this"}
```

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Agents Table
```sql
CREATE TYPE agentrole AS ENUM ('agent', 'interviewer');

CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    role agentrole NOT NULL,
    profile_name VARCHAR(255) NOT NULL,
    profile_organization VARCHAR(255),
    last_login_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Setup and Deployment

### Development Setup
```bash
cd src/fastapi
python scripts/setup_auth.py
```

### Docker Setup
```bash
cd src/fastapi
python scripts/docker_setup_auth.py
```

### Test Accounts
The system automatically creates 2 test accounts:

1. **Agent Account**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `agent`
   - Name: `佐藤 健太`

2. **Interviewer Account**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `interviewer`
   - Name: `鈴木 雅子`

## Testing

### Run Tests
```bash
cd src/fastapi
pytest tests/test_auth.py -v
```

### Test Coverage
- Login success/failure scenarios
- Token refresh mechanisms
- Profile retrieval
- Logout operations
- Role-based access control
- Password hashing/verification
- JWT token creation/validation

## Environment Variables

Required environment variables:

```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/mensetsu_db

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Optional: AI Services
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
```

## Error Handling

The system implements clean error handling:

- **401 Unauthorized**: Invalid credentials or expired tokens
- **403 Forbidden**: Insufficient permissions (role-based)
- **400 Bad Request**: Invalid request format
- **500 Internal Server Error**: Server-side errors

All errors return a structured JSON response with clear message and detail. 