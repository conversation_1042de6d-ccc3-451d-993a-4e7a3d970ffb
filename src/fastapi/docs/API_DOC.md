# API Documentation - Mensetsu-kun
## Tài Liệu API Cho Tester

---

## 📋 Tổng Quan

Tài liệu này mô tả chi tiết các API endpoints của hệ thống Mensetsu-kun, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho **tester** để thực hiện kiểm thử API một cách hiệu quả và toàn diện.

### 🎯 Thông Tin Cơ Bản

- **Base URL**: `http://localhost:8000/api`
- **Content-Type**: `application/json` (ngoại trừ upload file)
- **Authentication**: Bearer <PERSON>ken (JWT)
- **API Version**: 1.0.0

### 🔧 Environment Setup Cho Test

```bash
# Local Development
BASE_URL=http://localhost:8080/api

# Authentication Header (sau khi login)
Authorization: Bearer {access_token}
```

---

## 🗂️ API Groups Overview

| Group | Prefix | Mô <PERSON> | Số Endpoints |
|-------|--------|--------|--------------|
| **Authentication** | `/auth` | Đăng nhập, refresh token | 5 |
| **Companies** | `/companies` | Quản lý công ty và tài liệu | 4 |
| **Candidates** | `/candidates` | Quản lý ứng viên | 3 |
| **Interviews** | `/interviews` | Quản lý phỏng vấn | 3 |
| **Health** | `/health` | Kiểm tra hệ thống | 2 |
| **Analytics** | `/analytics` | Thống kê và báo cáo | 3 |
| **WebSocket** | `/ws` | Kết nối real-time | 1 |

---

## 🔐 Authentication APIs

### 1. Agent Login

**Endpoint**: `POST /auth/login`

**Mô tả**: Đăng nhập Agent (HR Hunter) vào hệ thống

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "test123"
}
```

**Response Success (200)**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86400
}
```

**Response Error (401)**:
```json
{
  "detail": "Incorrect email or password"
}
```

**Test Cases**:
- ✅ Valid credentials → Success 200
- ❌ Invalid email → Error 401  
- ❌ Invalid password → Error 401
- ❌ Missing fields → Error 422
- ❌ Invalid JSON format → Error 422

### 2. Refresh Token

**Endpoint**: `POST /auth/refresh`

**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response Success (200)**:
```json
{
  "access_token": "new_access_token...",
  "refresh_token": "new_refresh_token...",
  "expires_in": 86400
}
```

**Test Cases**:
- ✅ Valid refresh token → New tokens
- ❌ Expired refresh token → Error 401
- ❌ Invalid refresh token → Error 401

### 3. Get Current Profile

**Endpoint**: `GET /auth/me`

**Headers**: `Authorization: Bearer {access_token}`

**Response Success (200)**:
```json
{
  "id": "uuid-string",
  "email": "<EMAIL>",
  "role": "agent",
  "profile_name": "Agent Test",
  "profile_organization": "Test Company",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "last_login_at": "2024-01-01T00:00:00",
  "is_active": true
}
```

**Test Cases**:
- ✅ Valid token → Profile data
- ❌ Missing token → Error 401
- ❌ Invalid token → Error 401
- ❌ Expired token → Error 401

### 4. Logout

**Endpoint**: `POST /auth/logout`

**Request Body**:
```json
{
  "refresh_token": "refresh_token_here"
}
```

**Response Success (200)**:
```json
{
  "message": "Successfully logged out"
}
```

### 5. Logout All Devices

**Endpoint**: `POST /auth/logout-all`

**Headers**: `Authorization: Bearer {access_token}`

**Response Success (200)**:
```json
{
  "message": "Successfully logged out from all devices",
  "detail": "Revoked 3 tokens"
}
```

---

## 🏢 Companies APIs

### 1. Create Company

**Endpoint**: `POST /companies`

**Headers**: `Authorization: Bearer {access_token}`

**Request Body**:
```json
{
  "name": "Test Company Ltd",
  "industry": "Technology",
  "description": "A leading tech company",
  "core_values": ["Innovation", "Quality", "Teamwork"],
  "vision_mission": "To be the best tech company in Japan"
}
```

**Response Success (201)**:
```json
{
  "id": "company-uuid",
  "name": "Test Company Ltd",
  "industry": "Technology", 
  "description": "A leading tech company",
  "core_values": ["Innovation", "Quality", "Teamwork"],
  "vision_mission": "To be the best tech company in Japan",
  "created_at": "2024-01-01T00:00:00"
}
```

**Test Cases**:
- ✅ Valid company data → Created 201
- ❌ Missing required fields → Error 422
- ❌ Invalid data types → Error 422
- ❌ Unauthorized → Error 401

### 2. Get Company Details

**Endpoint**: `GET /companies/{company_id}`

**Headers**: `Authorization: Bearer {access_token}`

**Response Success (200)**:
```json
{
  "id": "company-uuid",
  "name": "Test Company Ltd",
  "industry": "Technology",
  "description": "A leading tech company",
  "core_values": ["Innovation", "Quality", "Teamwork"],
  "vision_mission": "To be the best tech company in Japan",
  "created_at": "2024-01-01T00:00:00",
  "ai_summary": "AI generated summary of company",
  "key_insights": ["Insight 1", "Insight 2"]
}
```

**Test Cases**:
- ✅ Valid company_id → Company data with AI analysis
- ❌ Non-existent company_id → Error 404
- ❌ Invalid UUID format → Error 422

### 3. Upload Company Documents

**Endpoint**: `POST /companies/{company_id}/documents`

**Headers**: `Authorization: Bearer {access_token}`

**Content-Type**: `multipart/form-data`

**Request Body**: 
```
file: [PDF/DOC/DOCX file]
```

**Response Success (200)**:
```json
{
  "id": "document-uuid",
  "file_name": "company_profile.pdf",
  "file_size": 1024000,
  "processed": true,
  "extracted_text_length": 5000,
  "rag_synced": true
}
```

**Test Cases**:
- ✅ Valid PDF file → Document uploaded and processed
- ✅ Valid DOC file → Document uploaded
- ✅ Valid DOCX file → Document uploaded
- ❌ Invalid file format (txt, jpg) → Error 400
- ❌ No file provided → Error 400
- ❌ File too large → Error 413
- ❌ Non-existent company → Error 404

### 4. Get Company Documents

**Endpoint**: `GET /companies/{company_id}/documents`

**Query Parameters**:
- `include_ai_analysis`: boolean (default: true)

**Response Success (200)**:
```json
{
  "company_id": "company-uuid",
  "company_name": "Test Company Ltd",
  "total_documents": 2,
  "documents": [
    {
      "id": "doc-uuid-1",
      "file_name": "company_profile.pdf",
      "document_type": "pdf",
      "file_size": 1024000,
      "upload_date": "2024-01-01T00:00:00",
      "processed": true,
      "company_id": "company-uuid"
    }
  ],
  "ai_analysis": {
    "documents_summary": "Summary of all documents",
    "key_topics": ["Topic 1", "Topic 2"],
    "document_insights": [],
    "total_chunks_analyzed": 15
  }
}
```

---

## 👥 Candidates APIs

### 1. Register Candidate

**Endpoint**: `POST /candidates/register`

**Content-Type**: `multipart/form-data`

**Request Body**:
```
candidate_name_kana: "田中太郎"
candidate_age: 30
candidate_employment_status: "employed"
candidate_email: "<EMAIL>"
candidate_has_resume: true
candidate_has_career_history: true
candidate_company: "Current Company"
candidate_position: "Software Engineer"
candidate_address: "Tokyo, Japan"
cv_file: [PDF file] (optional)
```

**Response Success (201)**:
```json
{
  "id": "candidate-uuid",
  "name_kana": "田中太郎",
  "age": 30,
  "employment_status": "employed",
  "email": "<EMAIL>",
  "company": "Current Company",
  "position": "Software Engineer",
  "created_at": "2024-01-01T00:00:00"
}
```

**Test Cases**:
- ✅ Valid candidate data → Created 201
- ✅ With CV file → Created with document
- ❌ Duplicate email → Error 400
- ❌ Missing required fields → Error 422
- ❌ Invalid age (negative) → Error 422
- ❌ Invalid employment status → Error 422

### 2. List Candidates

**Endpoint**: `GET /candidates`

**Query Parameters**:
- `skip`: int (default: 0)
- `limit`: int (default: 100, max: 1000)

**Response Success (200)**:
```json
[
  {
    "id": "candidate-uuid-1",
    "name_kana": "田中太郎",
    "age": 30,
    "employment_status": "employed",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00"
  }
]
```

**Test Cases**:
- ✅ Default parameters → List of candidates
- ✅ Custom skip/limit → Paginated results
- ❌ Invalid limit (>1000) → Error 422
- ❌ Negative skip → Error 422

### 3. Get Candidate Details

**Endpoint**: `GET /candidates/{user_id}`

**Response Success (200)**:
```json
{
  "id": "candidate-uuid",
  "userId": "user-uuid",
  "nameKana": "田中太郎",
  "age": 30,
  "employment_status": "employed",
  "company": "Current Company",
  "position": "Software Engineer",
  "documents": [
    {
      "id": "doc-uuid",
      "fileName": "resume.pdf",
      "fileSize": 500000,
      "documentType": "cv"
    }
  ],
  "ragSummary": {
    "user_id": "user-uuid",
    "status": "success",
    "personal_information": {},
    "skills_and_experience": {},
    "education_and_language": {},
    "summary_text": "Candidate summary"
  }
}
```

**Test Cases**:
- ✅ Valid user_id → Complete candidate details
- ✅ Candidate with documents → Details + documents
- ❌ Non-existent user_id → Error 404
- ❌ Invalid UUID format → Error 422

---

## 💼 Interviews APIs

### 1. Join Interview

**Endpoint**: `POST /interviews/join`

**Request Body**:
```json
{
  "interview_url": "https://interview.mensetsu-kun.com/abc123",
  "interview_id": "interview-uuid"
}
```

**Response Success (200)**:
```json
{
  "id": "interview-uuid",
  "company_id": "company-uuid",
  "candidate_name": "田中太郎",
  "status": "joined",
  "created_at": "2024-01-01T00:00:00"
}
```

**Test Cases**:
- ✅ Valid interview URL → Status changed to "joined"
- ❌ Invalid interview URL → Error 404
- ❌ Already started interview → Error 400

### 2. Start Interview

**Endpoint**: `POST /interviews/{interview_id}/start`

**Response Success (200)**:
```json
{
  "id": "interview-uuid",
  "company_id": "company-uuid", 
  "candidate_name": "田中太郎",
  "status": "started",
  "created_at": "2024-01-01T00:00:00"
}
```

**Test Cases**:
- ✅ Joined interview → Status changed to "started"
- ❌ Not joined interview → Error 400
- ❌ Non-existent interview → Error 404

### 3. Get Interview Feedback

**Endpoint**: `GET /interviews/{interview_id}/feedback`

**Response Success (200)**:
```json
{
  "session_id": "interview-uuid",
  "feedback": {
    "overall_assessment": "Good performance",
    "key_points_evaluation": {
      "point_1": "Excellent",
      "point_2": "Good", 
      "point_3": "Needs improvement"
    },
    "suggestions": ["Improve technical skills", "Practice communication"]
  },
  "overall_score": 85,
  "status": "completed"
}
```

**Test Cases**:
- ✅ Completed interview → Feedback data
- ❌ Incomplete interview → Error 400
- ❌ Non-existent interview → Error 404

---

## 🏥 Health Check APIs

### 1. Basic Health Check

**Endpoint**: `GET /health/health`

**Response Success (200)**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "websocket": {
      "active_connections": 5,
      "status": "healthy"
    }
  }
}
```

**Response Degraded (200)**:
```json
{
  "status": "degraded",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": "unhealthy: Connection timeout",
    "redis": "healthy",
    "websocket": {
      "active_connections": 0,
      "status": "healthy"
    }
  }
}
```

**Test Cases**:
- ✅ All services healthy → Status "healthy"
- ⚠️ Some services down → Status "degraded"
- ❌ Critical services down → Status "unhealthy"

### 2. System Metrics

**Endpoint**: `GET /health/metrics`

**Response Success (200)**:
```json
{
  "active_websocket_connections": 5,
  "connection_metadata": {
    "session-1": {
      "connected_duration": 300.5,
      "message_count": 25
    },
    "session-2": {
      "connected_duration": 150.2,
      "message_count": 10
    }
  }
}
```

---

## 🔌 WebSocket API

### Interview WebSocket Connection

**Endpoint**: `WS /ws/interview/{session_id}`

**Connection URL**: `ws://localhost:8000/ws/interview/{session_id}`

**Message Types**:

**1. Start Interview**
```json
{
  "type": "start_interview",
  "data": {
    "candidate_id": "candidate-uuid",
    "company_id": "company-uuid"
  }
}
```

**2. Audio Chunk (from candidate)**
```json
{
  "type": "audio_chunk",
  "data": {
    "audio_data": "base64_encoded_audio",
    "timestamp": "2024-01-01T00:00:00"
  }
}
```

**3. Question (from AI)**
```json
{
  "type": "question",
  "data": {
    "question_id": "question-uuid",
    "question_text": "Tell me about yourself",
    "key_point": "Personal Introduction",
    "expected_duration": 120
  }
}
```

**4. Answer Analysis (to candidate)**
```json
{
  "type": "answer_feedback",
  "data": {
    "question_id": "question-uuid",
    "transcribed_text": "I am a software engineer...",
    "feedback": "Good response, clear communication",
    "score": 8.5,
    "suggestions": ["Add more specific examples"]
  }
}
```

**Test Cases**:
- ✅ Valid session_id → Connection established
- ✅ Send audio chunks → Transcription received
- ✅ Complete interview → Final feedback
- ❌ Invalid session_id → Connection rejected
- ❌ Malformed messages → Error response

---

## 🧪 Testing Scenarios

### 1. Complete Authentication Flow

```bash
# 1. Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "test123"}'

# 2. Use access token for protected endpoints
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer {access_token}"

# 3. Refresh token when expired
curl -X POST http://localhost:8000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "{refresh_token}"}'
```

### 2. Company Management Flow

```bash
# 1. Create company
curl -X POST http://localhost:8000/api/companies \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Company",
    "industry": "Technology",
    "description": "A test company"
  }'

# 2. Upload company document
curl -X POST http://localhost:8000/api/companies/{company_id}/documents \
  -H "Authorization: Bearer {token}" \
  -F "file=@company_profile.pdf"

# 3. Get company with documents
curl -X GET http://localhost:8000/api/companies/{company_id}/documents \
  -H "Authorization: Bearer {token}"
```

### 3. Candidate Registration Flow

```bash
# Register new candidate
curl -X POST http://localhost:8000/api/candidates/register \
  -F "candidate_name_kana=田中太郎" \
  -F "candidate_age=30" \
  -F "candidate_employment_status=employed" \
  -F "candidate_email=<EMAIL>" \
  -F "candidate_has_resume=true" \
  -F "candidate_has_career_history=true" \
  -F "cv_file=@resume.pdf"
```

---

## ⚠️ Common Error Codes

| Status Code | Meaning | Common Causes |
|-------------|---------|---------------|
| **400** | Bad Request | Invalid input data, business logic violation |
| **401** | Unauthorized | Missing/invalid/expired token |
| **403** | Forbidden | Insufficient permissions |
| **404** | Not Found | Resource doesn't exist |
| **422** | Validation Error | JSON schema validation failed |
| **500** | Internal Error | Server-side error, database issues |

### Standard Error Response Format

```json
{
  "detail": "Error message description",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format",
      "type": "value_error"
    }
  ]
}
```

---

## 📝 Test Data Examples

### Valid Test Agent Account
```json
{
  "email": "<EMAIL>",
  "password": "test123",
  "role": "agent"
}
```

### Sample Company Data
```json
{
  "name": "テスト株式会社",
  "industry": "Technology",
  "description": "A leading technology company",
  "core_values": ["Innovation", "Quality", "Customer Focus"],
  "vision_mission": "To create innovative solutions for better tomorrow"
}
```

### Sample Candidate Data
```json
{
  "candidate_name_kana": "田中太郎",
  "candidate_age": 28,
  "candidate_employment_status": "employed",
  "candidate_email": "<EMAIL>",
  "candidate_has_resume": true,
  "candidate_has_career_history": true,
  "candidate_company": "現在の会社",
  "candidate_position": "ソフトウェアエンジニア"
}
```

---

## 🚀 Performance Testing

### Load Testing Endpoints

**High Priority**:
- `POST /auth/login` - 100 concurrent users
- `GET /companies/{id}` - 50 concurrent users  
- `POST /candidates/register` - 20 concurrent users
- `WebSocket /ws/interview/{session_id}` - 10 concurrent connections

**Response Time Targets**:
- Authentication: < 500ms
- CRUD operations: < 1000ms
- File upload: < 5000ms
- WebSocket messages: < 100ms

---

## 📊 Monitoring và Debugging

### Debug Headers
```bash
# Enable detailed error logging
X-Debug-Mode: true

# Request tracing
X-Request-ID: unique-request-id
```

### Health Check Schedule
- `/health/health` - Every 30 seconds
- `/health/metrics` - Every 5 minutes

---
