# Mensetsu-kun Backend Documentation

## Overview

Mensetsu-kun is an AI interview tool with 3D avatar support. The backend is built with FastAPI and provides APIs for managing interview sessions between AI interviewers and candidates. The system uses RAG (Retrieval Augmented Generation) to provide context-aware interviews based on company information.

## Project Structure

```
backend/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   ├── ai.py
│   │   │   ├── analytics.py
│   │   │   ├── companies.py
│   │   │   ├── health.py
│   │   │   ├── interviews.py
│   │   │   └── websocket.py
│   │   └── api.py
│   ├── core/
│   │   ├── config.py
│   │   ├── middleware.py
│   │   └── auth.py
│   ├── models/
│   │   ├── base.py
│   │   ├── company.py
│   │   ├── interview.py
│   │   ├── user.py
│   │   ├── __init__.py
│   │   └── schemas/
│   │       ├── company.py
│   │       ├── interview.py
│   │       └── user.py
│   ├── services/
│   │   ├── ai_service.py
│   │   ├── analytics_service.py
│   │   ├── azure_speech_service.py
│   │   ├── feedback_service.py
│   │   ├── interview_flow_service.py
│   │   ├── rag_service.py
│   │   ├── report_service.py
│   │   ├── response_analysis_service.py
│   │   ├── webrtc_service.py
│   │   └── websocket_service.py
│   └── main.py
├── data/
│   └── chroma/
├── alembic/
├── Dockerfile
├── requirements.txt
└── alembic.ini
```

## Dependencies

```
fastapi              # API framework
uvicorn              # ASGI server
websockets           # WebSocket support
sqlalchemy           # ORM
alembic              # Database migrations
psycopg2-binary      # PostgreSQL driver
redis                # Redis client
langchain            # LLM framework
langchain-openai     # OpenAI integration
chromadb             # Vector database
openai               # OpenAI API
pydantic             # Data validation
python-multipart     # Multipart form handling
python-jose          # JWT tokens
passlib              # Password hashing
pydantic_settings    # Settings management
langchain-google-genai # Google Generative AI integration
langchain-community  # Community integrations
pytest               # Testing framework
httpx                # HTTP client
aiohttp              # Async HTTP client
psutil               # System utilities
pandas               # Data analysis
numpy                # Numerical processing
azure-cognitiveservices-speech # Azure Speech services
seaborn              # Statistical data visualization
```

## Environment Configuration

The application uses the following environment variables:

```python
# Project information
PROJECT_NAME: str = "Mensetsu-kun"
VERSION: str = "1.0.0"
API_V1_STR: str = "/api/v1"

# Database with connection pooling
DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/mensetsu_db"
DATABASE_POOL_SIZE: int = 20
DATABASE_MAX_OVERFLOW: int = 0
DATABASE_POOL_TIMEOUT: int = 30
DATABASE_POOL_RECYCLE: int = 3600

# Redis
REDIS_URL: str = "redis://localhost:6379"

# OpenAI
OPENAI_API_KEY: str
OPENAI_MODEL: str = "gpt-4"

# Chroma
CHROMA_URL: str = "http://chroma:8000"

# Security
SECRET_KEY: str = "your-secret-key-here"
ALGORITHM: str = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

# Google
GOOGLE_API_KEY: str

# Environment
ENVIRONMENT: str = "development"
```

## Database Models

### Base Model

The base model sets up the database connection and session management:

```python
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    echo=settings.ENVIRONMENT == "development"
)

SessionLocal = sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=engine,
    expire_on_commit=False
)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### Company Model

```python
class Company(Base):
    __tablename__ = "companies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    industry = Column(String(100))
    description = Column(Text)
    core_values = Column(JSON)
    vision_mission = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    interviews = relationship(
        "InterviewSession", 
        back_populates="company", 
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    @classmethod
    def create_company(cls, db: Session, company_data):
        """Create new company"""
        db_company = cls(
            name=company_data.name,
            industry=company_data.industry,
            description=company_data.description,
            core_values=company_data.core_values,
            vision_mission=company_data.vision_mission
        )
        db.add(db_company)
        db.commit()
        db.refresh(db_company)
        return db_company
    
    @classmethod
    def get_company_by_id(cls, db: Session, company_id: uuid.UUID):
        return db.query(cls).filter(cls.id == company_id).first()
    
    @classmethod
    def get_all_companies(cls, db: Session, skip: int = 0, limit: int = 100):
        return db.query(cls).offset(skip).limit(limit).all()
```

### Interview Session Model

```python
class InterviewSession(Base):
    __tablename__ = "interview_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = Column(UUID(as_uuid=True), ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    candidate_name = Column(String(255), nullable=False)
    candidate_profile = Column(JSON, nullable=False, default=dict)
    status = Column(String(50), default="created", index=True)
    transcript = Column(JSON, default=list)
    feedback = Column(JSON, default=list)
    overall_score = Column(Integer, default=0)
    interview_url = Column(String(255), unique=True, index=True, nullable=False)
    session_token = Column(String(255), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    company = relationship("Company", back_populates="interviews")
    
    def __repr__(self):
        return f"<InterviewSession(id={self.id}, candidate={self.candidate_name}, status={self.status})>"
    
    @classmethod
    def create_interview(cls, db: Session, interview_data, company_id: UUID):
        """Create new interview"""
        db_interview = cls(
            company_id=company_id,
            candidate_name=interview_data.candidate_name,
            candidate_profile= {
                "name": interview_data.candidate_name,
                "email": "<EMAIL>",
                "phone": "0909090909",
                "resume": "resume.pdf",
                "cover_letter": "cover_letter.pdf",
            },
            status="created",
            interview_url=f"interview/{cls.generate_interview_url()}",
            session_token=cls.generate_session_token(),
            expires_at=datetime.utcnow() + timedelta(hours=1),
            transcript=[],
            feedback=[],
            overall_score=0,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(db_interview)
        db.commit()
        db.refresh(db_interview)
        return db_interview
    
    @staticmethod
    def generate_interview_url() -> str:
        """Generate unique interview URL"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(16))
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate unique session token"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))
```

## API Endpoints

### Companies API

- `POST /api/companies/` - Create a new company
  - Creates company in database
  - Sets up RAG with company data
  - Returns company details with generated ID

- `GET /api/companies/{company_id}` - Get company details by ID
  - Returns detailed company information

- `POST /api/companies/{company_id}/link-interview` - Create a new interview for a company
  - Creates an interview session with a unique URL
  - Associates the interview with the company
  - Returns interview details including URL for candidate access

### Interviews API

- `POST /api/interviews/join` - Join an interview session
  - Validates interview URL
  - Updates interview status to "joined"
  - Returns interview session details

- `POST /api/interviews/{interview_id}/start` - Start an interview session
  - Updates interview status to "started"
  - Returns updated interview session details

- `GET /api/interviews/{interview_id}/feedback` - Get interview feedback
  - Returns generated feedback and scores

- `PUT /api/interviews/{session_id}/complete` - Complete an interview
  - Generates final feedback using AI Service
  - Updates interview status to "completed"
  - Returns feedback summary

### AI API

- `POST /api/ai/generate-question` - Generate interview questions
  - Uses RAG to provide company-specific context
  - Returns tailored interview questions

- `POST /api/ai/analyze-response` - Analyze candidate responses
  - Evaluates answers across multiple dimensions
  - Returns scores and analysis

### Analytics API

- `GET /api/analytics/dashboard` - Get dashboard analytics
  - Returns aggregated interview data and metrics
  
- `GET /api/analytics/reports` - Generate performance reports
  - Creates detailed analysis of interview performance

### Health API

- `GET /health` - Health check endpoint
  - Returns service status and version information

### WebSocket API

- `WebSocket /ws/interview/{session_id}` - Real-time interview communication
  - Handles bidirectional communication during interview
  - Processes different message types: start_interview, candidate_response, next_question, end_interview

## Services

### RAG Service

The RAG (Retrieval Augmented Generation) service manages company context storage and retrieval:

```python
class RAGService:
    def __init__(self):
        # Initialize with Google Embeddings with fallback
        self.use_google_embeddings = True
        
        try:
            self.embeddings = GoogleGenerativeAIEmbeddings(
                google_api_key=settings.GOOGLE_API_KEY,
                model="models/gemini-embedding-exp-03-07",
                task_type="RETRIEVAL_DOCUMENT"
            )
        except Exception:
            # Fallback to default ChromaDB embeddings
            self.use_google_embeddings = False
            self.embeddings = embedding_functions.DefaultEmbeddingFunction()
        
        self.client = chromadb.HttpClient(
            host="chroma",
            port=8000
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100
        )
    
    async def create_company_collection(self, company_id: str):
        """Create a dedicated collection for each company"""
        collection_name = f"company_{company_id}"
        try:
            collection = self.client.get_or_create_collection(
                name=collection_name,
                metadata={"company_id": company_id}
            )
            return collection
        except Exception:
            return None
    
    async def load_company_data(self, company_id: str, company_data: Dict):
        """Load and index company data into vector database"""
        # Implementation details for storing company context
        # Includes description, core values, and vision/mission
        # Handles documents chunking and embedding generation
        # Has fallback mechanisms for API failures
    
    async def search_company_context(self, company_id: str, query: str, k: int = 3):
        """Search for relevant context from company data"""
        # Retrieves context for interview questions
```

### AI Service

The AI service handles the interview intelligence:

```python
class AIService:
    def __init__(self):
        # Primary LLM - Google Gemini
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            google_api_key=settings.GOOGLE_API_KEY,
        )
        
        self.rag_service = RAGService()
        self.conversation_history: List[Dict] = []
        self.max_conversation_pairs = 10
        self.setup_prompts()
    
    def add_conversation_exchange(self, question: str, answer: str):
        """Add a Q&A exchange to the conversation history"""
        self.conversation_history.append({
            "question": question,
            "answer": answer,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Keep conversation history within limits
        if len(self.conversation_history) > self.max_conversation_pairs:
            self.conversation_history = self.conversation_history[-self.max_conversation_pairs:]
    
    def get_conversation_context(self, last_n: int = 3) -> str:
        """Get formatted conversation history for context"""
        if not self.conversation_history:
            return "No previous conversation."
        
        recent_conversations = self.conversation_history[-last_n:]
        context_parts = []
        
        for i, exchange in enumerate(recent_conversations, 1):
            context_parts.append(f"Exchange {i}:")
            context_parts.append(f"Q: {exchange['question']}")
            context_parts.append(f"A: {exchange['answer']}")
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    async def generate_interview_question(
        self, 
        company_id: str, 
        candidate_profile: Dict, 
        interview_stage: str,
        session_id: str,
        conversation_history: List[Dict] = None
    ) -> Dict:
        """Generate context-aware interview questions with retry logic"""
        # Uses RAG to retrieve relevant company context
        # Constructs prompt with context and conversation history
        # Returns a tailored interview question
    
    async def analyze_response(
        self, 
        question: str, 
        answer: str, 
        company_context: List[str]
    ) -> Dict:
        """Analyze candidate responses"""
        # Evaluates answers across multiple dimensions
        # Returns scores and analysis
    
    async def generate_final_feedback(self, transcript: List[Dict] = None) -> Dict:
        """Generate comprehensive interview feedback"""
        # Analyzes full interview transcript
        # Returns structured feedback with scores and recommendations
```

### WebSocket Service

Manages real-time communication for the interview process:

```python
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.ai_service = AIService()
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """Establish WebSocket connection"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
    
    def disconnect(self, session_id: str):
        """Handle disconnection"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
    
    async def handle_interview_flow(self, session_id: str, data: dict):
        """Process interview flow based on message type"""
        message_type = data.get("type")
        
        if message_type == "start_interview":
            await self.start_interview(session_id, data)
        elif message_type == "candidate_response":
            await self.process_candidate_response(session_id, data)
        elif message_type == "next_question":
            await self.generate_next_question(session_id, data)
        elif message_type == "end_interview":
            await self.end_interview(session_id, data)
    
    # Implementation of each interview flow handler
```

### Azure Speech Service

Handles speech-to-text and text-to-speech for natural interaction:

```python
class AzureSpeechService:
    """Azure Cognitive Services Speech integration for STT and TTS"""
    
    def __init__(self):
        # Initialize Azure Speech SDK
        self.speech_key = settings.AZURE_SPEECH_KEY
        self.speech_region = settings.AZURE_SPEECH_REGION
        
    async def speech_to_text(self, audio_data: bytes) -> str:
        """Convert audio to text using Azure's speech recognition"""
        # Implementation for converting candidate speech to text
        
    async def text_to_speech(self, text: str, voice: str = "en-US-JennyNeural") -> bytes:
        """Convert text to speech using Azure's neural voices"""
        # Implementation for converting AI responses to speech
```

### Analytics Service

Provides data analysis and insights:

```python
class AnalyticsService:
    """Analytics for interview data and performance metrics"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_dashboard_metrics(self) -> Dict:
        """Get aggregated metrics for dashboard"""
        # Implementation for dashboard analytics
        
    async def generate_performance_report(self, company_id: str = None) -> Dict:
        """Generate detailed performance reports"""
        # Implementation for performance reporting
        
    async def analyze_trends(self, timeframe: str = "week") -> Dict:
        """Analyze trends in interview data"""
        # Implementation for trend analysis
```

## Main Application

The main application is configured in `main.py`:

```python
app = FastAPI(
    title="Mensetsu-kun API",
    description="AI Interview Tool with Avatar 3D",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api")

# Include WebSocket router
app.include_router(websocket_router)

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Mensetsu-kun API is running"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "service": "mensetsu-kun-api"
    }

# Custom error handling
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors = []
    for error in exc.errors():
        if error['type'] == 'json_invalid':
            errors.append({
                "field": "request_body",
                "message": "Invalid JSON format. Please check your JSON syntax.",
                "details": error.get('ctx', {}).get('error', 'JSON decode error')
            })
        else:
            errors.append({
                "field": " -> ".join(str(x) for x in error['loc']),
                "message": error['msg'],
                "type": error['type']
            })
    
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation error",
            "errors": errors
        }
    )
```

## Interview Flow

1. Company creates an account and provides company information
2. Company creates an interview session, generating a unique interview URL
3. Candidate accesses the interview URL and joins the session
4. Client connects to the WebSocket endpoint with the session ID
5. Client sends a "start_interview" message
6. Server generates the first question using RAG and the AI service
7. Client sends candidate responses
8. Server analyzes responses and generates follow-up questions
9. Process continues until interview completion
10. Final feedback is generated with strengths, areas for improvement, and scores

## Advanced Features

### Speech Recognition and Synthesis
- Integration with Azure Cognitive Services for speech-to-text and text-to-speech
- Enables natural conversation flow with AI interviewer

### WebRTC Support
- Real-time audio/video communication
- Support for 3D avatar rendering
- Handles media stream processing

### Analytics Dashboard
- Comprehensive interview metrics
- Performance trends and insights
- Comparative analysis across candidates

### Response Analysis
- Multi-dimensional scoring of candidate responses
- Real-time feedback generation
- Context-aware evaluation based on company values

## Deployment

The application is containerized using Docker:

```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## Database Migrations

Database migrations are managed using Alembic:

```
alembic init alembic
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

## Testing

The backend includes comprehensive test coverage:

- Unit tests for individual services and utilities
- Integration tests for API endpoints
- End-to-end tests for complete workflows

Tests are implemented using pytest and can be run with:

```
pytest app/tests/
```

## Security

The application implements several security measures:

- JWT-based authentication
- Password hashing with bcrypt
- Session expiry management
- Environment-based configuration
- Input validation with Pydantic

## Scalability Considerations

The system is designed with scalability in mind:

- Connection pooling for database access
- Stateless API design with session token authentication
- Redis for distributed caching
- Containerized deployment for horizontal scaling 