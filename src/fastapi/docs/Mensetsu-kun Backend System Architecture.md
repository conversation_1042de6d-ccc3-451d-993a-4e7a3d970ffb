# Mensetsu-kun Backend System Architecture

## Tổng quan hệ thống

Mensetsu-kun là một hệ thống phỏng vấn tự động sử dụng AI để mô phỏng quy trình phỏng vấn với một avatar 3D. Hệ thống được thiết kế để tương tác thời gian thực với ứng viên, phân tích câu trả lời và điều chỉnh các câu hỏi tiếp theo dựa trên phản hồi của ứng viên.

## Kiến trúc tổng thể

```mermaid
graph TD
    Client["Frontend Client"] <-->|WebSocket Protocol| WebsocketAPI["WebSocket API"]
    Client <-->|HTTP REST| RestAPI["REST API"]
    
    WebsocketAPI -->|Real-time communication| InterviewFlow["Interview Flow Service"]
    RestAPI --> Controllers["API Controllers"]
    
    InterviewFlow -->|Text Analysis| GeminiClassifier["Gemini Message Classifier"]
    InterviewFlow -->|Context Retrieval| RAGService["RAG Service"]
    InterviewFlow -->|Question Generation| InterviewOrchestrator["Interview Orchestrator"]
    
    GeminiClassifier -->|Embeddings| GoogleAI["Google Generative AI API"]
    RAGService -->|Vector Search| ChromaDB["Chroma Vector Database"]
    InterviewOrchestrator -->|LLM Processing| GoogleAI
    
    Controllers --> Database["PostgreSQL Database"]
    InterviewFlow --> SessionManager["Session Manager"]
    SessionManager <-->|Session Data| RedisCache["Redis Cache"]
    
    subgraph "Backend Services"
        InterviewFlow
        GeminiClassifier
        RAGService
        InterviewOrchestrator
        SessionManager
    end
    
    subgraph "External Dependencies"
        GoogleAI
        ChromaDB
        RedisCache
        Database
    end
```

## Luồng xử lý phỏng vấn

Hệ thống xử lý một buổi phỏng vấn theo quy trình logic sau:

```mermaid
sequenceDiagram
    participant Candidate as Ứng viên
    participant WebSocket as WebSocket Service
    participant Flow as Interview Flow
    participant Classifier as Message Classifier
    participant RAG as RAG Service
    participant Orchestrator as Interview Orchestrator
    participant SessionMgr as Session Manager

    Candidate->>WebSocket: Kết nối WebSocket
    WebSocket->>SessionMgr: Tạo phiên làm việc
    SessionMgr-->>WebSocket: Xác nhận phiên
    WebSocket-->>Candidate: Kết nối thành công
    
    Candidate->>WebSocket: Gửi tin nhắn
    WebSocket->>Flow: Xử lý tin nhắn ứng viên
    Flow->>SessionMgr: Cập nhật thời gian hoạt động
    Flow->>Classifier: Phân tích tin nhắn
    Classifier-->>Flow: Kết quả phân tích
    
    Flow->>RAG: Truy xuất thông tin công ty
    RAG-->>Flow: Thông tin ngữ cảnh công ty
    
    Flow->>Orchestrator: Tạo câu hỏi tiếp theo
    Orchestrator-->>Flow: Câu hỏi và phản hồi
    
    Flow->>SessionMgr: Cập nhật lịch sử hội thoại
    Flow-->>WebSocket: Phản hồi phỏng vấn
    WebSocket-->>Candidate: Câu hỏi tiếp theo + phản hồi
```

## Các thành phần chính

### 1. WebSocket Handler

Quản lý kết nối WebSocket với ứng viên, đảm bảo giao tiếp thời gian thực và xử lý các sự kiện kết nối.

**Chức năng chính:**
- Thiết lập và duy trì kết nối WebSocket
- Giám sát chất lượng kết nối và heartbeat
- Kiểm soát giới hạn tốc độ (rate limiting)
- Cung cấp thống kê kết nối

### 2. Interview Flow

Điều phối quy trình phỏng vấn và xử lý luồng logic chính của ứng dụng.

**Chức năng chính:**
- Xử lý tin nhắn từ ứng viên
- Cập nhật hồ sơ ứng viên dựa trên phân tích AI
- Tích hợp với các dịch vụ RAG để cung cấp ngữ cảnh công ty
- Tạo các câu hỏi tiếp theo phù hợp với giai đoạn phỏng vấn

### 3. Message Classifier

Sử dụng mô hình Gemini để phân tích và phân loại tin nhắn của ứng viên.

**Chức năng chính:**
- Xác định loại tin nhắn (giới thiệu, câu trả lời kỹ thuật, hành vi...)
- Trích xuất các thông tin quan trọng (kỹ năng, kinh nghiệm, ngành công nghiệp)
- Đánh giá mức độ rõ ràng và phù hợp của câu trả lời
- Sử dụng cache Redis để cải thiện hiệu suất

### 4. Interview Orchestrator

Quản lý tiến trình phỏng vấn qua các giai đoạn khác nhau và tạo các câu hỏi phù hợp với ngữ cảnh.

**Chức năng chính:**
- Định nghĩa và quản lý các giai đoạn phỏng vấn
- Tạo câu hỏi tiếp theo dựa trên ngữ cảnh
- Quyết định khi nào nên chuyển sang giai đoạn tiếp theo
- Xử lý các trường hợp dự phòng khi AI gặp lỗi

### 5. RAG Service (Retrieval-Augmented Generation)

Cung cấp thông tin ngữ cảnh về công ty để tăng cường chất lượng phỏng vấn.

**Chức năng chính:**
- Lưu trữ và quản lý dữ liệu công ty trong cơ sở dữ liệu vector
- Tạo và quản lý embeddings cho tài liệu
- Truy xuất thông tin phù hợp dựa trên ngữ cảnh phỏng vấn hiện tại
- Xử lý gracefully khi API embeddings vượt quá quota

### 6. Session Manager

Quản lý dữ liệu phiên làm việc cho các buổi phỏng vấn.

**Chức năng chính:**
- Tạo và quản lý phiên phỏng vấn
- Lưu trữ và cập nhật dữ liệu phiên trong Redis
- Đảm bảo tính nhất quán và khả dụng của dữ liệu phiên

## Các giai đoạn phỏng vấn

Hệ thống thực hiện phỏng vấn qua 6 giai đoạn chính:

```mermaid
graph LR
    Introduction["1. Giới thiệu"] --> Experience["2. Kinh nghiệm"]
    Experience --> Behavioral["3. Hành vi"]
    Behavioral --> Technical["4. Kỹ thuật"]
    Technical --> CompanyFit["5. Phù hợp công ty"]
    CompanyFit --> WrapUp["6. Kết thúc"]
    WrapUp --> Completed["Hoàn thành"]
    
    style Introduction fill:#c3e6cb
    style Experience fill:#d4edda
    style Behavioral fill:#d1ecf1
    style Technical fill:#bee5eb
    style CompanyFit fill:#ffeeba
    style WrapUp fill:#f8d7da
    style Completed fill:#d6d8db
```

Mỗi giai đoạn có các đặc điểm riêng:
1. **Giới thiệu**: Làm quen, thông tin cơ bản về ứng viên
2. **Kinh nghiệm**: Kinh nghiệm làm việc, thành tích
3. **Hành vi**: Kỹ năng mềm, làm việc nhóm, lãnh đạo
4. **Kỹ thuật**: Kỹ năng kỹ thuật, giải quyết vấn đề
5. **Phù hợp công ty**: Sự phù hợp văn hóa, động lực
6. **Kết thúc**: Ý kiến cuối cùng, câu hỏi của ứng viên

## Các API Endpoint

### REST API

| Endpoint | Phương thức | Mô tả |
|----------|------------|-------|
| `/api/interviews/join` | POST | Tham gia buổi phỏng vấn |
| `/api/interviews/{interview_id}/start` | POST | Bắt đầu buổi phỏng vấn |
| `/api/interviews/{interview_id}/feedback` | GET | Lấy phản hồi buổi phỏng vấn |
| `/api/companies/...` | Various | Quản lý thông tin công ty |
| `/api/analytics/...` | Various | Truy xuất dữ liệu phân tích |

### WebSocket

| Sự kiện | Hướng | Mô tả |
|---------|-------|-------|
| `connect` | Client -> Server | Thiết lập kết nối WebSocket |
| `candidate_message` | Client -> Server | Tin nhắn từ ứng viên |
| `interview_question` | Server -> Client | Câu hỏi phỏng vấn tiếp theo |
| `processing_status` | Server -> Client | Trạng thái xử lý |
| `system_message` | Server -> Client | Thông báo hệ thống |

## Cơ sở dữ liệu và bộ nhớ đệm

### PostgreSQL

Lưu trữ dữ liệu cố định và dài hạn:
- Thông tin công ty
- Phiên phỏng vấn
- Hồ sơ ứng viên
- Dữ liệu phân tích

### ChromaDB

Cơ sở dữ liệu vector lưu trữ:
- Embeddings của tài liệu công ty
- Thông tin ngữ cảnh cho RAG
- Collection riêng biệt cho mỗi công ty

### Redis

Bộ nhớ đệm lưu trữ:
- Dữ liệu phiên phỏng vấn
- Kết quả phân loại tin nhắn
- Dữ liệu tạm thời trong quá trình phỏng vấn

## Xử lý lỗi và khả năng chịu lỗi

Hệ thống được thiết kế với khả năng chịu lỗi cao:
- Xử lý gracefully khi API embeddings vượt quá quota
- Sử dụng embeddings mặc định khi không thể truy cập Google API
- Cơ chế heartbeat để phát hiện kết nối bị mất
- Rate limiting để tránh lạm dụng dịch vụ
- Cơ chế phục hồi phiên làm việc

## Mở rộng và khả năng mở rộng

### Mở rộng theo chiều ngang

Hệ thống có thể mở rộng theo chiều ngang bằng cách:
- Triển khai nhiều instance backend
- Cân bằng tải WebSocket thông qua load balancer
- Tách biệt các dịch vụ AI và xử lý tin nhắn

### Mở rộng theo chiều dọc

Khả năng mở rộng theo chiều dọc:
- Tối ưu hóa xử lý embeddings
- Nâng cấp phần cứng cho các dịch vụ AI đòi hỏi nhiều tài nguyên
- Cải thiện cơ chế cache

## Kết luận

Hệ thống backend Mensetsu-kun cung cấp một giải pháp phỏng vấn tự động mạnh mẽ và linh hoạt:
- Kiến trúc module hóa cho phép dễ dàng bảo trì và mở rộng
- Tích hợp AI tiên tiến để phân tích và tạo nội dung phỏng vấn
- Xử lý thời gian thực thông qua WebSocket
- Khả năng chịu lỗi cao với nhiều cơ chế dự phòng
- Dễ dàng tùy chỉnh cho các yêu cầu phỏng vấn khác nhau

Hệ thống này có thể cải thiện đáng kể quy trình phỏng vấn ban đầu, giúp tiết kiệm thời gian và nguồn lực cho các nhà tuyển dụng, đồng thời cung cấp trải nghiệm nhất quán cho ứng viên. 