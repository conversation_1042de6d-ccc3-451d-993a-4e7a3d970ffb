# A generic, single database configuration.

[alembic]
script_location = alembic
prepend_sys_path = .

# timezone =
# truncate_slug_length = 40

[alembic.ext]
sourceless = false

# version_locations = %(here)s/bar:%(here)s/bat:%(here)s/alembic/versions

# path_separator = os
# path_separator = :
# path_separator = ;
# path_separator = space
# path_separator = newline

path_separator = os

output_encoding = utf-8

sqlalchemy.url = postgresql://postgres:password@localhost:5432/mensetsu_db


[post_write_hooks]
# hooks = black
# black.type = console_scripts
# black.entrypoint = black
# black.options = -l 79 REVISION_SCRIPT_FILENAME

# hooks = ruff
# ruff.type = exec
# ruff.executable = %(here)s/.venv/bin/ruff
# ruff.options = check --fix REVISION_SCRIPT_FILENAME

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARNING
handlers = console
qualname =

[logger_sqlalchemy]
level = WARNING
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
