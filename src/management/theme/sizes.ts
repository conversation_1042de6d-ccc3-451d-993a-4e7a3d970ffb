/**
 * サイズ定義
 * コンポーネントの幅、高さ、その他のサイズ値
 */

export const sizes = {
  // コンテナサイズ
  container: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
    full: '100%',
  },
  
  // ボタンサイズ
  button: {
    xs: {
      h: '24px',
      minW: '24px',
      fontSize: 'xs',
      px: '2',
    },
    sm: {
      h: '32px',
      minW: '32px',
      fontSize: 'sm',
      px: '3',
    },
    md: {
      h: '40px',
      minW: '40px',
      fontSize: 'md',
      px: '4',
    },
    lg: {
      h: '48px',
      minW: '48px',
      fontSize: 'lg',
      px: '6',
    },
    xl: {
      h: '56px',
      minW: '56px',
      fontSize: 'xl',
      px: '8',
    },
  },
  
  // 入力フィールドサイズ
  input: {
    xs: '24px',
    sm: '32px',
    md: '40px',
    lg: '48px',
    xl: '56px',
  },
  
  // アイコンサイズ
  icon: {
    xs: '12px',
    sm: '16px',
    md: '20px',
    lg: '24px',
    xl: '32px',
  },
  
  // アバターサイズ
  avatar: {
    xs: '24px',
    sm: '32px',
    md: '40px',
    lg: '48px',
    xl: '64px',
    '2xl': '80px',
  },
};