/**
 * Chakra UIコンポーネントのカスタマイズ
 * 統一されたデザインシステムの実装
 */

import { defineStyleConfig, createMultiStyleConfigHelpers } from '@chakra-ui/react';

// ボタンコンポーネント
const Button = defineStyleConfig({
  baseStyle: {
    fontWeight: 'medium',
    borderRadius: 'lg',
    transition: 'all 0.2s',
    _focus: {
      boxShadow: 'outline',
    },
    _disabled: {
      opacity: 0.4,
      cursor: 'not-allowed',
      boxShadow: 'none',
    },
  },
  sizes: {
    xs: {
      h: 6,
      minW: 6,
      fontSize: 'xs',
      px: 2,
    },
    sm: {
      h: 8,
      minW: 8,
      fontSize: 'sm',
      px: 3,
    },
    md: {
      h: 10,
      minW: 10,
      fontSize: 'md',
      px: 4,
    },
    lg: {
      h: 12,
      minW: 12,
      fontSize: 'lg',
      px: 6,
    },
  },
  variants: {
    solid: {
      bg: 'brand.500',
      color: 'white',
      _hover: {
        bg: 'brand.600',
        transform: 'translateY(-1px)',
        boxShadow: 'md',
      },
      _active: {
        bg: 'brand.700',
        transform: 'translateY(0)',
      },
    },
    outline: {
      border: '2px solid',
      borderColor: 'brand.500',
      color: 'brand.500',
      _hover: {
        bg: 'brand.50',
        transform: 'translateY(-1px)',
        boxShadow: 'sm',
      },
      _active: {
        bg: 'brand.100',
        transform: 'translateY(0)',
      },
    },
    ghost: {
      color: 'brand.500',
      _hover: {
        bg: 'brand.50',
      },
      _active: {
        bg: 'brand.100',
      },
    },
  },
  defaultProps: {
    size: 'md',
    variant: 'solid',
    colorScheme: 'brand',
  },
});

// 入力フィールドコンポーネント
const Input = defineStyleConfig({
  baseStyle: {
    field: {
      width: '100%',
      minWidth: 0,
      outline: 0,
      position: 'relative',
      appearance: 'none',
      transitionProperty: 'common',
      transitionDuration: 'normal',
      _disabled: {
        opacity: 0.4,
        cursor: 'not-allowed',
      },
    },
  },
  sizes: {
    xs: {
      field: {
        borderRadius: 'sm',
        fontSize: 'xs',
        px: 2,
        h: 6,
      },
    },
    sm: {
      field: {
        borderRadius: 'md',
        fontSize: 'sm',
        px: 3,
        h: 8,
      },
    },
    md: {
      field: {
        borderRadius: 'md',
        fontSize: 'md',
        px: 4,
        h: 10,
      },
    },
    lg: {
      field: {
        borderRadius: 'md',
        fontSize: 'lg',
        px: 4,
        h: 12,
      },
    },
  },
  variants: {
    outline: {
      field: {
        border: '1px solid',
        borderColor: 'ui.border',
        bg: 'ui.surface',
        _hover: {
          borderColor: 'ui.borderHover',
        },
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
        },
        _invalid: {
          borderColor: 'semantic.error',
          boxShadow: '0 0 0 1px var(--chakra-colors-semantic-error)',
        },
      },
    },
    filled: {
      field: {
        border: '2px solid',
        borderColor: 'transparent',
        bg: 'gray.100',
        _hover: {
          bg: 'gray.200',
        },
        _focus: {
          bg: 'ui.surface',
          borderColor: 'brand.500',
        },
        _invalid: {
          borderColor: 'semantic.error',
        },
      },
    },
  },
  defaultProps: {
    size: 'md',
    variant: 'outline',
  },
});

// テキストエリアコンポーネント
const Textarea = defineStyleConfig({
  baseStyle: Input.baseStyle,
  sizes: Input.sizes,
  variants: Input.variants,
  defaultProps: Input.defaultProps,
});

// セレクトコンポーネント
const Select = defineStyleConfig({
  baseStyle: Input.baseStyle,
  sizes: Input.sizes,
  variants: Input.variants,
  defaultProps: Input.defaultProps,
});

// カードコンポーネント
// Define card anatomy manually since @chakra-ui/anatomy is not available
const cardParts = ['container', 'header', 'body', 'footer'];
const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(cardParts);

const Card = defineMultiStyleConfig({
  baseStyle: definePartsStyle({
    container: {
      bg: 'ui.surface',
      borderRadius: 'lg',
      borderWidth: '1px',
      borderColor: 'ui.border',
      boxShadow: 'sm',
      overflow: 'hidden',
      transition: 'all 0.2s',
      _hover: {
        boxShadow: 'md',
        transform: 'translateY(-2px)',
      },
    },
    header: {
      px: 6,
      py: 4,
      borderBottomWidth: '1px',
      borderBottomColor: 'ui.border',
    },
    body: {
      px: 6,
      py: 4,
    },
    footer: {
      px: 6,
      py: 4,
      borderTopWidth: '1px',
      borderTopColor: 'ui.border',
    },
  }),
  sizes: {
    sm: definePartsStyle({
      container: {
        borderRadius: 'md',
      },
      header: {
        px: 4,
        py: 3,
        fontSize: 'sm',
      },
      body: {
        px: 4,
        py: 3,
        fontSize: 'sm',
      },
      footer: {
        px: 4,
        py: 3,
        fontSize: 'sm',
      },
    }),
    md: definePartsStyle({}),
    lg: definePartsStyle({
      container: {
        borderRadius: 'xl',
      },
      header: {
        px: 8,
        py: 5,
        fontSize: 'lg',
      },
      body: {
        px: 8,
        py: 5,
        fontSize: 'md',
      },
      footer: {
        px: 8,
        py: 5,
      },
    }),
  },
  variants: {
    outline: definePartsStyle({
      container: {
        bg: 'transparent',
        boxShadow: 'none',
        borderWidth: '1px',
      },
    }),
    filled: definePartsStyle({
      container: {
        bg: 'gray.50',
        boxShadow: 'none',
        borderWidth: '0',
      },
    }),
    elevated: definePartsStyle({
      container: {
        bg: 'ui.surface',
        boxShadow: 'lg',
        borderWidth: '0',
        _hover: {
          boxShadow: 'xl',
        },
      },
    }),
  },
  defaultProps: {
    size: 'md',
    variant: 'outline',
  },
});

// バッジコンポーネント
const Badge = defineStyleConfig({
  baseStyle: {
    textTransform: 'none',
    fontWeight: 'medium',
    borderRadius: 'full',
    px: 2,
    py: 0.5,
  },
  sizes: {
    xs: {
      fontSize: 'xs',
      px: 1.5,
      py: 0.5,
    },
    sm: {
      fontSize: 'sm',
      px: 2,
      py: 0.5,
    },
    md: {
      fontSize: 'md',
      px: 3,
      py: 1,
    },
  },
  variants: {
    solid: {
      bg: 'brand.500',
      color: 'white',
    },
    subtle: {
      bg: 'brand.100',
      color: 'brand.800',
    },
    outline: {
      color: 'brand.500',
      boxShadow: 'inset 0 0 0px 1px var(--chakra-colors-brand-500)',
    },
  },
  defaultProps: {
    size: 'sm',
    variant: 'subtle',
    colorScheme: 'brand',
  },
});

// タブコンポーネント
const Tabs = {
  baseStyle: {
    tab: {
      fontWeight: 'medium',
      _selected: {
        color: 'brand.600',
      },
      _focus: {
        boxShadow: 'none',
      },
    },
  },
  variants: {
    line: {
      tab: {
        borderBottom: '2px solid',
        borderColor: 'transparent',
        _selected: {
          color: 'brand.600',
          borderColor: 'brand.500',
        },
        _hover: {
          color: 'brand.500',
        },
      },
    },
    enclosed: {
      tab: {
        borderTopRadius: 'md',
        border: '1px solid',
        borderColor: 'transparent',
        mb: '-1px',
        _selected: {
          color: 'brand.600',
          borderColor: 'inherit',
          borderBottomColor: 'white',
          bg: 'white',
        },
      },
    },
    'soft-rounded': {
      tab: {
        borderRadius: 'full',
        fontWeight: 'medium',
        color: 'gray.600',
        _selected: {
          color: 'brand.700',
          bg: 'brand.100',
        },
        _hover: {
          color: 'brand.600',
          bg: 'gray.100',
        },
      },
    },
  },
  defaultProps: {
    colorScheme: 'brand',
    variant: 'soft-rounded',
  },
};

// アラートコンポーネント
const Alert = {
  baseStyle: {
    container: {
      borderRadius: 'md',
      px: 4,
      py: 3,
    },
  },
  variants: {
    subtle: {
      container: {
        bg: 'brand.50',
      },
    },
    solid: {
      container: {
        bg: 'brand.500',
        color: 'white',
      },
    },
  },
};

// コンポーネント定義をエクスポート
export const components = {
  Button,
  Input,
  Textarea,
  Select,
  Card,
  Badge,
  Tabs,
  Alert,
  // フォーム要素
  FormLabel: {
    baseStyle: {
      fontSize: 'sm',
      fontWeight: 'medium',
      mb: 2,
      color: 'ui.text.primary',
    },
  },
  FormControl: {
    baseStyle: {
      marginBottom: 4,
    },
  },
  // その他のコンポーネント
  Heading: {
    baseStyle: {
      fontWeight: 'bold',
      lineHeight: 'shorter',
    },
    sizes: {
      xs: { fontSize: 'xs' },
      sm: { fontSize: 'sm' },
      md: { fontSize: 'md' },
      lg: { fontSize: 'lg' },
      xl: { fontSize: 'xl' },
      '2xl': { fontSize: '2xl' },
      '3xl': { fontSize: '3xl' },
      '4xl': { fontSize: '4xl' },
    },
  },
  Text: {
    baseStyle: {
      color: 'ui.text.primary',
      lineHeight: 'tall',
    },
  },
  Link: {
    baseStyle: {
      color: 'brand.500',
      _hover: {
        textDecoration: 'underline',
        color: 'brand.600',
      },
    },
  },
  Divider: {
    baseStyle: {
      borderColor: 'ui.border',
    },
  },
};