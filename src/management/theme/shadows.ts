/**
 * シャドウ定義
 * 深度と視覚的階層を表現するための影
 */

export const shadows = {
  none: 'none',
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
  outline: '0 0 0 3px rgba(66, 153, 225, 0.5)',
  
  // カラード・シャドウ
  brand: '0 4px 14px 0 rgba(5, 117, 255, 0.39)',
  success: '0 4px 14px 0 rgba(34, 197, 94, 0.39)',
  warning: '0 4px 14px 0 rgba(245, 158, 11, 0.39)',
  error: '0 4px 14px 0 rgba(239, 68, 68, 0.39)',
  
  // ホバー時のシャドウ
  hover: {
    sm: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
    md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
    lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
  },
  
  // カード用シャドウ
  card: {
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
    hover: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
    selected: '0 0 0 2px #0575FF, 0 10px 15px -3px rgba(5, 117, 255, 0.2)',
  },
};