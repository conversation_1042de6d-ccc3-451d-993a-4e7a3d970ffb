/**
 * カラーパレット定義
 * ブランドカラーとセマンティックカラー
 */

export const colors = {
  // ブランドカラー
  brand: {
    50: '#E6F2FF',
    100: '#BAD9FF',
    200: '#8CC0FF',
    300: '#5FA7FF',
    400: '#328EFF',
    500: '#0575FF', // メインブランドカラー
    600: '#0461D6',
    700: '#034EAD',
    800: '#023A85',
    900: '#01265C',
  },
  
  // セマンティックカラー
  semantic: {
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  // UIカラー
  ui: {
    background: '#F9FAFB',
    surface: '#FFFFFF',
    border: '#E5E7EB',
    borderHover: '#D1D5DB',
    text: {
      primary: '#111827',
      secondary: '#6B7280',
      tertiary: '#9CA3AF',
      inverse: '#FFFFFF',
    },
  },
  
  // 状態カラー
  state: {
    hover: {
      primary: 'rgba(5, 117, 255, 0.1)',
      secondary: 'rgba(107, 114, 128, 0.1)',
      success: 'rgba(34, 197, 94, 0.1)',
      warning: 'rgba(245, 158, 11, 0.1)',
      error: 'rgba(239, 68, 68, 0.1)',
    },
    active: {
      primary: 'rgba(5, 117, 255, 0.2)',
      secondary: 'rgba(107, 114, 128, 0.2)',
    },
    disabled: {
      background: '#F3F4F6',
      text: '#D1D5DB',
    },
  },
  
  // カテゴリ別カラー
  category: {
    candidate: '#8B5CF6', // 紫 - 候補者関連
    company: '#3B82F6',   // 青 - 企業関連
    interview: '#10B981', // 緑 - 面接関連
    document: '#F59E0B',  // 黄 - 書類関連
    analysis: '#EC4899',  // ピンク - 分析関連
  },
};