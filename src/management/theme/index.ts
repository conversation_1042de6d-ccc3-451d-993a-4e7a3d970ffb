/**
 * 面接くん管理画面 - Chakra UI カスタムテーマ
 * 統一されたデザインシステムの定義
 */

import { extendTheme } from '@chakra-ui/react';
import { mensetsukuTheme as baseTheme } from '../../shared/theme';
import { colors } from './colors';
import { typography } from './typography';
import { spaces } from './spaces';
import { components } from './components';
import { sizes } from './sizes';
import { radii } from './radii';
import { shadows } from './shadows';

// グローバルスタイル
const styles = {
  global: {
    body: {
      bg: 'gray.50',
      color: 'gray.900',
      fontSize: 'md',
      lineHeight: 'tall',
    },
    '*': {
      boxSizing: 'border-box',
    },
  },
};

// カスタムテーマ
export const theme = extendTheme(baseTheme, {
  styles,
  colors,
  ...typography,
  space: spaces,
  sizes,
  radii,
  shadows,
  components,
  // レスポンシブブレークポイント
  breakpoints: {
    sm: '30em',
    md: '48em',
    lg: '62em',
    xl: '80em',
    '2xl': '96em',
  },
});

// デザイントークンのエクスポート
export * from './colors';
export * from './typography';
export * from './spaces';
export * from './sizes';
export * from './radii';
export * from './shadows';