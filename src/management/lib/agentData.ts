/**
 * エージェント用データ構造
 * 企業情報設定とリンク管理機能
 */

// 候補者側のデータ構造をインポート（実際の実装では共通ライブラリ化）
export interface CompanyInfo {
  name: string;
  industry: string;
  position: string;
  description: string;
  interviewStyle: string;
  culture: string;
  location: string;
  employeeCount: string;
  founded: string;
  website?: string;
}

export interface InterviewLink {
  id: string;
  companyInfo: CompanyInfo;
  agentNotes: string;
  agentRecommendation: string;
  recommendedScenarios: string[];
  createdBy: string;
  createdByName: string;
  candidateEmail?: string;
  createdAt: string;
  expiresAt: string;
  status: 'active' | 'used' | 'expired';
  accessCount: number;
  lastAccessedAt?: string;
}

// エージェント作業用インターフェース
export interface CompanySetupForm {
  companyInfo: Partial<CompanyInfo>;
  agentNotes: string;
  agentRecommendation: string;
  recommendedScenarios: string[];
  candidateEmail: string;
  expirationDays: number;
}

// 利用可能なシナリオ
export const availableScenarios = [
  {
    id: "scenario_tech_senior",
    name: "テック系シニアエンジニア面接",
    description: "技術的深度と리더십能力を重視する面接",
    difficulty: "hard",
    industry: "tech"
  },
  {
    id: "scenario_consulting_case", 
    name: "戦略コンサルタント ケース面接",
    description: "論理的思考と構造化能力を評価するケース面接",
    difficulty: "expert",
    industry: "consulting"
  },
  {
    id: "scenario_pressure_test",
    name: "高圧面接シミュレーション",
    description: "ストレス下での判断力とコミュニケーション能力を評価",
    difficulty: "expert",
    industry: "general"
  }
];

// 業界テンプレート
export const industryTemplates: { [key: string]: Partial<CompanyInfo> } = {
  "tech": {
    industry: "IT・ソフトウェア",
    interviewStyle: "技術的深度と論理的思考力を重視",
    culture: "フラットな組織、チャレンジ精神重視"
  },
  "consulting": {
    industry: "コンサルティング", 
    interviewStyle: "論理的思考力、構造化能力、プレゼンテーション力を評価",
    culture: "成果主義、グローバル志向、継続的学習重視"
  },
  "finance": {
    industry: "金融・銀行",
    interviewStyle: "安定性重視、リスク管理意識、チームワークを評価",
    culture: "安定性重視、チームワーク、コンプライアンス重視"
  },
  "startup": {
    industry: "スタートアップ・ベンチャー",
    interviewStyle: "スピード感、実行力、創造性を重視",
    culture: "スピード重視、実験的アプローチ、失敗を恐れない"
  },
  "trading": {
    industry: "商社・貿易",
    interviewStyle: "人柄重視、継続力、コミュニケーション能力を評価",
    culture: "伝統と革新の両立、長期的関係重視、海外志向"
  }
};

// モックエージェントリンクデータ（managementサイド用）
export const mockAgentLinks: InterviewLink[] = [
  // 候補者側と同じデータを参照（実際の実装では共通データベース）
];

// ユーティリティ関数
export const createNewInterviewLink = (form: CompanySetupForm, agentId: string, agentName: string): InterviewLink => {
  const linkId = `link_${Math.random().toString(36).substring(2, 15)}`;
  const now = new Date();
  const expirationDate = new Date(now.getTime() + (form.expirationDays * 24 * 60 * 60 * 1000));
  
  return {
    id: linkId,
    companyInfo: form.companyInfo as CompanyInfo,
    agentNotes: form.agentNotes,
    agentRecommendation: form.agentRecommendation,
    recommendedScenarios: form.recommendedScenarios,
    createdBy: agentId,
    createdByName: agentName,
    candidateEmail: form.candidateEmail,
    createdAt: now.toISOString(),
    expiresAt: expirationDate.toISOString(),
    status: 'active',
    accessCount: 0
  };
};

export const generateInterviewLinkUrl = (linkId: string): string => {
  return `${process.env.NEXT_PUBLIC_CANDIDATE_URL || 'http://localhost:3003'}/interview-link/${linkId}`;
};

export const getDefaultAgentRecommendation = (scenarioIds: string[]): string => {
  const recommendations: { [key: string]: string } = {
    "scenario_tech_senior": "技術的な深度が求められるため、システム設計とアルゴリズムの練習を重点的に行ってください。",
    "scenario_consulting_case": "ケース面接対策が必須です。時間制限内での構造化思考と論理的説明力を磨いてください。",
    "scenario_pressure_test": "この企業は面接でストレス耐性を確認する傾向があります。プレッシャー下でも冷静に回答できるよう準備してください。"
  };
  
  const messages = scenarioIds.map(id => recommendations[id]).filter(Boolean);
  return messages.join('\n\n') || "企業の面接スタイルに合わせた練習を行ってください。";
};

export const validateCompanyForm = (form: CompanySetupForm): string[] => {
  const errors: string[] = [];
  
  if (!form.companyInfo.name?.trim()) {
    errors.push("会社名は必須です");
  }
  
  if (!form.companyInfo.position?.trim()) {
    errors.push("職種は必須です");
  }
  
  if (!form.candidateEmail?.trim()) {
    errors.push("候補者のメールアドレスは必須です");
  }
  
  if (form.recommendedScenarios.length === 0) {
    errors.push("推奨シナリオを最低1つ選択してください");
  }
  
  if (form.expirationDays < 1 || form.expirationDays > 30) {
    errors.push("有効期限は1日以上30日以内で設定してください");
  }
  
  return errors;
};