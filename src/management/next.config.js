/** @type {import('next').NextConfig} */
const path = require('path');
const dotenv = require('dotenv');

// ルートディレクトリの.envファイルを読み込む（ローカル開発環境用）
if (process.env.NODE_ENV !== 'production') {
  dotenv.config({ path: path.resolve(__dirname, '../../.env') });
}

const nextConfig = {
  // 外部ディレクトリのTranspile設定
  // experimental: {
  //   // 親ディレクトリの共有ファイルを許可
  //   externalDir: true,
  // },
  
  // webpack設定（pnpm workspacesでは基本的に不要）
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // TypeScript設定を拡張
    config.resolve.extensions = ['.ts', '.tsx', '.js', '.jsx', ...config.resolve.extensions];
    
    return config;
  },

  // トランスパイルするパッケージを指定
  transpilePackages: ['@mensetsu-kun/shared'],
  
  // 開発モード設定
  reactStrictMode: true,
  
  // TypeScript設定
  typescript: {
    // 型チェックエラーがあってもビルドを続行（開発環境）
    ignoreBuildErrors: false,
  },
};

module.exports = nextConfig;