<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>管理画面</title>
</head>
<body>
  <h1>管理画面</h1>
  <button id="load">質問取得</button>
  <pre id="questions"></pre>
  <button id="link">MTGリンク発行</button>
  <div id="mtg"></div>
  <script>
    document.getElementById('load').onclick = async () => {
      const res = await fetch('/api/interviews/join', {method: 'POST'});
      const data = await res.json();
      document.getElementById('questions').textContent = JSON.stringify(data.questions, null, 2);
    };
    document.getElementById('link').onclick = () => {
      document.getElementById('mtg').textContent = 'https://example.com/mtg/12345';
    };
  </script>
</body>
</html>
