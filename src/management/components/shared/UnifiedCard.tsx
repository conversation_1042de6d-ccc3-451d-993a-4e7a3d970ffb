/**
 * 統一カードコンポーネント - 全タブで一貫したデザインを提供
 */

import React from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Text,
  Badge,
  Avatar,
  Box,
  useColorModeValue,
} from '@chakra-ui/react';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';

// 基本カードプロパティ
export interface BaseCardProps {
  isSelected?: boolean;
  onClick?: () => void;
  children?: React.ReactNode;
  width?: string | object;
  minHeight?: string | object;
  maxHeight?: string | object;
}

// 候補者カードプロパティ
export interface CandidateCardProps extends BaseCardProps {
  name: string;
  nameKana: string;
  age: number;
  currentStatus: 'employed' | 'unemployed';
  currentEmployer?: string;
  previousEmployer?: string;
  position: string;
  address: string;
  hasResume: boolean;
  hasCareerHistory: boolean;
  score?: number;
  additionalInfo?: React.ReactNode;
  topRightBadge?: React.ReactNode;
}

// 統一カードベース
export const UnifiedCard: React.FC<BaseCardProps> = ({
  isSelected = false,
  onClick,
  children,
  width = "100%",
  minHeight = { base: "auto", md: "280px" },
  maxHeight = { base: "none", md: "320px" }
}) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  
  return (
    <Card
      variant="outline"
      cursor={onClick ? "pointer" : "default"}
      onClick={onClick}
      borderWidth={isSelected ? "2px" : "1px"}
      borderColor={isSelected ? "blue.500" : borderColor}
      bg={isSelected ? "blue.50" : "white"}
      _hover={onClick ? { 
        borderColor: "blue.300", 
        bg: "blue.50",
        transform: "translateY(-2px)",
        boxShadow: "lg"
      } : {}}
      transition="all 0.2s ease"
      minH={minHeight}
      maxH={maxHeight}
      width={width}
      position="relative"
    >
      {children}
    </Card>
  );
};

// 候補者カード
export const CandidateCard: React.FC<CandidateCardProps> = ({
  name,
  nameKana,
  age,
  currentStatus,
  currentEmployer,
  previousEmployer,
  position,
  address,
  hasResume,
  hasCareerHistory,
  score,
  additionalInfo,
  topRightBadge,
  ...cardProps
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 8) return 'green';
    if (score >= 6) return 'yellow';
    return 'red';
  };

  return (
    <UnifiedCard {...cardProps}>
      <CardBody p={spacing.cardPadding}>
        <VStack align="start" spacing={{ base: 2, md: 3 }} h="100%">
          {/* ヘッダー部分：名前と選択状態 */}
          <VStack spacing={{ base: 2, md: 3 }} w="100%" align="start">
            <HStack justify="space-between" w="100%" align="start">
              <VStack align="start" spacing={1} flex={1} minW={0}>
                <Text 
                  fontWeight="bold" 
                  fontSize={{ base: "md", md: "lg" }} 
                  lineHeight="1.2" 
                  noOfLines={1}
                >
                  {name}
                </Text>
                <Text fontSize={{ base: "xs", md: "sm" }} color="gray.500" noOfLines={1}>
                  {nameKana}
                </Text>
              </VStack>
              {(cardProps.isSelected || topRightBadge) && (
                <VStack spacing={2} flexShrink={0}>
                  {cardProps.isSelected && (
                    <Badge 
                      colorScheme="blue" 
                      variant="solid" 
                      borderRadius="full"
                      fontSize={{ base: "xs", md: "sm" }}
                    >
                      ✓ 選択中
                    </Badge>
                  )}
                  {topRightBadge}
                </VStack>
              )}
            </HStack>
          </VStack>

          {/* 基本情報セクション */}
          <VStack align="start" spacing={2} w="100%" flex={1}>
            <HStack spacing={2} wrap="wrap">
              <Badge 
                colorScheme={currentStatus === 'employed' ? 'green' : 'gray'} 
                size="sm"
                variant="subtle"
              >
                {currentStatus === 'employed' ? '現職' : '離職中'}
              </Badge>
              <Badge colorScheme="blue" size="sm" variant="outline">
                {age}歳
              </Badge>
              {score && (
                <Badge colorScheme={getScoreColor(score)} size="sm" variant="solid">
                  {score.toFixed(1)}/10
                </Badge>
              )}
            </HStack>

            <VStack align="start" spacing={1} w="100%">
              <HStack spacing={2} w="100%">
                <Text fontSize="xs" color="gray.400" minW="40px">勤務先</Text>
                <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                  {currentStatus === 'employed' ? currentEmployer : previousEmployer || '情報なし'}
                </Text>
              </HStack>
              <HStack spacing={2} w="100%">
                <Text fontSize="xs" color="gray.400" minW="40px">職種</Text>
                <Text fontSize="sm" noOfLines={1}>
                  {position}
                </Text>
              </HStack>
              <HStack spacing={2} w="100%">
                <Text fontSize="xs" color="gray.400" minW="40px">住所</Text>
                <Text fontSize="sm" color="gray.600" noOfLines={1}>
                  {address}
                </Text>
              </HStack>
            </VStack>
          </VStack>

          {/* 追加情報セクション */}
          {additionalInfo && (
            <VStack align="start" spacing={1} w="100%">
              {additionalInfo}
            </VStack>
          )}

          {/* 書類ステータス */}
          <HStack spacing={2} wrap="wrap" w="100%">
            <Badge
              colorScheme={hasResume ? "green" : "gray"}
              size="xs"
              variant={hasResume ? "solid" : "outline"}
            >
              📄 履歴書
            </Badge>
            <Badge
              colorScheme={hasCareerHistory ? "green" : "gray"}
              size="xs"
              variant={hasCareerHistory ? "solid" : "outline"}
            >
              📋 職歴書
            </Badge>
          </HStack>
        </VStack>
      </CardBody>
    </UnifiedCard>
  );
};

// セクションヘッダー
export interface SectionHeaderProps {
  title: string;
  count?: number;
  rightElement?: React.ReactNode;
  width?: string | object;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  count,
  rightElement,
  width = "100%"
}) => (
  <HStack justify="space-between" align="center" mb={4} width={width}>
    <Text fontWeight="bold" fontSize={{ base: "sm", md: "md" }}>
      {title}{count !== undefined && ` (${count}件)`}
    </Text>
    {rightElement}
  </HStack>
);

// 統一されたタブリスト
export interface UnifiedTabListProps {
  children: React.ReactNode;
  width?: string | object;
}

export const UnifiedTabList: React.FC<UnifiedTabListProps> = ({ 
  children, 
  width = "100%" 
}) => (
  <Box
    width={width}
    overflowX="auto"
    css={{
      '&::-webkit-scrollbar': {
        height: '3px',
      },
      '&::-webkit-scrollbar-track': {
        background: 'transparent',
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#c1c1c1',
        borderRadius: '10px',
      },
      '&::-webkit-scrollbar-thumb:hover': {
        background: '#a1a1a1',
      },
    }}
  >
    {children}
  </Box>
);

// 統一されたコンテナ
export interface UnifiedContainerProps {
  children: React.ReactNode;
  spacing?: number | object;
  padding?: number | object;
  width?: string | object;
}

export const UnifiedContainer: React.FC<UnifiedContainerProps> = ({
  children,
  spacing: containerSpacing = spacing.sectionSpacing,
  padding = spacing.cardPadding,
  width = "100%"
}) => (
  <VStack 
    spacing={containerSpacing} 
    align="stretch" 
    p={padding}
    width={width}
  >
    {children}
  </VStack>
);