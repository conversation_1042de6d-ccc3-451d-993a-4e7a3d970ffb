/**
 * 完全統一デザインシステム - 全タブで一貫したUI
 */

import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Card,
  CardBody,
  CardHeader,
  Badge,
  useColorModeValue,
} from '@chakra-ui/react';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';

// 統一されたタイトル
export interface UnifiedPageTitleProps {
  title: string;
  subtitle?: string;
  rightElement?: React.ReactNode;
}

export const UnifiedPageTitle: React.FC<UnifiedPageTitleProps> = ({
  title,
  subtitle,
  rightElement
}) => (
  <HStack justify="space-between" align="center" mb={6} w="100%">
    <VStack align="start" spacing={1}>
      <Heading size="lg" color="blue.600" fontWeight="bold">
        {title}
      </Heading>
      {subtitle && (
        <Text fontSize="sm" color="gray.600">
          {subtitle}
        </Text>
      )}
    </VStack>
    {rightElement}
  </HStack>
);

// 統一されたセクションヘッダー
export interface UnifiedSectionHeaderProps {
  title: string;
  count?: number;
  rightElement?: React.ReactNode;
}

export const UnifiedSectionHeader: React.FC<UnifiedSectionHeaderProps> = ({
  title,
  count,
  rightElement
}) => (
  <HStack justify="space-between" align="center" mb={4} w="100%">
    <Text fontWeight="bold" fontSize="md">
      {title}{count !== undefined && ` (${count}件)`}
    </Text>
    {rightElement}
  </HStack>
);

// 統一されたアラート
export interface UnifiedAlertProps {
  title: string;
  description: string;
  status?: 'info' | 'success' | 'warning' | 'error';
}

export const UnifiedAlert: React.FC<UnifiedAlertProps> = ({
  title,
  description,
  status = 'info'
}) => (
  <Alert status={status} borderRadius="md" mb={4}>
    <AlertIcon />
    <VStack align="start" spacing={1} flex={1}>
      <Text fontWeight="bold" fontSize="sm">
        {title}
      </Text>
      <Text fontSize="sm">
        {description}
      </Text>
    </VStack>
  </Alert>
);

// 統一されたボタン
export interface UnifiedButtonProps {
  children: React.ReactNode;
  leftIcon?: React.ReactElement;
  rightIcon?: React.ReactElement;
  variant?: 'solid' | 'outline' | 'ghost';
  colorScheme?: string;
  size?: 'sm' | 'md' | 'lg';
  isDisabled?: boolean;
  isLoading?: boolean;
  loadingText?: string;
  onClick?: () => void;
  type?: 'button' | 'submit';
  width?: string | object;
}

export const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  children,
  leftIcon,
  rightIcon,
  variant = 'solid',
  colorScheme = 'blue',
  size = 'md',
  isDisabled = false,
  isLoading = false,
  loadingText,
  onClick,
  type = 'button',
  width = 'auto'
}) => (
  <Button
    leftIcon={leftIcon}
    rightIcon={rightIcon}
    variant={variant}
    colorScheme={colorScheme}
    size={size}
    isDisabled={isDisabled}
    isLoading={isLoading}
    loadingText={loadingText}
    onClick={onClick}
    type={type}
    width={width}
    fontWeight="medium"
  >
    {children}
  </Button>
);

// 統一されたフォームフィールド
export interface UnifiedFormFieldProps {
  label: string;
  isRequired?: boolean;
  helpText?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'file';
  placeholder?: string;
  value?: string | number;
  onChange?: (value: string) => void;
  accept?: string;
  multiple?: boolean;
  rows?: number;
}

export const UnifiedFormField: React.FC<UnifiedFormFieldProps> = ({
  label,
  isRequired = false,
  helpText,
  type = 'text',
  placeholder,
  value,
  onChange,
  accept,
  multiple,
  rows = 4
}) => (
  <FormControl isRequired={isRequired} w="100%">
    <FormLabel fontSize="sm" fontWeight="medium">
      {label}
    </FormLabel>
    {helpText && (
      <Text fontSize="xs" color="gray.600" mb={2}>
        {helpText}
      </Text>
    )}
    {type === 'textarea' ? (
      <Textarea
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        rows={rows}
        size="md"
      />
    ) : type === 'file' ? (
      <Box
        borderWidth={2}
        borderStyle="dashed"
        borderColor="gray.300"
        borderRadius="md"
        p={6}
        textAlign="center"
        bg={useColorModeValue('gray.50', 'gray.800')}
        _hover={{ borderColor: 'blue.400', bg: useColorModeValue('blue.50', 'blue.900') }}
        transition="all 0.2s"
      >
        <input
          type="file"
          accept={accept}
          multiple={multiple}
          style={{ display: 'none' }}
          id="file-input"
        />
        <VStack spacing={3}>
          <Text fontSize="lg" fontWeight="bold">
            ファイルを選択
          </Text>
          <Text fontSize="sm" color="gray.600">
            {placeholder || 'ファイルを選択してください'}
          </Text>
          <UnifiedButton
            colorScheme="blue"
            onClick={() => document.getElementById('file-input')?.click()}
          >
            ファイルを選択
          </UnifiedButton>
        </VStack>
      </Box>
    ) : (
      <Input
        type={type}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        size="md"
      />
    )}
  </FormControl>
);

// 統一されたカード
export interface UnifiedCardProps {
  children: React.ReactNode;
  title?: string;
  rightElement?: React.ReactNode;
  isSelected?: boolean;
  onClick?: () => void;
  width?: string | object;
  variant?: 'outline' | 'filled' | 'elevated';
}

export const UnifiedCard: React.FC<UnifiedCardProps> = ({
  children,
  title,
  rightElement,
  isSelected = false,
  onClick,
  width = "100%",
  variant = 'outline'
}) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  
  return (
    <Card
      variant={variant}
      cursor={onClick ? "pointer" : "default"}
      onClick={onClick}
      borderWidth={isSelected ? "2px" : "1px"}
      borderColor={isSelected ? "blue.500" : borderColor}
      bg={isSelected ? "blue.50" : "white"}
      _hover={onClick ? { 
        borderColor: "blue.300", 
        bg: "blue.50",
        transform: "translateY(-2px)",
        boxShadow: "lg"
      } : {}}
      transition="all 0.2s ease"
      width={width}
    >
      {title && (
        <CardHeader pb={2}>
          <HStack justify="space-between" align="center">
            <Text fontWeight="bold" fontSize="md">
              {title}
            </Text>
            {rightElement}
          </HStack>
        </CardHeader>
      )}
      <CardBody pt={title ? 0 : undefined}>
        {children}
      </CardBody>
    </Card>
  );
};

// 統一されたコンテナ
export interface UnifiedContainerProps {
  children: React.ReactNode;
  spacing?: number | object;
  padding?: number | object;
  width?: string | object;
}

export const UnifiedContainer: React.FC<UnifiedContainerProps> = ({
  children,
  spacing: containerSpacing = 6,
  padding = 6,
  width = "100%"
}) => (
  <VStack 
    spacing={containerSpacing} 
    align="stretch" 
    p={padding}
    width={width}
  >
    {children}
  </VStack>
);

// 統一されたカウンターバッジ
export interface UnifiedCountBadgeProps {
  count: number;
  label?: string;
  colorScheme?: string;
}

export const UnifiedCountBadge: React.FC<UnifiedCountBadgeProps> = ({
  count,
  label,
  colorScheme = 'blue'
}) => (
  <Badge 
    colorScheme={colorScheme} 
    size="sm" 
    variant="subtle"
    px={2}
    py={1}
    borderRadius="full"
  >
    {count}{label && ` ${label}`}
  </Badge>
);

// 統一されたフォーム
export interface UnifiedFormProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  onSubmit?: () => void;
  submitButtonText?: string;
  isLoading?: boolean;
  isSubmitDisabled?: boolean;
}

export const UnifiedForm: React.FC<UnifiedFormProps> = ({
  title,
  description,
  children,
  onSubmit,
  submitButtonText = '送信',
  isLoading = false,
  isSubmitDisabled = false
}) => (
  <UnifiedCard width="100%">
    <VStack spacing={4} align="stretch" w="100%">
      <VStack align="start" spacing={1} w="100%">
        <HStack spacing={2}>
          <Text fontWeight="bold" fontSize="md">
            {title}
          </Text>
        </HStack>
        {description && (
          <Text fontSize="sm" color="gray.600">
            {description}
          </Text>
        )}
      </VStack>
      
      <Box as="form" onSubmit={(e: React.FormEvent) => { e.preventDefault(); onSubmit?.(); }} w="100%">
        <VStack spacing={4} align="stretch" w="100%">
          {children}
          {onSubmit && (
            <UnifiedButton
              type="submit"
              colorScheme="blue"
              size="lg"
              isLoading={isLoading}
              isDisabled={isSubmitDisabled}
              width="100%"
            >
              {submitButtonText}
            </UnifiedButton>
          )}
        </VStack>
      </Box>
    </VStack>
  </UnifiedCard>
);