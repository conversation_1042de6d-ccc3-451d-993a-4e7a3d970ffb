/**
 * AI面接官意図設定コンポーネント
 * 質問ではなく「何を聞きたいか」の意図を設定し、AIに深掘りさせる
 */

import { useState } from 'react';
import {
  Box,
  IconButton,
  useToast,
  Text,
  Badge,
  Divider,
  List,
  ListItem,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  HStack,
  VStack,
  FormControl,
  FormLabel,
  useColorModeValue,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, EditIcon, QuestionIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, slideInFromBottom, staggerItem } from '../motion/index';
import { 
  UnifiedPageTitle,
  UnifiedAlert,
  UnifiedButton,
  UnifiedFormField,
  UnifiedCard,
  UnifiedContainer
} from './shared/UnifiedComponents';

const MotionBox = motion(Box);
const MotionVStack = motion(VStack);

interface InterviewIntent {
  id: string;
  category: string;
  intent: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number; // 分
  followUpDepth: number; // 深掘りレベル (1-5)
  aiInstructions: string;
  exampleQuestions: string[];
}

interface InterviewIntentConfigProps {
  onIntentsChange?: (intents: InterviewIntent[]) => void;
  maxIntents?: number;
  initialIntents?: InterviewIntent[];
}

export const InterviewIntentConfig: React.FC<InterviewIntentConfigProps> = ({
  onIntentsChange,
  maxIntents = 10,
  initialIntents = []
}) => {
  const [intents, setIntents] = useState<InterviewIntent[]>(initialIntents);
  const [isAddingIntent, setIsAddingIntent] = useState(false);
  const [editingIntent, setEditingIntent] = useState<InterviewIntent | null>(null);
  
  // 新規意図入力フォーム
  const [newCategory, setNewCategory] = useState('');
  const [newIntent, setNewIntent] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [newPriority, setNewPriority] = useState<InterviewIntent['priority']>('medium');
  const [newEstimatedTime, setNewEstimatedTime] = useState(5);
  const [newFollowUpDepth, setNewFollowUpDepth] = useState(3);
  const [newAiInstructions, setNewAiInstructions] = useState('');

  const toast = useToast();

  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const infoBg = useColorModeValue('blue.50', 'blue.900');

  /**
   * カテゴリ別の既定値
   */
  const defaultCategorySettings = {
    '自己紹介・経歴': {
      description: '候補者の基本的な背景と経験を理解する',
      aiInstructions: '候補者の経歴について自然な会話形式で詳しく聞き出してください。表面的な回答の場合は具体例を求めて深掘りしてください。',
      exampleQuestions: ['簡単に自己紹介をお願いします', 'これまでのキャリアで最も印象に残っている経験は？']
    },
    '志望動機・企業理解': {
      description: '当社への関心度と企業研究の深さを確認する',
      aiInstructions: '表面的な志望動機ではなく、真の動機を探るために「なぜ」を重ねて質問してください。企業研究の具体性も確認してください。',
      exampleQuestions: ['なぜ当社を選んだのですか？', '当社の事業について、どの程度ご存知ですか？']
    },
    '技術スキル・専門性': {
      description: '職務に必要な技術レベルと実務経験を評価する',
      aiInstructions: '技術的な質問では、理論だけでなく実際の経験に基づいた回答を求めてください。困難だった点やその解決方法まで深掘りしてください。',
      exampleQuestions: ['○○の技術について実務での使用経験は？', '技術的に最も困難だった課題は？']
    },
    'コミュニケーション・チームワーク': {
      description: 'チーム内での協働能力と対人スキルを確認する',
      aiInstructions: '具体的なエピソードを通してコミュニケーション能力を確認してください。困難な状況での対応も聞き出してください。',
      exampleQuestions: ['チームでの役割は？', '意見の食い違いがあった時の対応は？']
    },
    '課題解決・思考力': {
      description: '問題解決のアプローチと論理的思考力を評価する',
      aiInstructions: '課題解決のプロセスを段階的に確認し、判断の根拠や代替案の検討についても質問してください。',
      exampleQuestions: ['困難な問題にどうアプローチしますか？', '判断に迷った時の決め方は？']
    }
  };

  /**
   * 意図を追加
   */
  const handleAddIntent = () => {
    if (!newCategory.trim() || !newIntent.trim()) {
      toast({
        title: '入力エラー',
        description: 'カテゴリと意図は必須項目です',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (intents.length >= maxIntents) {
      toast({
        title: '追加制限',
        description: `最大${maxIntents}件まで設定可能です`,
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const defaultSettings = defaultCategorySettings[newCategory as keyof typeof defaultCategorySettings];

    const newIntentItem: InterviewIntent = {
      id: `intent_${Date.now()}`,
      category: newCategory,
      intent: newIntent,
      description: newDescription || defaultSettings?.description || '',
      priority: newPriority,
      estimatedTime: newEstimatedTime,
      followUpDepth: newFollowUpDepth,
      aiInstructions: newAiInstructions || defaultSettings?.aiInstructions || '',
      exampleQuestions: defaultSettings?.exampleQuestions || []
    };

    const updatedIntents = [...intents, newIntentItem];
    setIntents(updatedIntents);
    onIntentsChange?.(updatedIntents);

    // フォームリセット
    setNewCategory('');
    setNewIntent('');
    setNewDescription('');
    setNewPriority('medium');
    setNewEstimatedTime(5);
    setNewFollowUpDepth(3);
    setNewAiInstructions('');
    setIsAddingIntent(false);

    toast({
      title: '意図を追加しました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  /**
   * 意図を削除
   */
  const handleDeleteIntent = (intentId: string) => {
    const updatedIntents = intents.filter(intent => intent.id !== intentId);
    setIntents(updatedIntents);
    onIntentsChange?.(updatedIntents);

    toast({
      title: '意図を削除しました',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  /**
   * 意図を編集
   */
  const handleEditIntent = (intent: InterviewIntent) => {
    setEditingIntent(intent);
    setNewCategory(intent.category);
    setNewIntent(intent.intent);
    setNewDescription(intent.description);
    setNewPriority(intent.priority);
    setNewEstimatedTime(intent.estimatedTime);
    setNewFollowUpDepth(intent.followUpDepth);
    setNewAiInstructions(intent.aiInstructions);
    setIsAddingIntent(true);
  };

  /**
   * 意図を更新
   */
  const handleUpdateIntent = () => {
    if (!editingIntent || !newCategory.trim() || !newIntent.trim()) {
      toast({
        title: '入力エラー',
        description: 'カテゴリと意図は必須項目です',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const defaultSettings = defaultCategorySettings[newCategory as keyof typeof defaultCategorySettings];

    const updatedIntent: InterviewIntent = {
      ...editingIntent,
      category: newCategory,
      intent: newIntent,
      description: newDescription || defaultSettings?.description || '',
      priority: newPriority,
      estimatedTime: newEstimatedTime,
      followUpDepth: newFollowUpDepth,
      aiInstructions: newAiInstructions || defaultSettings?.aiInstructions || '',
      exampleQuestions: defaultSettings?.exampleQuestions || editingIntent.exampleQuestions
    };

    const updatedIntents = intents.map(intent => 
      intent.id === editingIntent.id ? updatedIntent : intent
    );
    setIntents(updatedIntents);
    onIntentsChange?.(updatedIntents);

    // フォームリセット
    setNewCategory('');
    setNewIntent('');
    setNewDescription('');
    setNewPriority('medium');
    setNewEstimatedTime(5);
    setNewFollowUpDepth(3);
    setNewAiInstructions('');
    setIsAddingIntent(false);
    setEditingIntent(null);

    toast({
      title: '意図を更新しました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  /**
   * 優先度のカラー
   */
  const getPriorityColor = (priority: InterviewIntent['priority']) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'yellow';
      case 'low': return 'gray';
    }
  };

  /**
   * 優先度のラベル
   */
  const getPriorityLabel = (priority: InterviewIntent['priority']) => {
    switch (priority) {
      case 'high': return '重要';
      case 'medium': return '通常';
      case 'low': return '補助';
    }
  };

  return (
    <UnifiedContainer width="100%">
      <UnifiedPageTitle 
        title="AI面接官の質問意図設定"
        subtitle="直接的な質問ではなく「何を知りたいか」の意図を設定"
      />
      
      <UnifiedAlert
        title="意図ベースの質問設計"
        description="直接的な質問ではなく「何を知りたいか」の意図を設定することで、AIが自然に深掘りし、より本質的な情報を引き出します"
        status="info"
      />

      <HStack justify="space-between" width="100%">
        <Text fontSize="md" fontWeight="medium">
          設定済み意図: {intents.length}/{maxIntents}
        </Text>
        <UnifiedButton
          leftIcon={<AddIcon />}
          size="md"
          onClick={() => setIsAddingIntent(true)}
          isDisabled={intents.length >= maxIntents}
        >
          意図を追加
        </UnifiedButton>
      </HStack>

      <AnimatePresence>
        {isAddingIntent && (
          <MotionBox {...slideInFromBottom}>
            <UnifiedCard width="100%" variant="filled">
              <VStack spacing={4} align="stretch" width="100%">
                <Text fontSize="lg" fontWeight="bold" color="blue.700">
                  {editingIntent ? '質問意図を編集' : '新しい質問意図を追加'}
                </Text>

                <HStack spacing={4} width="100%">
                  <Box flex={1}>
                    <UnifiedFormField
                      label="カテゴリ"
                      isRequired
                      type="text"
                      placeholder="カテゴリを選択してください"
                    />
                    <Select
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder="カテゴリを選択"
                      size="md"
                      mt={2}
                    >
                      {Object.keys(defaultCategorySettings).map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                      <option value="custom">カスタム</option>
                    </Select>
                  </Box>

                  <Box w="200px">
                    <UnifiedFormField
                      label="優先度"
                      type="text"
                      placeholder="優先度を選択してください"
                    />
                    <Select
                      value={newPriority}
                      onChange={(e) => setNewPriority(e.target.value as InterviewIntent['priority'])}
                      size="md"
                      mt={2}
                    >
                      <option value="high">重要</option>
                      <option value="medium">通常</option>
                      <option value="low">補助</option>
                    </Select>
                  </Box>
                </HStack>

                {newCategory === 'custom' && (
                  <UnifiedFormField
                    label="カスタムカテゴリ名"
                    isRequired
                    type="text"
                    placeholder="例: リーダーシップ・マネジメント"
                    onChange={(value) => setNewCategory(value)}
                  />
                )}

                <UnifiedFormField
                  label="質問意図"
                  isRequired
                  type="text"
                  value={newIntent}
                  placeholder="例: 候補者のリーダーシップ経験と部下育成に対する考え方を知りたい"
                  onChange={(value) => setNewIntent(value)}
                />

                <UnifiedFormField
                  label="詳細説明"
                  type="textarea"
                  value={newDescription}
                  placeholder="この意図で何を評価したいかの詳細説明"
                  rows={2}
                  onChange={(value) => setNewDescription(value)}
                />

                <HStack spacing={4}>
                  <FormControl w="150px">
                    <FormLabel {...textStyles.label}>想定時間（分）</FormLabel>
                    <NumberInput
                      value={newEstimatedTime}
                      onChange={(value) => setNewEstimatedTime(Number(value))}
                      min={1}
                      max={30}
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl w="150px">
                    <FormLabel {...textStyles.label}>深掘りレベル</FormLabel>
                    <NumberInput
                      value={newFollowUpDepth}
                      onChange={(value) => setNewFollowUpDepth(Number(value))}
                      min={1}
                      max={5}
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>
                </HStack>

                <UnifiedFormField
                  label="AI面接官への指示"
                  type="textarea"
                  value={newAiInstructions}
                  placeholder="AIがどのように質問し、深掘りすべきかの指示"
                  rows={3}
                  onChange={(value) => setNewAiInstructions(value)}
                />

                <HStack justify="flex-end" spacing={3} width="100%">
                  <UnifiedButton
                    variant="ghost"
                    onClick={() => {
                      setIsAddingIntent(false);
                      setEditingIntent(null);
                      // フォームリセット
                      setNewCategory('');
                      setNewIntent('');
                      setNewDescription('');
                      setNewPriority('medium');
                      setNewEstimatedTime(5);
                      setNewFollowUpDepth(3);
                      setNewAiInstructions('');
                    }}
                  >
                    キャンセル
                  </UnifiedButton>
                  <UnifiedButton
                    onClick={editingIntent ? handleUpdateIntent : handleAddIntent}
                  >
                    {editingIntent ? '更新' : '追加'}
                  </UnifiedButton>
                </HStack>
              </VStack>
            </UnifiedCard>
          </MotionBox>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {intents.length > 0 && (
          <MotionVStack
            {...staggerItem}
            spacing={4}
            align="stretch"
            width="100%"
          >
            <Divider />
            
            <Text fontSize="lg" fontWeight="bold">
              設定済み質問意図
            </Text>

              <Accordion allowMultiple>
                {intents.map((intent, index) => (
                  <MotionBox
                    key={intent.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <AccordionItem border="1px" borderColor={borderColor} borderRadius="md" mb={2}>
                      <AccordionButton _hover={{ bg: hoverBg }}>
                        <Box flex="1" textAlign="left">
                          <HStack spacing={3}>
                            <Badge 
                              colorScheme={getPriorityColor(intent.priority)}
                              size="sm"
                            >
                              {getPriorityLabel(intent.priority)}
                            </Badge>
                            <Badge colorScheme="blue" variant="outline" size="sm">
                              {intent.category}
                            </Badge>
                            <Text {...textStyles.body} fontWeight="bold">
                              {intent.intent}
                            </Text>
                          </HStack>
                        </Box>
                        <HStack spacing={2}>
                          <Text {...textStyles.caption}>
                            {intent.estimatedTime}分
                          </Text>
                          <AccordionIcon />
                        </HStack>
                      </AccordionButton>
                      
                      <AccordionPanel>
                        <VStack spacing={4} align="stretch">
                          {intent.description && (
                            <Box>
                              <Text {...textStyles.label} mb={1}>詳細説明</Text>
                              <Text {...textStyles.body}>{intent.description}</Text>
                            </Box>
                          )}

                          <Box>
                            <Text {...textStyles.label} mb={1}>AI面接官への指示</Text>
                            <Text {...textStyles.body} 
                              p={2} 
                              bg={hoverBg} 
                              borderRadius="md"
                              fontSize="sm"
                            >
                              {intent.aiInstructions}
                            </Text>
                          </Box>

                          <HStack spacing={4}>
                            <Text {...textStyles.caption}>
                              深掘りレベル: {intent.followUpDepth}/5
                            </Text>
                            <Text {...textStyles.caption}>
                              想定時間: {intent.estimatedTime}分
                            </Text>
                          </HStack>

                          {intent.exampleQuestions.length > 0 && (
                            <Box>
                              <Text {...textStyles.label} mb={2}>参考質問例</Text>
                              <List spacing={1}>
                                {intent.exampleQuestions.map((question, qIndex) => (
                                  <ListItem key={qIndex}>
                                    <Text {...textStyles.caption} pl={4}>
                                      • {question}
                                    </Text>
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          )}

                          <HStack justify="flex-end" spacing={2}>
                            <IconButton
                              aria-label="編集"
                              icon={<EditIcon />}
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEditIntent(intent)}
                            />
                            <IconButton
                              aria-label="削除"
                              icon={<DeleteIcon />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => handleDeleteIntent(intent.id)}
                            />
                          </HStack>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </MotionBox>
                ))}
              </Accordion>

            <UnifiedAlert
              title="AI面接システムの動作"
              description="これらの意図に基づいて、AI面接官が自然な質問を生成し、回答内容に応じて適切に深掘りします。表面的な回答には具体例を求め、曖昧な部分には詳細確認を行います。"
              status="info"
            />
          </MotionVStack>
        )}
      </AnimatePresence>
    </UnifiedContainer>
  );
};