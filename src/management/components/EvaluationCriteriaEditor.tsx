'use client';

/**
 * 評価基準の詳細編集コンポーネント
 * 重み、スコアリング、フィードバックテンプレートの設定
 */
import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Text,
  Input,
  Textarea,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  FormControl,
  FormLabel,
  FormHelperText,
  IconButton,
  Badge,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Progress,
  Tooltip,
  Switch,
  Tag,
  TagLabel,
  TagCloseButton,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  AddIcon,
  DeleteIcon,
  EditIcon,
  InfoIcon,
  CheckCircleIcon,
  WarningIcon,
  StarIcon,
  DragHandleIcon,
} from '@chakra-ui/icons';
import {
  FiTarget,
  FiTrendingUp,
  FiAward,
  FiMessageCircle,
  FiSettings,
  FiPercent,
} from 'react-icons/fi';
import {
  EvaluationCriterion,
  EvaluationMatrix,
  QuestionCategoryConfiguration
} from '@mensetsu-kun/shared/types/evaluation';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface EvaluationCriteriaEditorProps {
  stepId: string;
  stepName: string;
  currentMatrix?: EvaluationMatrix;
  onSave: (matrix: EvaluationMatrix) => void;
  onCancel: () => void;
}

// デフォルト評価基準テンプレート
const defaultCriteriaTemplates: Record<string, Partial<EvaluationCriterion>[]> = {
  technical: [
    {
      name: '技術知識',
      description: '必要な技術的スキルと知識の深さ',
      weight: 30,
      category: 'technical',
      rubric: {
        excellent: {
          score: 5,
          description: '期待以上の技術知識を持ち、応用できる',
          examples: ['最新技術の理解', '実践的な応用例の提示']
        },
        good: {
          score: 4,
          description: '十分な技術知識があり、適切に説明できる',
          examples: ['基本概念の正確な理解', '具体例の提示']
        },
        satisfactory: {
          score: 3,
          description: '基本的な技術知識は持っている',
          examples: ['基本用語の理解', '簡単な説明が可能']
        },
        needsImprovement: {
          score: 2,
          description: '技術知識が不足している',
          examples: ['基本概念の誤解', '不正確な説明']
        }
      },
      feedbackTemplates: {
        positive: [
          '技術的な理解が深く、実践的な知識も豊富です',
          '最新技術への関心と学習意欲が感じられます'
        ],
        constructive: [
          '基本的な理解はありますが、より深い学習をお勧めします',
          '実践的な経験を積むことで理解が深まるでしょう'
        ],
        specific: [
          'XX技術について詳しく学習することをお勧めします',
          '実際のプロジェクトでの適用経験があると良いでしょう'
        ]
      }
    },
    {
      name: '問題解決能力',
      description: '論理的思考と問題への取り組み方',
      weight: 25,
      category: 'technical'
    }
  ],
  communication: [
    {
      name: 'コミュニケーション力',
      description: '明確で効果的なコミュニケーション能力',
      weight: 30,
      category: 'communication'
    },
    {
      name: 'プレゼンテーション力',
      description: '情報を整理し、分かりやすく伝える能力',
      weight: 20,
      category: 'communication'
    }
  ],
  cultural_fit: [
    {
      name: '企業文化適合性',
      description: '会社の価値観やカルチャーとの適合度',
      weight: 35,
      category: 'cultural_fit'
    },
    {
      name: 'チームワーク',
      description: 'チームでの協働と貢献能力',
      weight: 25,
      category: 'cultural_fit'
    }
  ]
};

const EvaluationCriteriaEditor: React.FC<EvaluationCriteriaEditorProps> = ({
  stepId,
  stepName,
  currentMatrix,
  onSave,
  onCancel
}) => {
  const [matrix, setMatrix] = useState<EvaluationMatrix>(
    currentMatrix || {
      id: `matrix_${stepId}`,
      stepId,
      criteria: [],
      totalWeight: 0,
      passingThreshold: 70,
      aggregationMethod: 'weighted_average',
      normalization: {
        enabled: false,
        method: 'z_score'
      }
    }
  );

  const [editingCriterion, setEditingCriterion] = useState<EvaluationCriterion | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  
  const { isOpen: isTemplateOpen, onOpen: onTemplateOpen, onClose: onTemplateClose } = useDisclosure();
  const { isOpen: isCriterionOpen, onOpen: onCriterionOpen, onClose: onCriterionClose } = useDisclosure();
  
  const toast = useToast();

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 総重みの計算
  const totalWeight = matrix.criteria.reduce((sum, criterion) => sum + criterion.weight, 0);
  const isWeightValid = Math.abs(totalWeight - 100) < 0.1;

  // 新しい評価基準の追加
  const handleAddCriterion = useCallback(() => {
    const newCriterion: EvaluationCriterion = {
      id: `criterion_${Date.now()}`,
      name: '',
      description: '',
      weight: Math.max(0, 100 - totalWeight),
      maxScore: 5,
      scoreType: 'numeric',
      scoreOptions: {
        min: 1,
        max: 5,
        step: 1
      },
      rubric: {
        excellent: { score: 5, description: '', examples: [] },
        good: { score: 4, description: '', examples: [] },
        satisfactory: { score: 3, description: '', examples: [] },
        needsImprovement: { score: 2, description: '', examples: [] }
      },
      feedbackTemplates: {
        positive: [],
        constructive: [],
        specific: []
      },
      relatedQuestionCategories: [],
      isRequired: true,
      category: 'custom'
    };
    
    setEditingCriterion(newCriterion);
    onCriterionOpen();
  }, [totalWeight, onCriterionOpen]);

  // テンプレートから追加
  const handleAddFromTemplate = useCallback(() => {
    if (!selectedTemplate) return;
    
    const templates = defaultCriteriaTemplates[selectedTemplate] || [];
    const newCriteria = templates.map((template, index) => ({
      id: `criterion_${Date.now()}_${index}`,
      name: template.name || '',
      description: template.description || '',
      weight: template.weight || 20,
      maxScore: 5,
      scoreType: 'numeric' as const,
      scoreOptions: {
        min: 1,
        max: 5,
        step: 1
      },
      rubric: template.rubric || {
        excellent: { score: 5, description: '', examples: [] },
        good: { score: 4, description: '', examples: [] },
        satisfactory: { score: 3, description: '', examples: [] },
        needsImprovement: { score: 2, description: '', examples: [] }
      },
      feedbackTemplates: template.feedbackTemplates || {
        positive: [],
        constructive: [],
        specific: []
      },
      relatedQuestionCategories: [],
      isRequired: true,
      category: template.category || 'custom'
    })) as EvaluationCriterion[];

    setMatrix(prev => ({
      ...prev,
      criteria: [...prev.criteria, ...newCriteria]
    }));

    onTemplateClose();
    setSelectedTemplate('');
    
    toast({
      title: 'テンプレートを追加しました',
      description: `${newCriteria.length}個の評価基準を追加しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  }, [selectedTemplate, onTemplateClose, toast]);

  // 評価基準の保存
  const handleSaveCriterion = useCallback((criterion: EvaluationCriterion) => {
    setMatrix(prev => {
      const existingIndex = prev.criteria.findIndex(c => c.id === criterion.id);
      if (existingIndex >= 0) {
        // 更新
        const newCriteria = [...prev.criteria];
        newCriteria[existingIndex] = criterion;
        return { ...prev, criteria: newCriteria };
      } else {
        // 新規追加
        return { ...prev, criteria: [...prev.criteria, criterion] };
      }
    });
    
    onCriterionClose();
    setEditingCriterion(null);
  }, [onCriterionClose]);

  // 評価基準の削除
  const handleDeleteCriterion = useCallback((criterionId: string) => {
    setMatrix(prev => ({
      ...prev,
      criteria: prev.criteria.filter(c => c.id !== criterionId)
    }));
  }, []);

  // 重みの正規化
  const handleNormalizeWeights = useCallback(() => {
    if (matrix.criteria.length === 0) return;
    
    const equalWeight = 100 / matrix.criteria.length;
    const normalizedCriteria = matrix.criteria.map(criterion => ({
      ...criterion,
      weight: Math.round(equalWeight * 10) / 10
    }));
    
    setMatrix(prev => ({ ...prev, criteria: normalizedCriteria }));
    
    toast({
      title: '重みを正規化しました',
      description: '全ての評価基準に均等な重みを設定しました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  }, [matrix.criteria, toast]);

  // 保存
  const handleSave = useCallback(() => {
    if (!isWeightValid) {
      toast({
        title: '重みの合計が100%ではありません',
        description: '評価基準の重みの合計を100%に調整してください',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    if (matrix.criteria.length === 0) {
      toast({
        title: '評価基準が設定されていません',
        description: '少なくとも1つの評価基準を設定してください',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    onSave(matrix);
  }, [matrix, isWeightValid, onSave, toast]);

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
        <CardHeader>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <HStack>
                <FiTarget />
                <Text fontSize="lg" fontWeight="bold">
                  評価基準設定 - {stepName}
                </Text>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                各評価項目の重み・スコアリング・フィードバックを設定
              </Text>
            </VStack>
            
            <HStack>
              <Badge
                colorScheme={isWeightValid ? 'green' : 'red'}
                fontSize="sm"
                px={3}
                py={1}
              >
                重み合計: {totalWeight.toFixed(1)}%
              </Badge>
            </HStack>
          </HStack>
        </CardHeader>

        <CardBody>
          <VStack spacing={6} align="stretch">
            
            {/* 重み調整ツール */}
            {matrix.criteria.length > 0 && (
              <Card bg="blue.50" borderColor="blue.200" borderWidth="1px">
                <CardBody>
                  <HStack justify="space-between">
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="medium">重み調整</Text>
                      <Progress
                        value={totalWeight}
                        max={100}
                        colorScheme={isWeightValid ? 'green' : 'red'}
                        size="sm"
                        w="200px"
                      />
                    </VStack>
                    
                    <HStack>
                      <Button
                        size="sm"
                        leftIcon={<FiPercent />}
                        onClick={handleNormalizeWeights}
                        variant="outline"
                      >
                        均等分割
                      </Button>
                    </HStack>
                  </HStack>
                </CardBody>
              </Card>
            )}

            {/* 評価基準一覧 */}
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between">
                <Text fontWeight="medium" fontSize="md">
                  評価基準 ({matrix.criteria.length}項目)
                </Text>
                
                <HStack>
                  <Button
                    size="sm"
                    leftIcon={<AddIcon />}
                    onClick={onTemplateOpen}
                    variant="outline"
                    colorScheme="blue"
                  >
                    テンプレート
                  </Button>
                  <Button
                    size="sm"
                    leftIcon={<AddIcon />}
                    onClick={handleAddCriterion}
                    colorScheme="blue"
                  >
                    カスタム追加
                  </Button>
                </HStack>
              </HStack>

              {/* 評価基準カード */}
              <AnimatePresence>
                {matrix.criteria.map((criterion, index) => (
                  <MotionCard
                    key={criterion.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    borderWidth="1px"
                    borderColor={borderColor}
                  >
                    <CardBody>
                      <HStack justify="space-between" align="start">
                        <VStack align="start" spacing={2} flex={1}>
                          <HStack>
                            <Badge colorScheme="purple" size="sm">
                              {criterion.category}
                            </Badge>
                            <Text fontWeight="medium">
                              {criterion.name || '未設定'}
                            </Text>
                            {criterion.isRequired && (
                              <Badge colorScheme="red" size="sm">必須</Badge>
                            )}
                          </HStack>
                          
                          <Text fontSize="sm" color="gray.600">
                            {criterion.description || '説明未設定'}
                          </Text>
                          
                          <HStack spacing={4}>
                            <HStack>
                              <Text fontSize="sm" color="gray.500">重み:</Text>
                              <Text fontSize="sm" fontWeight="medium">
                                {criterion.weight}%
                              </Text>
                            </HStack>
                            <HStack>
                              <Text fontSize="sm" color="gray.500">最大スコア:</Text>
                              <Text fontSize="sm" fontWeight="medium">
                                {criterion.maxScore}
                              </Text>
                            </HStack>
                            <HStack>
                              <Text fontSize="sm" color="gray.500">タイプ:</Text>
                              <Text fontSize="sm" fontWeight="medium">
                                {criterion.scoreType}
                              </Text>
                            </HStack>
                          </HStack>
                        </VStack>
                        
                        <HStack>
                          <Tooltip label="編集">
                            <IconButton
                              aria-label="編集"
                              icon={<EditIcon />}
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setEditingCriterion(criterion);
                                onCriterionOpen();
                              }}
                            />
                          </Tooltip>
                          <Tooltip label="削除">
                            <IconButton
                              aria-label="削除"
                              icon={<DeleteIcon />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => handleDeleteCriterion(criterion.id)}
                            />
                          </Tooltip>
                        </HStack>
                      </HStack>
                    </CardBody>
                  </MotionCard>
                ))}
              </AnimatePresence>

              {matrix.criteria.length === 0 && (
                <Card borderWidth="2px" borderStyle="dashed" borderColor="gray.300">
                  <CardBody textAlign="center" py={8}>
                    <VStack spacing={3}>
                      <FiTarget size={32} color="gray" />
                      <Text color="gray.500">
                        評価基準が設定されていません
                      </Text>
                      <Text fontSize="sm" color="gray.400">
                        テンプレートから追加するか、カスタム基準を作成してください
                      </Text>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </VStack>

            {/* プロセス設定 */}
            <Divider />
            
            <VStack spacing={4} align="stretch">
              <Text fontWeight="medium" fontSize="md">
                評価プロセス設定
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl>
                  <FormLabel>合格閾値</FormLabel>
                  <HStack>
                    <Slider
                      value={matrix.passingThreshold}
                      onChange={(value) => setMatrix(prev => ({ 
                        ...prev, 
                        passingThreshold: value 
                      }))}
                      min={0}
                      max={100}
                      step={5}
                      flex={1}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <SliderThumb />
                    </Slider>
                    <Text w="60px" textAlign="right">
                      {matrix.passingThreshold}%
                    </Text>
                  </HStack>
                  <FormHelperText>
                    候補者が合格となる最低スコア
                  </FormHelperText>
                </FormControl>

                <FormControl>
                  <FormLabel>集計方法</FormLabel>
                  <Select
                    value={matrix.aggregationMethod}
                    onChange={(e) => setMatrix(prev => ({ 
                      ...prev, 
                      aggregationMethod: e.target.value as any 
                    }))}
                  >
                    <option value="weighted_average">重み付き平均</option>
                    <option value="simple_average">単純平均</option>
                    <option value="custom">カスタム式</option>
                  </Select>
                  <FormHelperText>
                    複数評価基準のスコア集計方法
                  </FormHelperText>
                </FormControl>
              </SimpleGrid>

              <FormControl>
                <HStack justify="space-between">
                  <VStack align="start" spacing={1}>
                    <FormLabel mb={0}>スコア正規化</FormLabel>
                    <Text fontSize="sm" color="gray.600">
                      候補者間でのスコア比較を公平にする
                    </Text>
                  </VStack>
                  <Switch
                    isChecked={matrix.normalization.enabled}
                    onChange={(e) => setMatrix(prev => ({
                      ...prev,
                      normalization: {
                        ...prev.normalization,
                        enabled: e.target.checked
                      }
                    }))}
                  />
                </HStack>
              </FormControl>
            </VStack>

            {/* 警告メッセージ */}
            {!isWeightValid && (
              <Alert status="warning">
                <AlertIcon />
                <AlertDescription>
                  評価基準の重みの合計が100%ではありません。
                  保存前に調整してください。
                </AlertDescription>
              </Alert>
            )}

            {/* アクションボタン */}
            <HStack justify="flex-end" pt={4}>
              <Button variant="outline" onClick={onCancel}>
                キャンセル
              </Button>
              <Button
                colorScheme="blue"
                onClick={handleSave}
                isDisabled={!isWeightValid || matrix.criteria.length === 0}
                leftIcon={<CheckCircleIcon />}
              >
                設定を保存
              </Button>
            </HStack>
          </VStack>
        </CardBody>
      </Card>

      {/* テンプレート選択モーダル */}
      <Modal isOpen={isTemplateOpen} onClose={onTemplateClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>評価基準テンプレート</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>テンプレート選択</FormLabel>
                <Select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  placeholder="テンプレートを選択してください"
                >
                  <option value="technical">技術面接</option>
                  <option value="communication">コミュニケーション</option>
                  <option value="cultural_fit">カルチャーフィット</option>
                </Select>
              </FormControl>

              {selectedTemplate && (
                <Card bg="gray.50">
                  <CardBody>
                    <Text fontWeight="medium" mb={2}>
                      含まれる評価基準:
                    </Text>
                    <VStack align="start" spacing={1}>
                      {defaultCriteriaTemplates[selectedTemplate]?.map((template, index) => (
                        <HStack key={index}>
                          <Badge colorScheme="blue" size="sm">{template.weight}%</Badge>
                          <Text fontSize="sm">{template.name}</Text>
                        </HStack>
                      ))}
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onTemplateClose}>
              キャンセル
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleAddFromTemplate}
              isDisabled={!selectedTemplate}
            >
              追加
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 評価基準編集モーダル */}
      {editingCriterion && (
        <CriterionEditModal
          isOpen={isCriterionOpen}
          onClose={onCriterionClose}
          criterion={editingCriterion}
          onSave={handleSaveCriterion}
        />
      )}
    </MotionBox>
  );
};

// 評価基準編集モーダルコンポーネント
interface CriterionEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  criterion: EvaluationCriterion;
  onSave: (criterion: EvaluationCriterion) => void;
}

const CriterionEditModal: React.FC<CriterionEditModalProps> = ({
  isOpen,
  onClose,
  criterion,
  onSave
}) => {
  const [editedCriterion, setEditedCriterion] = useState<EvaluationCriterion>(criterion);

  const handleSave = useCallback(() => {
    onSave(editedCriterion);
    onClose();
  }, [editedCriterion, onSave, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl">
      <ModalOverlay />
      <ModalContent maxH="90vh">
        <ModalHeader>評価基準の詳細設定</ModalHeader>
        <ModalCloseButton />
        <ModalBody overflow="auto">
          <VStack spacing={6} align="stretch">
            {/* 基本情報 */}
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <FormControl isRequired>
                <FormLabel>評価基準名</FormLabel>
                <Input
                  value={editedCriterion.name}
                  onChange={(e) => setEditedCriterion(prev => ({
                    ...prev,
                    name: e.target.value
                  }))}
                  placeholder="例: 技術知識"
                />
              </FormControl>

              <FormControl>
                <FormLabel>カテゴリ</FormLabel>
                <Select
                  value={editedCriterion.category}
                  onChange={(e) => setEditedCriterion(prev => ({
                    ...prev,
                    category: e.target.value as any
                  }))}
                >
                  <option value="technical">技術</option>
                  <option value="communication">コミュニケーション</option>
                  <option value="cultural_fit">カルチャーフィット</option>
                  <option value="leadership">リーダーシップ</option>
                  <option value="custom">カスタム</option>
                </Select>
              </FormControl>
            </SimpleGrid>

            <FormControl>
              <FormLabel>説明</FormLabel>
              <Textarea
                value={editedCriterion.description}
                onChange={(e) => setEditedCriterion(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                placeholder="この評価基準で何を測定するかを説明してください"
                rows={3}
              />
            </FormControl>

            {/* スコア設定 */}
            <Divider />
            <Text fontWeight="medium">スコア設定</Text>
            
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
              <FormControl>
                <FormLabel>重み (%)</FormLabel>
                <NumberInput
                  value={editedCriterion.weight}
                  onChange={(_, value) => setEditedCriterion(prev => ({
                    ...prev,
                    weight: value || 0
                  }))}
                  min={0}
                  max={100}
                  step={1}
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel>最大スコア</FormLabel>
                <NumberInput
                  value={editedCriterion.maxScore}
                  onChange={(_, value) => setEditedCriterion(prev => ({
                    ...prev,
                    maxScore: value || 5
                  }))}
                  min={1}
                  max={10}
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>

              <FormControl>
                <FormLabel>スコアタイプ</FormLabel>
                <Select
                  value={editedCriterion.scoreType}
                  onChange={(e) => setEditedCriterion(prev => ({
                    ...prev,
                    scoreType: e.target.value as any
                  }))}
                >
                  <option value="numeric">数値</option>
                  <option value="rating">評価</option>
                  <option value="boolean">Yes/No</option>
                  <option value="text">テキスト</option>
                </Select>
              </FormControl>
            </SimpleGrid>

            {/* 評価ルーブリック */}
            <Divider />
            <Text fontWeight="medium">評価ルーブリック</Text>
            
            <Accordion allowToggle>
              {Object.entries(editedCriterion.rubric).map(([level, rubric]) => (
                <AccordionItem key={level}>
                  <AccordionButton>
                    <Box flex="1" textAlign="left">
                      <HStack>
                        <Badge 
                          colorScheme={
                            level === 'excellent' ? 'green' : 
                            level === 'good' ? 'blue' : 
                            level === 'satisfactory' ? 'yellow' : 'red'
                          }
                        >
                          {level === 'excellent' ? '優秀' :
                           level === 'good' ? '良好' :
                           level === 'satisfactory' ? '普通' : '要改善'}
                        </Badge>
                        <Text>スコア: {rubric.score}</Text>
                      </HStack>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel>
                    <VStack spacing={3} align="stretch">
                      <FormControl>
                        <FormLabel>説明</FormLabel>
                        <Textarea
                          value={rubric.description}
                          onChange={(e) => setEditedCriterion(prev => ({
                            ...prev,
                            rubric: {
                              ...prev.rubric,
                              [level]: {
                                ...prev.rubric[level as keyof typeof prev.rubric],
                                description: e.target.value
                              }
                            }
                          }))}
                          placeholder="このレベルの評価基準を説明してください"
                          rows={2}
                        />
                      </FormControl>
                    </VStack>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>

            {/* 必須フラグ */}
            <FormControl>
              <HStack justify="space-between">
                <VStack align="start" spacing={1}>
                  <FormLabel mb={0}>必須評価項目</FormLabel>
                  <Text fontSize="sm" color="gray.600">
                    この項目での評価が必須かどうか
                  </Text>
                </VStack>
                <Switch
                  isChecked={editedCriterion.isRequired}
                  onChange={(e) => setEditedCriterion(prev => ({
                    ...prev,
                    isRequired: e.target.checked
                  }))}
                />
              </HStack>
            </FormControl>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="outline" mr={3} onClick={onClose}>
            キャンセル
          </Button>
          <Button colorScheme="blue" onClick={handleSave}>
            保存
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default EvaluationCriteriaEditor;