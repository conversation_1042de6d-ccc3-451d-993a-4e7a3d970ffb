/**
 * 企業情報・求人票・履歴書アップロード機能
 * PDF、DOC、DOCX ファイルのアップロードとプレビュー
 * 求職者プロファイル機能対応（履歴書解析統合）
 */

import { useState, useRef } from 'react';
import {
  Box,
  IconButton,
  Progress,
  useToast,
  Text,
  Badge,
  Divider,
  List,
  ListItem,
  Flex,
  Spinner,
  HStack,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import { AttachmentIcon, DeleteIcon, DownloadIcon, InfoIcon, CheckCircleIcon, WarningIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, slideInFromBottom } from '../motion/index';
import ResumeParsingService from '@mensetsu-kun/shared/services/resumeParsingService';
import type { 
  ResumeFile, 
  ParsedResumeContent, 
  ResumeParsingResponse 
} from '@mensetsu-kun/shared/types/candidate-profile';
import { 
  UnifiedPageTitle,
  UnifiedAlert,
  UnifiedButton,
  UnifiedFormField,
  UnifiedCard,
  UnifiedContainer
} from './shared/UnifiedComponents';

const MotionBox = motion(Box);

// 文書の種類を定義
export type DocumentType = 'company' | 'resume';

interface UploadedDocument {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
  url: string;
  category: 'company-info' | 'job-description' | 'resume' | 'other';
  // 履歴書専用フィールド
  resumeData?: {
    isProcessed: boolean;
    processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
    parsedContent?: ParsedResumeContent;
    processingError?: string;
    confidence?: number;
    warnings?: string[];
  };
}

interface DocumentUploaderProps {
  documentType?: DocumentType;
  onDocumentsChange?: (documents: UploadedDocument[]) => void;
  onResumeProcessed?: (resumeFile: ResumeFile, parsedContent: ParsedResumeContent) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  initialDocuments?: UploadedDocument[];
}

export const DocumentUploader: React.FC<DocumentUploaderProps> = ({
  documentType = 'company',
  onDocumentsChange,
  onResumeProcessed,
  maxFiles = 5,
  acceptedTypes = ['.pdf', '.doc', '.docx', '.txt'],
  initialDocuments = []
}) => {
  const [documents, setDocuments] = useState<UploadedDocument[]>(initialDocuments);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [resumeParsingService] = useState(() => new ResumeParsingService());
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const infoBg = useColorModeValue('blue.50', 'blue.900');

  /**
   * ファイルサイズをフォーマット
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  /**
   * ファイルタイプを判定
   */
  const categorizeFile = (fileName: string): UploadedDocument['category'] => {
    if (documentType === 'resume') {
      return 'resume';
    }
    
    const name = fileName.toLowerCase();
    if (name.includes('会社') || name.includes('企業') || name.includes('company')) {
      return 'company-info';
    }
    if (name.includes('求人') || name.includes('job') || name.includes('募集')) {
      return 'job-description';
    }
    return 'other';
  };

  /**
   * 履歴書解析処理
   */
  const processResumeFile = async (document: UploadedDocument): Promise<UploadedDocument> => {
    if (documentType !== 'resume') return document;

    const resumeFile: ResumeFile = {
      id: document.id,
      fileName: document.fileName,
      fileSize: document.fileSize,
      fileType: document.fileType.split('/')[1] as 'pdf' | 'doc' | 'docx',
      uploadedAt: document.uploadedAt,
      filePath: document.url,
      isProcessed: false,
      processingStatus: 'pending',
    };

    try {
      // 解析処理開始
      const updatedDocument = {
        ...document,
        resumeData: {
          isProcessed: false,
          processingStatus: 'processing' as const,
        }
      };
      
      // リアルタイム更新
      setDocuments(prev => 
        prev.map(doc => doc.id === document.id ? updatedDocument : doc)
      );

      // 実際の解析処理
      const parseResult: ResumeParsingResponse = await resumeParsingService.parseResumeFile(resumeFile);

      if (parseResult.success && parseResult.parsedContent) {
        const processedDocument = {
          ...document,
          resumeData: {
            isProcessed: true,
            processingStatus: 'completed' as const,
            parsedContent: parseResult.parsedContent,
            confidence: parseResult.confidence,
            warnings: parseResult.warnings,
          }
        };

        // 解析完了コールバック
        onResumeProcessed?.(resumeFile, parseResult.parsedContent);

        toast({
          title: '履歴書解析完了',
          description: `信頼度: ${Math.round(parseResult.confidence * 100)}%`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        return processedDocument;
      } else {
        const errorDocument = {
          ...document,
          resumeData: {
            isProcessed: false,
            processingStatus: 'failed' as const,
            processingError: parseResult.errors?.join(', ') || '解析に失敗しました',
          }
        };

        toast({
          title: '履歴書解析エラー',
          description: parseResult.errors?.join(', ') || '解析に失敗しました',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });

        return errorDocument;
      }
    } catch (error) {
      console.error('履歴書解析エラー:', error);
      
      const errorDocument = {
        ...document,
        resumeData: {
          isProcessed: false,
          processingStatus: 'failed' as const,
          processingError: error instanceof Error ? error.message : '解析に失敗しました',
        }
      };

      toast({
        title: '履歴書解析エラー',
        description: '解析処理中にエラーが発生しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      return errorDocument;
    }
  };

  /**
   * ファイルアップロード処理
   */
  const handleFileUpload = async (files: FileList) => {
    if (documents.length + files.length > maxFiles) {
      toast({
        title: 'アップロード制限',
        description: `最大${maxFiles}ファイルまでアップロード可能です`,
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const newDocuments: UploadedDocument[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // ファイルサイズチェック（10MB制限）
        if (file.size > 10 * 1024 * 1024) {
          toast({
            title: 'ファイルサイズエラー',
            description: `${file.name}は10MBを超えています`,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
          continue;
        }

        // 進捗更新
        setUploadProgress(((i + 1) / files.length) * 100);

        // 実際のアップロード処理をシミュレート
        await new Promise(resolve => setTimeout(resolve, 1000));

        const newDocument: UploadedDocument = {
          id: `doc_${Date.now()}_${i}`,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type || 'application/octet-stream',
          uploadedAt: new Date().toISOString(),
          url: URL.createObjectURL(file), // 実際は S3 URL等
          category: categorizeFile(file.name),
          ...(documentType === 'resume' && {
            resumeData: {
              isProcessed: false,
              processingStatus: 'pending' as const,
            }
          })
        };

        newDocuments.push(newDocument);
      }

      const updatedDocuments = [...documents, ...newDocuments];
      setDocuments(updatedDocuments);
      onDocumentsChange?.(updatedDocuments);

      toast({
        title: 'アップロード完了',
        description: `${newDocuments.length}ファイルのアップロードが完了しました`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // 履歴書の場合は解析処理を開始
      if (documentType === 'resume') {
        for (const document of newDocuments) {
          try {
            const processedDocument = await processResumeFile(document);
            setDocuments(prev => 
              prev.map(doc => doc.id === document.id ? processedDocument : doc)
            );
          } catch (error) {
            console.error('履歴書解析エラー:', error);
          }
        }
      }

    } catch (error) {
      console.error('ファイルアップロードエラー:', error);
      toast({
        title: 'アップロードエラー',
        description: 'ファイルのアップロードに失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  /**
   * ファイル削除
   */
  const handleDeleteDocument = (documentId: string) => {
    const updatedDocuments = documents.filter(doc => doc.id !== documentId);
    setDocuments(updatedDocuments);
    onDocumentsChange?.(updatedDocuments);

    toast({
      title: 'ファイルを削除しました',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  /**
   * ファイル選択ダイアログを開く
   */
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  /**
   * カテゴリ別ラベル
   */
  const getCategoryLabel = (category: UploadedDocument['category']) => {
    switch (category) {
      case 'company-info': return '企業情報';
      case 'job-description': return '求人票';
      case 'resume': return '履歴書';
      default: return 'その他';
    }
  };

  const getCategoryColor = (category: UploadedDocument['category']) => {
    switch (category) {
      case 'company-info': return 'blue';
      case 'job-description': return 'green';
      case 'resume': return 'purple';
      default: return 'gray';
    }
  };

  /**
   * 履歴書解析状況の表示
   */
  const renderResumeStatus = (document: UploadedDocument) => {
    if (!document.resumeData) return null;

    const { processingStatus, confidence, warnings } = document.resumeData;

    switch (processingStatus) {
      case 'processing':
        return (
          <HStack spacing={2}>
            <Spinner size="xs" color="blue.500" />
            <Text fontSize="xs" color="blue.600">解析中...</Text>
          </HStack>
        );
      
      case 'completed':
        return (
          <VStack align="start" spacing={1}>
            <HStack spacing={2}>
              <CheckCircleIcon color="green.500" boxSize={3} />
              <Text fontSize="xs" color="green.600">解析完了</Text>
              {confidence && (
                <Badge colorScheme="green" fontSize="2xs">
                  {Math.round(confidence * 100)}%
                </Badge>
              )}
            </HStack>
            {warnings && warnings.length > 0 && (
              <HStack spacing={1}>
                <WarningIcon color="orange.500" boxSize={3} />
                <Text fontSize="2xs" color="orange.600">
                  {warnings.length}件の警告
                </Text>
              </HStack>
            )}
          </VStack>
        );
      
      case 'failed':
        return (
          <HStack spacing={2}>
            <WarningIcon color="red.500" boxSize={3} />
            <Text fontSize="xs" color="red.600">解析失敗</Text>
          </HStack>
        );
      
      default:
        return (
          <Text fontSize="xs" color="gray.500">解析待機中</Text>
        );
    }
  };

  return (
    <UnifiedContainer width="100%">
      <UnifiedPageTitle 
        title={documentType === 'resume' ? '履歴書・職務経歴書アップロード' : '企業情報・求人票アップロード'}
        subtitle="ファイルをアップロードしてAI面接システムで活用"
      />
      
      <UnifiedAlert
        title={documentType === 'resume' ? '履歴書アップロード' : 'アップロード可能ファイル'}
        description={documentType === 'resume' 
          ? 'PDF、Word文書（DOC/DOCX）の履歴書・職務経歴書をアップロードすると自動解析されます'
          : `PDF、Word文書（DOC/DOCX）、テキストファイル（最大10MB、${maxFiles}ファイルまで）`
        }
        status="info"
      />

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(',')}
        onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
        style={{ display: 'none' }}
      />
      
      <UnifiedButton
        onClick={openFileDialog}
        leftIcon={<AttachmentIcon />}
        colorScheme={documentType === 'resume' ? 'purple' : 'blue'}
        variant="outline"
        size="lg"
        isDisabled={isUploading || documents.length >= maxFiles}
        width="100%"
      >
        {documentType === 'resume' ? '履歴書を選択' : 'ファイルを選択'}
      </UnifiedButton>

      <AnimatePresence>
        {isUploading && (
          <MotionBox
            {...slideInFromBottom}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <UnifiedCard width="100%" variant="filled">
              <VStack spacing={2} width="100%">
                <Text fontSize="md" fontWeight="medium">アップロード中...</Text>
                <Progress value={uploadProgress} colorScheme="blue" w="full" />
              </VStack>
            </UnifiedCard>
          </MotionBox>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {documents.length > 0 && (
          <MotionBox
            variants={slideInFromBottom}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ delay: 0.2 }}
          >
            <VStack spacing={4} align="stretch" width="100%">
              <Divider />
              
              <Text fontSize="lg" fontWeight="bold">
                アップロード済みファイル ({documents.length}/{maxFiles})
              </Text>

                <List spacing={3}>
                  {documents.map((doc, index) => (
                    <MotionBox
                      key={doc.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <ListItem
                        p={spacing.cardPadding.base}
                        bg={hoverBg}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={borderColor}
                      >
                        <Flex justify="space-between" align="center">
                          <VStack align="start" spacing={1} flex={1}>
                            <HStack>
                              <Badge 
                                colorScheme={getCategoryColor(doc.category)}
                                size="sm"
                              >
                                {getCategoryLabel(doc.category)}
                              </Badge>
                              <Text {...textStyles.body} fontWeight="bold">
                                {doc.fileName}
                              </Text>
                            </HStack>
                            
                            <HStack spacing={4}>
                              <Text {...textStyles.caption}>
                                {formatFileSize(doc.fileSize)}
                              </Text>
                              <Text {...textStyles.caption}>
                                {new Date(doc.uploadedAt).toLocaleString('ja-JP')}
                              </Text>
                            </HStack>
                            
                            {/* 履歴書解析状況 */}
                            {doc.category === 'resume' && renderResumeStatus(doc)}
                          </VStack>

                          <HStack spacing={2}>
                            <IconButton
                              aria-label="ファイルをダウンロード"
                              icon={<DownloadIcon />}
                              size="sm"
                              variant="ghost"
                              onClick={() => window.open(doc.url, '_blank')}
                            />
                            <IconButton
                              aria-label="ファイルを削除"
                              icon={<DeleteIcon />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => handleDeleteDocument(doc.id)}
                            />
                          </HStack>
                        </Flex>
                      </ListItem>
                    </MotionBox>
                  ))}
                </List>

              <UnifiedAlert
                title="ファイル活用方法"
                description={documentType === 'resume' 
                  ? 'アップロードされた履歴書は自動解析され、個人化された面接質問の生成に活用されます'
                  : 'アップロードされたファイルはAI面接システムが参照し、企業に特化した質問を生成します'
                }
                status="info"
              />
            </VStack>
          </MotionBox>
        )}
      </AnimatePresence>
    </UnifiedContainer>
  );
};