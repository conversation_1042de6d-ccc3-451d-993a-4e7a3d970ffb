'use client';

/**
 * 録画設定の詳細編集コンポーネント
 * 品質、プライバシー、保存期間、アクセス制御の設定
 */
import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Text,
  FormControl,
  FormLabel,
  FormHelperText,
  Select,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Textarea,
  Badge,
  Divider,
  useColorModeValue,
  useToast,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Tooltip,
  Icon,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Checkbox,
  CheckboxGroup,
  Stack,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  SliderMark,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  CheckCircleIcon,
  WarningIcon,
  InfoIcon,
  LockIcon,
  UnlockIcon,
} from '@chakra-ui/icons';
import {
  FiVideo,
  FiMic,
  FiMonitor,
  FiShield,
  FiClock,
  FiUsers,
  FiDownload,
  FiTrash2,
  FiSettings,
  FiAlertTriangle,
} from 'react-icons/fi';
import { RecordingSettings } from '@mensetsu-kun/shared/types/evaluation';

const MotionBox = motion(Box);

interface RecordingSettingsEditorProps {
  stepId: string;
  stepName: string;
  currentSettings?: RecordingSettings;
  onSave: (settings: RecordingSettings) => void;
  onCancel: () => void;
}

const RecordingSettingsEditor: React.FC<RecordingSettingsEditorProps> = ({
  stepId,
  stepName,
  currentSettings,
  onSave,
  onCancel
}) => {
  const [settings, setSettings] = useState<RecordingSettings>(
    currentSettings || {
      id: `recording_${stepId}`,
      stepId,
      videoEnabled: true,
      audioEnabled: true,
      screenRecordingEnabled: false,
      videoQuality: '720p',
      audioQuality: 'high',
      frameRate: 30,
      retentionPeriod: 90,
      autoDeleteAfterPeriod: true,
      compressionEnabled: true,
      privacySettings: {
        requireConsent: true,
        consentText: '面接の録画・録音について同意いただき、品質向上と公正な評価のために使用させていただきます。',
        allowCandidateDownload: false,
        allowCandidateDelete: false,
        maskSensitiveInfo: true,
      },
      accessControl: {
        viewableByRoles: ['interviewer', 'hr'],
        downloadableByRoles: ['hr'],
        editableByRoles: ['hr'],
        shareable: false,
      },
      notifications: {
        notifyOnRecordingStart: true,
        notifyOnRecordingEnd: true,
        notifyOnAccessAttempt: true,
      },
    }
  );

  const toast = useToast();
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 品質レベルのメタデータ
  const qualityMetadata = {
    video: {
      '480p': { label: '480p (標準)', bandwidth: '低', storage: '少', description: '基本的な録画品質' },
      '720p': { label: '720p (HD)', bandwidth: '中', storage: '中', description: '推奨品質レベル' },
      '1080p': { label: '1080p (Full HD)', bandwidth: '高', storage: '多', description: '高画質（大容量）' },
    },
    audio: {
      low: { label: '低品質', bandwidth: '16kbps', description: '音声が聞き取れる最低限の品質' },
      medium: { label: '標準品質', bandwidth: '64kbps', description: '通常の面接に適した品質' },
      high: { label: '高品質', bandwidth: '128kbps', description: '音声が重要な面接向け' },
    }
  };

  // 設定の保存
  const handleSave = useCallback(() => {
    // バリデーション
    if (settings.retentionPeriod < 1) {
      toast({
        title: '設定エラー',
        description: '保存期間は1日以上に設定してください',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    if (!settings.videoEnabled && !settings.audioEnabled) {
      toast({
        title: '設定エラー',
        description: '動画または音声のいずれかを有効にしてください',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    onSave(settings);
    
    toast({
      title: '録画設定を保存しました',
      description: '設定が正常に保存されました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  }, [settings, onSave, toast]);

  // 予想ストレージサイズの計算
  const calculateStorageSize = useCallback(() => {
    const baseSize = settings.videoEnabled ? 
      (settings.videoQuality === '1080p' ? 500 : settings.videoQuality === '720p' ? 200 : 100) : 0;
    const audioSize = settings.audioEnabled ? 20 : 0;
    const screenSize = settings.screenRecordingEnabled ? 100 : 0;
    const compressionFactor = settings.compressionEnabled ? 0.7 : 1;
    
    return Math.round((baseSize + audioSize + screenSize) * compressionFactor);
  }, [settings]);

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
        <CardHeader>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <HStack>
                <FiVideo />
                <Text fontSize="lg" fontWeight="bold">
                  録画設定 - {stepName}
                </Text>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                録画品質・プライバシー・保存期間・アクセス制御の設定
              </Text>
            </VStack>
            
            <HStack>
              <Badge colorScheme="blue" fontSize="sm" px={3} py={1}>
                予想サイズ: {calculateStorageSize()}MB/30分
              </Badge>
            </HStack>
          </HStack>
        </CardHeader>

        <CardBody>
          <VStack spacing={6} align="stretch">

            {/* 基本録画設定 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="medium" fontSize="md">
                録画対象
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                <Card variant="outline" bg={settings.videoEnabled ? 'blue.50' : 'gray.50'}>
                  <CardBody>
                    <VStack spacing={3}>
                      <HStack justify="space-between" w="full">
                        <HStack>
                          <Icon as={FiVideo} color={settings.videoEnabled ? 'blue.500' : 'gray.500'} />
                          <Text fontWeight="medium">動画録画</Text>
                        </HStack>
                        <Switch
                          isChecked={settings.videoEnabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            videoEnabled: e.target.checked
                          }))}
                          colorScheme="blue"
                        />
                      </HStack>
                      {settings.videoEnabled && (
                        <Text fontSize="sm" color="gray.600">
                          候補者の表情や仕草を記録
                        </Text>
                      )}
                    </VStack>
                  </CardBody>
                </Card>

                <Card variant="outline" bg={settings.audioEnabled ? 'green.50' : 'gray.50'}>
                  <CardBody>
                    <VStack spacing={3}>
                      <HStack justify="space-between" w="full">
                        <HStack>
                          <Icon as={FiMic} color={settings.audioEnabled ? 'green.500' : 'gray.500'} />
                          <Text fontWeight="medium">音声録音</Text>
                        </HStack>
                        <Switch
                          isChecked={settings.audioEnabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            audioEnabled: e.target.checked
                          }))}
                          colorScheme="green"
                        />
                      </HStack>
                      {settings.audioEnabled && (
                        <Text fontSize="sm" color="gray.600">
                          回答内容の音声を記録
                        </Text>
                      )}
                    </VStack>
                  </CardBody>
                </Card>

                <Card variant="outline" bg={settings.screenRecordingEnabled ? 'purple.50' : 'gray.50'}>
                  <CardBody>
                    <VStack spacing={3}>
                      <HStack justify="space-between" w="full">
                        <HStack>
                          <Icon as={FiMonitor} color={settings.screenRecordingEnabled ? 'purple.500' : 'gray.500'} />
                          <Text fontWeight="medium">画面録画</Text>
                        </HStack>
                        <Switch
                          isChecked={settings.screenRecordingEnabled}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            screenRecordingEnabled: e.target.checked
                          }))}
                          colorScheme="purple"
                        />
                      </HStack>
                      {settings.screenRecordingEnabled && (
                        <Text fontSize="sm" color="gray.600">
                          技術面接での画面共有録画
                        </Text>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </VStack>

            {/* 品質設定 */}
            <Divider />
            
            <VStack spacing={4} align="stretch">
              <Text fontWeight="medium" fontSize="md">
                品質設定
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                {settings.videoEnabled && (
                  <FormControl>
                    <FormLabel>動画品質</FormLabel>
                    <Select
                      value={settings.videoQuality}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        videoQuality: e.target.value as any
                      }))}
                    >
                      {Object.entries(qualityMetadata.video).map(([key, meta]) => (
                        <option key={key} value={key}>
                          {meta.label} - 帯域: {meta.bandwidth}
                        </option>
                      ))}
                    </Select>
                    <FormHelperText>
                      {qualityMetadata.video[settings.videoQuality]?.description}
                    </FormHelperText>
                  </FormControl>
                )}

                {settings.audioEnabled && (
                  <FormControl>
                    <FormLabel>音声品質</FormLabel>
                    <Select
                      value={settings.audioQuality}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        audioQuality: e.target.value as any
                      }))}
                    >
                      {Object.entries(qualityMetadata.audio).map(([key, meta]) => (
                        <option key={key} value={key}>
                          {meta.label} - {meta.bandwidth}
                        </option>
                      ))}
                    </Select>
                    <FormHelperText>
                      {qualityMetadata.audio[settings.audioQuality]?.description}
                    </FormHelperText>
                  </FormControl>
                )}

                {settings.videoEnabled && (
                  <FormControl>
                    <FormLabel>フレームレート</FormLabel>
                    <Select
                      value={settings.frameRate}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        frameRate: parseInt(e.target.value) as any
                      }))}
                    >
                      <option value={24}>24 fps (映画品質)</option>
                      <option value={30}>30 fps (標準)</option>
                      <option value={60}>60 fps (高品質)</option>
                    </Select>
                    <FormHelperText>
                      フレームレートが高いほど滑らかですが、容量が増加します
                    </FormHelperText>
                  </FormControl>
                )}

                <FormControl>
                  <HStack justify="space-between">
                    <VStack align="start" spacing={1}>
                      <FormLabel mb={0}>データ圧縮</FormLabel>
                      <Text fontSize="sm" color="gray.600">
                        ストレージ容量を節約
                      </Text>
                    </VStack>
                    <Switch
                      isChecked={settings.compressionEnabled}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        compressionEnabled: e.target.checked
                      }))}
                    />
                  </HStack>
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* データ管理設定 */}
            <Divider />
            
            <VStack spacing={4} align="stretch">
              <Text fontWeight="medium" fontSize="md">
                データ管理
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl>
                  <FormLabel>保存期間 (日)</FormLabel>
                  <NumberInput
                    value={settings.retentionPeriod}
                    onChange={(_, value) => setSettings(prev => ({
                      ...prev,
                      retentionPeriod: value || 90
                    }))}
                    min={1}
                    max={3650}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <FormHelperText>
                    録画データを保存する期間
                  </FormHelperText>
                </FormControl>

                <FormControl>
                  <HStack justify="space-between">
                    <VStack align="start" spacing={1}>
                      <FormLabel mb={0}>自動削除</FormLabel>
                      <Text fontSize="sm" color="gray.600">
                        保存期間後に自動的にデータを削除
                      </Text>
                    </VStack>
                    <Switch
                      isChecked={settings.autoDeleteAfterPeriod}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        autoDeleteAfterPeriod: e.target.checked
                      }))}
                    />
                  </HStack>
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* プライバシー設定 */}
            <Divider />
            
            <Accordion allowToggle>
              <AccordionItem>
                <AccordionButton>
                  <Box flex="1" textAlign="left">
                    <HStack>
                      <Icon as={FiShield} />
                      <Text fontWeight="medium">プライバシー設定</Text>
                      <Badge colorScheme="green">重要</Badge>
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel>
                  <VStack spacing={4} align="stretch">
                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>同意確認</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            録画前に候補者の同意を取得
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.privacySettings.requireConsent}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            privacySettings: {
                              ...prev.privacySettings,
                              requireConsent: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>

                    {settings.privacySettings.requireConsent && (
                      <FormControl>
                        <FormLabel>同意文言</FormLabel>
                        <Textarea
                          value={settings.privacySettings.consentText}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            privacySettings: {
                              ...prev.privacySettings,
                              consentText: e.target.value
                            }
                          }))}
                          rows={3}
                          placeholder="録画に関する同意文言を入力してください"
                        />
                      </FormControl>
                    )}

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                      <FormControl>
                        <HStack justify="space-between">
                          <VStack align="start" spacing={1}>
                            <FormLabel mb={0}>候補者ダウンロード許可</FormLabel>
                            <Text fontSize="sm" color="gray.600">
                              候補者が自分の録画をダウンロード可能
                            </Text>
                          </VStack>
                          <Switch
                            isChecked={settings.privacySettings.allowCandidateDownload}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              privacySettings: {
                                ...prev.privacySettings,
                                allowCandidateDownload: e.target.checked
                              }
                            }))}
                          />
                        </HStack>
                      </FormControl>

                      <FormControl>
                        <HStack justify="space-between">
                          <VStack align="start" spacing={1}>
                            <FormLabel mb={0}>候補者削除権</FormLabel>
                            <Text fontSize="sm" color="gray.600">
                              候補者が録画の削除を要求可能
                            </Text>
                          </VStack>
                          <Switch
                            isChecked={settings.privacySettings.allowCandidateDelete}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              privacySettings: {
                                ...prev.privacySettings,
                                allowCandidateDelete: e.target.checked
                              }
                            }))}
                          />
                        </HStack>
                      </FormControl>
                    </SimpleGrid>

                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>機密情報マスキング</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            画面上の機密情報を自動的にマスキング
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.privacySettings.maskSensitiveInfo}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            privacySettings: {
                              ...prev.privacySettings,
                              maskSensitiveInfo: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>
                  </VStack>
                </AccordionPanel>
              </AccordionItem>

              {/* アクセス制御 */}
              <AccordionItem>
                <AccordionButton>
                  <Box flex="1" textAlign="left">
                    <HStack>
                      <Icon as={FiUsers} />
                      <Text fontWeight="medium">アクセス制御</Text>
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel>
                  <VStack spacing={4} align="stretch">
                    <FormControl>
                      <FormLabel>閲覧権限</FormLabel>
                      <CheckboxGroup
                        value={settings.accessControl.viewableByRoles}
                        onChange={(value) => setSettings(prev => ({
                          ...prev,
                          accessControl: {
                            ...prev.accessControl,
                            viewableByRoles: value as string[]
                          }
                        }))}
                      >
                        <Stack direction="row" wrap="wrap">
                          <Checkbox value="interviewer">面接官</Checkbox>
                          <Checkbox value="hr">人事</Checkbox>
                          <Checkbox value="manager">管理者</Checkbox>
                          <Checkbox value="candidate">候補者</Checkbox>
                        </Stack>
                      </CheckboxGroup>
                    </FormControl>

                    <FormControl>
                      <FormLabel>ダウンロード権限</FormLabel>
                      <CheckboxGroup
                        value={settings.accessControl.downloadableByRoles}
                        onChange={(value) => setSettings(prev => ({
                          ...prev,
                          accessControl: {
                            ...prev.accessControl,
                            downloadableByRoles: value as string[]
                          }
                        }))}
                      >
                        <Stack direction="row" wrap="wrap">
                          <Checkbox value="hr">人事</Checkbox>
                          <Checkbox value="manager">管理者</Checkbox>
                        </Stack>
                      </CheckboxGroup>
                    </FormControl>

                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>外部共有</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            組織外との録画共有を許可
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.accessControl.shareable}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            accessControl: {
                              ...prev.accessControl,
                              shareable: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>
                  </VStack>
                </AccordionPanel>
              </AccordionItem>

              {/* 通知設定 */}
              <AccordionItem>
                <AccordionButton>
                  <Box flex="1" textAlign="left">
                    <HStack>
                      <Icon as={FiAlertTriangle} />
                      <Text fontWeight="medium">通知設定</Text>
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel>
                  <VStack spacing={4} align="stretch">
                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>録画開始通知</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            録画が開始されたときに通知
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.notifications.notifyOnRecordingStart}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              notifyOnRecordingStart: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>

                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>録画終了通知</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            録画が終了したときに通知
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.notifications.notifyOnRecordingEnd}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              notifyOnRecordingEnd: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>

                    <FormControl>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <FormLabel mb={0}>アクセス通知</FormLabel>
                          <Text fontSize="sm" color="gray.600">
                            録画にアクセスされたときに通知
                          </Text>
                        </VStack>
                        <Switch
                          isChecked={settings.notifications.notifyOnAccessAttempt}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            notifications: {
                              ...prev.notifications,
                              notifyOnAccessAttempt: e.target.checked
                            }
                          }))}
                        />
                      </HStack>
                    </FormControl>
                  </VStack>
                </AccordionPanel>
              </AccordionItem>
            </Accordion>

            {/* 警告メッセージ */}
            {(!settings.videoEnabled && !settings.audioEnabled) && (
              <Alert status="warning">
                <AlertIcon />
                <AlertDescription>
                  動画または音声のいずれかを有効にしてください。
                </AlertDescription>
              </Alert>
            )}

            {settings.retentionPeriod > 365 && (
              <Alert status="info">
                <AlertIcon />
                <AlertDescription>
                  長期間の保存設定です。データ容量とプライバシー規制にご注意ください。
                </AlertDescription>
              </Alert>
            )}

            {/* アクションボタン */}
            <HStack justify="flex-end" pt={4}>
              <Button variant="outline" onClick={onCancel}>
                キャンセル
              </Button>
              <Button
                colorScheme="blue"
                onClick={handleSave}
                leftIcon={<CheckCircleIcon />}
                isDisabled={!settings.videoEnabled && !settings.audioEnabled}
              >
                設定を保存
              </Button>
            </HStack>
          </VStack>
        </CardBody>
      </Card>
    </MotionBox>
  );
};

export default RecordingSettingsEditor;