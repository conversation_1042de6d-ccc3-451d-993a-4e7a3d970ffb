/**
 * 発行済みリンク管理コンポーネント
 * リンクの一覧表示・編集・企業追加削除・期限延長・期限切れ非表示
 */

import { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  useDisclosure,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Alert,
  AlertIcon,
  AlertDescription,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  List,
  ListItem,
  Switch,
} from '@chakra-ui/react';
import {
  EditIcon,
  DeleteIcon,
  TimeIcon,
  LinkIcon,
  WarningIcon,
  CheckCircleIcon,
  AddIcon,
  ViewIcon,
  CopyIcon,
  CalendarIcon,
} from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, slideInFromBottom } from '../motion/index';

const MotionBox = motion.create(Box);
const MotionCard = motion.create(Card);

interface InterviewLink {
  id: string;
  candidateName: string;
  candidateEmail?: string;
  companies: Array<{
    id: string;
    name: string;
    position: string;
    industry: string;
  }>;
  createdAt: string;
  expiresAt: string;
  status: 'active' | 'expired' | 'used';
  accessCount: number;
  lastAccessedAt?: string;
  url: string;
  agentNotes?: string;
  intentsCount: number;
  documentsCount: number;
}

interface LinkManagerProps {
  showExpiredLinks?: boolean;
}

export const LinkManager: React.FC<LinkManagerProps> = ({
  showExpiredLinks = false
}) => {
  const [links, setLinks] = useState<InterviewLink[]>([]);
  const [selectedLink, setSelectedLink] = useState<InterviewLink | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showExpired, setShowExpired] = useState(showExpiredLinks);
  const [editingCompany, setEditingCompany] = useState<any>(null);
  
  // 編集用のフォーム状態
  const [editCandidateName, setEditCandidateName] = useState('');
  const [editCandidateEmail, setEditCandidateEmail] = useState('');
  const [editExpiresAt, setEditExpiresAt] = useState('');
  const [editAgentNotes, setEditAgentNotes] = useState('');
  const [newCompanyName, setNewCompanyName] = useState('');
  const [newCompanyPosition, setNewCompanyPosition] = useState('');
  const [newCompanyIndustry, setNewCompanyIndustry] = useState('');

  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isCompanyOpen, onOpen: onCompanyOpen, onClose: onCompanyClose } = useDisclosure();
  
  const toast = useToast();
  
  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  // モックデータ（実際は API から取得）
  const mockLinks: InterviewLink[] = [
    {
      id: 'link_001',
      candidateName: '山田太郎',
      candidateEmail: '<EMAIL>',
      companies: [
        {
          id: 'comp_001',
          name: '株式会社テクノロジーアドバンス',
          position: 'シニアエンジニア',
          industry: 'IT・ソフトウェア'
        },
        {
          id: 'comp_002',
          name: 'スタートアップ A',
          position: 'フロントエンドエンジニア',
          industry: 'IT・ソフトウェア'
        }
      ],
      createdAt: '2025-06-10T10:00:00Z',
      expiresAt: '2025-06-17T23:59:59Z',
      status: 'active',
      accessCount: 3,
      lastAccessedAt: '2025-06-15T14:30:00Z',
      url: 'http://localhost:3003/interview-prep?linkId=link_001&status=active',
      agentNotes: '技術面接重視。アルゴリズムとシステム設計を重点的に練習してください。',
      intentsCount: 5,
      documentsCount: 3
    },
    {
      id: 'link_002',
      candidateName: '鈴木花子',
      candidateEmail: '<EMAIL>',
      companies: [
        {
          id: 'comp_003',
          name: 'グローバルコンサルティング株式会社',
          position: 'ビジネスアナリスト',
          industry: 'コンサルティング'
        }
      ],
      createdAt: '2025-06-08T15:30:00Z',
      expiresAt: '2025-06-16T23:59:59Z',
      status: 'expired',
      accessCount: 1,
      lastAccessedAt: '2025-06-12T09:15:00Z',
      url: 'http://localhost:3003/interview-prep?linkId=link_002&status=expired',
      agentNotes: 'ケース面接対策が必要。論理的思考と構造化スキルを強化してください。',
      intentsCount: 4,
      documentsCount: 2
    },
    {
      id: 'link_003',
      candidateName: '田中一郎',
      candidateEmail: '<EMAIL>',
      companies: [
        {
          id: 'comp_004',
          name: '伝統商社株式会社',
          position: '営業マネージャー',
          industry: '商社・貿易'
        }
      ],
      createdAt: '2025-06-12T11:00:00Z',
      expiresAt: '2025-06-19T23:59:59Z',
      status: 'active',
      accessCount: 0,
      url: 'http://localhost:3003/interview-prep?linkId=link_003&status=active',
      agentNotes: '高圧面接対策。ストレス耐性とコミュニケーション力を重視してください。',
      intentsCount: 3,
      documentsCount: 1
    }
  ];

  useEffect(() => {
    const loadLinks = async () => {
      setIsLoading(true);
      // API 呼び出しをシミュレート
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLinks(mockLinks);
      setIsLoading(false);
    };

    loadLinks();
  }, []);

  // フィルタリングされたリンク
  const filteredLinks = links.filter(link => {
    if (!showExpired && link.status === 'expired') {
      return false;
    }
    return true;
  }).sort((a, b) => {
    // 有効期限が近い順
    return new Date(a.expiresAt).getTime() - new Date(b.expiresAt).getTime();
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'expired': return 'red';
      case 'used': return 'blue';
      default: return 'gray';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return '有効';
      case 'expired': return '期限切れ';
      case 'used': return '使用済み';
      default: return '不明';
    }
  };

  const isExpiringSoon = (expiresAt: string) => {
    const expireTime = new Date(expiresAt).getTime();
    const nowTime = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    return expireTime - nowTime < oneDayMs && expireTime > nowTime;
  };

  const handleEditLink = (link: InterviewLink) => {
    setSelectedLink(link);
    setEditCandidateName(link.candidateName);
    setEditCandidateEmail(link.candidateEmail || '');
    setEditExpiresAt(new Date(link.expiresAt).toISOString().slice(0, 16));
    setEditAgentNotes(link.agentNotes || '');
    onEditOpen();
  };

  const handleSaveEdit = async () => {
    if (!selectedLink) return;

    setIsLoading(true);
    try {
      // API 呼び出しをシミュレート
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedLink = {
        ...selectedLink,
        candidateName: editCandidateName,
        candidateEmail: editCandidateEmail,
        expiresAt: new Date(editExpiresAt).toISOString(),
        agentNotes: editAgentNotes,
      };

      setLinks(prev => 
        prev.map(link => 
          link.id === selectedLink.id ? updatedLink : link
        )
      );

      toast({
        title: 'リンク更新完了',
        description: '面接リンクの情報が更新されました',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      onEditClose();
    } catch (error) {
      toast({
        title: '更新エラー',
        description: 'リンクの更新に失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCompany = async () => {
    if (!selectedLink || !newCompanyName.trim() || !newCompanyPosition.trim()) {
      toast({
        title: '入力エラー',
        description: '企業名とポジションは必須です',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const newCompany = {
      id: `comp_${Date.now()}`,
      name: newCompanyName,
      position: newCompanyPosition,
      industry: newCompanyIndustry || 'その他'
    };

    const updatedLink = {
      ...selectedLink,
      companies: [...selectedLink.companies, newCompany]
    };

    setLinks(prev =>
      prev.map(link =>
        link.id === selectedLink.id ? updatedLink : link
      )
    );

    setSelectedLink(updatedLink);
    setNewCompanyName('');
    setNewCompanyPosition('');
    setNewCompanyIndustry('');

    toast({
      title: '企業追加完了',
      description: `${newCompany.name} を追加しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  const handleRemoveCompany = (companyId: string) => {
    if (!selectedLink) return;

    const updatedLink = {
      ...selectedLink,
      companies: selectedLink.companies.filter(comp => comp.id !== companyId)
    };

    setLinks(prev =>
      prev.map(link =>
        link.id === selectedLink.id ? updatedLink : link
      )
    );

    setSelectedLink(updatedLink);

    toast({
      title: '企業削除完了',
      description: '企業を削除しました',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url);
    toast({
      title: 'リンクをコピーしました',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const handleExtendExpiry = (linkId: string, days: number) => {
    const newExpiry = new Date();
    newExpiry.setDate(newExpiry.getDate() + days);

    setLinks(prev =>
      prev.map(link =>
        link.id === linkId
          ? { ...link, expiresAt: newExpiry.toISOString(), status: 'active' }
          : link
      )
    );

    toast({
      title: '期限延長完了',
      description: `${days}日間延長しました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <MotionBox {...fadeIn}>
      <VStack spacing={6} align="stretch">
        {/* 統計情報 */}
        <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4}>
          <Stat bg={bgColor} p={4} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <StatLabel>総リンク数</StatLabel>
            <StatNumber color="blue.600">{links.length}</StatNumber>
            <StatHelpText>発行済み</StatHelpText>
          </Stat>
          <Stat bg={bgColor} p={4} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <StatLabel>有効リンク</StatLabel>
            <StatNumber color="green.600">{links.filter(l => l.status === 'active').length}</StatNumber>
            <StatHelpText>アクティブ</StatHelpText>
          </Stat>
          <Stat bg={bgColor} p={4} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <StatLabel>期限切れ</StatLabel>
            <StatNumber color="red.600">{links.filter(l => l.status === 'expired').length}</StatNumber>
            <StatHelpText>要対応</StatHelpText>
          </Stat>
          <Stat bg={bgColor} p={4} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
            <StatLabel>総アクセス数</StatLabel>
            <StatNumber color="purple.600">{links.reduce((sum, l) => sum + l.accessCount, 0)}</StatNumber>
            <StatHelpText>累計</StatHelpText>
          </Stat>
        </SimpleGrid>

        {/* フィルター */}
        <Card>
          <CardBody>
            <HStack justify="space-between">
              <HStack spacing={4}>
                <Text fontWeight="bold">表示設定:</Text>
                <HStack>
                  <Switch
                    isChecked={showExpired}
                    onChange={(e) => setShowExpired(e.target.checked)}
                    colorScheme="blue"
                  />
                  <Text fontSize="sm">期限切れリンクも表示</Text>
                </HStack>
              </HStack>
              <Badge colorScheme="blue" fontSize="sm" px={3} py={1}>
                {filteredLinks.length} 件表示中
              </Badge>
            </HStack>
          </CardBody>
        </Card>

        {/* リンク一覧テーブル */}
        <Card>
          <CardHeader>
            <Heading size="md">📋 発行済みリンク一覧</Heading>
          </CardHeader>
          <CardBody>
            {isLoading ? (
              <Text>読み込み中...</Text>
            ) : filteredLinks.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                <AlertDescription>
                  {showExpired ? '発行済みリンクがありません' : '有効なリンクがありません'}
                </AlertDescription>
              </Alert>
            ) : (
              <Box overflowX="auto">
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th>候補者</Th>
                      <Th>対象企業</Th>
                      <Th>ステータス</Th>
                      <Th>有効期限</Th>
                      <Th>アクセス数</Th>
                      <Th>操作</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredLinks.map((link) => (
                      <Tr key={link.id} _hover={{ bg: hoverBg }}>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="bold" fontSize="sm">{link.candidateName}</Text>
                            {link.candidateEmail && (
                              <Text fontSize="xs" color="gray.500">{link.candidateEmail}</Text>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <VStack align="start" spacing={1}>
                            {link.companies.slice(0, 2).map((company) => (
                              <Badge key={company.id} variant="outline" fontSize="xs">
                                {company.name}
                              </Badge>
                            ))}
                            {link.companies.length > 2 && (
                              <Text fontSize="xs" color="gray.500">
                                +{link.companies.length - 2}社
                              </Text>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Badge colorScheme={getStatusColor(link.status)} size="sm">
                              {getStatusLabel(link.status)}
                            </Badge>
                            {isExpiringSoon(link.expiresAt) && (
                              <Badge colorScheme="orange" size="sm">
                                期限間近
                              </Badge>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <Text fontSize="xs">
                            {new Date(link.expiresAt).toLocaleDateString('ja-JP')}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {new Date(link.expiresAt).toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' })}
                          </Text>
                        </Td>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <Text fontSize="sm">{link.accessCount}回</Text>
                            {link.lastAccessedAt && (
                              <Text fontSize="xs" color="gray.500">
                                最終: {new Date(link.lastAccessedAt).toLocaleDateString('ja-JP')}
                              </Text>
                            )}
                          </VStack>
                        </Td>
                        <Td>
                          <HStack spacing={1}>
                            <IconButton
                              aria-label="編集"
                              icon={<EditIcon />}
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEditLink(link)}
                            />
                            <IconButton
                              aria-label="リンクコピー"
                              icon={<CopyIcon />}
                              size="sm"
                              variant="ghost"
                              onClick={() => handleCopyLink(link.url)}
                            />
                            {link.status === 'expired' && (
                              <IconButton
                                aria-label="期限延長"
                                icon={<CalendarIcon />}
                                size="sm"
                                variant="ghost"
                                colorScheme="green"
                                onClick={() => handleExtendExpiry(link.id, 7)}
                              />
                            )}
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </CardBody>
        </Card>

        {/* 編集モーダル */}
        <Modal isOpen={isEditOpen} onClose={onEditClose} size="xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>リンク編集</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4} align="stretch">
                <FormControl isRequired>
                  <FormLabel>候補者名</FormLabel>
                  <Input
                    value={editCandidateName}
                    onChange={(e) => setEditCandidateName(e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>メールアドレス</FormLabel>
                  <Input
                    type="email"
                    value={editCandidateEmail}
                    onChange={(e) => setEditCandidateEmail(e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>有効期限</FormLabel>
                  <Input
                    type="datetime-local"
                    value={editExpiresAt}
                    onChange={(e) => setEditExpiresAt(e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>エージェントメモ</FormLabel>
                  <Textarea
                    value={editAgentNotes}
                    onChange={(e) => setEditAgentNotes(e.target.value)}
                    rows={3}
                  />
                </FormControl>

                <Divider />

                <Box>
                  <HStack justify="space-between" mb={3}>
                    <Text fontWeight="bold">対象企業</Text>
                    <Button
                      size="sm"
                      leftIcon={<AddIcon />}
                      onClick={onCompanyOpen}
                    >
                      企業を追加
                    </Button>
                  </HStack>
                  
                  {selectedLink && (
                    <List spacing={2}>
                      {selectedLink.companies.map((company) => (
                        <ListItem key={company.id} p={2} bg={hoverBg} borderRadius="md">
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Text fontSize="sm" fontWeight="bold">{company.name}</Text>
                              <HStack spacing={2}>
                                <Badge variant="outline" fontSize="xs">{company.position}</Badge>
                                <Badge colorScheme="blue" fontSize="xs">{company.industry}</Badge>
                              </HStack>
                            </VStack>
                            <IconButton
                              aria-label="削除"
                              icon={<DeleteIcon />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => handleRemoveCompany(company.id)}
                            />
                          </HStack>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Box>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onEditClose}>
                キャンセル
              </Button>
              <Button colorScheme="blue" onClick={handleSaveEdit} isLoading={isLoading}>
                保存
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 企業追加モーダル */}
        <Modal isOpen={isCompanyOpen} onClose={onCompanyClose} size="md">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>企業追加</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4} align="stretch">
                <FormControl isRequired>
                  <FormLabel>企業名</FormLabel>
                  <Input
                    value={newCompanyName}
                    onChange={(e) => setNewCompanyName(e.target.value)}
                    placeholder="例：株式会社○○"
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>ポジション</FormLabel>
                  <Input
                    value={newCompanyPosition}
                    onChange={(e) => setNewCompanyPosition(e.target.value)}
                    placeholder="例：フロントエンドエンジニア"
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>業界</FormLabel>
                  <Select
                    value={newCompanyIndustry}
                    onChange={(e) => setNewCompanyIndustry(e.target.value)}
                  >
                    <option value="">選択してください</option>
                    <option value="IT・ソフトウェア">IT・ソフトウェア</option>
                    <option value="コンサルティング">コンサルティング</option>
                    <option value="金融・銀行">金融・銀行</option>
                    <option value="商社・貿易">商社・貿易</option>
                    <option value="製造業">製造業</option>
                    <option value="その他">その他</option>
                  </Select>
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onCompanyClose}>
                キャンセル
              </Button>
              <Button colorScheme="blue" onClick={handleAddCompany}>
                追加
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </VStack>
    </MotionBox>
  );
};