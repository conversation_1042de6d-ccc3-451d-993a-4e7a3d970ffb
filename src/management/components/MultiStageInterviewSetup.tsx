'use client';

/**
 * マルチステージ面接設定コンポーネント
 * 複数段階の面接プロセスを設計・管理する機能
 */
import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Text,
  Input,
  Textarea,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  Switch,
  FormControl,
  FormLabel,
  IconButton,
  Badge,
  Divider,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  SimpleGrid,
  Progress,
  Tooltip,
  Icon,
} from '@chakra-ui/react';
import { motion, Reorder } from 'framer-motion';
import {
  AddIcon,
  DeleteIcon,
  EditIcon,
  DragHandleIcon,
  InfoIcon,
  CheckCircleIcon,
  TimeIcon,
  SettingsIcon,
  StarIcon,
} from '@chakra-ui/icons';
import {
  FiUsers,
  FiClock,
  FiTarget,
  FiPlay,
  FiPause,
  FiSave,
  FiEye,
} from 'react-icons/fi';
import {
  InterviewProcess,
  InterviewStep,
  InterviewStepType,
  CreateInterviewProcessRequest,
  InterviewerRole,
} from '@mensetsu-kun/shared/types/interviewer-roles';
import InterviewerRoleSelector from './InterviewerRoleSelector';

const MotionCard = motion(Card);
const MotionBox = motion(Box);

// ステップタイプのメタデータ
const stepTypeMetadata: Record<InterviewStepType, {
  label: string;
  description: string;
  icon: any;
  suggestedDuration: number;
  color: string;
}> = {
  screening: {
    label: '書類選考面接',
    description: '履歴書・職歴の確認と基本的な適性評価',
    icon: FiEye,
    suggestedDuration: 30,
    color: 'blue',
  },
  first_round: {
    label: '1次面接',
    description: '基本スキルと企業文化適合性の評価',
    icon: FiPlay,
    suggestedDuration: 45,
    color: 'green',
  },
  second_round: {
    label: '2次面接',
    description: '詳細なスキル評価と実務能力の確認',
    icon: FiTarget,
    suggestedDuration: 60,
    color: 'orange',
  },
  technical: {
    label: '技術面接',
    description: '技術的な知識と実装能力の深掘り評価',
    icon: SettingsIcon,
    suggestedDuration: 90,
    color: 'purple',
  },
  cultural_fit: {
    label: 'カルチャーフィット面接',
    description: '企業文化との適合性と価値観の評価',
    icon: FiUsers,
    suggestedDuration: 45,
    color: 'pink',
  },
  final: {
    label: '最終面接',
    description: '総合的な判断と最終決定',
    icon: CheckCircleIcon,
    suggestedDuration: 60,
    color: 'red',
  },
  executive: {
    label: '役員面接',
    description: '経営陣による最終評価と会社方針の共有',
    icon: StarIcon,
    suggestedDuration: 45,
    color: 'gold',
  },
  informal: {
    label: 'カジュアル面談',
    description: 'リラックスした雰囲気での相互理解',
    icon: FiUsers,
    suggestedDuration: 30,
    color: 'cyan',
  },
};

interface MultiStageInterviewSetupProps {
  onProcessCreated: (process: InterviewProcess) => void;
  existingProcess?: InterviewProcess;
}

function MultiStageInterviewSetup({
  onProcessCreated,
  existingProcess,
}: MultiStageInterviewSetupProps) {
  const [processName, setProcessName] = useState(existingProcess?.name || '');
  const [processDescription, setProcessDescription] = useState(existingProcess?.description || '');
  const [companyId] = useState('default_company'); // 実際の実装では動的に設定
  const [positionId, setPositionId] = useState(existingProcess?.positionId || '');
  const [steps, setSteps] = useState<InterviewStep[]>(existingProcess?.steps || []);
  const [editingStep, setEditingStep] = useState<InterviewStep | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const bgColor = useColorModeValue('gray.50', 'gray.900');

  // 新しいステップを作成
  const createNewStep = useCallback((stepType: InterviewStepType): InterviewStep => {
    const metadata = stepTypeMetadata[stepType];
    const stepNumber = steps.length + 1;
    
    return {
      id: `step_${Date.now()}_${stepNumber}`,
      type: stepType,
      name: `${stepNumber}. ${metadata.label}`,
      description: metadata.description,
      order: stepNumber,
      duration: metadata.suggestedDuration,
      preparationTime: 5,
      interviewerRole: {} as InterviewerRole, // 後で設定
      questionCategories: ['general', 'behavioral'],
      evaluationCriteria: ['communication', 'technical_skills', 'cultural_fit'],
      passingScore: 70,
      isRequired: true,
      stepSpecificSettings: {
        allowRetake: false,
        feedbackTiming: 'step_end',
        recordingEnabled: true,
      },
    };
  }, [steps.length]);

  // ステップを追加
  const handleAddStep = (stepType: InterviewStepType) => {
    const newStep = createNewStep(stepType);
    setEditingStep(newStep);
    onOpen();
  };

  // ステップを編集
  const handleEditStep = (step: InterviewStep) => {
    setEditingStep(step);
    onOpen();
  };

  // ステップを保存
  const handleSaveStep = (updatedStep: InterviewStep) => {
    if (steps.find(s => s.id === updatedStep.id)) {
      // 既存ステップの更新
      setSteps(prev => prev.map(s => s.id === updatedStep.id ? updatedStep : s));
    } else {
      // 新しいステップの追加
      setSteps(prev => [...prev, updatedStep]);
    }
    onClose();
    setEditingStep(null);
  };

  // ステップを削除
  const handleDeleteStep = (stepId: string) => {
    setSteps(prev => prev.filter(s => s.id !== stepId));
    // 順序を再調整
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      order: index + 1,
      name: step.name.replace(/^\d+\./, `${index + 1}.`),
    })));
  };

  // ステップの順序を変更
  const handleReorderSteps = (newOrder: InterviewStep[]) => {
    const reorderedSteps = newOrder.map((step, index) => ({
      ...step,
      order: index + 1,
      name: step.name.replace(/^\d+\./, `${index + 1}.`),
    }));
    setSteps(reorderedSteps);
  };

  // プロセスを保存
  const handleSaveProcess = () => {
    if (!processName.trim()) {
      toast({
        title: 'エラー',
        description: 'プロセス名を入力してください',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    if (steps.length === 0) {
      toast({
        title: 'エラー',
        description: '少なくとも1つのステップを追加してください',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    const totalDuration = steps.reduce((sum, step) => sum + step.duration + (step.preparationTime || 0), 0);

    const processData: InterviewProcess = {
      id: existingProcess?.id || `process_${Date.now()}`,
      name: processName,
      description: processDescription,
      companyId,
      positionId: positionId || undefined,
      steps,
      totalEstimatedDuration: totalDuration,
      processSettings: {
        allowStepSkipping: false,
        requireAllStepsCompletion: true,
        maxRetakeAttempts: 1,
        cooldownPeriod: 24,
      },
      notificationSettings: {
        sendStepCompletionNotification: true,
        sendProcessCompletionNotification: true,
        sendFailureNotification: true,
      },
      createdBy: 'current_user', // 実際の実装では認証ユーザーから取得
      createdAt: existingProcess?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: (existingProcess?.version || 0) + 1,
      isActive: true,
      usageStats: existingProcess?.usageStats || {
        totalCandidates: 0,
        completionRate: 0,
        averageScore: 0,
        stepDropoffRates: {},
      },
    };

    onProcessCreated(processData);
    toast({
      title: 'プロセス保存完了',
      description: `面接プロセス「${processName}」が保存されました`,
      status: 'success',
      duration: 5000,
    });
  };

  const getTotalDuration = () => {
    return steps.reduce((sum, step) => sum + step.duration + (step.preparationTime || 0), 0);
  };

  return (
    <Box>
      <VStack align="stretch" spacing={6}>
        {/* ヘッダー */}
        <Card bg={cardBg} borderColor={borderColor}>
          <CardHeader>
            <HStack justify="space-between">
              <VStack align="start" spacing={1}>
                <HStack>
                  <Icon as={FiTarget} color="blue.500" />
                  <Text fontSize="lg" fontWeight="bold">
                    マルチステージ面接設定
                  </Text>
                </HStack>
                <Text fontSize="sm" color="gray.600">
                  複数段階の面接プロセスを設計・管理します
                </Text>
              </VStack>
              <HStack>
                <Button
                  leftIcon={<FiEye />}
                  variant="outline"
                  onClick={() => setIsPreviewMode(!isPreviewMode)}
                  isActive={isPreviewMode}
                >
                  プレビュー
                </Button>
                <Button
                  leftIcon={<FiSave />}
                  colorScheme="blue"
                  onClick={handleSaveProcess}
                  isDisabled={!processName.trim() || steps.length === 0}
                >
                  保存
                </Button>
              </HStack>
            </HStack>
          </CardHeader>

          <CardBody>
            <VStack align="stretch" spacing={4}>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired>
                  <FormLabel>プロセス名</FormLabel>
                  <Input
                    value={processName}
                    onChange={(e) => setProcessName(e.target.value)}
                    placeholder="新卒採用面接プロセス"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>対象ポジション</FormLabel>
                  <Input
                    value={positionId}
                    onChange={(e) => setPositionId(e.target.value)}
                    placeholder="フロントエンドエンジニア"
                  />
                </FormControl>
              </SimpleGrid>

              <FormControl>
                <FormLabel>プロセス説明</FormLabel>
                <Textarea
                  value={processDescription}
                  onChange={(e) => setProcessDescription(e.target.value)}
                  placeholder="このプロセスの目的と概要を説明してください"
                  rows={3}
                />
              </FormControl>

              {/* プロセス統計 */}
              {steps.length > 0 && (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <VStack align="start" spacing={1} flex={1}>
                    <AlertTitle fontSize="sm">プロセス概要</AlertTitle>
                    <HStack spacing={6} fontSize="sm">
                      <Text>
                        <strong>ステップ数:</strong> {steps.length}
                      </Text>
                      <Text>
                        <strong>予想時間:</strong> {Math.ceil(getTotalDuration() / 60)}時間
                      </Text>
                      <Text>
                        <strong>必須ステップ:</strong> {steps.filter(s => s.isRequired).length}
                      </Text>
                    </HStack>
                  </VStack>
                </Alert>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* ステップ追加セクション */}
        <Card bg={cardBg} borderColor={borderColor}>
          <CardHeader>
            <HStack>
              <Icon as={AddIcon} color="green.500" />
              <Text fontWeight="bold">面接ステップを追加</Text>
            </HStack>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 2, md: 4, lg: 4 }} spacing={3}>
              {(Object.keys(stepTypeMetadata) as InterviewStepType[]).map((stepType) => {
                const metadata = stepTypeMetadata[stepType];
                return (
                  <Button
                    key={stepType}
                    leftIcon={<Icon as={metadata.icon} />}
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddStep(stepType)}
                    colorScheme={metadata.color}
                    flexDirection="column"
                    h="auto"
                    py={3}
                    whiteSpace="normal"
                    textAlign="center"
                  >
                    <Text fontSize="xs" fontWeight="bold">
                      {metadata.label}
                    </Text>
                    <Text fontSize="xs" opacity={0.8} mt={1}>
                      {metadata.suggestedDuration}分
                    </Text>
                  </Button>
                );
              })}
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* ステップ一覧 */}
        {steps.length > 0 && (
          <Card bg={cardBg} borderColor={borderColor}>
            <CardHeader>
              <HStack justify="space-between">
                <HStack>
                  <Icon as={FiTarget} color="purple.500" />
                  <Text fontWeight="bold">面接ステップ ({steps.length})</Text>
                </HStack>
                <Text fontSize="sm" color="gray.600">
                  ドラッグして順序を変更できます
                </Text>
              </HStack>
            </CardHeader>
            <CardBody>
              <Reorder.Group
                axis="y"
                values={steps}
                onReorder={handleReorderSteps}
              >
                <VStack align="stretch" spacing={3}>
                  {steps.map((step, index) => (
                    <StepCard
                      key={step.id}
                      step={step}
                      index={index}
                      onEdit={handleEditStep}
                      onDelete={handleDeleteStep}
                      isPreview={isPreviewMode}
                    />
                  ))}
                </VStack>
              </Reorder.Group>
            </CardBody>
          </Card>
        )}

        {/* プレビューモード */}
        {isPreviewMode && steps.length > 0 && (
          <Card bg={bgColor} borderColor="blue.300" borderWidth="2px">
            <CardHeader>
              <HStack>
                <Icon as={FiEye} color="blue.500" />
                <Text fontWeight="bold" color="blue.600">
                  プロセスプレビュー
                </Text>
              </HStack>
            </CardHeader>
            <CardBody>
              <ProcessPreview process={{
                id: 'preview',
                name: processName,
                description: processDescription,
                steps,
                totalEstimatedDuration: getTotalDuration(),
              } as InterviewProcess} />
            </CardBody>
          </Card>
        )}
      </VStack>

      {/* ステップ編集モーダル */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {editingStep?.id && steps.find(s => s.id === editingStep.id) ? 'ステップ編集' : 'ステップ追加'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {editingStep && (
              <StepEditor
                step={editingStep}
                onSave={handleSaveStep}
                onCancel={onClose}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
}

// ステップカードコンポーネント
const StepCard: React.FC<{
  step: InterviewStep;
  index: number;
  onEdit: (step: InterviewStep) => void;
  onDelete: (stepId: string) => void;
  isPreview?: boolean;
}> = ({ step, index, onEdit, onDelete, isPreview }) => {
  const metadata = stepTypeMetadata[step.type];
  const cardBg = useColorModeValue('white', 'gray.700');

  return (
    <Reorder.Item value={step} id={step.id}>
      <MotionCard
        bg={cardBg}
        borderLeft="4px solid"
        borderLeftColor={`${metadata.color}.400`}
        _hover={{ boxShadow: 'md' }}
        cursor={isPreview ? 'default' : 'grab'}
      >
        <CardBody>
          <HStack justify="space-between">
            <HStack spacing={4} flex={1}>
              <VStack spacing={0} align="center" minW="40px">
                <Icon as={DragHandleIcon} color="gray.400" />
                <Badge colorScheme={metadata.color} size="sm">
                  {index + 1}
                </Badge>
              </VStack>
              
              <VStack align="start" spacing={1} flex={1}>
                <HStack>
                  <Icon as={metadata.icon} color={`${metadata.color}.500`} />
                  <Text fontWeight="bold">{step.name}</Text>
                  {step.isRequired && (
                    <Badge colorScheme="red" size="sm">必須</Badge>
                  )}
                </HStack>
                <Text fontSize="sm" color="gray.600" noOfLines={2}>
                  {step.description}
                </Text>
                <HStack spacing={4} fontSize="sm" color="gray.500">
                  <HStack>
                    <Icon as={FiClock} size="sm" />
                    <Text>{step.duration}分</Text>
                  </HStack>
                  <HStack>
                    <Icon as={FiTarget} size="sm" />
                    <Text>合格: {step.passingScore}%</Text>
                  </HStack>
                </HStack>
              </VStack>
            </HStack>

            {!isPreview && (
              <HStack>
                <IconButton
                  aria-label="編集"
                  icon={<EditIcon />}
                  size="sm"
                  variant="ghost"
                  onClick={() => onEdit(step)}
                />
                <IconButton
                  aria-label="削除"
                  icon={<DeleteIcon />}
                  size="sm"
                  variant="ghost"
                  colorScheme="red"
                  onClick={() => onDelete(step.id)}
                />
              </HStack>
            )}
          </HStack>
        </CardBody>
      </MotionCard>
    </Reorder.Item>
  );
};

// ステップエディターコンポーネント
const StepEditor: React.FC<{
  step: InterviewStep;
  onSave: (step: InterviewStep) => void;
  onCancel: () => void;
}> = ({ step, onSave, onCancel }) => {
  const [editedStep, setEditedStep] = useState<InterviewStep>(step);

  const handleSave = () => {
    onSave(editedStep);
  };

  return (
    <VStack align="stretch" spacing={6}>
      <SimpleGrid columns={2} spacing={4}>
        <FormControl isRequired>
          <FormLabel>ステップ名</FormLabel>
          <Input
            value={editedStep.name}
            onChange={(e) => setEditedStep(prev => ({ ...prev, name: e.target.value }))}
          />
        </FormControl>
        <FormControl isRequired>
          <FormLabel>面接時間（分）</FormLabel>
          <NumberInput
            value={editedStep.duration}
            onChange={(value) => setEditedStep(prev => ({ ...prev, duration: parseInt(value) || 30 }))}
            min={15}
            max={180}
          >
            <NumberInputField />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
        </FormControl>
      </SimpleGrid>

      <FormControl>
        <FormLabel>ステップ説明</FormLabel>
        <Textarea
          value={editedStep.description}
          onChange={(e) => setEditedStep(prev => ({ ...prev, description: e.target.value }))}
          rows={3}
        />
      </FormControl>

      <SimpleGrid columns={2} spacing={4}>
        <FormControl>
          <FormLabel>合格基準スコア（%）</FormLabel>
          <NumberInput
            value={editedStep.passingScore}
            onChange={(value) => setEditedStep(prev => ({ ...prev, passingScore: parseInt(value) || 70 }))}
            min={0}
            max={100}
          >
            <NumberInputField />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
        </FormControl>
        <FormControl>
          <FormLabel>準備時間（分）</FormLabel>
          <NumberInput
            value={editedStep.preparationTime || 0}
            onChange={(value) => setEditedStep(prev => ({ ...prev, preparationTime: parseInt(value) || 0 }))}
            min={0}
            max={30}
          >
            <NumberInputField />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
        </FormControl>
      </SimpleGrid>

      <HStack>
        <FormControl display="flex" alignItems="center">
          <FormLabel mb={0}>必須ステップ</FormLabel>
          <Switch
            isChecked={editedStep.isRequired}
            onChange={(e) => setEditedStep(prev => ({ ...prev, isRequired: e.target.checked }))}
          />
        </FormControl>
        <FormControl display="flex" alignItems="center">
          <FormLabel mb={0}>録画有効</FormLabel>
          <Switch
            isChecked={editedStep.stepSpecificSettings.recordingEnabled}
            onChange={(e) => setEditedStep(prev => ({
              ...prev,
              stepSpecificSettings: {
                ...prev.stepSpecificSettings,
                recordingEnabled: e.target.checked
              }
            }))}
          />
        </FormControl>
      </HStack>

      <HStack justify="flex-end" pt={4}>
        <Button variant="ghost" onClick={onCancel}>
          キャンセル
        </Button>
        <Button colorScheme="blue" onClick={handleSave}>
          保存
        </Button>
      </HStack>
    </VStack>
  );
};

// プロセスプレビューコンポーネント
const ProcessPreview: React.FC<{ process: InterviewProcess }> = ({ process }) => {
  const totalHours = Math.ceil(process.totalEstimatedDuration / 60);
  
  return (
    <VStack align="stretch" spacing={4}>
      <HStack justify="space-between">
        <Text fontSize="lg" fontWeight="bold">{process.name}</Text>
        <Badge colorScheme="blue" px={3} py={1}>
          {totalHours}時間 / {process.steps.length}ステップ
        </Badge>
      </HStack>

      <Text fontSize="sm" color="gray.600">
        {process.description}
      </Text>

      <Box>
        <Text fontSize="sm" fontWeight="bold" mb={2}>進行フロー</Text>
        <VStack align="stretch" spacing={2}>
          {process.steps.map((step, index) => {
            const metadata = stepTypeMetadata[step.type];
            const progress = ((index + 1) / process.steps.length) * 100;
            
            return (
              <HStack key={step.id} spacing={4}>
                <Box minW="20px" textAlign="center">
                  <Badge colorScheme={metadata.color} borderRadius="full">
                    {index + 1}
                  </Badge>
                </Box>
                <VStack align="start" spacing={0} flex={1}>
                  <HStack>
                    <Icon as={metadata.icon} color={`${metadata.color}.500`} size="sm" />
                    <Text fontSize="sm" fontWeight="semibold">{step.name}</Text>
                    <Text fontSize="xs" color="gray.500">({step.duration}分)</Text>
                  </HStack>
                  <Progress
                    value={progress}
                    size="sm"
                    colorScheme={metadata.color}
                    w="full"
                    borderRadius="md"
                  />
                </VStack>
              </HStack>
            );
          })}
        </VStack>
      </Box>
    </VStack>
  );
};

export default MultiStageInterviewSetup;