/**
 * 質問意図テンプレート定義
 */

import { AIGeneratedIntent } from '../types';

export interface IntentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  intents: AIGeneratedIntent[];
  createdAt: string;
  updatedAt: string;
  usageCount: number;
}

// モックテンプレートデータ
export const mockTemplates: IntentTemplate[] = [
  {
    id: 'template-1',
    name: 'エンジニア標準面接テンプレート',
    description: 'フロントエンド・バックエンドエンジニア向けの標準的な質問意図セット',
    category: 'technical',
    tags: ['エンジニア', '技術面接', '標準'],
    intents: [
      {
        id: 'intent-template-1',
        category: '技術スキル',
        intent: '実務で使用している技術スタックとその習熟度を確認したい',
        description: '候補者の技術的な能力とプロジェクト経験を評価します',
        priority: 'high',
        estimatedTime: 10,
        aiInstructions: '具体的なプロジェクト経験を交えて技術力を評価してください',
        confidence: 0.9,
        isEdited: false
      },
      {
        id: 'intent-template-2',
        category: 'チーム協働',
        intent: 'チーム開発における協調性とコミュニケーション能力を評価したい',
        description: 'チームワークとコミュニケーションスキルを確認します',
        priority: 'high',
        estimatedTime: 8,
        aiInstructions: '具体的なエピソードを引き出し、チームでの役割や貢献度を確認してください',
        confidence: 0.9,
        isEdited: false
      },
      {
        id: 'intent-template-3',
        category: '問題解決',
        intent: '技術的な課題に直面した際の問題解決アプローチを知りたい',
        description: '問題解決能力と思考プロセスを評価します',
        priority: 'medium',
        estimatedTime: 7,
        aiInstructions: '実際の問題解決事例を聞き、思考プロセスと結果を評価してください',
        confidence: 0.9,
        isEdited: false
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-02-01T14:30:00Z',
    usageCount: 45
  },
  {
    id: 'template-2',
    name: '営業職標準面接テンプレート',
    description: '営業職向けの成果重視型質問意図セット',
    category: 'sales',
    tags: ['営業', 'コミュニケーション', '成果'],
    intents: [
      {
        id: 'intent-template-4',
        category: '営業実績',
        intent: '過去の営業成績と目標達成への取り組みを確認したい',
        description: '営業成績と目標達成力を評価します',
        priority: 'high',
        estimatedTime: 10,
        aiInstructions: '具体的な数値や達成率を聞き出し、成果に対する意識を評価してください',
        confidence: 0.9,
        isEdited: false
      },
      {
        id: 'intent-template-5',
        category: '顧客対応',
        intent: '顧客との関係構築能力とトラブル対応力を評価したい',
        description: '対人スキルと問題対応能力を確認します',
        priority: 'high',
        estimatedTime: 8,
        aiInstructions: '実際の顧客対応エピソードを通じて、対人スキルを確認してください',
        confidence: 0.9,
        isEdited: false
      }
    ],
    createdAt: '2024-01-20T09:00:00Z',
    updatedAt: '2024-01-25T16:00:00Z',
    usageCount: 32
  },
  {
    id: 'template-3',
    name: 'マネージャー候補面接テンプレート',
    description: 'マネジメント経験とリーダーシップを評価する質問意図セット',
    category: 'management',
    tags: ['マネジメント', 'リーダーシップ', '組織運営'],
    intents: [
      {
        id: 'intent-template-6',
        category: 'マネジメント経験',
        intent: 'チームマネジメントの経験と成果を確認したい',
        description: 'リーダーシップとマネジメント能力を評価します',
        priority: 'high',
        estimatedTime: 12,
        aiInstructions: 'マネジメントスタイルと具体的な成功事例を詳しく聞いてください',
        confidence: 0.9,
        isEdited: false
      },
      {
        id: 'intent-template-7',
        category: '組織改善',
        intent: '組織課題の発見と改善への取り組み方を知りたい',
        description: '課題発見力と改善実行力を評価します',
        priority: 'medium',
        estimatedTime: 8,
        aiInstructions: '課題分析力と実行力を評価できる事例を引き出してください',
        confidence: 0.9,
        isEdited: false
      }
    ],
    createdAt: '2024-02-05T11:00:00Z',
    updatedAt: '2024-02-10T15:00:00Z',
    usageCount: 18
  }
];

// カテゴリ定義
export const TEMPLATE_CATEGORIES = {
  technical: { label: '技術職', color: 'blue' },
  sales: { label: '営業職', color: 'green' },
  management: { label: '管理職', color: 'purple' },
  general: { label: '一般職', color: 'gray' },
  custom: { label: 'カスタム', color: 'orange' }
};