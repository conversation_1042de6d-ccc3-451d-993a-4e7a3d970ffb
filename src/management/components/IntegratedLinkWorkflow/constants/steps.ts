/**
 * ワークフローのステップ定義
 */

export const WORKFLOW_STEPS = [
  {
    title: '候補者選択',
    titleFull: '候補者選択・追加',
    description: '既存候補者から選択または新規候補者を追加',
    descriptionShort: '候補者選択'
  },
  {
    title: '企業選択',
    titleFull: '企業選択・検索',
    description: '面接対象企業を検索・選択して企業情報を設定',
    descriptionShort: '企業選択'
  },
  {
    title: '資料アップロード',
    titleFull: '企業資料アップロード',
    description: '企業の詳細資料をアップロードしてAI分析を実行',
    descriptionShort: '資料アップロード'
  },
  {
    title: 'AI質問生成',
    titleFull: 'AI質問意図生成',
    description: 'アップロードした資料を基にAIが質問意図を自動生成',
    descriptionShort: 'AI生成'
  },
  {
    title: '編集・確認',
    titleFull: 'エージェント編集・確認',
    description: 'AI生成された質問意図をエージェントが確認・編集',
    descriptionShort: '編集確認'
  },
  {
    title: 'リンク発行',
    titleFull: 'リンク設定・発行',
    description: '候補者情報と期限を設定してリンク発行',
    descriptionShort: 'リンク発行'
  }
];

export const STEP_ICONS = {
  0: '👥',
  1: '🏢',
  2: '📁',
  3: '🤖',
  4: '✏️',
  5: '🔗'
};

export const FILE_UPLOAD_SETTINGS = {
  ACCEPT_TYPES: '.pdf,.doc,.docx,.txt',
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MULTIPLE: true
};

export const PRIORITY_COLORS = {
  high: 'red',
  medium: 'yellow',
  low: 'gray'
} as const;