/**
 * IntegratedLinkWorkflow用のモックデータ
 */

import { CandidateInfo } from '../types';

export const mockCandidates: CandidateInfo[] = [
  {
    id: 'candidate_1',
    name: '山田 太郎',
    nameKana: 'やまだ たろう',
    email: '<EMAIL>',
    age: 32,
    currentStatus: 'employed',
    currentEmployer: '株式会社ABCソリューションズ',
    previousEmployer: undefined,
    position: 'フロントエンドエンジニア',
    address: '東京都渋谷区',
    hasResume: true,
    hasCareerHistory: true,
    activeLinks: [
      {
        id: 'link_1',
        companyName: '株式会社Tech Corp',
        position: 'シニアエンジニア',
        interviewerRole: '技術責任者',
        practiceCount: 3,
        expirationDays: 5,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  },
  {
    id: 'candidate_2',
    name: '佐藤 花子',
    nameKana: 'さとう はなこ',
    email: '<EMAIL>',
    age: 28,
    currentStatus: 'unemployed',
    currentEmployer: undefined,
    previousEmployer: 'アクセンチュア株式会社',
    position: 'コンサルタント',
    address: '神奈川県横浜市',
    hasResume: true,
    hasCareerHistory: false,
    activeLinks: []
  },
  {
    id: 'candidate_3',
    name: '鈴木 一郎',
    nameKana: 'すずき いちろう',
    email: '', // メールアドレス未登録
    age: 45,
    currentStatus: 'employed',
    currentEmployer: '日本電気株式会社',
    previousEmployer: undefined,
    position: 'プロジェクトマネージャー',
    address: '千葉県千葉市',
    hasResume: false,
    hasCareerHistory: false,
    activeLinks: [
      {
        id: 'link_2',
        companyName: 'グローバルテック株式会社',
        position: 'テクニカルリード',
        interviewerRole: 'CEO',
        practiceCount: 1,
        expirationDays: 10,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'link_3',
        companyName: 'イノベーションラボ株式会社',
        position: 'エンジニアリングマネージャー',
        interviewerRole: '人事担当',
        practiceCount: 0,
        expirationDays: 3,
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  }
];

export const mockAIGeneratedIntents = [
  {
    id: 'intent_1',
    category: '企業理解',
    intent: '候補者の企業研究の深さと真の志望動機を探る',
    description: 'アップロードされた企業資料を基に、候補者がどの程度企業を理解しているか、表面的でない本当の志望理由を確認する',
    priority: 'high' as const,
    estimatedTime: 10,
    aiInstructions: '企業のビジョンや価値観について具体的に質問し、候補者の理解度を測る',
    confidence: 0.95,
    isEdited: false
  },
  {
    id: 'intent_2',
    category: '技術適合性',
    intent: '求人要件と候補者スキルの具体的な合致度を確認',
    description: '求人票の要件と候補者のスキル・経験の合致度を具体的なエピソードで確認',
    priority: 'high' as const,
    estimatedTime: 15,
    aiInstructions: '技術的な経験について具体例を求め、実際の業務での適用可能性を判断する',
    confidence: 0.88,
    isEdited: false
  },
  {
    id: 'intent_3',
    category: 'キャリア展望',
    intent: '将来ビジョンと企業成長戦略の整合性を評価',
    description: '候補者の将来的な目標と、それが企業の方向性と合致するかを評価',
    priority: 'medium' as const,
    estimatedTime: 8,
    aiInstructions: '3-5年後のキャリア目標について質問し、企業のロードマップとの整合性を確認',
    confidence: 0.82,
    isEdited: false
  }
];