/**
 * 統合リンク作成ワークフロー - メインコンテナ
 * リファクタリング後のクリーンなコンポーネント（約150行）
 */

import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  useToast,
  useDisclosure
} from '@chakra-ui/react';

// カスタムフック
import { useWorkflowState } from './hooks/useWorkflowState';
import { useCandidateState } from './hooks/useCandidateState';
import { useCompanyState } from './hooks/useCompanyState';

// ステップコンポーネント
import { CandidateSelectionStep } from './steps/CandidateSelectionStep';
import { CompanySelectionStep } from './steps/CompanySelectionStep';
import { DocumentUploadStep } from './steps/DocumentUploadStep';
import { QuestionIntentStep } from './steps/QuestionIntentStep';
import { LinkGenerationStep } from './steps/LinkGenerationStep';

// モーダルコンポーネント
import { NewCandidateModal } from './modals/NewCandidateModal';
import { IntentEditModal } from './modals/IntentEditModal';

// 定数とモックデータ
import { WORKFLOW_STEPS, PRIORITY_COLORS } from './constants/steps';
import { mockCandidates, mockAIGeneratedIntents } from './constants/mockData';

// 型定義
import { IntegratedLinkWorkflowProps, AIGeneratedIntent, CompanyDocument } from './types';
import { PositionMaster } from '@mensetsu-kun/shared/types/company-master';
import { CandidateInfo } from '../../types/candidate';

import { CandidateApi, ManagementApi, InterviewLinkResponse } from '../../services/api';

// Hàm upload file lên API
async function uploadCompanyDocuments(companyId: string, docs: CompanyDocument[]) {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
  for (const doc of docs) {
    if (!doc.originFile) continue;
    const formData = new FormData();
    formData.append('file', doc.originFile, doc.fileName);
    const response = await fetch(`${baseUrl}/companies/${companyId}/documents`, {
      method: 'POST',
      body: formData,
      // headers: { 'Authorization': `Bearer ${token}` } // Nếu cần
    });

    if (!response.ok) {
      throw new Error(`Upload failed for ${doc.fileName}`);
    }
  }
}

export const IntegratedLinkWorkflow: React.FC<IntegratedLinkWorkflowProps> = ({
  onLinkGenerated,
  existingLinkId
}) => {
  const toast = useToast();
  
  // 状態管理フック
  const workflowState = useWorkflowState();
  const candidateState = useCandidateState();
  const companyState = useCompanyState();
  
  // モーダル状態
  const { isOpen: isNewCandidateModalOpen, onOpen: onNewCandidateModalOpen, onClose: onNewCandidateModalClose } = useDisclosure();
  const { isOpen: isEditModalOpen, onOpen: onEditModalOpen, onClose: onEditModalClose } = useDisclosure();
  const { isOpen: isEditCandidateModalOpen, onOpen: onEditCandidateModalOpen, onClose: onEditCandidateModalClose } = useDisclosure();
  const [editingIntent, setEditingIntent] = React.useState<AIGeneratedIntent | null>(null);
  
  // 追加の状態
  const [enhancedMode, setEnhancedMode] = React.useState(false);
  const [linkExpirationDays, setLinkExpirationDays] = React.useState(7);
  const [agentNotes, setAgentNotes] = React.useState('');
  const [interviewerRole, setInterviewerRole] = React.useState('hr');
  const [interviewType, setInterviewType] = React.useState('first');
  const [shouldSaveAsTemplate, setShouldSaveAsTemplate] = React.useState(true);
  const [templateName, setTemplateName] = React.useState('');
  const [selectedPosition, setSelectedPosition] = React.useState<PositionMaster | null>(null);
  const [generatedLink, setGeneratedLink] = React.useState<InterviewLinkResponse | null>(null);

  // ユーティリティ関数
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getPriorityColor = (priority: string) => PRIORITY_COLORS[priority as keyof typeof PRIORITY_COLORS] || 'gray';

  // イベントハンドラー
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const newDocs: CompanyDocument[] = Array.from(files).map(file => {
      let fileType: 'pdf' | 'doc' | 'text' = 'text';
      if (file.name.endsWith('.pdf')) {
        fileType = 'pdf';
      } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
        fileType = 'doc';
      }
      return {
        id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.name,
        fileSize: file.size,
        fileType,
        uploadedAt: new Date().toISOString(),
        content: '',
        originFile: file,
      };
    });

    workflowState.setUploadedDocs(prev => [...prev, ...newDocs]);
    
    toast({
      title: 'ファイルアップロード完了',
      description: `${files.length}件のファイルがアップロードされました`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });

    // ファイル入力をリセット
    event.target.value = '';
  };

  const handleGenerateQuestions = async () => {
    workflowState.setIsLoading(true);
    try {
      // AI質問生成のシミュレーション
      await new Promise(resolve => setTimeout(resolve, 2000));
      workflowState.setGeneratedIntents(mockAIGeneratedIntents);
      workflowState.setCurrentStep(3);
      toast({
        title: 'AI質問意図生成完了',
        description: '質問意図が生成されました',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      workflowState.setIsLoading(false);
    }
  };

  const handleGenerateLink = async () => {
    workflowState.setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      const linkData = {
        candidateName: candidateState.candidateName,
        expirationDays: linkExpirationDays,
        interviewerRole,
        interviewType
      };
      
      // ここでは古いコールバックを呼び出さない
      // onLinkGenerated?.(linkData);
    } finally {
      workflowState.setIsLoading(false);
    }
  };

  // 新しい関数: API経由でリンク生成と結果処理
  const handleLinkGenerated = (linkData: InterviewLinkResponse) => {
    setGeneratedLink(linkData);
    
    // 親コンポーネントにも通知
    if (onLinkGenerated) {
      onLinkGenerated({
        id: linkData.id,
        candidateName: linkData.candidate_name,
        url: linkData.interview_url || '',
        expiresAt: linkData.expires_at,
        createdAt: new Date().toISOString(),
        status: "active"
      });
    }
    
    // 次のステップに進む（例：成功画面など）
    // workflowState.setCurrentStep(5);
  };

  const handleEditIntent = (intent: AIGeneratedIntent) => {
    setEditingIntent(intent);
    onEditModalOpen();
  };

  const handleSaveEditedIntent = (updatedIntent: AIGeneratedIntent) => {
    const updatedIntents = workflowState.generatedIntents.map(intent =>
      intent.id === updatedIntent.id ? updatedIntent : intent
    );
    workflowState.setGeneratedIntents(updatedIntents);
    toast({
      title: '質問意図更新完了',
      description: '質問意図が正常に更新されました',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  // Thêm function để reload candidates từ API
  const reloadCandidatesFromAPI = async () => {
    try {
      const candidates = await CandidateApi.getCandidatesList();
      candidateState.setExistingCandidates(candidates);
    } catch (error) {
      console.error('Error reloading candidates from API:', error);
      toast({
        title: '候補者取得エラー',
        description: '候補者一覧の取得に失敗しました。',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // 初期化
  React.useEffect(() => {
    const loadCandidatesFromAPI = async () => {
      try {
        const candidates = await CandidateApi.getCandidatesList();
        candidateState.setExistingCandidates(candidates);
      } catch (error) {
        console.error('Error loading candidates from API:', error);
        toast({
          title: '候補者取得エラー',
          description: '候補者一覧の取得に失敗しました。モックデータを使用します。',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }
    };

    loadCandidatesFromAPI();
  }, []);

  return (
    <VStack spacing={4} align="stretch">
      {/* ステップ表示 - 横スクロール型 */}
      <Box bg="white" borderRadius="lg" border="1px" borderColor="gray.200" py={3} px={0}>
        {/* デスクトップ版 - 詳細表示 */}
        <Box display={{ base: "none", md: "block" }}>
          <Box
            overflowX="auto"
            overflowY="hidden"
            px={6}
            css={{
              '&::-webkit-scrollbar': {
                height: '4px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#a1a1a1',
              },
            }}
          >
            <HStack
              spacing={0}
              minW="max-content"
              role="tablist"
              aria-label="ワークフロー進行状況"
            >
              {WORKFLOW_STEPS.map((step, index) => (
                <VStack
                  key={index}
                  spacing={2}
                  minW="120px"
                  px={2}
                  py={2}
                  role="tab"
                  aria-selected={workflowState.currentStep === index}
                  aria-current={workflowState.currentStep === index ? "step" : undefined}
                  position="relative"
                >
                  {/* ステップインジケーター */}
                  <Box
                    w={8}
                    h={8}
                    borderRadius="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    flexShrink={0}
                    bg={
                      workflowState.currentStep > index
                        ? "blue.500"
                        : workflowState.currentStep === index
                        ? "blue.500"
                        : "gray.200"
                    }
                    color={
                      workflowState.currentStep >= index ? "white" : "gray.500"
                    }
                    fontSize="sm"
                    fontWeight="bold"
                    transition="all 0.2s ease"
                  >
                    {workflowState.currentStep > index ? "✓" : index + 1}
                  </Box>

                  {/* ステップタイトル */}
                  <Text
                    fontSize="xs"
                    fontWeight={workflowState.currentStep === index ? "bold" : "medium"}
                    color={
                      workflowState.currentStep >= index ? "blue.600" : "gray.600"
                    }
                    textAlign="center"
                    lineHeight="1.2"
                    transition="all 0.2s ease"
                  >
                    {step.title}
                  </Text>

                  {/* 接続線 */}
                  {index < WORKFLOW_STEPS.length - 1 && (
                    <Box
                      position="absolute"
                      right={0}
                      top="20px"
                      w="20px"
                      h="2px"
                      bg={
                        workflowState.currentStep > index ? "blue.500" : "gray.200"
                      }
                      transition="all 0.2s ease"
                      zIndex={1}
                    />
                  )}
                </VStack>
              ))}
            </HStack>
          </Box>
        </Box>

        {/* モバイル版 - インジケーター + タイトル */}
        <Box display={{ base: "block", md: "none" }} pl={4} pr={0}>
          <Box
            overflow="hidden"
            w="100%"
          >
            <HStack
              spacing={4}
              role="tablist"
              aria-label="ワークフロー進行状況"
              py={2}
              transform={`translateX(-${workflowState.currentStep * 100}px)`}
              transition="transform 0.3s ease"
            >
              {WORKFLOW_STEPS.map((step, index) => (
                <VStack
                  key={index}
                  role="tab"
                  aria-selected={workflowState.currentStep === index}
                  aria-current={workflowState.currentStep === index ? "step" : undefined}
                  position="relative"
                  spacing={1}
                  align="center"
                  minW="fit-content"
                  flexShrink={0}
                >
                  {/* ステップインジケーター */}
                  <Box
                    w={7}
                    h={7}
                    borderRadius="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    bg={
                      workflowState.currentStep > index
                        ? "blue.500"
                        : workflowState.currentStep === index
                        ? "blue.500"
                        : "gray.200"
                    }
                    color={
                      workflowState.currentStep >= index ? "white" : "gray.500"
                    }
                    fontSize="sm"
                    fontWeight="bold"
                    transition="all 0.2s ease"
                    boxShadow={workflowState.currentStep === index ? "0 0 0 2px rgba(59, 130, 246, 0.3)" : "none"}
                  >
                    {workflowState.currentStep > index ? "✓" : index + 1}
                  </Box>

                  {/* ステップタイトル */}
                  <Text
                    fontSize="xs"
                    fontWeight={workflowState.currentStep === index ? "bold" : "medium"}
                    color={
                      workflowState.currentStep >= index ? "blue.600" : "gray.600"
                    }
                    textAlign="center"
                    lineHeight="1.1"
                    transition="all 0.2s ease"
                    whiteSpace="nowrap"
                    px={1}
                  >
                    {step.title}
                  </Text>

                  {/* 接続線 */}
                  {index < WORKFLOW_STEPS.length - 1 && (
                    <Box
                      position="absolute"
                      right="-14px"
                      top="14px"
                      w="12px"
                      h="2px"
                      bg={
                        workflowState.currentStep > index ? "blue.500" : "gray.200"
                      }
                      transition="all 0.2s ease"
                      zIndex={1}
                    />
                  )}
                </VStack>
              ))}
            </HStack>
          </Box>
        </Box>

        {/* モバイル版 - 簡素表示 */}
        <Box display={{ base: "block", md: "none" }} px={4} mt={3}>
          <Box
            w="100%"
            h="4px"
            bg="gray.200"
            borderRadius="full"
            position="relative"
          >
            <Box
              h="100%"
              bg="blue.500"
              borderRadius="full"
              transition="all 0.3s ease"
              w={`${((workflowState.currentStep + 1) / WORKFLOW_STEPS.length) * 100}%`}
            />
          </Box>
          <HStack justify="space-between" mt={2}>
            <Text fontSize="sm" color="gray.600" fontWeight="medium">
              ステップ {workflowState.currentStep + 1} / {WORKFLOW_STEPS.length}
            </Text>
            <Text fontSize="sm" color="blue.600" fontWeight="bold">
              {Math.round(((workflowState.currentStep + 1) / WORKFLOW_STEPS.length) * 100)}% 完了
            </Text>
          </HStack>
        </Box>

        {/* デスクトップ版 - 進行状況バー */}
        <Box display={{ base: "none", md: "block" }} px={6} mt={3}>
          <Box
            w="100%"
            h="2px"
            bg="gray.200"
            borderRadius="full"
            position="relative"
          >
            <Box
              h="100%"
              bg="blue.500"
              borderRadius="full"
              transition="all 0.3s ease"
              w={`${((workflowState.currentStep + 1) / WORKFLOW_STEPS.length) * 100}%`}
            />
          </Box>
          <HStack justify="space-between" mt={1}>
            <Text fontSize="xs" color="gray.500">
              ステップ {workflowState.currentStep + 1} / {WORKFLOW_STEPS.length}
            </Text>
            <Text fontSize="xs" color="blue.600" fontWeight="medium">
              {Math.round(((workflowState.currentStep + 1) / WORKFLOW_STEPS.length) * 100)}% 完了
            </Text>
          </HStack>
        </Box>
        </Box>

      {/* ステップ別コンテンツ */}
      <Box bg="white" borderRadius="lg" border="1px" borderColor="gray.200" p={4}>
        {workflowState.currentStep === 0 && (
          <CandidateSelectionStep
                  selectedCandidate={candidateState.selectedCandidate}
                  existingCandidates={candidateState.existingCandidates}
                  showNewCandidateForm={candidateState.showNewCandidateForm}
                  onCandidateSelect={candidateState.setSelectedCandidate}
                  onShowNewCandidateForm={onNewCandidateModalOpen}
                  onEditCandidate={onEditCandidateModalOpen}
            onNextStep={() => workflowState.setCurrentStep(1)}
            loadExistingCandidates={reloadCandidatesFromAPI}
          />
        )}

        {workflowState.currentStep === 1 && (
          <CompanySelectionStep
            selectedCompany={companyState.selectedCompany}
            selectedPosition={selectedPosition}
            onCompanySelect={companyState.setSelectedCompany}
            onPositionSelect={setSelectedPosition}
            onPrevStep={() => workflowState.setCurrentStep(0)}
            onNextStep={() => workflowState.setCurrentStep(2)}
          />
        )}

        {workflowState.currentStep === 2 && (
          <DocumentUploadStep
                  uploadedDocs={workflowState.uploadedDocs}
                  enhancedMode={enhancedMode}
                  isLoading={workflowState.isLoading}
                  onFileUpload={handleFileUpload}
                  onDeleteDoc={(docId) => workflowState.setUploadedDocs(prev => prev.filter(d => d.id !== docId))}
                  onEnhancedModeToggle={setEnhancedMode}
            onPrevStep={() => workflowState.setCurrentStep(1)}
            onGenerateQuestions={handleGenerateQuestions}
            formatFileSize={formatFileSize}
            selectedCompany={companyState.selectedCompany}
          />
        )}

        {workflowState.currentStep === 3 && (
          <QuestionIntentStep
                  generatedIntents={workflowState.generatedIntents}
                  shouldSaveAsTemplate={shouldSaveAsTemplate}
                  templateName={templateName}
                  onEditIntent={handleEditIntent}
                  onSaveAsTemplateToggle={setShouldSaveAsTemplate}
                  onTemplateNameChange={setTemplateName}
            onPrevStep={() => workflowState.setCurrentStep(2)}
            onNextStep={() => workflowState.setCurrentStep(4)}
            getPriorityColor={getPriorityColor}
            onIntentsUpdate={workflowState.setGeneratedIntents}
            selectedCompany={companyState.selectedCompany}
          />
        )}

        {workflowState.currentStep === 4 && (
          <LinkGenerationStep
                  selectedCandidate={candidateState.selectedCandidate}
                  candidateName={candidateState.candidateName}
                  candidateEmail={candidateState.candidateEmail}
                  linkExpirationDays={linkExpirationDays}
                  agentNotes={agentNotes}
                  candidateProfile={candidateState.candidateProfile}
                  interviewerRole={interviewerRole}
                  interviewType={interviewType}
                  isLoading={workflowState.isLoading}
                  onCandidateNameChange={candidateState.setCandidateName}
                  onCandidateEmailChange={candidateState.setCandidateEmail}
                  onExpirationDaysChange={setLinkExpirationDays}
                  onAgentNotesChange={setAgentNotes}
                  onInterviewerRoleChange={setInterviewerRole}
                  onInterviewTypeChange={setInterviewType}
            onPrevStep={() => workflowState.setCurrentStep(3)}
            onGenerateLink={handleGenerateLink}
            onEditCandidate={onEditCandidateModalOpen}
            selectedCompanyId={companyState.selectedCompany?.id}
            onLinkGenerated={handleLinkGenerated}
          />
        )}
      </Box>

      {/* モーダル */}
      <NewCandidateModal
          isOpen={isNewCandidateModalOpen}
          onClose={onNewCandidateModalClose}
          candidateName={candidateState.candidateName}
          candidateNameKana={candidateState.candidateNameKana}
          candidateEmail={candidateState.candidateEmail}
          candidateAge={candidateState.candidateAge}
          candidateStatus={candidateState.candidateStatus}
          candidateCompany={candidateState.candidateCompany}
          candidatePosition={candidateState.candidatePosition}
          candidateAddress={candidateState.candidateAddress}
          candidateResumeFile={candidateState.candidateResumeFile}
          candidateCareerHistoryFile={candidateState.candidateCareerHistoryFile}
          candidateProfile={candidateState.candidateProfile}
          onCandidateNameChange={candidateState.setCandidateName}
          onCandidateNameKanaChange={candidateState.setCandidateNameKana}
          onCandidateEmailChange={candidateState.setCandidateEmail}
          onCandidateAgeChange={candidateState.setCandidateAge}
          onCandidateStatusChange={candidateState.setCandidateStatus}
          onCandidateCompanyChange={candidateState.setCandidateCompany}
          onCandidatePositionChange={candidateState.setCandidatePosition}
          onCandidateAddressChange={candidateState.setCandidateAddress}
          onCandidateResumeFileChange={candidateState.setCandidateResumeFile}
          onCandidateCareerHistoryFileChange={candidateState.setCandidateCareerHistoryFile}
          onCandidateProfileChange={candidateState.setCandidateProfile}
          onRegister={async (candidate) => {
            // 候補者登録処理
            onNewCandidateModalClose();
            candidateState.resetCandidateForm();

            // Reload danh sách candidates từ API
            await reloadCandidatesFromAPI();

            toast({
              title: '候補者登録完了',
              description: '候補者が正常に登録されました',
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }}
        />

        <IntentEditModal
          isOpen={isEditModalOpen}
          onClose={onEditModalClose}
          intent={editingIntent}
          onSave={handleSaveEditedIntent}
        />

        {/* 候補者編集モーダル */}
        <NewCandidateModal
          isOpen={isEditCandidateModalOpen}
          onClose={onEditCandidateModalClose}
          isEditing={true}
          candidateName={candidateState.selectedCandidate?.name || ''}
          candidateNameKana={candidateState.selectedCandidate?.nameKana || ''}
          candidateEmail={candidateState.selectedCandidate?.email || ''}
          candidateAge={candidateState.selectedCandidate?.age || 25}
          candidateStatus={candidateState.selectedCandidate?.currentStatus || 'unemployed'}
          candidateCompany={candidateState.selectedCandidate?.currentEmployer || candidateState.selectedCandidate?.previousEmployer || ''}
          candidatePosition={candidateState.selectedCandidate?.position || ''}
          candidateAddress={candidateState.selectedCandidate?.address || ''}
          candidateResumeFile={candidateState.candidateResumeFile}
          candidateCareerHistoryFile={candidateState.candidateCareerHistoryFile}
          candidateProfile={candidateState.selectedCandidate?.profile || ''}
          onCandidateNameChange={candidateState.setCandidateName}
          onCandidateNameKanaChange={candidateState.setCandidateNameKana}
          onCandidateEmailChange={candidateState.setCandidateEmail}
          onCandidateAgeChange={candidateState.setCandidateAge}
          onCandidateStatusChange={candidateState.setCandidateStatus}
          onCandidateCompanyChange={candidateState.setCandidateCompany}
          onCandidatePositionChange={candidateState.setCandidatePosition}
          onCandidateAddressChange={candidateState.setCandidateAddress}
          onCandidateResumeFileChange={candidateState.setCandidateResumeFile}
          onCandidateCareerHistoryFileChange={candidateState.setCandidateCareerHistoryFile}
          onCandidateProfileChange={candidateState.setCandidateProfile}
          onRegister={() => {
            // 候補者更新処理
            onEditCandidateModalClose();
            toast({
              title: '候補者情報更新完了',
              description: `${candidateState.selectedCandidate?.name}さんの情報を更新しました`,
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }}
        />
    </VStack>
  );
};