/**
 * 企業関連の状態管理用カスタムフック
 */

import { useState } from 'react';
import { CompanyInfo } from '../types';

interface UseCompanyStateReturn {
  // 企業選択
  selectedCompany: CompanyInfo | null;
  setSelectedCompany: (company: CompanyInfo | null) => void;
  
  // 企業入力（旧版との互換性）
  companyNameInput: string;
  setCompanyNameInput: (name: string) => void;
  positionInput: string;
  setPositionInput: (position: string) => void;
  
  // 企業ID選択（旧版との互換性）
  selectedCompanyId: string | null;
  setSelectedCompanyId: (id: string | null) => void;
  
  // リセット関数
  resetCompanyState: () => void;
}

export const useCompanyState = (): UseCompanyStateReturn => {
  const [selectedCompany, setSelectedCompany] = useState<CompanyInfo | null>(null);
  const [companyNameInput, setCompanyNameInput] = useState('');
  const [positionInput, setPositionInput] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(null);

  const resetCompanyState = () => {
    setSelectedCompany(null);
    setCompanyNameInput('');
    setPositionInput('');
    setSelectedCompanyId(null);
  };

  return {
    selectedCompany,
    setSelectedCompany,
    companyNameInput,
    setCompanyNameInput,
    positionInput,
    setPositionInput,
    selectedCompanyId,
    setSelectedCompanyId,
    resetCompanyState
  };
};