/**
 * ワークフローの状態管理用カスタムフック
 */

import { useState } from 'react';
import { CompanyDocument, AIGeneratedIntent, CandidateInfo, WorkflowState } from '../types';

interface UseWorkflowStateReturn {
  // 基本ワークフロー状態
  currentStep: number;
  setCurrentStep: (step: number) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  
  // ドキュメント関連
  uploadedDocs: CompanyDocument[];
  setUploadedDocs: React.Dispatch<React.SetStateAction<CompanyDocument[]>>;
  
  // AI生成関連
  generatedIntents: AIGeneratedIntent[];
  setGeneratedIntents: React.Dispatch<React.SetStateAction<AIGeneratedIntent[]>>;
  generatedQuestions: any[];
  setGeneratedQuestions: React.Dispatch<React.SetStateAction<any[]>>;
  
  // リセット関数
  resetWorkflow: () => void;
}

export const useWorkflowState = (): UseWorkflowStateReturn => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedDocs, setUploadedDocs] = useState<CompanyDocument[]>([]);
  const [generatedIntents, setGeneratedIntents] = useState<AIGeneratedIntent[]>([]);
  const [generatedQuestions, setGeneratedQuestions] = useState<any[]>([]);

  const resetWorkflow = () => {
    setCurrentStep(0);
    setUploadedDocs([]);
    setGeneratedIntents([]);
    setGeneratedQuestions([]);
  };

  return {
    currentStep,
    setCurrentStep,
    isLoading,
    setIsLoading,
    uploadedDocs,
    setUploadedDocs,
    generatedIntents,
    setGeneratedIntents,
    generatedQuestions,
    setGeneratedQuestions,
    resetWorkflow
  };
};