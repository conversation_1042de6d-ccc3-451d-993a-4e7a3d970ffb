/**
 * 候補者関連の状態管理用カスタムフック
 */

import { useState } from 'react';
import { CandidateInfo } from '../types';
import { CandidateProfile } from '@mensetsu-kun/shared/types/candidate-profile';

interface UseCandidateStateReturn {
  // 基本候補者情報
  candidateName: string;
  setCandidateName: (name: string) => void;
  candidateEmail: string;
  setCandidateEmail: (email: string) => void;
  candidateNameKana: string;
  setCandidateNameKana: (nameKana: string) => void;
  candidateAge: number;
  setCandidateAge: (age: number) => void;
  candidateStatus: string;
  setCandidateStatus: (status: string) => void;
  candidateCompany: string;
  setCandidateCompany: (company: string) => void;
  candidatePosition: string;
  setCandidatePosition: (position: string) => void;
  candidateAddress: string;
  setCandidateAddress: (address: string) => void;
  candidateHasResume: boolean;
  setCandidateHasResume: (hasResume: boolean) => void;
  candidateHasCareerHistory: boolean;
  setCandidateHasCareerHistory: (hasCareerHistory: boolean) => void;
  candidateResumeFile: File | null;
  setCandidateResumeFile: (file: File | null) => void;
  candidateCareerHistoryFile: File | null;
  setCandidateCareerHistoryFile: (file: File | null) => void;
  
  // 候補者選択・管理
  selectedCandidate: CandidateInfo | null;
  setSelectedCandidate: (candidate: CandidateInfo | null) => void;
  existingCandidates: CandidateInfo[];
  setExistingCandidates: React.Dispatch<React.SetStateAction<CandidateInfo[]>>;
  showNewCandidateForm: boolean;
  setShowNewCandidateForm: (show: boolean) => void;
  
  // プロファイル関連
  candidateProfile: CandidateProfile | null;
  setCandidateProfile: (profile: CandidateProfile | null) => void;
  enableProfileIntegration: boolean;
  setEnableProfileIntegration: (enable: boolean) => void;
  
  // リセット関数
  resetCandidateForm: () => void;
}

export const useCandidateState = (): UseCandidateStateReturn => {
  // 基本候補者情報
  const [candidateName, setCandidateName] = useState('');
  const [candidateEmail, setCandidateEmail] = useState('');
  const [candidateNameKana, setCandidateNameKana] = useState('');
  const [candidateAge, setCandidateAge] = useState(25);
  const [candidateStatus, setCandidateStatus] = useState('');
  const [candidateCompany, setCandidateCompany] = useState('');
  const [candidatePosition, setCandidatePosition] = useState('');
  const [candidateAddress, setCandidateAddress] = useState('');
  const [candidateHasResume, setCandidateHasResume] = useState(false);
  const [candidateHasCareerHistory, setCandidateHasCareerHistory] = useState(false);
  const [candidateResumeFile, setCandidateResumeFile] = useState<File | null>(null);
  const [candidateCareerHistoryFile, setCandidateCareerHistoryFile] = useState<File | null>(null);
  
  // 候補者選択・管理
  const [selectedCandidate, setSelectedCandidateInternal] = useState<CandidateInfo | null>(null);
  const [existingCandidates, setExistingCandidates] = useState<CandidateInfo[]>([]);
  const [showNewCandidateForm, setShowNewCandidateForm] = useState(false);
  
  // 候補者選択時に関連情報も更新する
  const setSelectedCandidate = (candidate: CandidateInfo | null) => {
    setSelectedCandidateInternal(candidate);
    if (candidate) {
      setCandidateName(candidate.name);
      setCandidateEmail(candidate.email || '');
      setCandidateNameKana(candidate.nameKana);
      setCandidateAge(candidate.age);
      setCandidateStatus(candidate.currentStatus);
      setCandidateCompany(candidate.currentStatus === 'employed' ? candidate.currentEmployer || '' : candidate.previousEmployer || '');
      setCandidatePosition(candidate.position);
      setCandidateAddress(candidate.address);
      setCandidateHasResume(candidate.hasResume);
      setCandidateHasCareerHistory(candidate.hasCareerHistory);
      setCandidateResumeFile(candidate.resumeFile);
      setCandidateCareerHistoryFile(candidate.careerHistoryFile);
    }
  };
  
  // プロファイル関連
  const [candidateProfile, setCandidateProfile] = useState<CandidateProfile | null>(null);
  const [enableProfileIntegration, setEnableProfileIntegration] = useState(false);

  const resetCandidateForm = () => {
    setCandidateName('');
    setCandidateEmail('');
    setCandidateNameKana('');
    setCandidateAge(25);
    setCandidateStatus('');
    setCandidateCompany('');
    setCandidatePosition('');
    setCandidateAddress('');
    setCandidateHasResume(false);
    setCandidateHasCareerHistory(false);
    setCandidateResumeFile(null);
    setCandidateCareerHistoryFile(null);
    setSelectedCandidate(null);
    setShowNewCandidateForm(false);
    setCandidateProfile(null);
    setEnableProfileIntegration(false);
  };

  return {
    candidateName,
    setCandidateName,
    candidateEmail,
    setCandidateEmail,
    candidateNameKana,
    setCandidateNameKana,
    candidateAge,
    setCandidateAge,
    candidateStatus,
    setCandidateStatus,
    candidateCompany,
    setCandidateCompany,
    candidatePosition,
    setCandidatePosition,
    candidateAddress,
    setCandidateAddress,
    candidateHasResume,
    setCandidateHasResume,
    candidateHasCareerHistory,
    setCandidateHasCareerHistory,
    candidateResumeFile,
    setCandidateResumeFile,
    candidateCareerHistoryFile,
    setCandidateCareerHistoryFile,
    selectedCandidate,
    setSelectedCandidate,
    existingCandidates,
    setExistingCandidates,
    showNewCandidateForm,
    setShowNewCandidateForm,
    candidateProfile,
    setCandidateProfile,
    enableProfileIntegration,
    setEnableProfileIntegration,
    resetCandidateForm
  };
};