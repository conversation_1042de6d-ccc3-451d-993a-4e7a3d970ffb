/**
 * テンプレート状態管理フック
 */

import { useState, useEffect } from 'react';
import { IntentTemplate } from '../constants/templates';
import { AIGeneratedIntent } from '../types';

export const useTemplateState = () => {
  const [templates, setTemplates] = useState<IntentTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 初期化時にローカルストレージからテンプレートを読み込み
  useEffect(() => {
    const savedTemplates = localStorage.getItem('interview-templates');
    if (savedTemplates) {
      try {
        setTemplates(JSON.parse(savedTemplates));
      } catch (error) {
        console.error('Failed to load templates from localStorage:', error);
      }
    }
  }, []);

  // テンプレートをローカルストレージに保存
  const saveTemplates = (newTemplates: IntentTemplate[]) => {
    try {
      localStorage.setItem('interview-templates', JSON.stringify(newTemplates));
      setTemplates(newTemplates);
    } catch (error) {
      console.error('Failed to save templates to localStorage:', error);
    }
  };

  // 新しいテンプレートを保存
  const saveTemplate = async (
    name: string,
    description: string,
    category: string,
    intents: AIGeneratedIntent[]
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const newTemplate: IntentTemplate = {
        id: `template-${Date.now()}`,
        name,
        description,
        category,
        tags: [], // 自動生成またはユーザー指定
        intents,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        usageCount: 0
      };

      const updatedTemplates = [...templates, newTemplate];
      saveTemplates(updatedTemplates);
      
      // 成功をシミュレート
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } catch (error) {
      console.error('Failed to save template:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // テンプレートの使用回数を増加
  const incrementUsageCount = (templateId: string) => {
    const updatedTemplates = templates.map(template =>
      template.id === templateId
        ? { ...template, usageCount: template.usageCount + 1 }
        : template
    );
    saveTemplates(updatedTemplates);
  };

  // テンプレートを削除
  const deleteTemplate = (templateId: string) => {
    const updatedTemplates = templates.filter(template => template.id !== templateId);
    saveTemplates(updatedTemplates);
  };

  // テンプレートを更新
  const updateTemplate = (templateId: string, updates: Partial<IntentTemplate>) => {
    const updatedTemplates = templates.map(template =>
      template.id === templateId
        ? { ...template, ...updates, updatedAt: new Date().toISOString() }
        : template
    );
    saveTemplates(updatedTemplates);
  };

  return {
    templates,
    isLoading,
    saveTemplate,
    incrementUsageCount,
    deleteTemplate,
    updateTemplate
  };
};