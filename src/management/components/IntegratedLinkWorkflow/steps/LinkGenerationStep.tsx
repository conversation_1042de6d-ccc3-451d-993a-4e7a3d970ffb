/**
 * ステップ4: リンク設定・発行
 */

import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  H<PERSON><PERSON>ck,
  Text,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Badge,
  Alert,
  AlertIcon,
  AlertDescription,
  Box,
  SimpleGrid,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useToast
} from '@chakra-ui/react';
import {
  LinkIcon,
  AttachmentIcon
} from '@chakra-ui/icons';
import { CandidateInfo } from '../types';
import { ManagementApi, InterviewLinkCreate, InterviewLinkResponse } from '../../../services/api';

interface LinkGenerationStepProps {
  selectedCandidate: CandidateInfo | null;
  candidateName: string;
  candidateEmail: string;
  linkExpirationDays: number;
  agentNotes: string;
  candidateProfile: any;
  interviewerRole: string;
  interviewType: string;
  isLoading: boolean;
  onCandidateNameChange: (name: string) => void;
  onCandidateEmailChange: (email: string) => void;
  onExpirationDaysChange: (days: number) => void;
  onAgentNotesChange: (notes: string) => void;
  onInterviewerRoleChange: (role: string) => void;
  onInterviewTypeChange: (type: string) => void;
  onPrevStep: () => void;
  onGenerateLink: () => void;
  onEditCandidate?: () => void;
  selectedCompanyId?: string;
  onLinkGenerated?: (linkData: InterviewLinkResponse) => void;
}

export const LinkGenerationStep: React.FC<LinkGenerationStepProps> = ({
  selectedCandidate,
  candidateName,
  candidateEmail,
  linkExpirationDays,
  agentNotes,
  candidateProfile,
  interviewerRole,
  interviewType,
  isLoading,
  onCandidateNameChange,
  onCandidateEmailChange,
  onExpirationDaysChange,
  onAgentNotesChange,
  onInterviewerRoleChange,
  onInterviewTypeChange,
  onPrevStep,
  onGenerateLink,
  onEditCandidate,
  selectedCompanyId,
  onLinkGenerated
}) => {
  const toast = useToast();

  const handleGenerateLink = async () => {
    if (!selectedCandidate || !selectedCompanyId) {
      toast({
        title: "エラー",
        description: "候補者と企業を選択してください",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    try {
      // 元のonGenerateLink関数を呼び出す
      onGenerateLink();

      // API呼び出し用のデータを作成
      const linkData: InterviewLinkCreate = {
        candidate_id: selectedCandidate.id,
        company_id: selectedCompanyId,
        expiration_days: linkExpirationDays,
        agent_notes: agentNotes,
        interviewer_role: interviewerRole,
        interview_type: interviewType,
        candidate_email: candidateEmail
      };

      // APIを呼び出して面接リンクを生成
      const response = await ManagementApi.generateInterviewLink(linkData);
      
      // 成功した場合、親コンポーネントに通知
      if (onLinkGenerated) {
        onLinkGenerated(response);
      }

      // 成功メッセージを表示
      toast({
        title: "リンク生成成功",
        description: `${candidateName}さんの面接リンクが生成されました。`,
        status: "success",
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error("面接リンク生成エラー:", error);
      toast({
        title: "エラー",
        description: "面接リンクの生成に失敗しました。もう一度お試しください。",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <VStack spacing={6} align="stretch">
      <Alert status="success">
        <AlertIcon />
        <AlertDescription fontSize="sm">
          面接リンクの有効期限とメモを設定して発行します。候補者情報は確認・必要に応じて編集できます。
        </AlertDescription>
      </Alert>

      {/* 選択された候補者情報の確認・編集 */}
      <Card bg="blue.50" variant="outline">
        <CardHeader>
          <HStack justify="space-between">
            <Text fontWeight="bold" fontSize="sm">👤 選択された候補者情報</Text>
            <Badge colorScheme="blue" size="sm">確認・編集可能</Badge>
          </HStack>
        </CardHeader>
        <CardBody>
          <VStack spacing={4} align="stretch">
            {selectedCandidate ? (
              <>
                {/* 既存候補者の場合 */}
                <Card variant="outline">
                  <CardBody>
                    <VStack align="stretch" spacing={3}>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="bold">{selectedCandidate.name}</Text>
                          <Text fontSize="sm" color="gray.600">
                            {selectedCandidate.nameKana} • {selectedCandidate.age}歳
                          </Text>
                          <Badge colorScheme={selectedCandidate.currentStatus === 'employed' ? 'green' : 'gray'}>
                            {selectedCandidate.currentStatus === 'employed' ? '現職' : '離職中'}
                          </Badge>
                        </VStack>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          colorScheme="blue"
                          onClick={onEditCandidate}
                        >
                          編集
                        </Button>
                      </HStack>
                      
                      <Box>
                        <Text fontSize="sm" color="gray.600">
                          <strong>勤務先:</strong> {selectedCandidate.currentStatus === 'employed' ? selectedCandidate.currentEmployer : selectedCandidate.previousEmployer}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          <strong>職種:</strong> {selectedCandidate.position}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          <strong>住所:</strong> {selectedCandidate.address}
                        </Text>
                      </Box>
                      
                      <HStack spacing={2}>
                        <Badge colorScheme={selectedCandidate.hasResume ? "green" : "red"} size="sm">
                          {selectedCandidate.hasResume ? "履歴書あり" : "履歴書なし"}
                        </Badge>
                        <Badge colorScheme={selectedCandidate.hasCareerHistory ? "green" : "red"} size="sm">
                          {selectedCandidate.hasCareerHistory ? "職務経歴書あり" : "職務経歴書なし"}
                        </Badge>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              </>
            ) : (
              <>
                {/* 新規候補者の場合 */}
                <SimpleGrid columns={2} spacing={4}>
                  <FormControl isRequired>
                    <FormLabel fontSize="sm">候補者名</FormLabel>
                    <Input
                      value={candidateName}
                      onChange={(e) => onCandidateNameChange(e.target.value)}
                      placeholder="例：山田 太郎"
                      size="sm"
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel fontSize="sm">メールアドレス</FormLabel>
                    <Input
                      type="email"
                      value={candidateEmail}
                      onChange={(e) => onCandidateEmailChange(e.target.value)}
                      placeholder="例：<EMAIL>"
                      size="sm"
                    />
                  </FormControl>
                </SimpleGrid>
                
                <Alert status="warning" size="sm">
                  <AlertIcon />
                  <AlertDescription fontSize="xs">
                    新規候補者として登録されます。詳細なプロファイル情報は面接後に追加できます。
                  </AlertDescription>
                </Alert>
              </>
            )}
          </VStack>
        </CardBody>
      </Card>

      {/* 面接設定 */}
      <Card variant="outline">
        <CardHeader>
          <Text fontWeight="bold" fontSize="sm">⚙️ 面接設定</Text>
        </CardHeader>
        <CardBody>
          <VStack spacing={4} align="stretch">
            <SimpleGrid columns={2} spacing={4}>
              <FormControl>
                <FormLabel fontSize="sm">面接官役割</FormLabel>
                <Select
                  value={interviewerRole}
                  onChange={(e) => onInterviewerRoleChange(e.target.value)}
                  size="sm"
                >
                  <option value="hr">人事担当</option>
                  <option value="manager">直属上司</option>
                  <option value="technical">技術責任者</option>
                  <option value="ceo">CEO・役員</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel fontSize="sm">面接種別</FormLabel>
                <Select
                  value={interviewType}
                  onChange={(e) => onInterviewTypeChange(e.target.value)}
                  size="sm"
                >
                  <option value="first">一次面接</option>
                  <option value="second">二次面接</option>
                  <option value="final">最終面接</option>
                  <option value="technical">技術面接</option>
                </Select>
              </FormControl>
            </SimpleGrid>
          </VStack>
        </CardBody>
      </Card>

      {/* リンク設定 */}
      <FormControl>
        <FormLabel>リンクの有効期限</FormLabel>
        <NumberInput
          value={linkExpirationDays}
          onChange={(_, valueAsNumber) => onExpirationDaysChange(valueAsNumber || 7)}
          min={1}
          max={30}
          size="sm"
        >
          <NumberInputField />
          <NumberInputStepper>
            <NumberIncrementStepper />
            <NumberDecrementStepper />
          </NumberInputStepper>
        </NumberInput>
        <Text fontSize="xs" color="gray.600" mt={1}>
          1-30日の間で設定してください
        </Text>
      </FormControl>

      <FormControl>
        <FormLabel>エージェントメモ（候補者向け）</FormLabel>
        <Textarea
          value={agentNotes}
          onChange={(e) => onAgentNotesChange(e.target.value)}
          placeholder="候補者へのアドバイスやメッセージを入力..."
          size="sm"
          rows={3}
        />
      </FormControl>

      {/* 追加情報表示 */}
      {candidateProfile && (
        <Alert status="info" size="sm">
          <AlertIcon />
          <AlertDescription fontSize="xs">
            • 候補者プロファイル: {candidateProfile.personalInfo.name}
            {candidateProfile.personalInfo.currentPosition && ` (${candidateProfile.personalInfo.currentPosition})`}
          </AlertDescription>
        </Alert>
      )}

      <Alert status="info" size="sm">
        <AlertIcon />
        <AlertDescription fontSize="xs">
          💡 詳細な情報を入力することで、候補者に合わせた質問生成の精度が向上します
        </AlertDescription>
      </Alert>

      {/* アクションボタン */}
      <HStack justify="space-between">
        <Button
          leftIcon={<AttachmentIcon />}
          variant="outline"
          onClick={onPrevStep}
        >
          戻る
        </Button>

        <Button
          rightIcon={<LinkIcon />}
          colorScheme="blue"
          onClick={handleGenerateLink}
          isDisabled={!candidateName.trim() || !selectedCompanyId}
          isLoading={isLoading}
          loadingText="リンク生成中..."
          size="lg"
        >
          面接リンクを生成
        </Button>
      </HStack>
    </VStack>
  );
};