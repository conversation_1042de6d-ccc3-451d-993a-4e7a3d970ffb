/**
 * 新規候補者登録モーダル
 */

import React, { useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Box,
  IconButton,
  useToast
} from '@chakra-ui/react';
import { AddIcon, AttachmentIcon, DeleteIcon } from '@chakra-ui/icons';
import CandidateProfileInput from '../../CandidateProfileInput';
import { CandidateApi } from '../../../services/api';

interface NewCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  isEditing?: boolean;
  candidateName: string;
  candidateNameKana: string;
  candidateEmail: string;
  candidateAge: number;
  candidateStatus: string;
  candidateCompany: string;
  candidatePosition: string;
  candidateAddress: string;
  candidateResumeFile: File | null;
  candidateCareerHistoryFile: File | null;
  candidateProfile: any;
  onCandidateNameChange: (name: string) => void;
  onCandidateNameKanaChange: (nameKana: string) => void;
  onCandidateEmailChange: (email: string) => void;
  onCandidateAgeChange: (age: number) => void;
  onCandidateStatusChange: (status: string) => void;
  onCandidateCompanyChange: (company: string) => void;
  onCandidatePositionChange: (position: string) => void;
  onCandidateAddressChange: (address: string) => void;
  onCandidateResumeFileChange: (file: File | null) => void;
  onCandidateCareerHistoryFileChange: (file: File | null) => void;
  onCandidateProfileChange: (profile: any) => void;
  onRegister: (candidate: any) => void;
}

export const NewCandidateModal: React.FC<NewCandidateModalProps> = ({
  isOpen,
  onClose,
  isEditing = false,
  candidateName,
  candidateNameKana,
  candidateEmail,
  candidateAge,
  candidateStatus,
  candidateCompany,
  candidatePosition,
  candidateAddress,
  candidateResumeFile,
  candidateCareerHistoryFile,
  candidateProfile,
  onCandidateNameChange,
  onCandidateNameKanaChange,
  onCandidateEmailChange,
  onCandidateAgeChange,
  onCandidateStatusChange,
  onCandidateCompanyChange,
  onCandidatePositionChange,
  onCandidateAddressChange,
  onCandidateResumeFileChange,
  onCandidateCareerHistoryFileChange,
  onCandidateProfileChange,
  onRegister
}) => {
  const toast = useToast();
  const resumeFileRef = useRef<HTMLInputElement>(null);
  const careerHistoryFileRef = useRef<HTMLInputElement>(null);

  const isFormValid = candidateName.trim() &&
                     candidateNameKana.trim() &&
                     candidateEmail.trim() &&
                     candidateAge > 0 &&
                     candidateStatus &&
                     candidateCompany.trim() &&
                     candidatePosition.trim() &&
                     candidateAddress.trim();

  const handleRegister = async () => {
    if (!isFormValid) {
      toast({
        title: '入力エラー',
        description: '必須項目をすべて入力してください',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const formData = new FormData();
    formData.append('candidate_name', candidateName);
    formData.append('candidate_name_kana', candidateNameKana);
    formData.append('candidate_age', String(candidateAge));
    formData.append('candidate_employment_status', candidateStatus);
    formData.append('candidate_email', candidateEmail);
    formData.append('candidate_company', candidateCompany);
    formData.append('candidate_position', candidatePosition);
    formData.append('candidate_address', candidateAddress);
    formData.append('candidate_profile', JSON.stringify(candidateProfile || {}));
    
    // Add files if they exist
    if (candidateResumeFile) {
      formData.append('resume_file', candidateResumeFile);
    }
    if (candidateCareerHistoryFile) {
      formData.append('career_history_file', candidateCareerHistoryFile);
    }

    try {
      const result = await CandidateApi.registerCandidate(formData);
      toast({
        title: '登録が成功しました',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      onRegister(result); // Truyền dữ liệu candidate mới
      onClose();
    } catch (error: any) {
      toast({
        title: '登録に失敗しました',
        description: error.message || 'エラーが発生しました',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: "full", sm: "xl", md: "2xl" }}>
      <ModalOverlay />
      <ModalContent 
        maxH={{ base: "calc(100vh - 32px)", sm: "calc(100vh - 64px)", md: "calc(100vh - 96px)" }}
        overflowY="auto"
        mx={{ base: 4, sm: 8, md: 12 }}
        my={{ base: 4, sm: 8, md: 12 }}
        maxW={{ base: "calc(100vw - 32px)", sm: "calc(100vw - 64px)", md: "calc(100vw - 96px)" }}
      >
        <ModalHeader>
          <HStack>
            <AddIcon color={isEditing ? "blue.500" : "green.500"} />
            <Text>{isEditing ? "候補者情報編集" : "新規候補者登録"}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Alert status="info" size="sm">
              <AlertIcon />
              <AlertDescription fontSize="xs">
                候補者の詳細情報を入力してください。すべての情報が面接カスタマイズに活用されます。
              </AlertDescription>
            </Alert>

            {/* 基本情報 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">👤 基本情報</Text>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">氏名</FormLabel>
                  <Input
                    value={candidateName}
                    onChange={(e) => onCandidateNameChange(e.target.value)}
                    placeholder="例：山田 太郎"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">氏名（かな）</FormLabel>
                  <Input
                    value={candidateNameKana}
                    onChange={(e) => onCandidateNameKanaChange(e.target.value)}
                    placeholder="例：やまだ たろう"
                    size="sm"
                  />
                </FormControl>
              </SimpleGrid>

              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">メールアドレス</FormLabel>
                  <Input
                    type="email"
                    value={candidateEmail}
                    onChange={(e) => onCandidateEmailChange(e.target.value)}
                    placeholder="例：<EMAIL>"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">年齢</FormLabel>
                  <Input
                    type="number"
                    value={candidateAge}
                    onChange={(e) => onCandidateAgeChange(parseInt(e.target.value) || 0)}
                    placeholder="例：32"
                    size="sm"
                    min="18"
                    max="70"
                  />
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* 就労状況 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">💼 就労状況</Text>
              
              <FormControl isRequired>
                <FormLabel fontSize="sm">現在の就労状況</FormLabel>
                <Select 
                  placeholder="選択してください" 
                  size="sm"
                  value={candidateStatus}
                  onChange={(e) => onCandidateStatusChange(e.target.value)}
                >
                  <option value="employed">現職</option>
                  <option value="unemployed">離職中</option>
                </Select>
              </FormControl>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontSize="sm">
                    {candidateStatus === 'employed' ? '現在の勤務先' : '前職の勤務先'}
                  </FormLabel>
                  <Input
                    value={candidateCompany}
                    onChange={(e) => onCandidateCompanyChange(e.target.value)}
                    placeholder="例：株式会社ABC"
                    size="sm"
                  />
                </FormControl>
                
                <FormControl isRequired>
                  <FormLabel fontSize="sm">職種</FormLabel>
                  <Input
                    value={candidatePosition}
                    onChange={(e) => onCandidatePositionChange(e.target.value)}
                    placeholder="例：フロントエンドエンジニア"
                    size="sm"
                  />
                </FormControl>
              </SimpleGrid>
              
              {/* <FormControl isRequired>
                <FormLabel fontSize="sm">住所（都道府県・市区町村）</FormLabel>
                <Input
                  value={candidateAddress}
                  onChange={(e) => onCandidateAddressChange(e.target.value)}
                  placeholder="例：東京都渋谷区"
                  size="sm"
                />
              </FormControl> */}
            </VStack>

            {/* 応募書類 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">📄 応募書類</Text>
              
              <SimpleGrid columns={2} spacing={4}>
                <FormControl>
                  <FormLabel fontSize="sm">履歴書</FormLabel>
                  <VStack spacing={2} align="stretch">
                    <Input
                      type="file"
                      ref={resumeFileRef}
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        onCandidateResumeFileChange(file);
                      }}
                      accept=".pdf,.doc,.docx"
                      display="none"
                    />
                    <HStack>
                      <Button
                        leftIcon={<AttachmentIcon />}
                        size="sm"
                        variant="outline"
                        onClick={() => resumeFileRef.current?.click()}
                      >
                        ファイル選択
                      </Button>
                      {candidateResumeFile && (
                        <IconButton
                          icon={<DeleteIcon />}
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          aria-label="ファイル削除"
                          onClick={() => onCandidateResumeFileChange(null)}
                        />
                      )}
                    </HStack>
                    {candidateResumeFile && (
                      <Text fontSize="xs" color="green.500">
                        {candidateResumeFile.name}
                      </Text>
                    )}
                  </VStack>
                </FormControl>
                
                <FormControl>
                  <FormLabel fontSize="sm">職務経歴書</FormLabel>
                  <VStack spacing={2} align="stretch">
                    <Input
                      type="file"
                      ref={careerHistoryFileRef}
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null;
                        onCandidateCareerHistoryFileChange(file);
                      }}
                      accept=".pdf,.doc,.docx"
                      display="none"
                    />
                    <HStack>
                      <Button
                        leftIcon={<AttachmentIcon />}
                        size="sm"
                        variant="outline"
                        onClick={() => careerHistoryFileRef.current?.click()}
                      >
                        ファイル選択
                      </Button>
                      {candidateCareerHistoryFile && (
                        <IconButton
                          icon={<DeleteIcon />}
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          aria-label="ファイル削除"
                          onClick={() => onCandidateCareerHistoryFileChange(null)}
                        />
                      )}
                    </HStack>
                    {candidateCareerHistoryFile && (
                      <Text fontSize="xs" color="green.500">
                        {candidateCareerHistoryFile.name}
                      </Text>
                    )}
                  </VStack>
                </FormControl>
              </SimpleGrid>
            </VStack>

            {/* プロファイル作成 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">🎯 詳細プロファイル（任意）</Text>
              <Alert status="success" size="sm">
                <AlertIcon />
                <AlertDescription fontSize="xs">
                  詳細な候補者プロファイルを作成して面接質問をカスタマイズ
                </AlertDescription>
              </Alert>

              <CandidateProfileInput
                currentProfile={candidateProfile}
                onProfileSet={onCandidateProfileChange}
              />
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            キャンセル
          </Button>
          <Button
            colorScheme={isEditing ? "blue" : "green"}
            onClick={handleRegister}
            isDisabled={!isFormValid}
          >
            {isEditing ? "更新" : "登録"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};