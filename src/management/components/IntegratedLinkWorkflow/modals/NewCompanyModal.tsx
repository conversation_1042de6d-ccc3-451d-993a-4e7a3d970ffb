/**
 * 新規企業登録モーダル
 */

import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  useToast,
  Select,
  Tag,
  TagLabel,
  TagCloseButton
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';

interface NewCompanyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCompanyCreated: (company: any) => void;
}

interface CompanyFormData {
  name: string;
  industry: string;
  description: string;
  core_values: string[];
  vision_mission: string;
}

export const NewCompanyModal: React.FC<NewCompanyModalProps> = ({
  isOpen,
  onClose,
  onCompanyCreated
}) => {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [newCoreValue, setNewCoreValue] = useState('');
  
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    industry: '',
    description: '',
    core_values: [],
    vision_mission: ''
  });

  const industryOptions = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'Manufacturing',
    'Retail',
    'Consulting',
    'Media',
    'Transportation',
    'Energy',
    'Real Estate',
    'Other'
  ];

  const isFormValid = formData.name.trim() && 
    formData.industry.trim() && 
    formData.description.trim();

  const handleInputChange = (field: keyof CompanyFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddCoreValue = () => {
    if (newCoreValue.trim() && !formData.core_values.includes(newCoreValue.trim())) {
      setFormData(prev => ({
        ...prev,
        core_values: [...prev.core_values, newCoreValue.trim()]
      }));
      setNewCoreValue('');
    }
  };

  const handleRemoveCoreValue = (value: string) => {
    setFormData(prev => ({
      ...prev,
      core_values: prev.core_values.filter(v => v !== value)
    }));
  };

  const handleSubmit = async () => {
    if (!isFormValid) {
      toast({
        title: '入力エラー',
        description: '必須項目をすべて入力してください',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

      const response = await fetch(`${baseUrl}/companies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
        },
        body: JSON.stringify({
          name: formData.name,
          industry: formData.industry,
          description: formData.description,
          core_values: formData.core_values,
          vision_mission: formData.vision_mission
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const newCompany = await response.json();
      
      toast({
        title: '企業登録完了',
        description: `${formData.name}が正常に登録されました`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

        // Callback để parent component xử lý
        onCompanyCreated(newCompany);
    
        // Đóng modal
        handleClose();
    } catch (error: any) {
      console.error('企業登録エラー:', error);
      toast({
        title: '登録に失敗しました',
        description: error.message || 'エラーが発生しました',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      industry: '',
      description: '',
      core_values: [],
      vision_mission: ''
    });
    setNewCoreValue('');
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl">
      <ModalOverlay />
      
      {/* <ModalContent
          maxH={{ base: "calc(100vh - 32px)", sm: "calc(100vh - 64px)", md: "calc(100vh - 96px)" }}
          overflowY="auto"
          mx={{ base: 4, sm: 8, md: 12 }}
          my={{ base: 4, sm: 8, md: 12 }}
          maxW={{ base: "calc(100vw - 32px)", sm: "calc(100vw - 64px)", md: "calc(100vw - 96px)" }}
      > */}
      <ModalContent>
        <ModalHeader>
          <HStack>
            <AddIcon color="green.500" />
            <Text>新規企業登録</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Alert status="info" size="sm">
              <AlertIcon />
              <AlertDescription fontSize="xs">
                企業の詳細情報を入力してください。すべての情報が面接質問生成に活用されます。
              </AlertDescription>
            </Alert>

            {/* 基本情報 */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">🏢 基本情報</Text>
              
              <FormControl isRequired>
                <FormLabel fontSize="sm">企業名</FormLabel>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="例：株式会社ABC"
                  size="sm"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontSize="sm">業界</FormLabel>
                <Select
                  value={formData.industry}
                  onChange={(e) => handleInputChange('industry', e.target.value)}
                  placeholder="業界を選択"
                  size="sm"
                >
                  {industryOptions.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </Select>
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontSize="sm">企業説明</FormLabel>
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="企業の事業内容、特徴、文化などを詳しく説明してください"
                  size="sm"
                  rows={3}
                />
              </FormControl>
            </VStack>

            {/* 企業理念・ビジョン */}
            <VStack spacing={4} align="stretch">
              <Text fontWeight="bold" fontSize="sm">🎯 企業理念・ビジョン</Text>
              
              <FormControl>
                <FormLabel fontSize="sm">ビジョン・ミッション</FormLabel>
                <Textarea
                  value={formData.vision_mission}
                  onChange={(e) => handleInputChange('vision_mission', e.target.value)}
                  placeholder="企業のビジョン、ミッション、経営理念などを入力してください"
                  size="sm"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">コアバリュー（企業価値観）</FormLabel>
                <VStack spacing={2} align="stretch">
                  <HStack>
                    <Input
                      value={newCoreValue}
                      onChange={(e) => setNewCoreValue(e.target.value)}
                      placeholder="例：イノベーション"
                      size="sm"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddCoreValue();
                        }
                      }}
                    />
                    <Button
                      size="sm"
                      colorScheme="blue"
                      onClick={handleAddCoreValue}
                      isDisabled={!newCoreValue.trim()}
                    >
                      追加
                    </Button>
                  </HStack>
                  
                  {formData.core_values.length > 0 && (
                    <HStack spacing={2} flexWrap="wrap">
                      {formData.core_values.map((value, index) => (
                        <Tag key={index} size="sm" colorScheme="blue" borderRadius="full">
                          <TagLabel>{value}</TagLabel>
                          <TagCloseButton onClick={() => handleRemoveCoreValue(value)} />
                        </Tag>
                      ))}
                    </HStack>
                  )}
                </VStack>
              </FormControl>
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            キャンセル
          </Button>
          <Button
            colorScheme="green"
            onClick={handleSubmit}
            isLoading={isLoading}
            loadingText="登録中..."
            isDisabled={!isFormValid}
          >
            企業を登録
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}; 