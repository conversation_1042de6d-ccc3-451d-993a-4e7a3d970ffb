/**
 * 質問意図編集モーダル
 */

import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  AlertDescription
} from '@chakra-ui/react';
import { EditIcon } from '@chakra-ui/icons';
import { AIGeneratedIntent } from '../types';

interface IntentEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  intent: AIGeneratedIntent | null;
  onSave: (updatedIntent: AIGeneratedIntent) => void;
}

export const IntentEditModal: React.FC<IntentEditModalProps> = ({
  isOpen,
  onClose,
  intent,
  onSave
}) => {
  const [formData, setFormData] = useState<AIGeneratedIntent | null>(intent);
  const [errors, setErrors] = useState<Record<string, string>>({});

  React.useEffect(() => {
    if (intent) {
      setFormData({ ...intent });
      setErrors({});
    }
  }, [intent]);

  const handleChange = (field: keyof AIGeneratedIntent, value: any) => {
    if (!formData) return;
    
    setFormData({
      ...formData,
      [field]: value,
      isEdited: true
    });
    
    // エラーをクリア
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: ''
      });
    }
  };

  const validateForm = (): boolean => {
    if (!formData) return false;
    
    const newErrors: Record<string, string> = {};
    
    if (!formData.category.trim()) {
      newErrors.category = 'カテゴリは必須です';
    }
    
    if (!formData.intent.trim()) {
      newErrors.intent = '質問意図は必須です';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = '説明は必須です';
    }
    
    if (formData.estimatedTime < 1 || formData.estimatedTime > 60) {
      newErrors.estimatedTime = '予想時間は1-60分の間で設定してください';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!formData || !validateForm()) return;
    
    onSave(formData);
    onClose();
  };

  if (!formData) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack>
            <EditIcon color="blue.500" />
            <Text>質問意図の編集</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Alert status="info" size="sm">
              <AlertIcon />
              <AlertDescription fontSize="xs">
                質問意図を編集してより効果的な面接にカスタマイズできます。
              </AlertDescription>
            </Alert>

            <FormControl isInvalid={!!errors.category}>
              <FormLabel fontSize="sm">カテゴリ</FormLabel>
              <Input
                value={formData.category}
                onChange={(e) => handleChange('category', e.target.value)}
                placeholder="例：技術適合性"
                size="sm"
              />
              {errors.category && (
                <Text fontSize="xs" color="red.500" mt={1}>
                  {errors.category}
                </Text>
              )}
            </FormControl>

            <FormControl isInvalid={!!errors.intent}>
              <FormLabel fontSize="sm">質問意図</FormLabel>
              <Textarea
                value={formData.intent}
                onChange={(e) => handleChange('intent', e.target.value)}
                placeholder="例：候補者の技術経験と求人要件の合致度を確認する"
                size="sm"
                rows={2}
              />
              {errors.intent && (
                <Text fontSize="xs" color="red.500" mt={1}>
                  {errors.intent}
                </Text>
              )}
            </FormControl>

            <FormControl isInvalid={!!errors.description}>
              <FormLabel fontSize="sm">詳細説明</FormLabel>
              <Textarea
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="具体的にどのような点を確認したいか説明してください"
                size="sm"
                rows={3}
              />
              {errors.description && (
                <Text fontSize="xs" color="red.500" mt={1}>
                  {errors.description}
                </Text>
              )}
            </FormControl>

            <HStack spacing={4}>
              <FormControl>
                <FormLabel fontSize="sm">優先度</FormLabel>
                <Select
                  value={formData.priority}
                  onChange={(e) => handleChange('priority', e.target.value as 'high' | 'medium' | 'low')}
                  size="sm"
                >
                  <option value="high">高</option>
                  <option value="medium">中</option>
                  <option value="low">低</option>
                </Select>
              </FormControl>

              <FormControl isInvalid={!!errors.estimatedTime}>
                <FormLabel fontSize="sm">予想時間（分）</FormLabel>
                <NumberInput
                  value={formData.estimatedTime}
                  onChange={(_, valueAsNumber) => handleChange('estimatedTime', valueAsNumber || 5)}
                  min={1}
                  max={60}
                  size="sm"
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
                {errors.estimatedTime && (
                  <Text fontSize="xs" color="red.500" mt={1}>
                    {errors.estimatedTime}
                  </Text>
                )}
              </FormControl>
            </HStack>

            <FormControl>
              <FormLabel fontSize="sm">AI指示（任意）</FormLabel>
              <Textarea
                value={formData.aiInstructions}
                onChange={(e) => handleChange('aiInstructions', e.target.value)}
                placeholder="AIに対する特別な指示があれば入力してください"
                size="sm"
                rows={2}
              />
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            キャンセル
          </Button>
          <Button colorScheme="blue" onClick={handleSave}>
            保存
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};