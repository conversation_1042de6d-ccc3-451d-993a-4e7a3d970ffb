import { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  Text,
  HStack,
  IconButton,
  Textarea,
  useColorModeValue,
  Badge,
  Divider,
  Select,
  Switch,
  Card,
  CardBody,
  CardHeader,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Icon,
} from '@chakra-ui/react';
import { CopyIcon, LinkIcon, InfoIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { ManagementApi, type MeetingLink, type AgentInterviewConfig } from '../services/api';
import { spacing, textStyles, commonStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, staggerContainer, staggerItem } from '../motion/index';
import { CandidateProfile } from '@mensetsu-kun/shared/types/candidate-profile';
import { Interviewer<PERSON><PERSON> } from '@mensetsu-kun/shared/types/interviewer-roles';
import InterviewerRoleSelector from './InterviewerRoleSelector';
import CandidateProfileInput from './CandidateProfileInput';

const MotionBox = motion(Box);

export const MeetingLinkGenerator = () => {
  const [candidateName, setCandidateName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [position, setPosition] = useState('');
  const [requirements, setRequirements] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [meetingLink, setMeetingLink] = useState<MeetingLink | null>(null);
  const [expirationDays, setExpirationDays] = useState(7);
  
  // 新機能: 候補者プロファイル連携
  const [enableProfileIntegration, setEnableProfileIntegration] = useState(false);
  const [candidateProfile, setCandidateProfile] = useState<CandidateProfile | null>(null);
  const [selectedInterviewerRole, setSelectedInterviewerRole] = useState<InterviewerRole | null>(null);
  
  const toast = useToast();
  
  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const successBg = useColorModeValue('green.50', 'green.900');

  /**
   * ミーティングリンク生成処理（統合API版）
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('🔗 ミーティングリンク生成開始:', { 
        candidateName, 
        companyName, 
        position,
        enableProfileIntegration,
        hasProfile: !!candidateProfile,
        hasInterviewerRole: !!selectedInterviewerRole
      });
      
      // 企業情報がある場合は設定に含める
      const config: AgentInterviewConfig | undefined = companyName ? {
        companyName,
        position,
        requirements: requirements.split('\n').filter(req => req.trim()),
        interviewType: 'behavioral',
        estimatedDuration: 45,
        // 新機能: 候補者プロファイルと面接官役割を含める
        ...(enableProfileIntegration && {
          candidateProfile,
          interviewerRole: selectedInterviewerRole,
        })
      } : undefined;
      
      // 統合APIでミーティングリンクを生成（期限設定を含む）
      const generatedLink = await ManagementApi.generateMeetingLink(candidateName, config, expirationDays);
      
      setMeetingLink(generatedLink);
      console.log('✅ ミーティングリンク生成完了:', generatedLink.url);

      const profileInfo = enableProfileIntegration && candidateProfile ? 
        `（プロファイル連携: ${candidateProfile.personalInfo.name || candidateName}）` : '';
      const roleInfo = selectedInterviewerRole ? 
        `（面接官: ${selectedInterviewerRole.title}）` : '';

      toast({
        title: 'ミーティングリンクを生成しました',
        description: `${candidateName}様用のリンクが生成されました${profileInfo}${roleInfo}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('ミーティングリンク生成エラー:', error);
      toast({
        title: 'エラーが発生しました',
        description: 'ミーティングリンクの生成に失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = () => {
    if (meetingLink) {
      navigator.clipboard.writeText(meetingLink.url);
      toast({
        title: 'リンクをコピーしました',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }
  };

  return (
    <MotionBox
      {...fadeIn}
      bg={bgColor}
      borderRadius="md"
      borderWidth="1px"
      borderColor={borderColor}
      p={spacing.cardPadding}
      boxShadow="sm"
    >
      <VStack spacing={spacing.sectionSpacing} align="stretch">
        <Box as="form" onSubmit={handleSubmit}>
          <VStack spacing={spacing.cardPadding} align="stretch">
            <FormControl isRequired>
              <FormLabel {...textStyles.label}>候補者名</FormLabel>
              <Input
                value={candidateName}
                onChange={(e) => setCandidateName(e.target.value)}
                placeholder="例：山田 太郎"
                size="md"
              />
            </FormControl>

            <Divider />

            <Text {...textStyles.subheading} color="gray.600">
              企業情報（オプション）
            </Text>

            <FormControl>
              <FormLabel {...textStyles.label}>企業名</FormLabel>
              <Input
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="例：株式会社〇〇"
                size="md"
              />
            </FormControl>

            <FormControl>
              <FormLabel {...textStyles.label}>募集ポジション</FormLabel>
              <Input
                value={position}
                onChange={(e) => setPosition(e.target.value)}
                placeholder="例：フロントエンドエンジニア"
                size="md"
              />
            </FormControl>

            <FormControl>
              <FormLabel {...textStyles.label}>求める要件</FormLabel>
              <Textarea
                value={requirements}
                onChange={(e) => setRequirements(e.target.value)}
                placeholder="React経験3年以上&#10;TypeScript実務経験&#10;チーム開発経験"
                rows={3}
                resize="vertical"
              />
            </FormControl>

            <Divider />

            {/* 新機能: プロファイル連携設定 */}
            <Card variant="outline" bg={useColorModeValue('blue.50', 'blue.900')}>
              <CardHeader pb={2}>
                <HStack justify="space-between">
                  <HStack>
                    <Icon as={InfoIcon} color="blue.500" />
                    <Text fontWeight="bold" color="blue.700">
                      プロファイル連携設定
                    </Text>
                  </HStack>
                  <Switch
                    isChecked={enableProfileIntegration}
                    onChange={(e) => setEnableProfileIntegration(e.target.checked)}
                    colorScheme="blue"
                  />
                </HStack>
              </CardHeader>
              {enableProfileIntegration && (
                <CardBody pt={0}>
                  <VStack align="stretch" spacing={4}>
                    <Text fontSize="sm" color="gray.600">
                      候補者プロファイルと面接官役割を指定して、パーソナライズされた面接体験を提供します
                    </Text>
                    
                    <Accordion allowToggle>
                      <AccordionItem>
                        <AccordionButton px={0}>
                          <Box flex="1" textAlign="left">
                            <Text fontWeight="semibold">候補者プロファイル設定</Text>
                            {candidateProfile && (
                              <Badge colorScheme="green" size="sm" ml={2}>
                                設定済み
                              </Badge>
                            )}
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel px={0} pb={4}>
                          <CandidateProfileInput 
                            onProfileSet={setCandidateProfile}
                            currentProfile={candidateProfile}
                          />
                        </AccordionPanel>
                      </AccordionItem>
                      
                      <AccordionItem>
                        <AccordionButton px={0}>
                          <Box flex="1" textAlign="left">
                            <Text fontWeight="semibold">面接官役割選択</Text>
                            {selectedInterviewerRole && (
                              <Badge colorScheme="blue" size="sm" ml={2}>
                                {selectedInterviewerRole.title}
                              </Badge>
                            )}
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel px={0} pb={4}>
                          <InterviewerRoleSelector
                            onRoleSelected={setSelectedInterviewerRole}
                            selectedRole={selectedInterviewerRole || undefined}
                            allowCustomization={true}
                          />
                        </AccordionPanel>
                      </AccordionItem>
                    </Accordion>
                  </VStack>
                </CardBody>
              )}
            </Card>

            <Divider />

            <FormControl>
              <FormLabel {...textStyles.label}>リンクの有効期限</FormLabel>
              <Select
                value={expirationDays}
                onChange={(e) => setExpirationDays(Number(e.target.value))}
                size="md"
              >
                <option value={1}>1日後</option>
                <option value={3}>3日後</option>
                <option value={7}>7日後（推奨）</option>
                <option value={14}>14日後</option>
                <option value={30}>30日後</option>
              </Select>
              <Text {...textStyles.caption} color="gray.500" mt={1}>
                期限切れリンクは自動的に無効化され、サーバーコストを削減します
              </Text>
            </FormControl>

            <Button
              type="submit"
              colorScheme="blue"
              isLoading={isLoading}
              loadingText="生成中..."
              leftIcon={<LinkIcon />}
              px={spacing.buttonPadding.px}
              py={spacing.buttonPadding.py}
              size="lg"
            >
              ミーティングリンクを生成
            </Button>
          </VStack>
        </Box>

        <AnimatePresence>
          {meetingLink && (
            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              p={spacing.cardPadding}
              bg={successBg}
              borderRadius="md"
              borderWidth="1px"
              borderColor="green.200"
            >
              <VStack align="stretch" spacing={spacing.cardPadding}>
                <HStack>
                  <LinkIcon color="green.600" />
                  <Text {...textStyles.subheading} color="green.700">
                    生成されたミーティングリンク
                  </Text>
                  <Badge colorScheme="green" ml="auto">
                    {meetingLink.status}
                  </Badge>
                </HStack>

                <HStack spacing={2}>
                  <Text 
                    flex={1} 
                    p={spacing.cardPadding.base}
                    bg="white" 
                    borderRadius="md"
                    {...textStyles.body}
                    fontFamily="mono"
                    fontSize="sm"
                    overflowWrap="break-word"
                  >
                    {meetingLink.url}
                  </Text>
                  <IconButton
                    aria-label="リンクをコピー"
                    icon={<CopyIcon />}
                    onClick={handleCopyLink}
                    colorScheme="green"
                    variant="outline"
                  />
                </HStack>

                {meetingLink.companyInfo && (
                  <Box p={spacing.cardPadding.base} bg="white" borderRadius="md">
                    <Text {...textStyles.label} mb={2}>設定情報</Text>
                    <VStack align="start" spacing={1}>
                      <Text {...textStyles.body}>
                        <strong>企業:</strong> {meetingLink.companyInfo.name}
                      </Text>
                      <Text {...textStyles.body}>
                        <strong>ポジション:</strong> {meetingLink.companyInfo.position}
                      </Text>
                      {meetingLink.companyInfo.requirements.length > 0 && (
                        <Box>
                          <Text {...textStyles.body} mb={1}>
                            <strong>要件:</strong>
                          </Text>
                          <VStack align="start" spacing={0} pl={4}>
                            {meetingLink.companyInfo.requirements.map((req, index) => (
                              <Text key={index} {...textStyles.caption}>
                                • {req}
                              </Text>
                            ))}
                          </VStack>
                        </Box>
                      )}
                    </VStack>
                  </Box>
                )}

                {/* プロファイル連携情報の表示 */}
                {enableProfileIntegration && (candidateProfile || selectedInterviewerRole) && (
                  <Box p={spacing.cardPadding.base} bg="white" borderRadius="md">
                    <Text {...textStyles.label} mb={2}>プロファイル連携情報</Text>
                    <VStack align="start" spacing={2}>
                      {candidateProfile && (
                        <Box>
                          <Text {...textStyles.body}>
                            <strong>候補者プロファイル:</strong> {candidateProfile.personalInfo.name || candidateName}
                          </Text>
                          {candidateProfile.personalInfo.currentPosition && (
                            <Text {...textStyles.caption} pl={2}>
                              現職: {candidateProfile.personalInfo.currentPosition}
                            </Text>
                          )}
                          {candidateProfile.personalInfo.yearsOfExperience && (
                            <Text {...textStyles.caption} pl={2}>
                              経験年数: {candidateProfile.personalInfo.yearsOfExperience}年
                            </Text>
                          )}
                        </Box>
                      )}
                      {selectedInterviewerRole && (
                        <Box>
                          <Text {...textStyles.body}>
                            <strong>面接官役割:</strong> {selectedInterviewerRole.title}
                          </Text>
                          <Text {...textStyles.caption} pl={2}>
                            {selectedInterviewerRole.description}
                          </Text>
                        </Box>
                      )}
                    </VStack>
                  </Box>
                )}

                <HStack justify="space-between">
                  <Text {...textStyles.caption}>
                    生成日時: {new Date(meetingLink.createdAt).toLocaleString('ja-JP')}
                  </Text>
                  <Text {...textStyles.caption}>
                    有効期限: {new Date(meetingLink.expiresAt).toLocaleDateString('ja-JP')}
                  </Text>
                </HStack>
              </VStack>
            </MotionBox>
          )}
        </AnimatePresence>
      </VStack>
    </MotionBox>
  );
}; 