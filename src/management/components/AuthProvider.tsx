import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../hooks/useAuth';
import { Box, Spinner, VStack, Text } from '@chakra-ui/react';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const router = useRouter();
  const { isLoading, isAuthenticated } = useAuth('agent');

  // Xử lý redirect khi đã login
  useEffect(() => {
    if (!isLoading && isAuthenticated && router.pathname.includes('/demo-login')) {
      router.push('/');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minH="100vh"
        bg="gray.50"
      >
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" thickness="4px" />
          <Text color="gray.600">認証確認中...</Text>
        </VStack>
      </Box>
    );
  }

  return <>{children}</>;
}; 