import { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  useToast,
  useColorModeValue,
  Text,
  HStack,
  Icon,
  Badge,
} from '@chakra-ui/react';
import { QuestionIcon, CheckCircleIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';
import { ManagementApi } from '../services/api';
import type { Question } from '../../shared/types';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn } from '../motion/index';

const MotionBox = motion(Box);

// 管理画面用の質問型
interface GeneratedQuestion {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number;
}

interface QuestionScriptGeneratorProps {
  onQuestionsGenerated: (questions: GeneratedQuestion[]) => void;
}

export const QuestionScriptGenerator: React.FC<QuestionScriptGeneratorProps> = ({ onQuestionsGenerated }) => {
  const [companyName, setCompanyName] = useState('');
  const [position, setPosition] = useState('');
  const [requirements, setRequirements] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [generatedCount, setGeneratedCount] = useState(0);
  const toast = useToast();
  
  // カラーモード対応
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  /**
   * 質問スクリプト生成処理（統合API版）
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('🔄 質問生成開始:', { companyName, position, requirements });
      
      // 要件を配列に変換
      const requirementsList = requirements.split('\n').filter(req => req.trim());
      
      // 統合APIで質問生成を実行
      const basicQuestions: Question[] = await ManagementApi.generateQuestionScript(
        companyName,
        position,
        requirementsList
      );

      // 管理画面用の形式に変換
      const formattedQuestions: GeneratedQuestion[] = basicQuestions.map((q) => ({
        id: q.id,
        text: q.text,
        category: q.category,
        difficulty: q.difficulty,
        estimatedTime: q.estimatedTime
      }));

      onQuestionsGenerated(formattedQuestions);
      setGeneratedCount(formattedQuestions.length);

      console.log('✅ 質問生成完了:', formattedQuestions);

      toast({
        title: '質問スクリプトを生成しました',
        description: `${formattedQuestions.length}個の質問を生成しました`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('質問生成エラー:', error);
      toast({
        title: 'エラーが発生しました',
        description: '質問スクリプトの生成に失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MotionBox
      {...fadeIn}
      bg={bgColor}
      borderRadius="md"
      borderWidth="1px"
      borderColor={borderColor}
      p={spacing.cardPadding}
      boxShadow="sm"
      width="100%"
    >
      <Box as="form" onSubmit={handleSubmit}>
        <VStack spacing={spacing.cardPadding} align="stretch">
          <HStack>
            <Icon as={QuestionIcon} color="blue.500" boxSize={spacing.iconBoxSize} />
            <Text {...textStyles.subheading}>質問スクリプト生成</Text>
            {generatedCount > 0 && (
              <Badge colorScheme="green" ml="auto">
                {generatedCount}問 生成済み
              </Badge>
            )}
          </HStack>

          <FormControl isRequired>
            <FormLabel {...textStyles.label}>企業名</FormLabel>
            <Input
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
              placeholder="例：株式会社テクノロジーソリューション"
              size="md"
            />
          </FormControl>

          <FormControl isRequired>
            <FormLabel {...textStyles.label}>募集ポジション</FormLabel>
            <Input
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              placeholder="例：フロントエンドエンジニア"
              size="md"
            />
          </FormControl>

          <FormControl isRequired>
            <FormLabel {...textStyles.label}>求める要件</FormLabel>
            <Text {...textStyles.caption} mb={2}>
              改行で区切って複数の要件を入力してください
            </Text>
            <Textarea
              value={requirements}
              onChange={(e) => setRequirements(e.target.value)}
              placeholder="React開発経験3年以上&#10;TypeScript実務経験&#10;チーム開発経験&#10;アジャイル開発経験"
              rows={4}
              resize="vertical"
            />
          </FormControl>

          <Button
            type="submit"
            colorScheme="blue"
            isLoading={isLoading}
            loadingText="質問を生成中..."
            leftIcon={<QuestionIcon />}
            px={spacing.buttonPadding.px}
            py={spacing.buttonPadding.py}
            size="lg"
            isDisabled={!companyName || !position || !requirements}
          >
            質問スクリプトを生成
          </Button>

          {generatedCount > 0 && (
            <HStack justify="center" color="green.600">
              <CheckCircleIcon />
              <Text {...textStyles.body}>
                {generatedCount}個の質問が生成されました
              </Text>
            </HStack>
          )}
        </VStack>
      </Box>
    </MotionBox>
  );
}; 