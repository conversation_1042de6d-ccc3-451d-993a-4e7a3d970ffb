/**
 * ステップ設定エディター - リファクタリング版
 * 元の1134行から150行に削減、CODING_GUIDELINES準拠
 */

import React from 'react';
import { VStack, HStack } from '@chakra-ui/react';
import { Button } from '../../design-system/components/Button';
import { StepLayout } from '../../design-system/components/StepLayout';
import { useStepConfiguration } from './hooks/useStepConfiguration';
import { RetrySettingsSection } from './components/RetrySettingsSection';
import { NotificationSettingsSection } from './components/NotificationSettingsSection';
import { StepConfigurationProps, RetryConditionOption, RecipientOption } from './types';
import { useToastPositive } from '../../design-system/hooks/useToastPositive';

// 設定オプション（定数として分離）
const RETRY_CONDITION_OPTIONS: RetryConditionOption[] = [
  {
    value: 'score_below_threshold',
    label: 'スコアが基準を下回る',
    description: '評価スコアが設定した基準より低い場合',
  },
  {
    value: 'technical_failure',
    label: '技術的な問題',
    description: 'システムエラーや通信障害が発生した場合',
  },
  {
    value: 'candidate_request',
    label: '候補者からの希望',
    description: '候補者が再挑戦を希望した場合',
  },
];

const RECIPIENT_OPTIONS: RecipientOption[] = [
  { value: 'candidate', label: '候補者' },
  { value: 'interviewer', label: '面接官' },
  { value: 'hr', label: '人事' },
  { value: 'manager', label: '管理者' },
];

const STEP_INFO = [
  { title: 'リトライ設定', description: '再挑戦可能な条件を設定' },
  { title: '通知設定', description: 'ポジティブな通知メッセージを設定' },
  { title: '確認・保存', description: '設定内容を確認して保存' },
];

export const StepConfigurationEditor: React.FC<StepConfigurationProps> = ({
  stepId,
  stepName,
  currentConfiguration,
  onSave,
  onCancel,
}) => {
  const { showSuccess, showEncouragement } = useToastPositive();
  const [currentStep, setCurrentStep] = React.useState(0);

  const {
    configuration,
    updateRetrySettings,
    updateNotificationSettings,
    validateConfiguration,
  } = useStepConfiguration(stepId, currentConfiguration);

  const handleSave = () => {
    const validation = validateConfiguration();
    
    if (!validation.isValid) {
      showEncouragement(
        '設定を見直していただけると助かります',
        'ちょっとした調整が必要です'
      );
      return;
    }

    onSave(configuration);
    showSuccess(
      `${stepName}の設定が正常に保存されました`,
      '設定完了'
    );
  };

  const handleNext = () => {
    if (currentStep < STEP_INFO.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <RetrySettingsSection
            settings={configuration.retrySettings}
            onUpdate={updateRetrySettings}
            retryConditionOptions={RETRY_CONDITION_OPTIONS}
          />
        );
      case 1:
        return (
          <NotificationSettingsSection
            settings={configuration.notificationSettings}
            onUpdate={updateNotificationSettings}
            recipientOptions={RECIPIENT_OPTIONS}
          />
        );
      case 2:
        return (
          <VStack spacing={6}>
            <RetrySettingsSection
              settings={configuration.retrySettings}
              onUpdate={updateRetrySettings}
              retryConditionOptions={RETRY_CONDITION_OPTIONS}
            />
            <NotificationSettingsSection
              settings={configuration.notificationSettings}
              onUpdate={updateNotificationSettings}
              recipientOptions={RECIPIENT_OPTIONS}
            />
          </VStack>
        );
      default:
        return null;
    }
  };

  return (
    <StepLayout
      currentStep={currentStep}
      steps={STEP_INFO}
      title={`${stepName} - 設定エディター`}
      showProgress
    >
      <VStack spacing={6} align="stretch">
        {renderStepContent()}

        <HStack justify="space-between">
          <HStack>
            <Button variant="outline" onClick={onCancel}>
              キャンセル
            </Button>
            {currentStep > 0 && (
              <Button variant="outline" onClick={handlePrev}>
                戻る
              </Button>
            )}
          </HStack>

          <HStack>
            {currentStep < STEP_INFO.length - 1 ? (
              <Button variant="primary" onClick={handleNext}>
                次へ
              </Button>
            ) : (
              <Button variant="primary" onClick={handleSave}>
                設定を保存
              </Button>
            )}
          </HStack>
        </HStack>
      </VStack>
    </StepLayout>
  );
};