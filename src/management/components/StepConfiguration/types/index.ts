/**
 * ステップ固有設定の型定義
 * CODING_GUIDELINES.mdに従って型を一元管理
 */

export interface RetrySettings {
  allowRetry: boolean;
  maxRetryAttempts: number;
  retryInterval: number;
  retryConditions: string[];
  resetOnRetry: boolean;
}

export interface Dependencies {
  prerequisiteSteps: string[];
  conditionalLogic: ConditionalLogic[];
  skipConditions: SkipCondition[];
}

export interface ConditionalLogic {
  id: string;
  rule: string;
  description: string;
  action: 'show' | 'hide' | 'require' | 'skip';
}

export interface SkipCondition {
  id: string;
  condition: string;
  reason: string;
}

export interface NotificationTemplate {
  enabled: boolean;
  template: string;
  recipients: string[];
}

export interface ReminderNotifications {
  enabled: boolean;
  intervals: number[];
  template: string;
}

export interface NotificationSettings {
  stepStartNotification: NotificationTemplate;
  stepCompletionNotification: NotificationTemplate;
  reminderNotifications: ReminderNotifications;
}

export interface TimeoutSettings {
  sessionTimeout: number;
  warningTime: number;
  autoSubmitOnTimeout: boolean;
  allowExtension: boolean;
  maxExtensionTime: number;
}

export interface StepSpecificConfiguration {
  id: string;
  stepId: string;
  stepType: string;
  retrySettings: RetrySettings;
  dependencies: Dependencies;
  notificationSettings: NotificationSettings;
  timeoutSettings: TimeoutSettings;
  customSettings: Record<string, any>;
}

export interface StepConfigurationProps {
  stepId: string;
  stepName: string;
  currentConfiguration?: StepSpecificConfiguration;
  availableSteps?: Array<{ id: string; name: string; order: number }>;
  onSave: (configuration: StepSpecificConfiguration) => void;
  onCancel: () => void;
}

// 設定オプション
export interface RetryConditionOption {
  value: string;
  label: string;
  description: string;
}

export interface RecipientOption {
  value: string;
  label: string;
}