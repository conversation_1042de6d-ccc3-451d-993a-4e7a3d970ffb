/**
 * 通知設定セクション
 * プロダクト憲法に基づくポジティブトーン適用
 */

import React from 'react';
import {
  VStack,
  HStack,
  Switch,
  FormControl,
  FormLabel,
  Textarea,
  CheckboxGroup,
  Checkbox,
  Stack,
  Text,
  SimpleGrid,
  NumberInput,
  NumberInputField,
  Tag,
  TagLabel,
  TagCloseButton,
  Input,
  Button,
} from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { Card } from '../../../design-system/components/Card';
import { NotificationSettings, RecipientOption } from '../types';

interface NotificationSettingsSectionProps {
  settings: NotificationSettings;
  onUpdate: (updates: Partial<NotificationSettings>) => void;
  recipientOptions: RecipientOption[];
}

export const NotificationSettingsSection: React.FC<NotificationSettingsSectionProps> = ({
  settings,
  onUpdate,
  recipientOptions,
}) => {
  const [newInterval, setNewInterval] = React.useState<number>(30);

  const handleReminderIntervalsChange = (intervals: number[]) => {
    onUpdate({
      reminderNotifications: {
        ...settings.reminderNotifications,
        intervals,
      },
    });
  };

  const addReminderInterval = () => {
    if (!settings.reminderNotifications.intervals.includes(newInterval)) {
      handleReminderIntervalsChange([
        ...settings.reminderNotifications.intervals,
        newInterval,
      ]);
    }
  };

  const removeReminderInterval = (intervalToRemove: number) => {
    handleReminderIntervalsChange(
      settings.reminderNotifications.intervals.filter(interval => interval !== intervalToRemove)
    );
  };

  return (
    <Card variant="outline" padding="md">
      <VStack align="stretch" spacing={6}>
        <Text fontSize="lg" fontWeight="semibold">
          📢 通知設定
        </Text>

        {/* ステップ開始通知 */}
        <VStack align="stretch" spacing={3}>
          <HStack justify="space-between">
            <Text fontSize="md" fontWeight="medium">
              ステップ開始通知
            </Text>
            <Switch
              isChecked={settings.stepStartNotification.enabled}
              onChange={(e) =>
                onUpdate({
                  stepStartNotification: {
                    ...settings.stepStartNotification,
                    enabled: e.target.checked,
                  },
                })
              }
              colorScheme="blue"
            />
          </HStack>

          {settings.stepStartNotification.enabled && (
            <VStack align="stretch" spacing={3}>
              <FormControl>
                <FormLabel fontSize="sm">メッセージテンプレート</FormLabel>
                <Textarea
                  value={settings.stepStartNotification.template}
                  onChange={(e) =>
                    onUpdate({
                      stepStartNotification: {
                        ...settings.stepStartNotification,
                        template: e.target.value,
                      },
                    })
                  }
                  placeholder="応援メッセージを入力してください"
                  size="sm"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">送信先</FormLabel>
                <CheckboxGroup
                  value={settings.stepStartNotification.recipients}
                  onChange={(values) =>
                    onUpdate({
                      stepStartNotification: {
                        ...settings.stepStartNotification,
                        recipients: values as string[],
                      },
                    })
                  }
                >
                  <Stack direction="row" wrap="wrap">
                    {recipientOptions.map((option) => (
                      <Checkbox key={option.value} value={option.value} size="sm">
                        {option.label}
                      </Checkbox>
                    ))}
                  </Stack>
                </CheckboxGroup>
              </FormControl>
            </VStack>
          )}
        </VStack>

        {/* ステップ完了通知 */}
        <VStack align="stretch" spacing={3}>
          <HStack justify="space-between">
            <Text fontSize="md" fontWeight="medium">
              ステップ完了通知
            </Text>
            <Switch
              isChecked={settings.stepCompletionNotification.enabled}
              onChange={(e) =>
                onUpdate({
                  stepCompletionNotification: {
                    ...settings.stepCompletionNotification,
                    enabled: e.target.checked,
                  },
                })
              }
              colorScheme="blue"
            />
          </HStack>

          {settings.stepCompletionNotification.enabled && (
            <VStack align="stretch" spacing={3}>
              <FormControl>
                <FormLabel fontSize="sm">お祝いメッセージ</FormLabel>
                <Textarea
                  value={settings.stepCompletionNotification.template}
                  onChange={(e) =>
                    onUpdate({
                      stepCompletionNotification: {
                        ...settings.stepCompletionNotification,
                        template: e.target.value,
                      },
                    })
                  }
                  placeholder="完了を祝福するメッセージを入力してください"
                  size="sm"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">送信先</FormLabel>
                <CheckboxGroup
                  value={settings.stepCompletionNotification.recipients}
                  onChange={(values) =>
                    onUpdate({
                      stepCompletionNotification: {
                        ...settings.stepCompletionNotification,
                        recipients: values as string[],
                      },
                    })
                  }
                >
                  <Stack direction="row" wrap="wrap">
                    {recipientOptions.map((option) => (
                      <Checkbox key={option.value} value={option.value} size="sm">
                        {option.label}
                      </Checkbox>
                    ))}
                  </Stack>
                </CheckboxGroup>
              </FormControl>
            </VStack>
          )}
        </VStack>

        {/* リマインダー通知 */}
        <VStack align="stretch" spacing={3}>
          <HStack justify="space-between">
            <Text fontSize="md" fontWeight="medium">
              応援リマインダー
            </Text>
            <Switch
              isChecked={settings.reminderNotifications.enabled}
              onChange={(e) =>
                onUpdate({
                  reminderNotifications: {
                    ...settings.reminderNotifications,
                    enabled: e.target.checked,
                  },
                })
              }
              colorScheme="blue"
            />
          </HStack>

          {settings.reminderNotifications.enabled && (
            <VStack align="stretch" spacing={3}>
              <FormControl>
                <FormLabel fontSize="sm">応援メッセージ</FormLabel>
                <Textarea
                  value={settings.reminderNotifications.template}
                  onChange={(e) =>
                    onUpdate({
                      reminderNotifications: {
                        ...settings.reminderNotifications,
                        template: e.target.value,
                      },
                    })
                  }
                  placeholder="応援とサポートのメッセージを入力してください"
                  size="sm"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">リマインダー間隔（分）</FormLabel>
                <VStack align="stretch" spacing={2}>
                  <HStack>
                    <NumberInput
                      value={newInterval}
                      onChange={(_, value) => setNewInterval(value || 30)}
                      min={5}
                      max={1440}
                      size="sm"
                      flex={1}
                    >
                      <NumberInputField />
                    </NumberInput>
                    <Button size="sm" leftIcon={<AddIcon />} onClick={addReminderInterval}>
                      追加
                    </Button>
                  </HStack>
                  
                  <Stack direction="row" wrap="wrap" spacing={2}>
                    {settings.reminderNotifications.intervals.map((interval) => (
                      <Tag key={interval} size="sm" colorScheme="blue">
                        <TagLabel>{interval}分</TagLabel>
                        <TagCloseButton onClick={() => removeReminderInterval(interval)} />
                      </Tag>
                    ))}
                  </Stack>
                </VStack>
              </FormControl>
            </VStack>
          )}
        </VStack>
      </VStack>
    </Card>
  );
};