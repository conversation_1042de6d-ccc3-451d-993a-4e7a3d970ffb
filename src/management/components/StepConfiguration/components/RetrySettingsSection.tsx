/**
 * リトライ設定セクション
 * 100文字以内のコード行数制限に準拠
 */

import React from 'react';
import {
  VStack,
  HStack,
  Switch,
  FormControl,
  FormLabel,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  CheckboxGroup,
  Checkbox,
  Stack,
  Text,
} from '@chakra-ui/react';
import { Card } from '../../../design-system/components/Card';
import { RetrySettings, RetryConditionOption } from '../types';

interface RetrySettingsSectionProps {
  settings: RetrySettings;
  onUpdate: (updates: Partial<RetrySettings>) => void;
  retryConditionOptions: RetryConditionOption[];
}

export const RetrySettingsSection: React.FC<RetrySettingsSectionProps> = ({
  settings,
  onUpdate,
  retryConditionOptions,
}) => {
  return (
    <Card variant="outline" padding="md">
      <VStack align="stretch" spacing={4}>
        <Text fontSize="lg" fontWeight="semibold">
          🔄 リトライ設定
        </Text>

        <FormControl>
          <HStack justify="space-between">
            <FormLabel fontSize="sm" mb={0}>
              リトライを許可する
            </FormLabel>
            <Switch
              isChecked={settings.allowRetry}
              onChange={(e) => onUpdate({ allowRetry: e.target.checked })}
              colorScheme="blue"
            />
          </HStack>
        </FormControl>

        {settings.allowRetry && (
          <VStack align="stretch" spacing={4}>
            <FormControl>
              <FormLabel fontSize="sm">最大リトライ回数</FormLabel>
              <NumberInput
                value={settings.maxRetryAttempts}
                onChange={(_, value) => onUpdate({ maxRetryAttempts: value || 1 })}
                min={1}
                max={10}
                size="sm"
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm">リトライ間隔（分）</FormLabel>
              <NumberInput
                value={settings.retryInterval}
                onChange={(_, value) => onUpdate({ retryInterval: value || 60 })}
                min={0}
                size="sm"
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm">リトライ条件</FormLabel>
              <CheckboxGroup
                value={settings.retryConditions}
                onChange={(values) => onUpdate({ retryConditions: values as string[] })}
              >
                <Stack direction="column" spacing={2}>
                  {retryConditionOptions.map((option) => (
                    <Checkbox key={option.value} value={option.value} size="sm">
                      <VStack align="start" spacing={1}>
                        <Text fontSize="sm">{option.label}</Text>
                        <Text fontSize="xs" color="gray.600">
                          {option.description}
                        </Text>
                      </VStack>
                    </Checkbox>
                  ))}
                </Stack>
              </CheckboxGroup>
            </FormControl>

            <FormControl>
              <HStack justify="space-between">
                <FormLabel fontSize="sm" mb={0}>
                  リトライ時にリセット
                </FormLabel>
                <Switch
                  isChecked={settings.resetOnRetry}
                  onChange={(e) => onUpdate({ resetOnRetry: e.target.checked })}
                  colorScheme="blue"
                  size="sm"
                />
              </HStack>
            </FormControl>
          </VStack>
        )}
      </VStack>
    </Card>
  );
};