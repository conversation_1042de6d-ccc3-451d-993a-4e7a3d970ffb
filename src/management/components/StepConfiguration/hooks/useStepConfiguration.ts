/**
 * ステップ設定管理用カスタムフック
 * 単一責任の原則に従い、状態管理を分離
 */

import { useState, useCallback } from 'react';
import {
  StepSpecificConfiguration,
  ConditionalLogic,
  SkipCondition,
} from '../types';
import { useToastPositive } from '../../../design-system/hooks/useToastPositive';

export const useStepConfiguration = (
  stepId: string,
  initialConfig?: StepSpecificConfiguration
) => {
  const { showSuccess, showProgress } = useToastPositive();

  const [configuration, setConfiguration] = useState<StepSpecificConfiguration>(
    initialConfig || {
      id: `config_${stepId}`,
      stepId,
      stepType: 'interview',
      retrySettings: {
        allowRetry: false,
        maxRetryAttempts: 1,
        retryInterval: 60,
        retryConditions: [],
        resetOnRetry: false,
      },
      dependencies: {
        prerequisiteSteps: [],
        conditionalLogic: [],
        skipConditions: [],
      },
      notificationSettings: {
        stepStartNotification: {
          enabled: false,
          template: 'ステップ「{{stepName}}」を開始します。',
          recipients: ['candidate'],
        },
        stepCompletionNotification: {
          enabled: false,
          template: 'ステップ「{{stepName}}」が完了しました。',
          recipients: ['candidate', 'hr'],
        },
        reminderNotifications: {
          enabled: false,
          intervals: [30, 60],
          template: 'ステップ「{{stepName}}」の完了をお待ちしています。',
        },
      },
      timeoutSettings: {
        sessionTimeout: 120,
        warningTime: 10,
        autoSubmitOnTimeout: false,
        allowExtension: true,
        maxExtensionTime: 30,
      },
      customSettings: {},
    }
  );

  // リトライ設定の更新
  const updateRetrySettings = useCallback((updates: Partial<typeof configuration.retrySettings>) => {
    setConfiguration(prev => ({
      ...prev,
      retrySettings: { ...prev.retrySettings, ...updates }
    }));
  }, []);

  // 依存関係の更新
  const updateDependencies = useCallback((updates: Partial<typeof configuration.dependencies>) => {
    setConfiguration(prev => ({
      ...prev,
      dependencies: { ...prev.dependencies, ...updates }
    }));
  }, []);

  // 通知設定の更新
  const updateNotificationSettings = useCallback((updates: Partial<typeof configuration.notificationSettings>) => {
    setConfiguration(prev => ({
      ...prev,
      notificationSettings: { ...prev.notificationSettings, ...updates }
    }));
  }, []);

  // タイムアウト設定の更新
  const updateTimeoutSettings = useCallback((updates: Partial<typeof configuration.timeoutSettings>) => {
    setConfiguration(prev => ({
      ...prev,
      timeoutSettings: { ...prev.timeoutSettings, ...updates }
    }));
  }, []);

  // 条件ロジックの追加
  const addConditionalLogic = useCallback((logic: Omit<ConditionalLogic, 'id'>) => {
    const newLogic: ConditionalLogic = {
      ...logic,
      id: `logic_${Date.now()}`,
    };
    
    updateDependencies({
      conditionalLogic: [...configuration.dependencies.conditionalLogic, newLogic]
    });
    
    showSuccess('条件ロジックを追加しました');
  }, [configuration.dependencies.conditionalLogic, updateDependencies, showSuccess]);

  // 条件ロジックの削除
  const removeConditionalLogic = useCallback((logicId: string) => {
    updateDependencies({
      conditionalLogic: configuration.dependencies.conditionalLogic.filter(logic => logic.id !== logicId)
    });
    
    showProgress('条件ロジックを削除しました');
  }, [configuration.dependencies.conditionalLogic, updateDependencies, showProgress]);

  // スキップ条件の追加
  const addSkipCondition = useCallback((condition: Omit<SkipCondition, 'id'>) => {
    const newCondition: SkipCondition = {
      ...condition,
      id: `skip_${Date.now()}`,
    };
    
    updateDependencies({
      skipConditions: [...configuration.dependencies.skipConditions, newCondition]
    });
    
    showSuccess('スキップ条件を追加しました');
  }, [configuration.dependencies.skipConditions, updateDependencies, showSuccess]);

  // スキップ条件の削除
  const removeSkipCondition = useCallback((conditionId: string) => {
    updateDependencies({
      skipConditions: configuration.dependencies.skipConditions.filter(condition => condition.id !== conditionId)
    });
    
    showProgress('スキップ条件を削除しました');
  }, [configuration.dependencies.skipConditions, updateDependencies, showProgress]);

  // バリデーション
  const validateConfiguration = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // リトライ設定のバリデーション
    if (configuration.retrySettings.maxRetryAttempts < 0 || configuration.retrySettings.maxRetryAttempts > 10) {
      errors.push('最大リトライ回数は0-10回の範囲で設定してください');
    }

    if (configuration.retrySettings.retryInterval < 0) {
      errors.push('リトライ間隔は0以上で設定してください');
    }

    // タイムアウト設定のバリデーション
    if (configuration.timeoutSettings.sessionTimeout < 30) {
      errors.push('セッションタイムアウトは30秒以上で設定してください');
    }

    if (configuration.timeoutSettings.warningTime < 0) {
      errors.push('警告時間は0以上で設定してください');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [configuration]);

  return {
    configuration,
    setConfiguration,
    updateRetrySettings,
    updateDependencies,
    updateNotificationSettings,
    updateTimeoutSettings,
    addConditionalLogic,
    removeConditionalLogic,
    addSkipCondition,
    removeSkipCondition,
    validateConfiguration,
  };
};