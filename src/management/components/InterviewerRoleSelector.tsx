'use client';

/**
 * 面接官役割選択コンポーネント
 * 役割別の質問生成とカスタマイズを提供
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardBody,
  CardHeader,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  Icon,
  useColorModeValue,
  SimpleGrid,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  FormControl,
  FormLabel,
  Select,
  Switch,
  Divider,
  Progress,
  Tooltip,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Textarea,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FiUser,
  FiUsers,
  FiSettings,
  FiTarget,
  FiStar,
  FiBriefcase,
  FiShield,
  FiEye,
  FiHeart,
  FiClock,
  FiTrendingUp,
  FiCheckCircle,
  FiEdit3,
} from 'react-icons/fi';
import {
  InterviewerRole,
  InterviewerRoleType,
  InterviewerPersonality,
  QuestionStyle,
  CreateInterviewerRoleRequest,
} from '@mensetsu-kun/shared/types/interviewer-roles';
import { getDefaultRoleSettings } from '@mensetsu-kun/shared/services/roleBasedQuestionGenerationDemo';

const MotionCard = motion(Card);

// 役割アイコンマッピング
const roleIcons: Record<InterviewerRoleType, any> = {
  hr: FiHeart,
  ceo: FiStar,
  team_leader: FiUsers,
  technical_lead: FiSettings,
  peer: FiUser,
  senior_manager: FiBriefcase,
  external: FiEye,
};

// 役割の表示名
const roleDisplayNames: Record<InterviewerRoleType, string> = {
  hr: '人事',
  ceo: 'CEO・社長',
  team_leader: 'チームリーダー',
  technical_lead: '技術責任者',
  peer: '同僚・メンバー',
  senior_manager: '上級管理職',
  external: '外部面接官',
};

// 役割の説明
const roleDescriptions: Record<InterviewerRoleType, string> = {
  hr: '企業文化適合性とコミュニケーション能力を重視',
  ceo: 'ビジョンとリーダーシップポテンシャルを評価',
  team_leader: 'チームワークと協調性を中心に判定',
  technical_lead: '技術スキルと問題解決能力を深く評価',
  peer: '実務レベルでの協力関係と適合性を確認',
  senior_manager: '管理能力と組織への貢献度を評価',
  external: '客観的で構造化された総合評価',
};

interface InterviewerRoleSelectorProps {
  onRoleSelected: (role: InterviewerRole) => void;
  selectedRole?: InterviewerRole;
  allowCustomization?: boolean;
  disabledRoles?: string[];
  existingLinks?: any[];
}

function InterviewerRoleSelector({
  onRoleSelected,
  selectedRole,
  allowCustomization = true,
  disabledRoles = [],
  existingLinks = [],
}: InterviewerRoleSelectorProps) {
  const [selectedRoleType, setSelectedRoleType] = useState<InterviewerRoleType | null>(
    selectedRole?.type || null
  );
  const [customRole, setCustomRole] = useState<InterviewerRole | null>(null);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const selectedBorderColor = useColorModeValue('blue.500', 'blue.300');

  // デフォルト役割設定の読み込み
  const handleRoleSelect = (roleType: InterviewerRoleType) => {
    setSelectedRoleType(roleType);
    const defaultSettings = getDefaultRoleSettings(roleType);
    onRoleSelected(defaultSettings);
  };

  // カスタマイズモーダルを開く
  const handleCustomizeRole = () => {
    if (selectedRoleType) {
      const baseRole = getDefaultRoleSettings(selectedRoleType);
      setCustomRole(baseRole);
      onOpen();
    }
  };

  // カスタマイズ保存
  const handleSaveCustomization = () => {
    if (customRole) {
      onRoleSelected(customRole);
      setIsCustomizing(true);
      onClose();
      toast({
        title: 'カスタマイズ完了',
        description: '面接官役割の設定が保存されました',
        status: 'success',
        duration: 3000,
      });
    }
  };

  const RoleCard = ({ roleType }: { roleType: InterviewerRoleType }) => {
    const isSelected = selectedRoleType === roleType;
    const isDisabled = disabledRoles.includes(roleType);
    const IconComponent = roleIcons[roleType];
    
    // 既存リンクでこの役割が使われているか確認
    const existingLinkWithRole = existingLinks.find(link => link.interviewerRole?.id === roleType);

    return (
      <MotionCard
        whileHover={!isDisabled ? { scale: 1.02 } : {}}
        whileTap={!isDisabled ? { scale: 0.98 } : {}}
        bg={cardBg}
        borderWidth="2px"
        borderColor={isSelected ? selectedBorderColor : borderColor}
        cursor={isDisabled ? "not-allowed" : "pointer"}
        onClick={() => !isDisabled && handleRoleSelect(roleType)}
        boxShadow={isSelected ? 'lg' : 'sm'}
        transition={{ duration: 0.2, ease: "easeOut" }}
        opacity={isDisabled ? 0.6 : 1}
      >
        <CardHeader pb={2}>
          <HStack justify="space-between">
            <HStack spacing={3}>
              <Icon as={IconComponent} boxSize={6} color="blue.500" />
              <VStack align="start" spacing={0}>
                <Text fontWeight="bold" fontSize="md">
                  {roleDisplayNames[roleType]}
                </Text>
                <Text fontSize="sm" color="gray.600">
                  {roleDescriptions[roleType]}
                </Text>
              </VStack>
            </HStack>
            {isSelected && (
              <Icon as={FiCheckCircle} color="blue.500" boxSize={5} />
            )}
          </HStack>
        </CardHeader>
        
        <CardBody pt={0}>
          <VStack align="stretch" spacing={2}>
            {/* 役割の特性表示 */}
            <HStack spacing={2} flexWrap="wrap">
              <Badge colorScheme="blue" size="sm">評価重視</Badge>
              <Badge colorScheme="green" size="sm">質問スタイル</Badge>
              <Badge colorScheme="purple" size="sm">パーソナリティ</Badge>
            </HStack>
            
            {/* 既存リンクがある場合の警告表示 */}
            {isDisabled && existingLinkWithRole && (
              <Box>
                <Divider my={2} />
                <HStack spacing={2}>
                  <Icon as={FiShield} color="orange.500" boxSize={4} />
                  <Text fontSize="xs" color="orange.600" fontWeight="medium">
                    この役割の面接リンクは既に発行済みです
                  </Text>
                </HStack>
                <Text fontSize="xs" color="gray.500" mt={1}>
                  作成日: {new Date(existingLinkWithRole.createdAt).toLocaleDateString()}
                </Text>
              </Box>
            )}
            
            {isSelected && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                transition={{ duration: 0.3 }}
              >
                <Divider my={2} />
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">
                    選択中
                  </Text>
                  {allowCustomization && (
                    <Button
                      size="sm"
                      variant="outline"
                      leftIcon={<FiEdit3 />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCustomizeRole();
                      }}
                    >
                      カスタマイズ
                    </Button>
                  )}
                </HStack>
              </motion.div>
            )}
          </VStack>
        </CardBody>
      </MotionCard>
    );
  };

  return (
    <Box>
      <VStack align="stretch" spacing={6}>
        {/* ヘッダー */}
        <Box>
          <HStack justify="space-between" mb={4}>
            <VStack align="start" spacing={1}>
              <HStack>
                <Icon as={FiTarget} color="blue.500" />
                <Text fontSize="lg" fontWeight="bold">
                  面接官役割選択
                </Text>
              </HStack>
              <Text fontSize="sm" color="gray.600">
                面接の目的に応じて最適な面接官役割を選択してください
              </Text>
            </VStack>
            
            {selectedRoleType && (
              <Badge colorScheme="blue" px={3} py={1} borderRadius="full">
                {roleDisplayNames[selectedRoleType]} 選択中
              </Badge>
            )}
          </HStack>
        </Box>

        {/* 役割カード一覧 */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
          {(Object.keys(roleDisplayNames) as InterviewerRoleType[]).map((roleType) => (
            <RoleCard key={roleType} roleType={roleType} />
          ))}
        </SimpleGrid>

        {/* 選択された役割の詳細 */}
        {selectedRoleType && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardHeader>
                <HStack>
                  <Icon as={FiTrendingUp} color="green.500" />
                  <Text fontWeight="bold">選択された役割の特性</Text>
                </HStack>
              </CardHeader>
              <CardBody>
                <RoleDetailsDisplay roleType={selectedRoleType} />
              </CardBody>
            </Card>
          </motion.div>
        )}
      </VStack>

      {/* カスタマイズモーダル */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <Icon as={FiEdit3} />
              <Text>面接官役割カスタマイズ</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {customRole && (
              <RoleCustomizationForm
                role={customRole}
                onChange={setCustomRole}
              />
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              キャンセル
            </Button>
            <Button colorScheme="blue" onClick={handleSaveCustomization}>
              保存
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}

// 役割詳細表示コンポーネント
const RoleDetailsDisplay: React.FC<{ roleType: InterviewerRoleType }> = ({ roleType }) => {
  const role = getDefaultRoleSettings(roleType);

  return (
    <VStack align="stretch" spacing={4}>
      <SimpleGrid columns={2} spacing={4}>
        <Box>
          <Text fontSize="sm" fontWeight="bold" mb={2}>
            パーソナリティ
          </Text>
          <VStack align="stretch" spacing={1}>
            <HStack justify="space-between">
              <Text fontSize="xs">フォーマル度</Text>
              <Badge size="sm">{role.personality.formality}</Badge>
            </HStack>
            <HStack justify="space-between">
              <Text fontSize="xs">サポート性</Text>
              <Badge size="sm">{role.personality.supportiveness}</Badge>
            </HStack>
            <HStack justify="space-between">
              <Text fontSize="xs">質問スタイル</Text>
              <Badge size="sm">{role.personality.questioningStyle}</Badge>
            </HStack>
          </VStack>
        </Box>

        <Box>
          <Text fontSize="sm" fontWeight="bold" mb={2}>
            質問フォーカス
          </Text>
          <VStack align="stretch" spacing={1}>
            <HStack justify="space-between">
              <Text fontSize="xs">行動面接</Text>
              <Progress 
                value={role.questionStyle.behavioralFocus * 100} 
                size="sm" 
                colorScheme="blue" 
                flex={1} 
                ml={2}
              />
            </HStack>
            <HStack justify="space-between">
              <Text fontSize="xs">技術重視</Text>
              <Progress 
                value={role.questionStyle.technicalFocus * 100} 
                size="sm" 
                colorScheme="green" 
                flex={1} 
                ml={2}
              />
            </HStack>
            <HStack justify="space-between">
              <Text fontSize="xs">リーダーシップ</Text>
              <Progress 
                value={role.questionStyle.leadershipFocus * 100} 
                size="sm" 
                colorScheme="purple" 
                flex={1} 
                ml={2}
              />
            </HStack>
          </VStack>
        </Box>
      </SimpleGrid>

      <Box>
        <Text fontSize="sm" fontWeight="bold" mb={2}>
          主要評価項目
        </Text>
        <HStack spacing={2} flexWrap="wrap">
          {role.evaluationCriteria.primaryCriteria.map((criteria, index) => (
            <Badge key={index} colorScheme="orange" size="sm">
              {criteria}
            </Badge>
          ))}
        </HStack>
      </Box>
    </VStack>
  );
};

// 役割カスタマイズフォーム
const RoleCustomizationForm: React.FC<{
  role: InterviewerRole;
  onChange: (role: InterviewerRole) => void;
}> = ({ role, onChange }) => {
  const updatePersonality = (updates: Partial<InterviewerPersonality>) => {
    onChange({
      ...role,
      personality: { ...role.personality, ...updates },
    });
  };

  const updateQuestionStyle = (updates: Partial<QuestionStyle>) => {
    onChange({
      ...role,
      questionStyle: { ...role.questionStyle, ...updates },
    });
  };

  return (
    <VStack align="stretch" spacing={6}>
      <Accordion allowToggle>
        <AccordionItem>
          <AccordionButton>
            <Box flex="1" textAlign="left">
              <Text fontWeight="bold">パーソナリティ設定</Text>
            </Box>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel>
            <VStack align="stretch" spacing={4}>
              <FormControl>
                <FormLabel fontSize="sm">フォーマル度</FormLabel>
                <Select
                  value={role.personality.formality}
                  onChange={(e) => updatePersonality({ 
                    formality: e.target.value as 'casual' | 'formal' | 'balanced' 
                  })}
                >
                  <option value="casual">カジュアル</option>
                  <option value="balanced">バランス</option>
                  <option value="formal">フォーマル</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">サポート性</FormLabel>
                <Select
                  value={role.personality.supportiveness}
                  onChange={(e) => updatePersonality({ 
                    supportiveness: e.target.value as 'encouraging' | 'neutral' | 'challenging' 
                  })}
                >
                  <option value="encouraging">励まし重視</option>
                  <option value="neutral">中立</option>
                  <option value="challenging">挑戦的</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">質問スタイル</FormLabel>
                <Select
                  value={role.personality.questioningStyle}
                  onChange={(e) => updatePersonality({ 
                    questioningStyle: e.target.value as 'directive' | 'conversational' | 'socratic' 
                  })}
                >
                  <option value="directive">指示的</option>
                  <option value="conversational">対話的</option>
                  <option value="socratic">ソクラテス式</option>
                </Select>
              </FormControl>
            </VStack>
          </AccordionPanel>
        </AccordionItem>

        <AccordionItem>
          <AccordionButton>
            <Box flex="1" textAlign="left">
              <Text fontWeight="bold">質問フォーカス調整</Text>
            </Box>
            <AccordionIcon />
          </AccordionButton>
          <AccordionPanel>
            <VStack align="stretch" spacing={4}>
              <FormControl>
                <FormLabel fontSize="sm">
                  行動面接重視度: {Math.round(role.questionStyle.behavioralFocus * 100)}%
                </FormLabel>
                <Slider
                  value={role.questionStyle.behavioralFocus * 100}
                  onChange={(value) => updateQuestionStyle({ behavioralFocus: value / 100 })}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">
                  技術重視度: {Math.round(role.questionStyle.technicalFocus * 100)}%
                </FormLabel>
                <Slider
                  value={role.questionStyle.technicalFocus * 100}
                  onChange={(value) => updateQuestionStyle({ technicalFocus: value / 100 })}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>

              <FormControl>
                <FormLabel fontSize="sm">
                  リーダーシップ重視度: {Math.round(role.questionStyle.leadershipFocus * 100)}%
                </FormLabel>
                <Slider
                  value={role.questionStyle.leadershipFocus * 100}
                  onChange={(value) => updateQuestionStyle({ leadershipFocus: value / 100 })}
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb />
                </Slider>
              </FormControl>
            </VStack>
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
    </VStack>
  );
};

export default InterviewerRoleSelector;