import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Box,
  HStack,
  Text,
  VStack,
  Icon,
  useColorModeValue
} from "@chakra-ui/react";
import { FiClock } from "react-icons/fi";
import { textStyles } from "@mensetsu-kun/shared/components/CommonStyles";
import type { GeneratedQuestion, QuestionCategory } from "../services/intelligentQuestionGenerator";

export interface CategoryLabel {
  value: QuestionCategory;
  label: string;
}

export interface QuestionAccordionListProps {
  questions: GeneratedQuestion[];
  categories: CategoryLabel[];
}

const QuestionAccordionList: React.FC<QuestionAccordionListProps> = ({ questions, categories }) => (
  <Accordion allowMultiple>
    {questions.map((question, index) => (
      <AccordionItem key={question.id} border="none">
        <AccordionButton borderRadius="md" _hover={{ bg: useColorModeValue("gray.50", "gray.700") }} py={4}>
          <Box flex="1" textAlign="left">
            <HStack spacing={3} wrap="wrap">
              <Badge colorScheme="blue" fontSize="xs">質問 {index + 1}</Badge>
              <Badge
                colorScheme={question.difficulty === "easy" ? "green" : question.difficulty === "medium" ? "yellow" : "red"}
                fontSize="xs"
              >
                {question.difficulty === "easy" ? "易しい" : question.difficulty === "medium" ? "標準" : "難しい"}
              </Badge>
              <Badge colorScheme="purple" fontSize="xs">
                {categories.find((c) => c.value === question.category)?.label || question.category}
              </Badge>
              <Text {...textStyles.body} fontWeight="medium" noOfLines={1} flex="1">
                {question.text}
              </Text>
            </HStack>
          </Box>
          <AccordionIcon />
        </AccordionButton>
        <AccordionPanel pb={4}>
          <VStack align="stretch" spacing={4} pl={4}>
            <Box>
              <Text {...textStyles.label} mb={2} color="gray.700">
                💭 質問内容
              </Text>
              <Text {...textStyles.body} lineHeight="tall">
                {question.text}
              </Text>
            </Box>
            {question.supportiveElements.encouragementPrefix && (
              <Box>
                <Text {...textStyles.label} mb={2} color="green.600">
                  🌟 励ましメッセージ
                </Text>
                <Text color="green.600" fontSize="sm" lineHeight="tall">
                  {question.supportiveElements.encouragementPrefix}
                </Text>
              </Box>
            )}
            {question.supportiveElements.reassuranceMessage && (
              <Box>
                <Text {...textStyles.label} mb={2} color="blue.600">
                  🤗 安心メッセージ
                </Text>
                <Text color="blue.600" fontSize="sm" lineHeight="tall">
                  {question.supportiveElements.reassuranceMessage}
                </Text>
              </Box>
            )}
            {question.followUpQuestions && question.followUpQuestions.length > 0 && (
              <Box>
                <Text {...textStyles.label} mb={2} color="purple.600">
                  🔄 フォローアップ質問
                </Text>
                <VStack align="stretch" spacing={1} pl={3}>
                  {question.followUpQuestions.map((followUp, i) => (
                    <Text key={i} fontSize="sm" lineHeight="tall">
                      • {followUp}
                    </Text>
                  ))}
                </VStack>
              </Box>
            )}
            <HStack spacing={4} pt={2}>
              <HStack spacing={2}>
                <Icon as={FiClock} color="gray.500" boxSize={4} />
                <Text {...textStyles.caption}>
                  予想回答時間: {Math.floor(question.estimatedAnswerTime / 60)}分{question.estimatedAnswerTime % 60}秒
                </Text>
              </HStack>
            </HStack>
          </VStack>
        </AccordionPanel>
      </AccordionItem>
    ))}
  </Accordion>
);

export default QuestionAccordionList;
