import { test } from 'node:test';
import assert from 'node:assert/strict';
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import QuestionAccordionList from '../QuestionAccordionList';
import type { GeneratedQuestion } from '../../services/intelligentQuestionGenerator';

const sample: GeneratedQuestion = {
  id: 'q1',
  text: 'What is your strength?',
  difficulty: 'easy',
  category: 'self-introduction',
  estimatedAnswerTime: 30,
  supportiveElements: {
    encouragementPrefix: '',
    reassuranceMessage: ''
  },
  followUpQuestions: []
};

test('renders question text', () => {
  const html = renderToStaticMarkup(
    <QuestionAccordionList
      questions={[sample]}
      categories={[{ value: 'self-introduction', label: '自己紹介' }]}
    />
  );
  assert.ok(html.includes('What is your strength?'));
});
