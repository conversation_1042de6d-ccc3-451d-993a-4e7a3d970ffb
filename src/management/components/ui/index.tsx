/**
 * 統一UIコンポーネント
 * Chakra UIをベースにした再利用可能なコンポーネント集
 */

import React from 'react';
import {
  Box,
  Button as ChakraButton,
  ButtonProps as ChakraButtonProps,
  Card as ChakraCard,
  CardBody,
  CardHeader,
  CardFooter,
  CardProps as ChakraCardProps,
  Container as ChakraContainer,
  ContainerProps as ChakraContainerProps,
  Heading,
  HeadingProps,
  Text,
  TextProps,
  VStack,
  HStack,
  StackProps,
  Badge as ChakraBadge,
  BadgeProps as ChakraBadgeProps,
  Alert as ChakraAlert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  AlertProps as ChakraAlertProps,
  FormControl,
  FormLabel,
  FormHelperText,
  FormErrorMessage,
  Input as ChakraInput,
  InputProps as ChakraInputProps,
  Textarea as ChakraTextarea,
  TextareaProps as ChakraTextareaProps,
  Select as ChakraSelect,
  SelectProps as ChakraSelectProps,
  Icon,
  IconProps,
  useColorModeValue,
} from '@chakra-ui/react';

// ========== ページタイトル ==========
export interface PageTitleProps {
  title: string;
  subtitle?: string;
  rightElement?: React.ReactNode;
}

export const PageTitle: React.FC<PageTitleProps> = ({ title, subtitle, rightElement }) => {
  const titleColor = useColorModeValue('brand.600', 'brand.400');
  const subtitleColor = useColorModeValue('ui.text.secondary', 'gray.400');

  return (
    <HStack justify="space-between" align="start" mb={8} w="100%">
      <VStack align="start" spacing={1}>
        <Heading size="lg" color={titleColor}>
          {title}
        </Heading>
        {subtitle && (
          <Text fontSize="md" color={subtitleColor}>
            {subtitle}
          </Text>
        )}
      </VStack>
      {rightElement}
    </HStack>
  );
};

// ========== セクションヘッダー ==========
export interface SectionHeaderProps {
  title: string;
  count?: number;
  rightElement?: React.ReactNode;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({ title, count, rightElement }) => {
  return (
    <HStack justify="space-between" align="center" mb={4} w="100%">
      <HStack spacing={2}>
        <Text fontWeight="semibold" fontSize="md">
          {title}
        </Text>
        {count !== undefined && (
          <Badge colorScheme="gray" size="sm">
            {count}件
          </Badge>
        )}
      </HStack>
      {rightElement}
    </HStack>
  );
};

// ========== ボタン ==========
export interface ButtonProps extends ChakraButtonProps {
  loading?: boolean;
  loadingText?: string;
}

export const Button: React.FC<ButtonProps> = ({ 
  children, 
  loading = false, 
  loadingText,
  isLoading,
  ...props 
}) => {
  return (
    <ChakraButton
      isLoading={loading || isLoading}
      loadingText={loadingText}
      {...props}
    >
      {children}
    </ChakraButton>
  );
};

// ========== カード ==========
export interface CardProps extends ChakraCardProps {
  title?: string;
  isSelected?: boolean;
  isHoverable?: boolean;
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  title,
  isSelected = false,
  isHoverable = true,
  onClick,
  ...props 
}) => {
  const selectedBorderColor = useColorModeValue('brand.500', 'brand.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  return (
    <ChakraCard
      cursor={onClick ? 'pointer' : 'default'}
      onClick={onClick}
      borderWidth={isSelected ? '2px' : '1px'}
      borderColor={isSelected ? selectedBorderColor : undefined}
      bg={isSelected ? useColorModeValue('brand.50', 'brand.900') : undefined}
      _hover={isHoverable && onClick ? {
        bg: hoverBg,
        transform: 'translateY(-2px)',
        boxShadow: 'md',
      } : undefined}
      transition="all 0.2s"
      {...props}
    >
      {title && (
        <CardHeader pb={3}>
          <Text fontWeight="semibold" fontSize="md">
            {title}
          </Text>
        </CardHeader>
      )}
      <CardBody pt={title ? 0 : undefined}>
        {children}
      </CardBody>
    </ChakraCard>
  );
};

// ========== バッジ ==========
export interface BadgeProps extends ChakraBadgeProps {
  category?: 'candidate' | 'company' | 'interview' | 'document' | 'analysis';
}

export const Badge: React.FC<BadgeProps> = ({ category, colorScheme, ...props }) => {
  const getColorScheme = () => {
    if (colorScheme) return colorScheme;
    switch (category) {
      case 'candidate': return 'purple';
      case 'company': return 'blue';
      case 'interview': return 'green';
      case 'document': return 'yellow';
      case 'analysis': return 'pink';
      default: return 'gray';
    }
  };

  return <ChakraBadge colorScheme={getColorScheme()} {...props} />;
};

// ========== アラート ==========
export interface AlertProps extends ChakraAlertProps {
  title?: string;
  description?: string;
}

export const Alert: React.FC<AlertProps> = ({ 
  title, 
  description, 
  children,
  ...props 
}) => {
  return (
    <ChakraAlert borderRadius="md" {...props}>
      <AlertIcon />
      <Box>
        {title && <AlertTitle mb={description ? 1 : 0}>{title}</AlertTitle>}
        {description && <AlertDescription>{description}</AlertDescription>}
        {children}
      </Box>
    </ChakraAlert>
  );
};

// ========== フォームフィールド ==========
export interface FormFieldProps {
  label: string;
  isRequired?: boolean;
  error?: string;
  helpText?: string;
  children: React.ReactElement;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  isRequired = false,
  error,
  helpText,
  children,
}) => {
  return (
    <FormControl isRequired={isRequired} isInvalid={!!error}>
      <FormLabel>{label}</FormLabel>
      {children}
      {helpText && !error && <FormHelperText>{helpText}</FormHelperText>}
      {error && <FormErrorMessage>{error}</FormErrorMessage>}
    </FormControl>
  );
};

// ========== 入力フィールド ==========
export const Input: React.FC<ChakraInputProps> = (props) => {
  return <ChakraInput {...props} />;
};

export const Textarea: React.FC<ChakraTextareaProps> = (props) => {
  return <ChakraTextarea {...props} />;
};

export const Select: React.FC<ChakraSelectProps> = (props) => {
  return <ChakraSelect {...props} />;
};

// ========== コンテナ ==========
export interface ContainerProps extends ChakraContainerProps {
  spacing?: number;
}

export const Container: React.FC<ContainerProps> = ({ 
  children, 
  spacing = 6,
  ...props 
}) => {
  return (
    <ChakraContainer maxW="container.xl" py={6} {...props}>
      <VStack spacing={spacing} align="stretch" w="100%">
        {children}
      </VStack>
    </ChakraContainer>
  );
};

// ========== レイアウトコンポーネント ==========
export const PageContainer: React.FC<StackProps> = ({ children, ...props }) => {
  return (
    <VStack spacing={6} align="stretch" p={6} w="100%" {...props}>
      {children}
    </VStack>
  );
};

export const ContentSection: React.FC<StackProps> = ({ children, ...props }) => {
  return (
    <VStack spacing={4} align="stretch" w="100%" {...props}>
      {children}
    </VStack>
  );
};

// ========== ユーティリティコンポーネント ==========
export interface EmptyStateProps {
  icon?: React.ElementType;
  title: string;
  description?: string;
  action?: React.ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
}) => {
  const bg = useColorModeValue('gray.50', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      p={8}
      bg={bg}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      textAlign="center"
    >
      <VStack spacing={4}>
        {icon && <Icon as={icon} boxSize={12} color="gray.400" />}
        <VStack spacing={2}>
          <Text fontSize="lg" fontWeight="medium">
            {title}
          </Text>
          {description && (
            <Text fontSize="sm" color="gray.600">
              {description}
            </Text>
          )}
        </VStack>
        {action}
      </VStack>
    </Box>
  );
};