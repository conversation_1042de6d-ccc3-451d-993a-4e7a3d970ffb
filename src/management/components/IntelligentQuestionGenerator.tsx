/**
 * インテリジェント質問生成コンポーネント
 * プロダクト憲法第一条（心理的安全性の絶対的保障）に基づく
 */
import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Checkbox,
  CheckboxGroup,
  FormControl,
  FormLabel,
  HStack,
  Input,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  Stack,
  Switch,
  Text,
  Textarea,
  VStack,
  useToast,
  Divider,
  Badge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  useDisclosure,
  Container,
  useColorModeValue,
  Icon,
  Progress,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Tooltip,
  Spinner,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { 
  FiSettings, 
  FiUser, 
  FiHelpCircle, 
  FiStar,
  FiShield,
  FiClock,
  FiTarget,
  FiCheck,
  FiInfo,
  FiDownload,
} from 'react-icons/fi';
import {
  IntelligentQuestionGenerator as QuestionGeneratorService,
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  GeneratedQuestion,
  QuestionCategory,
} from '../services/intelligentQuestionGenerator';
import { DataService, CompanyInfo } from '../lib/unified-data';
import { textStyles, spacing, commonStyles } from '@mensetsu-kun/shared/components/CommonStyles';

import QuestionAccordionList from "./QuestionAccordionList";
// Animation variants - inline definition to avoid import issues
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
};

const staggerItemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.3 }
  }
};

interface IntelligentQuestionGeneratorProps {
  onQuestionsGenerated?: (questions: GeneratedQuestion[]) => void;
  companyInfo?: CompanyInfo;
  candidateResumeData?: {
    personalInfo?: {
      name?: string;
      currentPosition?: string;
      yearsOfExperience?: number;
    };
    workExperience?: Array<{
      company: string;
      position: string;
      duration: string;
      achievements?: string[];
    }>;
    education?: Array<{
      institution: string;
      degree: string;
      field?: string;
    }>;
    certifications?: string[];
    projects?: Array<{
      name: string;
      description: string;
      technologies?: string[];
    }>;
    languages?: Array<{
      language: string;
      proficiency: string;
    }>;
  };
}

const QUESTION_CATEGORIES: Array<{ value: QuestionCategory; label: string }> = [
  { value: 'self-introduction', label: '自己紹介' },
  { value: 'experience-skills', label: '経験・スキル' },
  { value: 'motivation', label: '志望動機' },
  { value: 'problem-solving', label: '問題解決' },
  { value: 'teamwork', label: 'チームワーク' },
  { value: 'leadership', label: 'リーダーシップ' },
  { value: 'career-goals', label: 'キャリア目標' },
  { value: 'company-culture-fit', label: '企業文化適合性' },
  { value: 'technical', label: '技術的質問' },
  { value: 'behavioral', label: '行動面接' },
  { value: 'situational', label: '状況判断' },
];

const EXPERIENCE_LEVELS = [
  { value: 'entry', label: '新卒・未経験' },
  { value: 'mid', label: '中堅（2-5年）' },
  { value: 'senior', label: 'シニア（5-10年）' },
  { value: 'executive', label: 'エグゼクティブ（10年以上）' },
];

const DIFFICULTY_LEVELS = [
  { value: 'easy', label: '易しい' },
  { value: 'medium', label: '標準' },
  { value: 'hard', label: '難しい' },
  { value: 'adaptive', label: '適応的' },
];

// Motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);

export const IntelligentQuestionGeneratorComponent: React.FC<IntelligentQuestionGeneratorProps> = ({
  onQuestionsGenerated,
  companyInfo: initialCompanyInfo,
  candidateResumeData,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedQuestions, setGeneratedQuestions] = useState<GeneratedQuestion[]>([]);
  const [generationResponse, setGenerationResponse] = useState<QuestionGenerationResponse | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  const toast = useToast();

  // Color mode values
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // フォーム状態
  const [formData, setFormData] = useState({
    // 企業情報
    companyInfo: {
      name: initialCompanyInfo?.name || '',
      industry: initialCompanyInfo?.industry || '',
      position: initialCompanyInfo?.position || '',
      requirements: [] as string[],
      culture: initialCompanyInfo?.culture || '',
      interviewStyle: initialCompanyInfo?.interviewStyle || '',
    },
    // 候補者プロファイル
    candidateProfile: {
      experienceLevel: 'mid' as const,
      skills: [] as string[],
      background: [] as string[],
      previousInterviews: 0,
      resumeData: candidateResumeData || undefined,
    },
    // 質問設定
    questionSettings: {
      totalQuestions: 5,
      categories: ['self-introduction', 'experience-skills'] as QuestionCategory[],
      difficulty: 'medium' as const,
      estimatedDuration: 30,
      includeWarmup: true,
      includeFollowUp: true,
    },
    // 適応的設定
    adaptiveSettings: {
      personalizeForCandidate: true,
      adjustForAnxiety: true,
      emphasizeGrowth: true,
      avoidNegativeLanguage: true,
    },
  });

  const questionGenerator = new QuestionGeneratorService();

  const handleGenerateQuestions = async () => {
    setIsGenerating(true);
    
    try {
      const request: QuestionGenerationRequest = {
        companyInfo: {
          ...formData.companyInfo,
          requirements: formData.companyInfo.requirements.filter(r => r.trim()),
        },
        candidateProfile: {
          ...formData.candidateProfile,
          skills: formData.candidateProfile.skills.filter(s => s.trim()),
          background: formData.candidateProfile.background.filter(b => b.trim()),
        },
        questionSettings: formData.questionSettings,
        adaptiveSettings: formData.adaptiveSettings,
      };

      const response = await questionGenerator.generateQuestions(request);
      
      setGeneratedQuestions(response.questions);
      setGenerationResponse(response);
      onQuestionsGenerated?.(response.questions);
      
      toast({
        title: '質問生成完了',
        description: `${response.questions.length}個の質問を生成しました`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('質問生成エラー:', error);
      toast({
        title: '質問生成エラー',
        description: '質問の生成に失敗しました。設定を確認してもう一度お試しください。',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClearResults = () => {
    setGeneratedQuestions([]);
    setGenerationResponse(null);
    onClose();
  };

  const updateArrayField = (
    path: string,
    value: string,
    action: 'add' | 'remove'
  ) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let target = newData as any;
      
      for (let i = 0; i < keys.length - 1; i++) {
        target = target[keys[i]];
      }
      
      const finalKey = keys[keys.length - 1];
      const currentArray = target[finalKey] as string[];
      
      if (action === 'add' && value.trim() && !currentArray.includes(value)) {
        target[finalKey] = [...currentArray, value];
      } else if (action === 'remove') {
        target[finalKey] = currentArray.filter(item => item !== value);
      }
      
      return newData;
    });
  };

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="container.xl" py={spacing.sectionSpacing}>
        <MotionBox
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <VStack spacing={spacing.sectionSpacing} align="stretch">
            {/* ヘッダーセクション */}
            <MotionCard
              variants={cardVariants}
              bg={cardBgColor}
              borderColor={borderColor}
              borderWidth="1px"
              borderRadius="lg"
              boxShadow="sm"
              p={spacing.cardPadding}
            >
              <VStack spacing={4} align="center" textAlign="center">
                <HStack spacing={3}>
                  <Icon as={FiStar} boxSize={6} color="blue.500" />
                  <Text {...textStyles.heading} color="blue.600">
                    インテリジェント質問生成システム
                  </Text>
                  <Icon as={FiStar} boxSize={6} color="blue.500" />
                </HStack>
                
                <Text {...textStyles.body} color="gray.600" maxW="2xl">
                  プロダクト憲法の3原則に基づき、AI技術と心理的安全性を組み合わせた革新的な質問生成システムです。
                  候補者一人ひとりの状況に合わせて最適化された質問を自動生成し、成長を促す面接体験を提供します。
                </Text>

                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} w="full" maxW="2xl">
                  <Flex align="center" justify="center" direction="column" p={3}>
                    <Icon as={FiShield} boxSize={5} color="blue.500" mb={2} />
                    <Badge colorScheme="blue" size="sm">心理的安全性保障</Badge>
                  </Flex>
                  <Flex align="center" justify="center" direction="column" p={3}>
                    <Icon as={FiTarget} boxSize={5} color="green.500" mb={2} />
                    <Badge colorScheme="green" size="sm">適応的調整</Badge>
                  </Flex>
                  <Flex align="center" justify="center" direction="column" p={3}>
                    <Icon as={FiStar} boxSize={5} color="purple.500" mb={2} />
                    <Badge colorScheme="purple" size="sm">成長志向</Badge>
                  </Flex>
                  <Flex align="center" justify="center" direction="column" p={3}>
                    <Icon as={FiCheck} boxSize={5} color="orange.500" mb={2} />
                    <Badge colorScheme="orange" size="sm">透明性</Badge>
                  </Flex>
                </SimpleGrid>
              </VStack>
            </MotionCard>

            {/* 設定フォーム */}
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={spacing.sectionSpacing}>
              {/* 企業情報 */}
              <MotionCard
                variants={cardVariants}
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
              >
                <CardHeader>
                  <HStack spacing={3}>
                    <Icon as={FiSettings} color="blue.500" boxSize={spacing.iconBoxSize} />
                    <Text {...textStyles.subheading}>企業情報</Text>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={4}>
                      <FormControl>
                        <FormLabel {...textStyles.label}>企業名</FormLabel>
                        <Input
                          value={formData.companyInfo.name}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            companyInfo: { ...prev.companyInfo, name: e.target.value }
                          }))}
                          placeholder="株式会社サンプル"
                          focusBorderColor="blue.500"
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel {...textStyles.label}>業界</FormLabel>
                        <Input
                          value={formData.companyInfo.industry}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            companyInfo: { ...prev.companyInfo, industry: e.target.value }
                          }))}
                          placeholder="IT・ソフトウェア"
                          focusBorderColor="blue.500"
                        />
                      </FormControl>
                    </HStack>
                    <FormControl>
                      <FormLabel {...textStyles.label}>募集ポジション</FormLabel>
                      <Input
                        value={formData.companyInfo.position}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          companyInfo: { ...prev.companyInfo, position: e.target.value }
                        }))}
                        placeholder="シニアエンジニア"
                        focusBorderColor="blue.500"
                      />
                    </FormControl>
                    <FormControl>
                      <FormLabel {...textStyles.label}>企業文化</FormLabel>
                      <Textarea
                        value={formData.companyInfo.culture}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          companyInfo: { ...prev.companyInfo, culture: e.target.value }
                        }))}
                        placeholder="フラットな組織構造、チャレンジ精神を重視..."
                        rows={3}
                        focusBorderColor="blue.500"
                        resize="vertical"
                      />
                    </FormControl>
                  </VStack>
                </CardBody>
              </MotionCard>

              {/* 候補者プロファイル */}
              <MotionCard
                variants={cardVariants}
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
              >
                <CardHeader>
                  <HStack spacing={3}>
                    <Icon as={FiUser} color="green.500" boxSize={spacing.iconBoxSize} />
                    <Text {...textStyles.subheading}>候補者プロファイル</Text>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={4}>
                      <FormControl>
                        <FormLabel {...textStyles.label}>経験レベル</FormLabel>
                        <Select
                          value={formData.candidateProfile.experienceLevel}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            candidateProfile: {
                              ...prev.candidateProfile,
                              experienceLevel: e.target.value as any
                            }
                          }))}
                          focusBorderColor="green.500"
                        >
                          {EXPERIENCE_LEVELS.map(level => (
                            <option key={level.value} value={level.value}>
                              {level.label}
                            </option>
                          ))}
                        </Select>
                      </FormControl>
                      <FormControl>
                        <FormLabel {...textStyles.label}>過去の面接回数</FormLabel>
                        <NumberInput
                          value={formData.candidateProfile.previousInterviews}
                          onChange={(valueString) => setFormData(prev => ({
                            ...prev,
                            candidateProfile: {
                              ...prev.candidateProfile,
                              previousInterviews: parseInt(valueString) || 0
                            }
                          }))}
                          min={0}
                          focusBorderColor="green.500"
                        >
                          <NumberInputField />
                          <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                          </NumberInputStepper>
                        </NumberInput>
                      </FormControl>
                    </HStack>

                    {/* 履歴書データがある場合の表示 */}
                    {candidateResumeData && (
                      <Box
                        p={4}
                        bg={useColorModeValue('green.50', 'green.900')}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={useColorModeValue('green.200', 'green.700')}
                      >
                        <VStack align="stretch" spacing={3}>
                          <HStack>
                            <Icon as={FiCheck} color="green.500" />
                            <Text fontWeight="bold" color="green.700">
                              履歴書データを読み込み済み
                            </Text>
                          </HStack>
                          
                          {candidateResumeData.personalInfo && (
                            <Box>
                              <Text fontSize="sm" fontWeight="semibold">基本情報:</Text>
                              <Text fontSize="sm">
                                {candidateResumeData.personalInfo.name && `氏名: ${candidateResumeData.personalInfo.name}`}
                                {candidateResumeData.personalInfo.currentPosition && ` / 現職: ${candidateResumeData.personalInfo.currentPosition}`}
                                {candidateResumeData.personalInfo.yearsOfExperience && ` / 経験年数: ${candidateResumeData.personalInfo.yearsOfExperience}年`}
                              </Text>
                            </Box>
                          )}
                          
                          {candidateResumeData.workExperience && candidateResumeData.workExperience.length > 0 && (
                            <Box>
                              <Text fontSize="sm" fontWeight="semibold">最新の職歴:</Text>
                              <Text fontSize="sm">
                                {candidateResumeData.workExperience[0].company} - {candidateResumeData.workExperience[0].position}
                              </Text>
                            </Box>
                          )}
                          
                          {candidateResumeData.certifications && candidateResumeData.certifications.length > 0 && (
                            <Box>
                              <Text fontSize="sm" fontWeight="semibold">資格:</Text>
                              <Text fontSize="sm">
                                {candidateResumeData.certifications.slice(0, 3).join(', ')}
                                {candidateResumeData.certifications.length > 3 && ` 他${candidateResumeData.certifications.length - 3}件`}
                              </Text>
                            </Box>
                          )}
                          
                          <Text fontSize="xs" color="gray.600">
                            この情報を基に、より個人化された質問を生成します
                          </Text>
                        </VStack>
                      </Box>
                    )}
                  </VStack>
                </CardBody>
              </MotionCard>

              {/* 質問設定 */}
              <MotionCard
                variants={cardVariants}
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
              >
                <CardHeader>
                  <HStack spacing={3}>
                    <Icon as={FiHelpCircle} color="purple.500" boxSize={spacing.iconBoxSize} />
                    <Text {...textStyles.subheading}>質問設定</Text>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack spacing={4}>
                      <FormControl>
                        <FormLabel {...textStyles.label}>質問数</FormLabel>
                        <NumberInput
                          value={formData.questionSettings.totalQuestions}
                          onChange={(valueString) => setFormData(prev => ({
                            ...prev,
                            questionSettings: {
                              ...prev.questionSettings,
                              totalQuestions: parseInt(valueString) || 5
                            }
                          }))}
                          min={1}
                          max={20}
                          focusBorderColor="purple.500"
                        >
                          <NumberInputField />
                          <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                          </NumberInputStepper>
                        </NumberInput>
                      </FormControl>
                      <FormControl>
                        <FormLabel {...textStyles.label}>難易度</FormLabel>
                        <Select
                          value={formData.questionSettings.difficulty}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            questionSettings: {
                              ...prev.questionSettings,
                              difficulty: e.target.value as any
                            }
                          }))}
                          focusBorderColor="purple.500"
                        >
                          {DIFFICULTY_LEVELS.map(level => (
                            <option key={level.value} value={level.value}>
                              {level.label}
                            </option>
                          ))}
                        </Select>
                      </FormControl>
                      <FormControl>
                        <FormLabel {...textStyles.label}>予想時間（分）</FormLabel>
                        <NumberInput
                          value={formData.questionSettings.estimatedDuration}
                          onChange={(valueString) => setFormData(prev => ({
                            ...prev,
                            questionSettings: {
                              ...prev.questionSettings,
                              estimatedDuration: parseInt(valueString) || 30
                            }
                          }))}
                          min={10}
                          max={180}
                          focusBorderColor="purple.500"
                        >
                          <NumberInputField />
                          <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                          </NumberInputStepper>
                        </NumberInput>
                      </FormControl>
                    </HStack>

                    <FormControl>
                      <FormLabel {...textStyles.label}>質問カテゴリ</FormLabel>
                      <CheckboxGroup
                        value={formData.questionSettings.categories}
                        onChange={(values) => setFormData(prev => ({
                          ...prev,
                          questionSettings: {
                            ...prev.questionSettings,
                            categories: values as QuestionCategory[]
                          }
                        }))}
                      >
                        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={2}>
                          {QUESTION_CATEGORIES.map(category => (
                            <Checkbox 
                              key={category.value} 
                              value={category.value}
                              colorScheme="purple"
                              size="sm"
                            >
                              <Text fontSize="sm">{category.label}</Text>
                            </Checkbox>
                          ))}
                        </SimpleGrid>
                      </CheckboxGroup>
                    </FormControl>

                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>ウォームアップ質問を含める</Text>
                        <Switch
                          isChecked={formData.questionSettings.includeWarmup}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            questionSettings: {
                              ...prev.questionSettings,
                              includeWarmup: e.target.checked
                            }
                          }))}
                          colorScheme="purple"
                        />
                      </HStack>
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>フォローアップ質問を含める</Text>
                        <Switch
                          isChecked={formData.questionSettings.includeFollowUp}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            questionSettings: {
                              ...prev.questionSettings,
                              includeFollowUp: e.target.checked
                            }
                          }))}
                          colorScheme="purple"
                        />
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </MotionCard>

              {/* 適応的設定 */}
              <MotionCard
                variants={cardVariants}
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
              >
                <CardHeader>
                  <HStack spacing={3}>
                    <Icon as={FiShield} color="orange.500" boxSize={spacing.iconBoxSize} />
                    <Text {...textStyles.subheading}>心理的安全性・適応設定</Text>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>候補者に合わせて個別化</Text>
                        <Switch
                          isChecked={formData.adaptiveSettings.personalizeForCandidate}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            adaptiveSettings: {
                              ...prev.adaptiveSettings,
                              personalizeForCandidate: e.target.checked
                            }
                          }))}
                          colorScheme="orange"
                        />
                      </HStack>
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>不安レベルに応じて調整</Text>
                        <Switch
                          isChecked={formData.adaptiveSettings.adjustForAnxiety}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            adaptiveSettings: {
                              ...prev.adaptiveSettings,
                              adjustForAnxiety: e.target.checked
                            }
                          }))}
                          colorScheme="orange"
                        />
                      </HStack>
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>成長志向を強調</Text>
                        <Switch
                          isChecked={formData.adaptiveSettings.emphasizeGrowth}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            adaptiveSettings: {
                              ...prev.adaptiveSettings,
                              emphasizeGrowth: e.target.checked
                            }
                          }))}
                          colorScheme="orange"
                        />
                      </HStack>
                      <HStack justify="space-between">
                        <Text {...textStyles.label}>ネガティブ表現を回避</Text>
                        <Switch
                          isChecked={formData.adaptiveSettings.avoidNegativeLanguage}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            adaptiveSettings: {
                              ...prev.adaptiveSettings,
                              avoidNegativeLanguage: e.target.checked
                            }
                          }))}
                          colorScheme="orange"
                        />
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </MotionCard>
            </SimpleGrid>

            {/* 生成ボタン */}
            <MotionBox
              variants={staggerItemVariants}
              textAlign="center"
              py={spacing.cardPadding}
            >
              <Button
                colorScheme="blue"
                size="lg"
                onClick={handleGenerateQuestions}
                isLoading={isGenerating}
                loadingText="質問を生成中..."
                isDisabled={!formData.companyInfo.name || formData.questionSettings.categories.length === 0}
                leftIcon={<Icon as={FiTarget} />}
                px={spacing.buttonPadding.px}
                py={spacing.buttonPadding.py}
                borderRadius="lg"
                boxShadow="lg"
                _hover={{
                  transform: 'translateY(-2px)',
                  boxShadow: 'xl',
                }}
                transition="all 0.2s"
              >
                質問を生成
              </Button>
              {!formData.companyInfo.name || formData.questionSettings.categories.length === 0 ? (
                <Text {...textStyles.caption} mt={2}>
                  企業名と質問カテゴリを設定してください
                </Text>
              ) : null}
            </MotionBox>

            {/* 生成結果 */}
            {generatedQuestions.length > 0 && (
              <MotionCard
                variants={cardVariants}
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
              >
                <CardHeader>
                  <HStack justify="space-between" align="center">
                    <HStack spacing={3}>
                      <Icon as={FiCheck} color="green.500" boxSize={spacing.iconBoxSize} />
                      <Text {...textStyles.subheading}>
                        生成された質問 ({generatedQuestions.length}個)
                      </Text>
                      <Badge colorScheme="green" variant="solid">
                        生成完了
                      </Badge>
                    </HStack>
                    <HStack spacing={2}>
                      <Button 
                        size="sm" 
                        leftIcon={<Icon as={FiDownload} />}
                        variant="outline"
                        colorScheme="blue"
                      >
                        エクスポート
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        colorScheme="red"
                        onClick={onOpen}
                      >
                        結果をクリア
                      </Button>
                    </HStack>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack spacing={6} align="stretch">
                    <QuestionAccordionList questions={generatedQuestions} categories={QUESTION_CATEGORIES} />


                    {generationResponse && (
                      <Box 
                        mt={4} 
                        p={4} 
                        bg={useColorModeValue('blue.50', 'blue.900')} 
                        borderRadius="lg"
                        borderWidth="1px"
                        borderColor={useColorModeValue('blue.200', 'blue.700')}
                      >
                        <HStack spacing={3} mb={3}>
                          <Icon as={FiInfo} color="blue.500" boxSize={spacing.iconBoxSize} />
                          <Text {...textStyles.label} color="blue.600">
                            生成メタデータ
                          </Text>
                        </HStack>
                        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
                          <Stat size="sm">
                            <StatLabel fontSize="xs">処理時間</StatLabel>
                            <StatNumber fontSize="md" color="blue.600">
                              {generationResponse.metadata.processingTime}ms
                            </StatNumber>
                          </Stat>
                          <Stat size="sm">
                            <StatLabel fontSize="xs">品質スコア</StatLabel>
                            <StatNumber fontSize="md" color="green.600">
                              {(generationResponse.metadata.qualityScore * 100).toFixed(1)}%
                            </StatNumber>
                          </Stat>
                          <Stat size="sm">
                            <StatLabel fontSize="xs">予想合計時間</StatLabel>
                            <StatNumber fontSize="md" color="purple.600">
                              {generationResponse.sessionConfig.totalDuration}分
                            </StatNumber>
                          </Stat>
                          {generationResponse.sessionConfig.breakPoints.length > 0 && (
                            <Stat size="sm">
                              <StatLabel fontSize="xs">休憩ポイント</StatLabel>
                              <StatNumber fontSize="md" color="orange.600">
                                {generationResponse.sessionConfig.breakPoints.length}箇所
                              </StatNumber>
                            </Stat>
                          )}
                        </SimpleGrid>
                      </Box>
                    )}
                  </VStack>
                </CardBody>
              </MotionCard>
            )}
          </VStack>
        </MotionBox>
      </Container>

      {/* 確認ダイアログ */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              結果をクリア
            </AlertDialogHeader>
            <AlertDialogBody>
              生成された質問をすべてクリアしますか？この操作は取り消せません。
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onClose}>
                キャンセル
              </Button>
              <Button colorScheme="red" onClick={handleClearResults} ml={3}>
                クリア
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default IntelligentQuestionGeneratorComponent;