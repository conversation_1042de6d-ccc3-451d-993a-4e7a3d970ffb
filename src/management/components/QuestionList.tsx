import {
  Box,
  List,
  ListItem,
  Text,
  VStack,
  Badge,
  HStack,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react';
import { QuestionIcon, TimeIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { staggerContainer, staggerItem } from '../motion/index';

const MotionBox = motion(Box);
const MotionListItem = motion(ListItem);

interface Question {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number;
}

interface QuestionListProps {
  questions: Question[];
}

export const QuestionList = ({ questions }: QuestionListProps) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const emptyBg = useColorModeValue('gray.50', 'gray.700');

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'green';
      case 'medium': return 'yellow';
      case 'hard': return 'red';
      default: return 'gray';
    }
  };

  if (!questions.length) {
    return (
      <Box p={spacing.cardPadding} bg={emptyBg} borderRadius="md" textAlign="center">
        <VStack spacing={2}>
          <Icon as={QuestionIcon} boxSize={8} color="gray.400" />
          <Text color="gray.500" {...textStyles.body}>
            質問がまだ生成されていません
          </Text>
          <Text {...textStyles.caption}>
            上記フォームから質問スクリプトを生成してください
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <MotionBox {...staggerContainer}>
      <List spacing={spacing.cardPadding}>
        {questions.map((question, index) => (
          <MotionListItem
            key={`question-${question.id}-${index}`}
            {...staggerItem}
            p={spacing.cardPadding}
            bg={bgColor}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
            boxShadow="sm"
            _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
            sx={{ transition: 'all 0.2s ease' }}
          >
            <VStack align="start" spacing={spacing.cardPadding.base}>
              <HStack justify="space-between" w="full">
                <HStack spacing={2}>
                  <Icon as={QuestionIcon} color="blue.500" />
                  <Text {...textStyles.label}>
                    質問 {index + 1}
                  </Text>
                </HStack>
                <HStack spacing={2}>
                  {question.difficulty && (
                    <Badge colorScheme={getDifficultyColor(question.difficulty)} size="sm">
                      {question.difficulty.toUpperCase()}
                    </Badge>
                  )}
                  {question.estimatedTime && (
                    <HStack spacing={1}>
                      <TimeIcon color="gray.500" />
                      <Text {...textStyles.caption}>
                        {Math.round(question.estimatedTime / 60)}分
                      </Text>
                    </HStack>
                  )}
                </HStack>
              </HStack>

              <Text {...textStyles.body} lineHeight="tall">
                {question.text}
              </Text>

              {question.category && (
                <Badge colorScheme="blue" variant="subtle">
                  {question.category}
                </Badge>
              )}
            </VStack>
          </MotionListItem>
        ))}
      </List>
    </MotionBox>
  );
}; 