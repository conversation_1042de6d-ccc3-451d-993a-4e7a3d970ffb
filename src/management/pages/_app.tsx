import { ChakraProvider } from '@chakra-ui/react';
import { AuthProvider } from '../components/AuthProvider';
import { theme } from '../theme';
import type { AppProps } from 'next/app';

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <ChakraProvider theme={theme}>
      <AuthProvider>
        <Component {...pageProps} />
      </AuthProvider>
    </ChakraProvider>
  );
}

export default MyApp;