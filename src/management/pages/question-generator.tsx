/**
 * 質問生成システム管理画面
 * インテリジェント質問生成システムのメインページ
 */
import React, { useState } from 'react';
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Container,
  VStack,
  Heading,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  Button,
  useColorModeValue,
} from '@chakra-ui/react';
import { ChevronRightIcon, AddIcon } from '@chakra-ui/icons';
import Link from 'next/link';
import { useRouter } from 'next/router';
import IntelligentQuestionGeneratorComponent from '../components/IntelligentQuestionGenerator';
import { GeneratedQuestion } from '../services/intelligentQuestionGenerator';

const QuestionGeneratorPage: React.FC = () => {
  const router = useRouter();
  const [generatedQuestions, setGeneratedQuestions] = useState<GeneratedQuestion[]>([]);
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const handleQuestionsGenerated = (questions: GeneratedQuestion[]) => {
    setGeneratedQuestions(questions);
  };

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
      <Container maxW="8xl" py={8}>
        <VStack spacing={6} align="stretch">
          {/* パンくずリスト */}
          <Breadcrumb 
            spacing="8px" 
            separator={<ChevronRightIcon color="gray.500" />}
            mb={6}
          >
            <BreadcrumbItem>
              <BreadcrumbLink as={Link} href="/">
                管理画面
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink>質問生成システム</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>

          {/* ヘッダー */}
          <Box mb={8}>
            <Heading as="h1" size="xl" mb={2}>
              インテリジェント質問生成システム
            </Heading>
            <Text fontSize="lg" color="gray.600">
              AI技術と心理的安全性を組み合わせた質問生成システム
            </Text>
          </Box>

          {/* メインコンテンツ */}
          <Box
            bg={bgColor}
            borderWidth="1px"
            borderColor={borderColor}
            borderRadius="lg"
            boxShadow="sm"
          >
            <Tabs variant="enclosed">
              <TabList>
                <Tab>質問生成</Tab>
                <Tab>候補者プロファイル</Tab>
                <Tab>質問テンプレート管理</Tab>
                <Tab>生成履歴</Tab>
                <Tab>効果分析</Tab>
              </TabList>

              <TabPanels>
                {/* 質問生成タブ */}
                <TabPanel p={0}>
                  <IntelligentQuestionGeneratorComponent
                    onQuestionsGenerated={handleQuestionsGenerated}
                  />
                </TabPanel>

                {/* 候補者プロファイルタブ */}
                <TabPanel p={6}>
                  <VStack spacing={6} align="stretch">
                    <Box textAlign="center">
                      <Heading size="md" mb={4}>
                        候補者プロファイル管理
                      </Heading>
                      <Text color="gray.600" mb={6}>
                        履歴書解析による個人化された面接質問生成のためのプロファイル管理
                      </Text>
                      
                      <Button
                        leftIcon={<AddIcon />}
                        colorScheme="purple"
                        size="lg"
                        onClick={() => router.push('/candidate-profile-creation')}
                      >
                        新しいプロファイルを作成
                      </Button>
                    </Box>

                    <Box 
                      p={6} 
                      bg={useColorModeValue('blue.50', 'blue.900')} 
                      borderRadius="md"
                      textAlign="center"
                    >
                      <Text fontSize="sm" color="blue.700">
                        💡 候補者プロファイル機能により、履歴書を解析して個人の経験やスキルに基づいた
                        カスタマイズされた面接質問を生成できます
                      </Text>
                    </Box>
                  </VStack>
                </TabPanel>

                {/* 質問テンプレート管理タブ */}
                <TabPanel p={6}>
                  <Box textAlign="center" py={12}>
                    <Heading size="md" color="gray.500" mb={4}>
                      質問テンプレート管理
                    </Heading>
                    <Text color="gray.500">
                      カスタム質問テンプレートの作成・編集・管理機能
                    </Text>
                    <Text fontSize="sm" color="gray.400" mt={2}>
                      （今後実装予定）
                    </Text>
                  </Box>
                </TabPanel>

                {/* 生成履歴タブ */}
                <TabPanel p={6}>
                  <Box textAlign="center" py={12}>
                    <Heading size="md" color="gray.500" mb={4}>
                      質問生成履歴
                    </Heading>
                    <Text color="gray.500">
                      過去の質問生成セッションの履歴と詳細分析
                    </Text>
                    <Text fontSize="sm" color="gray.400" mt={2}>
                      （今後実装予定）
                    </Text>
                  </Box>
                </TabPanel>

                {/* 効果分析タブ */}
                <TabPanel p={6}>
                  <Box textAlign="center" py={12}>
                    <Heading size="md" color="gray.500" mb={4}>
                      質問効果分析
                    </Heading>
                    <Text color="gray.500">
                      生成された質問の効果測定と改善提案
                    </Text>
                    <Text fontSize="sm" color="gray.400" mt={2}>
                      （今後実装予定）
                    </Text>
                  </Box>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default QuestionGeneratorPage;