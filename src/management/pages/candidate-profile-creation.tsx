/**
 * 候補者プロファイル作成画面
 * 履歴書アップロード・解析・プロファイル管理
 */
import React, { useState } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Alert,
  AlertIcon,
  Badge,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useToast,
  useColorModeValue,
  Divider,
  SimpleGrid,
  Flex,
  Spacer,
} from '@chakra-ui/react';
import { ArrowBackIcon, CheckCircleIcon } from '@chakra-ui/icons';
import { useRouter } from 'next/router';
import { motion, AnimatePresence } from 'framer-motion';
import { DocumentUploader, DocumentType } from '../components/DocumentUploader';
import type { 
  CandidateProfile, 
  CreateCandidateProfileRequest,
  ResumeFile,
  ParsedResumeContent
} from '@mensetsu-kun/shared/types/candidate-profile';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface CandidateProfileFormData {
  personalInfo: {
    name: string;
    email: string;
    phone?: string;
    location?: string;
  };
  jobSearchPreferences: {
    targetIndustries: string[];
    targetRoles: string[];
    locationPreferences: string[];
    workArrangement: 'onsite' | 'remote' | 'hybrid' | 'flexible';
    currentJobSearchStatus: 'active' | 'passive' | 'not_looking';
  };
  resumeData?: {
    file: ResumeFile;
    parsedContent?: ParsedResumeContent;
  };
}

const CandidateProfileCreation: React.FC = () => {
  const router = useRouter();
  const toast = useToast();
  
  // カラーモード対応
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const primaryText = useColorModeValue('primary.700', 'primary.300');
  
  const [formData, setFormData] = useState<CandidateProfileFormData>({
    personalInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
    },
    jobSearchPreferences: {
      targetIndustries: [],
      targetRoles: [],
      locationPreferences: [],
      workArrangement: 'hybrid',
      currentJobSearchStatus: 'active',
    },
  });
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [createdProfile, setCreatedProfile] = useState<CandidateProfile | null>(null);

  const steps = [
    { title: '基本情報入力', description: '候補者の基本情報を入力してください' },
    { title: '履歴書アップロード', description: '履歴書・職務経歴書をアップロードして解析します' },
    { title: '求職活動情報', description: '希望する業界・職種を設定してください' },
    { title: '確認・作成', description: 'プロファイル内容を確認して作成します' },
  ];

  /**
   * 履歴書解析完了ハンドラー
   */
  const handleResumeProcessed = (resumeFile: ResumeFile, parsedContent: ParsedResumeContent) => {
    console.log('履歴書解析完了:', { resumeFile, parsedContent });
    
    setFormData(prev => ({
      ...prev,
      resumeData: {
        file: resumeFile,
        parsedContent,
      },
      // 解析結果から基本情報を自動入力
      personalInfo: {
        ...prev.personalInfo,
        // name: parsedContent.personalInfo?.name || prev.personalInfo.name,
        // 解析されたデータから名前などを抽出（実装後）
      }
    }));

    toast({
      title: '履歴書解析完了',
      description: '履歴書から情報を抽出しました。内容を確認してください。',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  /**
   * 基本情報入力ハンドラー
   */
  const handlePersonalInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value,
      }
    }));
  };

  /**
   * 求職活動情報入力ハンドラー
   */
  const handleJobSearchChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobSearchPreferences: {
        ...prev.jobSearchPreferences,
        [field]: value,
      }
    }));
  };

  /**
   * プロファイル作成
   */
  const createCandidateProfile = async () => {
    setIsCreating(true);
    
    try {
      // 作成リクエストデータの準備
      const request: CreateCandidateProfileRequest = {
        personalInfo: formData.personalInfo,
        resumeFileId: formData.resumeData?.file.id,
        jobSearchPreferences: formData.jobSearchPreferences,
        privacySettings: {
          dataRetentionPeriod: 180, // 6ヶ月
          allowDataSharing: false,
          allowAnalytics: true,
        },
      };

      // 実際のAPI呼び出し（現在はモック）
      await new Promise(resolve => setTimeout(resolve, 2000));

      // モックプロファイル作成
      const mockProfile: CandidateProfile = {
        id: `candidate_${Date.now()}`,
        personalInfo: formData.personalInfo,
        ...(formData.resumeData && {
          resume: {
            file: formData.resumeData.file,
            parsedContent: formData.resumeData.parsedContent,
          }
        }),
        career: {
          level: 'mid', // 解析結果から決定
          totalYearsOfExperience: 3,
          primarySkills: formData.resumeData?.parsedContent?.skills.map(s => s.name) || [],
          industries: ['IT・ソフトウェア'],
          functionAreas: ['エンジニアリング'],
          previousRoles: formData.resumeData?.parsedContent?.experience || [],
        },
        jobSearchPreferences: formData.jobSearchPreferences,
        aiAnalysis: {
          strengthsIdentified: ['技術スキル', 'チームワーク'],
          improvementAreas: ['リーダーシップ', 'プレゼンテーション'],
          recommendedQuestionCategories: ['技術面接', '行動面接'],
          interviewReadinessScore: 75,
          lastAnalyzedAt: new Date().toISOString(),
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'agent_demo',
        dataSource: 'resume_upload',
        privacySettings: {
          dataRetentionPeriod: 180,
          allowDataSharing: false,
          allowAnalytics: true,
        },
        expirationDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(),
        isActive: true,
      };

      setCreatedProfile(mockProfile);
      setCurrentStep(steps.length);

      toast({
        title: 'プロファイル作成完了',
        description: '候補者プロファイルが正常に作成されました',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

    } catch (error) {
      console.error('プロファイル作成エラー:', error);
      toast({
        title: 'プロファイル作成エラー',
        description: 'プロファイルの作成に失敗しました',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * ナビゲーション
   */
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      createCandidateProfile();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push('/question-generator');
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // 基本情報
        return formData.personalInfo.name && formData.personalInfo.email;
      case 1: // 履歴書
        return true; // 履歴書は任意
      case 2: // 求職活動情報
        return formData.jobSearchPreferences.targetRoles.length > 0;
      case 3: // 確認
        return true;
      default:
        return false;
    }
  };

  // 完了画面
  if (createdProfile) {
    return (
      <Box minH="100vh" bg={bgColor}>
        <Container maxW="container.lg" py={8}>
          <MotionCard
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            bg={cardBg}
          >
            <CardBody>
              <VStack spacing={6} textAlign="center">
                <Box>
                  <CheckCircleIcon color="green.500" boxSize={16} />
                </Box>
                
                <VStack spacing={2}>
                  <Heading size="lg" color={primaryText}>
                    候補者プロファイル作成完了
                  </Heading>
                  <Text color="gray.600">
                    プロファイルID: {createdProfile.id}
                  </Text>
                </VStack>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} w="full">
                  <Card variant="outline">
                    <CardHeader>
                      <Text fontWeight="bold">基本情報</Text>
                    </CardHeader>
                    <CardBody>
                      <VStack align="start" spacing={2}>
                        <Text><strong>名前:</strong> {createdProfile.personalInfo.name}</Text>
                        <Text><strong>メール:</strong> {createdProfile.personalInfo.email}</Text>
                        {createdProfile.personalInfo.location && (
                          <Text><strong>所在地:</strong> {createdProfile.personalInfo.location}</Text>
                        )}
                      </VStack>
                    </CardBody>
                  </Card>

                  <Card variant="outline">
                    <CardHeader>
                      <Text fontWeight="bold">キャリア情報</Text>
                    </CardHeader>
                    <CardBody>
                      <VStack align="start" spacing={2}>
                        {createdProfile.career ? (
                          <>
                            <Text><strong>レベル:</strong> {createdProfile.career.level}</Text>
                            <Text><strong>経験年数:</strong> {createdProfile.career.totalYearsOfExperience}年</Text>
                            <Text><strong>主要スキル:</strong> {createdProfile.career.primarySkills.join(', ')}</Text>
                          </>
                        ) : (
                          <Text color="gray.500">キャリア情報が設定されていません</Text>
                        )}
                      </VStack>
                    </CardBody>
                  </Card>
                </SimpleGrid>

                {createdProfile.aiAnalysis && (
                  <Card variant="outline" w="full">
                    <CardHeader>
                      <Text fontWeight="bold">AI分析結果</Text>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <VStack align="start" spacing={2}>
                          <Text fontWeight="medium">強み</Text>
                          {createdProfile.aiAnalysis.strengthsIdentified.map((strength, index) => (
                            <Badge key={index} colorScheme="green">{strength}</Badge>
                          ))}
                        </VStack>
                        <VStack align="start" spacing={2}>
                          <Text fontWeight="medium">改善点</Text>
                          {createdProfile.aiAnalysis.improvementAreas.map((area, index) => (
                            <Badge key={index} colorScheme="orange">{area}</Badge>
                          ))}
                        </VStack>
                      </SimpleGrid>
                      <Divider my={4} />
                      <Text>
                        <strong>面接準備度スコア:</strong> {createdProfile.aiAnalysis.interviewReadinessScore}/100
                      </Text>
                    </CardBody>
                  </Card>
                )}

                <HStack spacing={4}>
                  <Button
                    colorScheme="blue"
                    onClick={() => router.push('/question-generator')}
                  >
                    面接質問生成に進む
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    新しいプロファイルを作成
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </MotionCard>
        </Container>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="container.lg" py={8}>
        <VStack spacing={8}>
          {/* ヘッダー */}
          <Flex w="full" align="center">
            <Button
              variant="ghost"
              leftIcon={<ArrowBackIcon />}
              onClick={handleBack}
            >
              戻る
            </Button>
            <Spacer />
            <VStack spacing={1} textAlign="center">
              <Heading size="lg" color={primaryText}>
                候補者プロファイル作成
              </Heading>
              <Text color="gray.600">
                ステップ {currentStep + 1} / {steps.length}: {steps[currentStep].title}
              </Text>
            </VStack>
            <Spacer />
          </Flex>

          {/* ステップインジケーター */}
          <HStack spacing={4} w="full" justify="center">
            {steps.map((step, index) => (
              <VStack key={index} spacing={2} align="center">
                <Box
                  w={8}
                  h={8}
                  borderRadius="full"
                  bg={index <= currentStep ? 'blue.500' : 'gray.300'}
                  color="white"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  fontSize="sm"
                  fontWeight="bold"
                >
                  {index + 1}
                </Box>
                <Text fontSize="xs" textAlign="center" maxW="80px">
                  {step.title}
                </Text>
              </VStack>
            ))}
          </HStack>

          {/* メインコンテンツ */}
          <AnimatePresence mode="wait">
            <MotionBox
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              w="full"
            >
              {currentStep === 0 && (
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">基本情報入力</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4}>
                      <FormControl isRequired>
                        <FormLabel>氏名</FormLabel>
                        <Input
                          value={formData.personalInfo.name}
                          onChange={(e) => handlePersonalInfoChange('name', e.target.value)}
                          placeholder="田中 太郎"
                        />
                      </FormControl>

                      <FormControl isRequired>
                        <FormLabel>メールアドレス</FormLabel>
                        <Input
                          type="email"
                          value={formData.personalInfo.email}
                          onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>電話番号</FormLabel>
                        <Input
                          value={formData.personalInfo.phone}
                          onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                          placeholder="090-1234-5678"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>所在地</FormLabel>
                        <Input
                          value={formData.personalInfo.location}
                          onChange={(e) => handlePersonalInfoChange('location', e.target.value)}
                          placeholder="東京都渋谷区"
                        />
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              )}

              {currentStep === 1 && (
                <VStack spacing={6}>
                  <DocumentUploader
                    documentType="resume"
                    maxFiles={1}
                    acceptedTypes={['.pdf', '.doc', '.docx']}
                    onResumeProcessed={handleResumeProcessed}
                  />

                  {formData.resumeData?.parsedContent && (
                    <Card bg={cardBg} w="full">
                      <CardHeader>
                        <Heading size="md">解析結果プレビュー</Heading>
                      </CardHeader>
                      <CardBody>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                          <VStack align="start" spacing={2}>
                            <Text fontWeight="bold">抽出されたスキル</Text>
                            <Flex wrap="wrap" gap={2}>
                              {formData.resumeData.parsedContent.skills.slice(0, 5).map((skill, index) => (
                                <Badge key={index} colorScheme="blue">{skill.name}</Badge>
                              ))}
                              {formData.resumeData.parsedContent.skills.length > 5 && (
                                <Badge colorScheme="gray">+{formData.resumeData.parsedContent.skills.length - 5}個</Badge>
                              )}
                            </Flex>
                          </VStack>

                          <VStack align="start" spacing={2}>
                            <Text fontWeight="bold">職歴</Text>
                            <Text fontSize="sm" color="gray.600">
                              {formData.resumeData.parsedContent.experience.length}件の職歴が検出されました
                            </Text>
                          </VStack>
                        </SimpleGrid>
                      </CardBody>
                    </Card>
                  )}
                </VStack>
              )}

              {currentStep === 2 && (
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">求職活動情報</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={4}>
                      <FormControl isRequired>
                        <FormLabel>希望職種（カンマ区切り）</FormLabel>
                        <Input
                          value={formData.jobSearchPreferences.targetRoles.join(', ')}
                          onChange={(e) => handleJobSearchChange('targetRoles', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                          placeholder="フロントエンドエンジニア, Webエンジニア"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>希望業界（カンマ区切り）</FormLabel>
                        <Input
                          value={formData.jobSearchPreferences.targetIndustries.join(', ')}
                          onChange={(e) => handleJobSearchChange('targetIndustries', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                          placeholder="IT・ソフトウェア, スタートアップ"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>希望勤務地（カンマ区切り）</FormLabel>
                        <Input
                          value={formData.jobSearchPreferences.locationPreferences.join(', ')}
                          onChange={(e) => handleJobSearchChange('locationPreferences', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                          placeholder="東京都, 大阪府, リモート"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel>希望働き方</FormLabel>
                        <select
                          value={formData.jobSearchPreferences.workArrangement}
                          onChange={(e) => handleJobSearchChange('workArrangement', e.target.value)}
                          style={{
                            padding: '8px 12px',
                            borderRadius: '6px',
                            border: '1px solid #E2E8F0',
                            width: '100%',
                          }}
                        >
                          <option value="onsite">出社</option>
                          <option value="remote">リモート</option>
                          <option value="hybrid">ハイブリッド</option>
                          <option value="flexible">フレキシブル</option>
                        </select>
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              )}

              {currentStep === 3 && (
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">プロファイル内容確認</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack spacing={6} align="stretch">
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                        <Card variant="outline">
                          <CardHeader>
                            <Text fontWeight="bold">基本情報</Text>
                          </CardHeader>
                          <CardBody>
                            <VStack align="start" spacing={2}>
                              <Text><strong>名前:</strong> {formData.personalInfo.name}</Text>
                              <Text><strong>メール:</strong> {formData.personalInfo.email}</Text>
                              {formData.personalInfo.phone && (
                                <Text><strong>電話:</strong> {formData.personalInfo.phone}</Text>
                              )}
                              {formData.personalInfo.location && (
                                <Text><strong>所在地:</strong> {formData.personalInfo.location}</Text>
                              )}
                            </VStack>
                          </CardBody>
                        </Card>

                        <Card variant="outline">
                          <CardHeader>
                            <Text fontWeight="bold">求職活動情報</Text>
                          </CardHeader>
                          <CardBody>
                            <VStack align="start" spacing={2}>
                              <Text><strong>希望職種:</strong></Text>
                              <Flex wrap="wrap" gap={2}>
                                {formData.jobSearchPreferences.targetRoles.map((role, index) => (
                                  <Badge key={index} colorScheme="green">{role}</Badge>
                                ))}
                              </Flex>
                              {formData.jobSearchPreferences.targetIndustries.length > 0 && (
                                <>
                                  <Text><strong>希望業界:</strong></Text>
                                  <Flex wrap="wrap" gap={2}>
                                    {formData.jobSearchPreferences.targetIndustries.map((industry, index) => (
                                      <Badge key={index} colorScheme="blue">{industry}</Badge>
                                    ))}
                                  </Flex>
                                </>
                              )}
                              {formData.jobSearchPreferences.locationPreferences.length > 0 && (
                                <>
                                  <Text><strong>希望勤務地:</strong></Text>
                                  <Flex wrap="wrap" gap={2}>
                                    {formData.jobSearchPreferences.locationPreferences.map((location, index) => (
                                      <Badge key={index} colorScheme="teal">{location}</Badge>
                                    ))}
                                  </Flex>
                                </>
                              )}
                              <Text><strong>希望働き方:</strong> {formData.jobSearchPreferences.workArrangement}</Text>
                            </VStack>
                          </CardBody>
                        </Card>
                      </SimpleGrid>

                      {formData.resumeData && (
                        <Card variant="outline">
                          <CardHeader>
                            <Text fontWeight="bold">履歴書情報</Text>
                          </CardHeader>
                          <CardBody>
                            <VStack align="start" spacing={2}>
                              <Text><strong>ファイル名:</strong> {formData.resumeData.file.fileName}</Text>
                              <Text><strong>アップロード日:</strong> {new Date(formData.resumeData.file.uploadedAt).toLocaleString('ja-JP')}</Text>
                              {formData.resumeData.parsedContent && (
                                <>
                                  <Text><strong>解析されたスキル数:</strong> {formData.resumeData.parsedContent.skills.length}個</Text>
                                  <Text><strong>職歴数:</strong> {formData.resumeData.parsedContent.experience.length}件</Text>
                                </>
                              )}
                            </VStack>
                          </CardBody>
                        </Card>
                      )}

                      <Alert status="info">
                        <AlertIcon />
                        作成されたプロファイルは180日間保持され、その後自動削除されます。
                      </Alert>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </MotionBox>
          </AnimatePresence>

          {/* ナビゲーションボタン */}
          <HStack justify="space-between" w="full">
            <Button
              variant="ghost"
              leftIcon={<ArrowBackIcon />}
              onClick={handleBack}
            >
              {currentStep === 0 ? '質問生成に戻る' : '前へ'}
            </Button>

            <Button
              colorScheme="blue"
              onClick={handleNext}
              isDisabled={!canProceed()}
              isLoading={isCreating}
              loadingText={currentStep === steps.length - 1 ? '作成中...' : undefined}
            >
              {currentStep === steps.length - 1 ? 'プロファイルを作成' : '次へ'}
            </Button>
          </HStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default CandidateProfileCreation;