/**
 * インテリジェント質問生成サービス（管理画面用）
 * プロダクト憲法の3原則に基づく動的質問生成システム
 * 
 * 第一条：心理的安全性の絶対的保障
 * 第二条：個人の尊重と個別化  
 * 第三条：透明性と信頼
 */

// ===== 型定義 =====

import type { InterviewerRole } from "@mensetsu-kun/shared/types/interviewer-roles";

export type QuestionCategory = 
  | 'self-introduction'
  | 'experience-skills'
  | 'motivation'
  | 'problem-solving'
  | 'teamwork'
  | 'leadership'
  | 'career-goals'
  | 'company-culture-fit'
  | 'technical'
  | 'behavioral'
  | 'situational';

export interface QuestionGenerationRequest {
  companyInfo: {
    name: string;
    industry: string;
    position: string;
    requirements: string[];
    culture?: string;
    interviewStyle?: string;
  };
  candidateProfile?: {
    experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
    skills: string[];
    background: string[];
    previousInterviews?: number;
  };
  questionSettings: {
    totalQuestions: number;
    categories: QuestionCategory[];
    difficulty: 'easy' | 'medium' | 'hard' | 'adaptive';
    estimatedDuration: number;
    includeWarmup: boolean;
    includeFollowUp: boolean;
  };
  adaptiveSettings?: {
    personalizeForCandidate: boolean;
    adjustForAnxiety: boolean;
    emphasizeGrowth: boolean;
    avoidNegativeLanguage: boolean;
  };
}

export interface GeneratedQuestion {
  id: string;
  text: string;
  category: QuestionCategory;
  intent: {
    primary: string;
    secondary?: string[];
    skillAssessment: string[];
    personalityTraits: string[];
  };
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedAnswerTime: number;
  followUpQuestions?: string[];
  evaluationCriteria: {
    name: string;
    description: string;
    weight: number;
    scoringGuideline: {
      excellent: string;
      good: string;
      acceptable: string;
      needsImprovement: string;
    };
  }[];
  supportiveElements: {
    encouragementPrefix?: string;
    clarificationNote?: string;
    reassuranceMessage?: string;
  };
  metadata: {
    generatedAt: string;
    version: string;
    source: 'ai' | 'template' | 'custom';
    adaptationReason?: string;
  };
}

export interface QuestionGenerationResponse {
  requestId: string;
  questions: GeneratedQuestion[];
  sessionConfig: {
    totalDuration: number;
    breakPoints: number[];
    adaptiveFlow: boolean;
    difficultyProgression: 'linear' | 'adaptive' | 'plateau';
  };
  supportiveMessaging: {
    openingMessage: string;
    transitionMessages: string[];
    closingMessage: string;
    encouragementTriggers: {
      condition: string;
      message: string;
      timing: string;
    }[];
  };
  metadata: {
    generatedAt: string;
    processingTime: number;
    adaptationApplied: string[];
    qualityScore: number;
  };
}

interface QuestionTemplate {
  id: string;
  name: string;
  category: QuestionCategory;
  template: string;
  variables: {
    name: string;
    type: string;
    required: boolean;
  }[];
  adaptationRules: any[];
  safetyConfiguration: {
    avoidTriggerWords: string[];
    includeReassurance: boolean;
    gentleVersion?: string;
    encouragingVersion?: string;
  };
}

// ===== 言語処理ユーティリティ =====

const POSITIVE_ALTERNATIVES: Record<string, string> = {
  '改善点': '成長の機会',
  'エラー': 'もう一度トライするチャンス',
  '不十分': 'さらに良くできるポイント',
  'スコアが低い': '伸びしろがある',
  '間違い': '学習のきっかけ',
  '失敗': '経験値',
  '悪い': '改善できる',
  '問題': 'チャレンジ',
  '不合格': '次回への準備',
  'ミス': '気づき',
  '下手': '練習の余地がある',
  'できていない': '次のステップへ',
  'ダメ': '別のアプローチを'
};

const ENCOURAGEMENT_PHRASES = [
  '素晴らしい挑戦でした！',
  '確実に成長しています',
  '次はもっと上手くいきますよ',
  'あなたの強みが光っていました',
  'とても良い気づきですね',
  'その調子で頑張りましょう',
  '素敵な表現力をお持ちですね',
  '自信を持って進んでください',
  'あなたなら必ずできます',
  '着実に上達していますよ'
];

const convertToPositiveLanguage = (text: string): string => {
  let convertedText = text;
  Object.entries(POSITIVE_ALTERNATIVES).forEach(([negative, positive]) => {
    const regex = new RegExp(negative, 'g');
    convertedText = convertedText.replace(regex, positive);
  });
  return convertedText;
};

const getRandomEncouragement = (): string => {
  const randomIndex = Math.floor(Math.random() * ENCOURAGEMENT_PHRASES.length);
  return ENCOURAGEMENT_PHRASES[randomIndex];
};

// ===== メインクラス =====

export class IntelligentQuestionGenerator {
  private questionTemplates: Map<QuestionCategory, QuestionTemplate[]> = new Map();
  private adaptiveHistory: any[] = [];
  
  constructor() {
    this.initializeQuestionTemplates();
  }

  async generateQuestions(
    request: QuestionGenerationRequest,
    interviewerRole?: InterviewerRole
  ): Promise<QuestionGenerationResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // 1. 候補者プロファイル分析
    const candidateAnalysis = this.analyzeCandidateProfile(request.candidateProfile);
    
    // 2. 心理的安全性設定の適用
    const safetyConfig = this.configurePsychologicalSafety(request.adaptiveSettings);
    
    // 3. 質問選択と生成
    const questions = await this.selectAndGenerateQuestions(
      request,
      candidateAnalysis,
      safetyConfig
    );
    
    // 4. セッション設定の最適化
    const sessionConfig = this.optimizeSessionConfiguration(questions, request);
    
    // 5. 支援メッセージの生成
    const supportiveMessaging = this.generateSupportiveMessaging(
      request,
      candidateAnalysis,
      safetyConfig
    );
    
    const processingTime = Date.now() - startTime;
    
    return {
      requestId,
      questions,
      sessionConfig,
      supportiveMessaging,
      metadata: {
        generatedAt: new Date().toISOString(),
        processingTime,
        adaptationApplied: this.getAppliedAdaptations(),
        qualityScore: this.calculateQualityScore(questions),
      },
    };
  }

  private analyzeCandidateProfile(profile?: QuestionGenerationRequest['candidateProfile']) {
    if (!profile) {
      return {
        experienceLevel: 'mid' as const,
        anxietyLevel: 'medium' as const,
        supportNeeds: 'standard' as const,
        adaptationStrategy: 'balanced' as const,
      };
    }

    const anxietyLevel = this.assessAnxietyLevel(profile);
    const supportNeeds = this.determineSupportNeeds(profile, anxietyLevel);
    const adaptationStrategy = this.selectAdaptationStrategy(profile, anxietyLevel);

    return {
      experienceLevel: profile.experienceLevel,
      anxietyLevel,
      supportNeeds,
      adaptationStrategy,
    };
  }

  private configurePsychologicalSafety(settings?: QuestionGenerationRequest['adaptiveSettings']) {
    const defaultConfig = {
      personalizeForCandidate: true,
      adjustForAnxiety: true,
      emphasizeGrowth: true,
      avoidNegativeLanguage: true,
    };

    return {
      ...defaultConfig,
      ...settings,
      enableEncouragement: true,
      usePositiveLanguage: true,
      provideClarification: true,
      allowThinkingTime: true,
    };
  }

  private async selectAndGenerateQuestions(
    request: QuestionGenerationRequest,
    candidateAnalysis: any,
    safetyConfig: any
  ): Promise<GeneratedQuestion[]> {
    const { questionSettings, companyInfo } = request;
    const questions: GeneratedQuestion[] = [];
    
    const categoryDistribution = this.distributeCategoriesByCount(
      questionSettings.categories,
      questionSettings.totalQuestions
    );

    for (const [category, count] of Array.from(categoryDistribution.entries())) {
      const categoryQuestions = await this.generateCategoryQuestions(
        category,
        count,
        companyInfo,
        candidateAnalysis,
        safetyConfig,
        questionSettings.difficulty
      );
      
      questions.push(...categoryQuestions);
    }

    return this.optimizeQuestionOrder(questions, candidateAnalysis, safetyConfig);
  }

  private async generateCategoryQuestions(
    category: QuestionCategory,
    count: number,
    companyInfo: any,
    candidateAnalysis: any,
    safetyConfig: any,
    baseDifficulty: string
  ): Promise<GeneratedQuestion[]> {
    const templates = this.questionTemplates.get(category) || [];
    const selectedTemplates = this.selectBestTemplates(templates, count, candidateAnalysis);
    
    const questions: GeneratedQuestion[] = [];
    
    for (let i = 0; i < count; i++) {
      const template = selectedTemplates[i % selectedTemplates.length];
      const question = await this.generateQuestionFromTemplate(
        template,
        companyInfo,
        candidateAnalysis,
        safetyConfig,
        baseDifficulty,
        i
      );
      questions.push(question);
    }
    
    return questions;
  }

  private async generateQuestionFromTemplate(
    template: QuestionTemplate,
    companyInfo: any,
    candidateAnalysis: any,
    safetyConfig: any,
    baseDifficulty: string,
    index: number
  ): Promise<GeneratedQuestion> {
    // テンプレート変数の置換
    let questionText = this.replaceTemplateVariables(template.template, {
      companyName: companyInfo.name,
      position: companyInfo.position,
      industry: companyInfo.industry,
    });

    // 心理的安全性の適用
    questionText = this.applySafetyModifications(questionText, safetyConfig, candidateAnalysis);
    
    // 難易度調整
    const adjustedDifficulty = this.adjustDifficulty(baseDifficulty, candidateAnalysis, index);
    
    // 支援要素の生成
    const supportiveElements = this.generateSupportiveElements(
      template.category,
      candidateAnalysis,
      safetyConfig
    );

    // フォローアップ質問の生成
    const followUpQuestions = this.generateFollowUpQuestions(
      questionText,
      template.category,
      adjustedDifficulty
    );

    return {
      id: this.generateQuestionId(template.id, index),
      text: questionText,
      category: template.category,
      intent: this.generateQuestionIntent(template, companyInfo),
      difficulty: adjustedDifficulty as 'easy' | 'medium' | 'hard',
      estimatedAnswerTime: this.calculateEstimatedTime(adjustedDifficulty, template.category),
      followUpQuestions,
      evaluationCriteria: this.generateEvaluationCriteria(template.category, companyInfo),
      supportiveElements,
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'ai',
        adaptationReason: this.getAdaptationReason(candidateAnalysis),
      },
    };
  }

  private applySafetyModifications(
    questionText: string,
    safetyConfig: any,
    candidateAnalysis: any
  ): string {
    let modifiedText = questionText;

    if (safetyConfig.avoidNegativeLanguage) {
      modifiedText = convertToPositiveLanguage(modifiedText);
    }

    if (candidateAnalysis.anxietyLevel === 'high') {
      modifiedText = this.adjustForHighAnxiety(modifiedText);
    }

    if (safetyConfig.emphasizeGrowth) {
      modifiedText = this.addGrowthOrientedLanguage(modifiedText);
    }

    return modifiedText;
  }

  private adjustForHighAnxiety(questionText: string): string {
    const gentlePrefixes = [
      "お時間のあるときで構いませんので、",
      "ご自身のペースで、",
      "リラックスして、",
      "どんな内容でも大丈夫ですので、",
    ];
    
    const randomPrefix = gentlePrefixes[Math.floor(Math.random() * gentlePrefixes.length)];
    return `${randomPrefix}${questionText}`;
  }

  private addGrowthOrientedLanguage(questionText: string): string {
    const growthSuffixes = [
      "どんな経験からも学びがあると思いますので、お聞かせください。",
      "あなたの成長の過程を教えていただけますか。",
      "どのような気づきや学びがありましたか。",
    ];
    
    if (questionText.length < 100) {
      const randomSuffix = growthSuffixes[Math.floor(Math.random() * growthSuffixes.length)];
      return `${questionText} ${randomSuffix}`;
    }
    
    return questionText;
  }

  private generateSupportiveMessaging(
    request: QuestionGenerationRequest,
    candidateAnalysis: any,
    safetyConfig: any
  ) {
    const openingMessage = this.generateOpeningMessage(candidateAnalysis, request.companyInfo);
    const transitionMessages = this.generateTransitionMessages(candidateAnalysis);
    const closingMessage = this.generateClosingMessage();
    const encouragementTriggers = this.generateEncouragementTriggers(candidateAnalysis);

    return {
      openingMessage,
      transitionMessages,
      closingMessage,
      encouragementTriggers,
    };
  }

  private generateOpeningMessage(candidateAnalysis: any, companyInfo: any): string {
    const baseMessage = `${companyInfo.name}での面接練習を始めましょう。`;
    
    if (candidateAnalysis.anxietyLevel === 'high') {
      return `${baseMessage} リラックスして、ご自身のペースで進めてください。間違いなどを心配する必要はありません。この時間は、あなたの成長のためのものです。準備ができましたら、最初の質問にお答えください。`;
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      return `${baseMessage} 初めての面接練習かもしれませんが、どんな回答でも大丈夫です。この経験を通じて、きっと新しい発見があります。楽しみながら進めましょう。`;
    }
    
    return `${baseMessage} あなたの経験や考えを自由にお聞かせください。この練習が、より良い面接のきっかけになることを願っています。`;
  }

  private generateTransitionMessages(candidateAnalysis: any): string[] {
    const messages = [
      "素晴らしい回答でした。次の質問に移りましょう。",
      "とても興味深いお話をありがとうございます。続いて、",
      "あなたの経験がよく伝わってきました。では、次に",
      "そのような視点をお持ちなのですね。それでは、",
    ];

    if (candidateAnalysis.anxietyLevel === 'high') {
      messages.push(
        "ここまでとてもよくできています。少し深呼吸をして、次に進みましょう。",
        "順調に進んでいますね。次の質問もあなたのペースで大丈夫です。"
      );
    }

    return messages;
  }

  private generateClosingMessage(): string {
    return "面接練習お疲れさまでした。今日の経験は必ずあなたの成長につながります。すべての回答に、あなたらしさと成長への意欲が感じられました。この調子で、自信を持って本番に臨んでください。";
  }

  private generateEncouragementTriggers(candidateAnalysis: any) {
    const triggers = [
      {
        condition: 'after_difficult_question',
        message: 'チャレンジングな質問でしたが、よく考えて答えてくださいました。',
        timing: 'immediate',
      },
      {
        condition: 'mid_session',
        message: 'ここまでの回答、とても充実していますね。この調子で続けましょう。',
        timing: 'delayed',
      },
    ];

    if (candidateAnalysis.anxietyLevel === 'high') {
      triggers.push({
        condition: 'low_confidence_detected',
        message: '完璧である必要はありません。あなたの率直な思いを聞かせてください。',
        timing: 'immediate',
      });
    }

    return triggers;
  }

  // ===== ユーティリティメソッド =====

  private generateRequestId(): string {
    return `qg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateQuestionId(templateId: string, index: number): string {
    return `q_${templateId}_${index}_${Date.now()}`;
  }

  private assessAnxietyLevel(profile: any): 'low' | 'medium' | 'high' {
    if (profile.previousInterviews === 0) return 'high';
    if (profile.previousInterviews < 3) return 'medium';
    return 'low';
  }

  private determineSupportNeeds(profile: any, anxietyLevel: string): 'minimal' | 'standard' | 'high' {
    if (anxietyLevel === 'high') return 'high';
    if (profile.experienceLevel === 'entry') return 'high';
    return 'standard';
  }

  private selectAdaptationStrategy(profile: any, anxietyLevel: string): string {
    if (anxietyLevel === 'high') return 'supportive';
    if (profile.experienceLevel === 'executive') return 'challenging';
    return 'balanced';
  }

  private distributeCategoriesByCount(
    categories: QuestionCategory[], 
    totalQuestions: number
  ): Map<QuestionCategory, number> {
    const distribution = new Map<QuestionCategory, number>();
    const baseCount = Math.floor(totalQuestions / categories.length);
    const remainder = totalQuestions % categories.length;

    categories.forEach((category, index) => {
      const count = baseCount + (index < remainder ? 1 : 0);
      distribution.set(category, count);
    });

    return distribution;
  }

  private selectBestTemplates(
    templates: QuestionTemplate[], 
    count: number, 
    candidateAnalysis: any
  ): QuestionTemplate[] {
    const scoredTemplates = templates.map(template => ({
      template,
      score: this.scoreTemplate(template, candidateAnalysis),
    }));

    scoredTemplates.sort((a, b) => b.score - a.score);
    
    return scoredTemplates
      .slice(0, Math.max(count, templates.length))
      .map(item => item.template);
  }

  private scoreTemplate(template: QuestionTemplate, candidateAnalysis: any): number {
    let score = 0.5;

    if (template.safetyConfiguration.includeReassurance) score += 0.2;
    if (template.safetyConfiguration.gentleVersion) score += 0.1;

    if (candidateAnalysis.anxietyLevel === 'high' && template.safetyConfiguration.gentleVersion) {
      score += 0.3;
    }

    return Math.min(1, score);
  }

  private replaceTemplateVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value);
    });
    return result;
  }

  private adjustDifficulty(
    baseDifficulty: string, 
    candidateAnalysis: any, 
    questionIndex: number
  ): string {
    if (candidateAnalysis.anxietyLevel === 'high') {
      return questionIndex < 2 ? 'easy' : 'medium';
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      return baseDifficulty === 'hard' ? 'medium' : baseDifficulty;
    }
    
    return baseDifficulty;
  }

  private generateSupportiveElements(
    category: QuestionCategory,
    candidateAnalysis: any,
    safetyConfig: any
  ) {
    const elements: any = {};

    if (candidateAnalysis.anxietyLevel === 'high') {
      elements.encouragementPrefix = "ご自身の経験を思い返しながら、";
      elements.reassuranceMessage = "どのような内容でも、あなたの経験として価値があります。";
    }

    if (category === 'technical' || category === 'problem-solving') {
      elements.clarificationNote = "分からない部分があれば、遠慮なくお尋ねください。";
    }

    return elements;
  }

  private generateFollowUpQuestions(
    questionText: string,
    category: QuestionCategory,
    difficulty: string
  ): string[] {
    const followUps: string[] = [];

    if (category === 'experience-skills') {
      followUps.push("その経験から何を学ばれましたか？");
      followUps.push("同じような状況になったら、どう対応されますか？");
    }

    if (category === 'problem-solving') {
      followUps.push("他に考えられる解決方法はありますか？");
      followUps.push("その判断をした理由を教えてください。");
    }

    return followUps;
  }

  private generateQuestionIntent(template: QuestionTemplate, companyInfo: any) {
    return {
      primary: `${template.category}の評価`,
      secondary: ['コミュニケーション能力', '論理的思考'],
      skillAssessment: this.getSkillAssessmentForCategory(template.category),
      personalityTraits: this.getPersonalityTraitsForCategory(template.category),
    };
  }

  private generateEvaluationCriteria(category: QuestionCategory, companyInfo: any) {
    const baseCriteria = [
      {
        name: '明確性',
        description: '回答が明確で理解しやすいか',
        weight: 0.3,
        scoringGuideline: {
          excellent: '非常に明確で具体的',
          good: '概ね明確',
          acceptable: '理解可能',
          needsImprovement: 'より具体的な説明があると良い',
        },
      },
    ];

    return baseCriteria;
  }

  private getSkillAssessmentForCategory(category: QuestionCategory): string[] {
    const skillMap: Record<QuestionCategory, string[]> = {
      'self-introduction': ['コミュニケーション', 'プレゼンテーション'],
      'experience-skills': ['専門スキル', '問題解決', '学習能力'],
      'motivation': ['意欲', '価値観', '目標設定'],
      'problem-solving': ['論理的思考', '創造性', '分析力'],
      'teamwork': ['協調性', 'リーダーシップ', 'コミュニケーション'],
      'leadership': ['リーダーシップ', '意思決定', '人材育成'],
      'career-goals': ['計画性', '自己認識', '成長意欲'],
      'company-culture-fit': ['価値観', '適応性', '企業理解'],
      'technical': ['技術スキル', '専門知識', '実装能力'],
      'behavioral': ['行動特性', '価値観', '判断力'],
      'situational': ['応用力', '適応性', '意思決定'],
    };

    return skillMap[category] || ['総合評価'];
  }

  private getPersonalityTraitsForCategory(category: QuestionCategory): string[] {
    const traitMap: Record<QuestionCategory, string[]> = {
      'self-introduction': ['自信', '社交性'],
      'experience-skills': ['責任感', '継続力'],
      'motivation': ['情熱', '目的意識'],
      'problem-solving': ['論理性', '創造性'],
      'teamwork': ['協調性', '共感性'],
      'leadership': ['主体性', '影響力'],
      'career-goals': ['計画性', '向上心'],
      'company-culture-fit': ['適応性', '価値観'],
      'technical': ['論理性', '探究心'],
      'behavioral': ['誠実性', '一貫性'],
      'situational': ['柔軟性', '判断力'],
    };

    return traitMap[category] || ['総合的な人柄'];
  }

  private optimizeQuestionOrder(
    questions: GeneratedQuestion[],
    candidateAnalysis: any,
    safetyConfig: any
  ): GeneratedQuestion[] {
    const ordered = [...questions];

    ordered.sort((a, b) => {
      if (a.category === 'self-introduction') return -1;
      if (b.category === 'self-introduction') return 1;
      return 0;
    });

    if (candidateAnalysis.anxietyLevel === 'high') {
      const selfIntro = ordered.filter(q => q.category === 'self-introduction');
      const easy = ordered.filter(q => q.difficulty === 'easy' && q.category !== 'self-introduction');
      const medium = ordered.filter(q => q.difficulty === 'medium');
      const hard = ordered.filter(q => q.difficulty === 'hard');
      
      return [...selfIntro, ...easy, ...medium, ...hard];
    }

    return ordered;
  }

  private optimizeSessionConfiguration(
    questions: GeneratedQuestion[],
    request: QuestionGenerationRequest
  ) {
    const totalDuration = questions.reduce((sum, q) => sum + q.estimatedAnswerTime, 0) / 60;
    
    const breakPoints: number[] = [];
    let cumulativeTime = 0;
    
    questions.forEach((question, index) => {
      cumulativeTime += question.estimatedAnswerTime / 60;
      if (cumulativeTime >= 20 && breakPoints.length === 0) {
        breakPoints.push(index + 1);
      } else if (cumulativeTime >= 40 && breakPoints.length === 1) {
        breakPoints.push(index + 1);
      }
    });

    return {
      totalDuration: Math.ceil(totalDuration),
      breakPoints,
      adaptiveFlow: true,
      difficultyProgression: 'adaptive' as const,
    };
  }

  private calculateEstimatedTime(difficulty: string, category: QuestionCategory): number {
    const baseTime: Record<string, number> = {
      'easy': 120,
      'medium': 180,
      'hard': 240,
    };

    const categoryMultiplier: Record<QuestionCategory, number> = {
      'self-introduction': 0.8,
      'experience-skills': 1.2,
      'motivation': 1.0,
      'problem-solving': 1.5,
      'teamwork': 1.1,
      'leadership': 1.3,
      'career-goals': 1.0,
      'company-culture-fit': 1.0,
      'technical': 1.8,
      'behavioral': 1.2,
      'situational': 1.4,
    };

    return Math.round((baseTime[difficulty] || 180) * (categoryMultiplier[category] || 1.0));
  }

  private getAppliedAdaptations(): string[] {
    return this.adaptiveHistory.map(adj => adj.type);
  }

  private calculateQualityScore(questions: GeneratedQuestion[]): number {
    let totalScore = 0;
    
    questions.forEach(question => {
      let questionScore = 0.5;
      
      if (question.supportiveElements.encouragementPrefix) questionScore += 0.1;
      if (question.supportiveElements.reassuranceMessage) questionScore += 0.1;
      if (question.supportiveElements.clarificationNote) questionScore += 0.1;
      
      if (question.followUpQuestions && question.followUpQuestions.length > 0) {
        questionScore += 0.2;
      }
      
      totalScore += questionScore;
    });
    
    return Math.min(1, totalScore / questions.length);
  }

  private getAdaptationReason(candidateAnalysis: any): string {
    const reasons: string[] = [];
    
    if (candidateAnalysis.anxietyLevel === 'high') {
      reasons.push('高不安レベルに対応');
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      reasons.push('初心者レベルに適応');
    }
    
    if (candidateAnalysis.supportNeeds === 'high') {
      reasons.push('高サポートニーズに対応');
    }
    
    return reasons.join(', ') || '標準設定';
  }

  private initializeQuestionTemplates() {
    // 自己紹介カテゴリ
    this.questionTemplates.set('self-introduction', [
      {
        id: 'self_intro_basic',
        name: '基本的な自己紹介',
        category: 'self-introduction',
        template: 'まずは簡単に自己紹介をお願いします。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'お時間のあるときで、ご自身のペースで自己紹介をお聞かせください。',
          encouragingVersion: 'あなたのことを教えてください。どんなお話でも楽しみにしています。',
        },
      },
      {
        id: 'self_intro_strength',
        name: '強みを含む自己紹介',
        category: 'self-introduction',
        template: 'ご自身の強みを含めて自己紹介をお聞かせください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'ご自身の良いところを含めて、お話しいただけますか。',
          encouragingVersion: 'あなたらしさが伝わる自己紹介をお聞かせください。',
        },
      },
    ]);

    // 経験・スキルカテゴリ
    this.questionTemplates.set('experience-skills', [
      {
        id: 'experience_challenge',
        name: '困難な経験について',
        category: 'experience-skills',
        template: 'これまでの経験で、成長につながった出来事について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '困難', '問題'],
          includeReassurance: true,
          gentleVersion: 'これまでの経験の中で、学びがあった出来事があれば教えてください。',
          encouragingVersion: 'あなたが頑張って取り組んだ経験について、ぜひお聞かせください。',
        },
      },
      {
        id: 'experience_achievement',
        name: '誇れる成果について',
        category: 'experience-skills',
        template: 'これまでで最も誇らしく思う成果について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'これまでで達成できて嬉しかったことがあれば教えてください。',
          encouragingVersion: 'あなたが自信を持てる成果について、ぜひお聞かせください。',
        },
      },
    ]);

    // 志望動機カテゴリ
    this.questionTemplates.set('motivation', [
      {
        id: 'motivation_company',
        name: '企業志望動機',
        category: 'motivation',
        template: '{{companyName}}を志望された理由を教えてください。',
        variables: [
          {
            name: 'companyName',
            type: 'company',
            required: true,
          },
        ],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '{{companyName}}に興味を持たれたきっかけがあれば教えてください。',
          encouragingVersion: '{{companyName}}のどのような点に魅力を感じられましたか。',
        },
      },
    ]);

    // 他のカテゴリも同様に初期化（簡略化のため基本的なものを含める）
    this.questionTemplates.set('problem-solving', [
      {
        id: 'problem_solving_approach',
        name: '問題解決アプローチ',
        category: 'problem-solving',
        template: '新しい課題に取り組む時の、あなたなりのアプローチ方法を教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['難しい', '困難'],
          includeReassurance: true,
          gentleVersion: '新しい課題に取り組む時に、どのように進めることが多いですか。',
          encouragingVersion: 'あなたなりの課題解決の工夫があれば教えてください。',
        },
      },
    ]);

    this.questionTemplates.set('teamwork', [
      {
        id: 'teamwork_collaboration',
        name: 'チーム協力経験',
        category: 'teamwork',
        template: 'チームで協力して成果を上げた経験について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'みんなで一緒に取り組んだ経験があれば教えてください。',
          encouragingVersion: 'チームの一員として活動した経験について、お聞かせください。',
        },
      },
    ]);

    this.questionTemplates.set('leadership', [
      {
        id: 'leadership_experience',
        name: 'リーダーシップ経験',
        category: 'leadership',
        template: 'リーダーとして人をまとめた経験について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '人をサポートしたり、まとめ役をした経験があれば教えてください。',
          encouragingVersion: 'チームを引っ張ったり、支えたりした経験について、お聞かせください。',
        },
      },
    ]);

    this.questionTemplates.set('career-goals', [
      {
        id: 'career_vision',
        name: '将来のビジョン',
        category: 'career-goals',
        template: '5年後、どのような自分になっていたいですか。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '将来に向けて、どのような成長をしていきたいですか。',
          encouragingVersion: 'あなたの理想の将来像について教えてください。',
        },
      },
    ]);

    this.questionTemplates.set('company-culture-fit', [
      {
        id: 'culture_values',
        name: '価値観の適合性',
        category: 'company-culture-fit',
        template: '仕事をする上で大切にしている価値観を教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '働く時に大切に思っていることがあれば教えてください。',
          encouragingVersion: 'あなたが仕事で大事にしていることについて、お聞かせください。',
        },
      },
    ]);

    this.questionTemplates.set('technical', [
      {
        id: 'technical_experience',
        name: '技術的経験',
        category: 'technical',
        template: 'これまでの技術的な経験や取り組みについて教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '技術的なことで学んだり、作ったりした経験があれば教えてください。',
          encouragingVersion: '技術的な分野での経験や興味について、お聞かせください。',
        },
      },
    ]);

    this.questionTemplates.set('behavioral', [
      {
        id: 'behavioral_decision',
        name: '意思決定の経験',
        category: 'behavioral',
        template: '重要な決断をした経験について、そのプロセスを教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '何かを決める時に気をつけていることがあれば教えてください。',
          encouragingVersion: '大切な選択をした経験について、お聞かせください。',
        },
      },
    ]);

    this.questionTemplates.set('situational', [
      {
        id: 'situational_pressure',
        name: 'プレッシャー下での判断',
        category: 'situational',
        template: '時間やプレッシャーがある状況で、どのように判断を行いますか。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['プレッシャー', '時間'],
          includeReassurance: true,
          gentleVersion: '忙しい状況でも冷静に判断するコツがあれば教えてください。',
          encouragingVersion: '限られた時間の中で、どのように優先順位をつけていますか。',
        },
      },
    ]);
  }
}