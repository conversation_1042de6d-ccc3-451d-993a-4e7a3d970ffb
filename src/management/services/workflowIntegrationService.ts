/**
 * ワークフロー統合サービス
 * IntegratedLinkWorkflow と IntelligentQuestionGenerator の橋渡し
 */
import { 
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  GeneratedQuestion,
  QuestionCategory 
} from '@mensetsu-kun/shared/types/intelligent-questions';
import { IntelligentQuestionGenerator } from '@mensetsu-kun/shared/services/intelligentQuestionGenerator';
import { CompanyMasterService } from '@mensetsu-kun/shared/services/companyMasterService';
import { 
  CompanyMaster, 
  PositionMaster, 
  QuestionTemplate,
  TemplateQuestion 
} from '@mensetsu-kun/shared/types/company-master';

// 既存のAI質問意図型定義
export interface AIGeneratedIntent {
  id: string;
  category: string;
  intent: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  aiInstructions: string;
  confidence: number;
  isEdited: boolean;
}

// 企業資料から抽出された情報
export interface ExtractedCompanyInfo {
  companyName: string;
  industry: string;
  position: string;
  requirements: string[];
  companyDescription: string;
  culture?: string;
  interviewStyle?: 'casual' | 'formal' | 'hybrid';
  businessDescription?: string;
  vision?: string;
  mission?: string;
  values?: string[];
  size?: 'startup' | 'sme' | 'large' | 'enterprise';
  location?: string[];
  benefits?: string[];
}

/**
 * ワークフロー統合サービスクラス
 */
export class WorkflowIntegrationService {
  private questionGenerator: IntelligentQuestionGenerator;
  private companyMasterService: CompanyMasterService;

  constructor() {
    this.questionGenerator = new IntelligentQuestionGenerator();
    this.companyMasterService = new CompanyMasterService();
  }

  /**
   * 企業情報のサジェスト検索
   */
  async suggestCompanies(query: string) {
    return await this.companyMasterService.suggestCompanies(query);
  }

  /**
   * 職種のサジェスト検索
   */
  async suggestPositions(query: string, companyId?: string) {
    return await this.companyMasterService.suggestPositions(query, companyId);
  }

  /**
   * 企業マスターの作成または更新
   */
  async createOrUpdateCompanyMaster(
    extractedInfo: ExtractedCompanyInfo,
    agentId: string
  ): Promise<CompanyMaster> {
    const companyData: Partial<CompanyMaster> = {
      name: extractedInfo.companyName,
      industry: extractedInfo.industry,
      description: extractedInfo.companyDescription,
      culture: extractedInfo.culture,
      businessDescription: extractedInfo.businessDescription,
      vision: extractedInfo.vision,
      mission: extractedInfo.mission,
      values: extractedInfo.values,
      interviewStyle: extractedInfo.interviewStyle || 'formal',
      size: extractedInfo.size || 'sme',
      location: extractedInfo.location,
      benefits: extractedInfo.benefits,
      positions: [],
      questionTemplates: [],
    };

    return await this.companyMasterService.createOrUpdateCompany(companyData, agentId);
  }

  /**
   * 職種マスターの作成または更新
   */
  async createOrUpdatePositionMaster(
    extractedInfo: ExtractedCompanyInfo,
    companyId: string
  ): Promise<PositionMaster> {
    const positionData: Partial<PositionMaster> = {
      title: extractedInfo.position,
      department: this.extractDepartmentFromPosition(extractedInfo.position),
      level: this.determineLevelFromRequirements(extractedInfo.requirements),
      requirements: extractedInfo.requirements,
      preferredSkills: [],
      expectedDuration: 60,
      difficulty: 'medium',
      focusAreas: this.extractFocusAreas(extractedInfo.requirements),
    };

    return await this.companyMasterService.createOrUpdatePosition(positionData, companyId);
  }

  /**
   * 生成された質問をテンプレートとして保存
   */
  async saveGeneratedQuestionsAsTemplate(
    questions: GeneratedQuestion[],
    companyId: string,
    positionId: string,
    agentId: string,
    templateName?: string
  ): Promise<QuestionTemplate> {
    const templateQuestions: TemplateQuestion[] = questions.map((q, index) => ({
      id: q.id,
      order: index + 1,
      text: q.text,
      category: q.category,
      intent: q.intent.primary,
      difficulty: q.difficulty,
      estimatedAnswerTime: q.estimatedAnswerTime,
      evaluationCriteria: q.evaluationCriteria.map(c => c.name),
      scoringWeight: 1 / questions.length, // 均等配分
      supportiveElements: q.supportiveElements || {},
      followUpQuestions: q.followUpQuestions || [],
      adaptiveOptions: {},
    }));

    const template: Partial<QuestionTemplate> = {
      name: templateName || `自動生成テンプレート_${new Date().toLocaleDateString()}`,
      description: `AI生成された質問テンプレート（${questions.length}問）`,
      positionId,
      questions: templateQuestions,
      totalEstimatedTime: questions.reduce((sum, q) => sum + q.estimatedAnswerTime, 0),
      difficulty: this.determineDifficultyFromQuestions(questions),
      psychologicalSafety: {
        enabled: true,
        anxietyReduction: true,
        encouragementLevel: 'medium',
        personalizedMessaging: true,
      },
      tags: [
        'AI生成',
        '自動作成',
        ...this.extractTagsFromQuestions(questions)
      ],
    };

    return await this.companyMasterService.saveQuestionTemplate(template, companyId, agentId);
  }

  /**
   * 企業の既存質問テンプレート取得
   */
  getQuestionTemplates(companyId: string, positionId?: string): QuestionTemplate[] {
    return this.companyMasterService.getQuestionTemplates(companyId, positionId);
  }

  /**
   * 企業マスター情報の取得
   */
  async getCompanyMasterById(companyId: string): Promise<CompanyMaster | null> {
    // 簡易的な実装：企業IDで検索して返す
    try {
      const suggestions = await this.companyMasterService.suggestCompanies(companyId);
      const exactMatch = suggestions.find(company => company.id === companyId);
      if (exactMatch) {
        // CompanySuggestionからCompanyMasterに変換
        return {
          id: exactMatch.id,
          name: exactMatch.name,
          industry: exactMatch.industry,
          description: '',
          culture: '',
          website: '',
          businessDescription: '',
          size: 'sme',
          location: [],
          benefits: [],
          workEnvironment: {
            remoteWork: true,
            flexTime: true,
            averageOvertimeHours: 20,
          },
          interviewStyle: 'formal',
          positions: [],
          questionTemplates: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastUsed: new Date().toISOString(),
          usageCount: exactMatch.usageCount,
          createdBy: 'system',
        } as CompanyMaster;
      }
      return null;
    } catch (error) {
      console.error('企業情報取得エラー:', error);
      return null;
    }
  }

  /**
   * PDF解析結果から企業情報を抽出し、高度な質問生成を実行
   */
  async generateEnhancedQuestions(
    extractedInfo: ExtractedCompanyInfo,
    candidateInfo?: {
      experienceLevel?: 'entry' | 'mid' | 'senior' | 'executive';
      skills?: string[];
      background?: string[];
      interviewConfig?: {
        interviewerRole: any | null; // InterviewerRole型は後で適切にインポート
        interviewProcess: any | null; // InterviewProcess型は後で適切にインポート
        isMultiStage: boolean;
      };
    },
    existingIntents?: AIGeneratedIntent[]
  ): Promise<{
    generatedQuestions: GeneratedQuestion[];
    enhancedIntents: AIGeneratedIntent[];
    sessionConfig: any;
    supportiveMessaging: any;
  }> {
    
    // 1. 既存の質問意図カテゴリを分析して最適なカテゴリを選択
    const selectedCategories = this.mapIntentsToCategories(existingIntents);
    
    // 2. IntelligentQuestionGenerator 用のリクエストを構築
    const request: QuestionGenerationRequest = {
      companyInfo: {
        name: extractedInfo.companyName,
        industry: extractedInfo.industry,
        position: extractedInfo.position,
        requirements: extractedInfo.requirements,
        culture: extractedInfo.culture,
        interviewStyle: extractedInfo.interviewStyle || 'standard',
      },
      candidateProfile: candidateInfo ? {
        experienceLevel: candidateInfo.experienceLevel || 'mid',
        skills: candidateInfo.skills || [],
        background: candidateInfo.background || [],
        previousInterviews: 0,
      } : undefined,
      // 面接官役割情報を追加（存在する場合）
      interviewerRole: candidateInfo?.interviewConfig?.interviewerRole || undefined,
      questionSettings: {
        totalQuestions: Math.max(5, existingIntents?.length || 5),
        categories: selectedCategories,
        difficulty: this.determineDifficulty(candidateInfo?.experienceLevel),
        estimatedDuration: this.calculateEstimatedDuration(existingIntents),
        includeWarmup: true,
        includeFollowUp: true,
      },
      adaptiveSettings: {
        personalizeForCandidate: true,
        adjustForAnxiety: true,
        emphasizeGrowth: true,
        avoidNegativeLanguage: true,
      },
    };

    // 3. 高度な質問生成を実行
    const response = await this.questionGenerator.generateQuestions(request);

    // 4. 生成された質問から質問意図を強化
    const enhancedIntents = this.enhanceExistingIntents(
      existingIntents || [],
      response.questions,
      extractedInfo
    );

    return {
      generatedQuestions: response.questions,
      enhancedIntents,
      sessionConfig: response.sessionConfig,
      supportiveMessaging: response.supportiveMessaging,
    };
  }

  /**
   * 既存の質問意図をIntelligentQuestionGeneratorの結果で強化
   */
  private enhanceExistingIntents(
    existingIntents: AIGeneratedIntent[],
    generatedQuestions: GeneratedQuestion[],
    companyInfo: ExtractedCompanyInfo
  ): AIGeneratedIntent[] {
    
    const enhancedIntents: AIGeneratedIntent[] = [...existingIntents];

    // 生成された質問の内容を元に既存の意図を強化
    generatedQuestions.forEach((question, index) => {
      if (index < enhancedIntents.length) {
        const intent = enhancedIntents[index];
        
        // 心理的安全性の配慮を既存の意図に追加
        const safetyEnhancements = this.generateSafetyEnhancements(question);
        
        enhancedIntents[index] = {
          ...intent,
          description: `${intent.description}\n\n【心理的安全性配慮】\n${safetyEnhancements}`,
          aiInstructions: this.enhanceAIInstructions(intent.aiInstructions, question),
          confidence: Math.min(intent.confidence + 0.1, 0.98), // 信頼度を向上
          isEdited: true,
        };
      } else {
        // 新しい質問から追加の意図を生成
        const newIntent: AIGeneratedIntent = {
          id: `enhanced_intent_${index}`,
          category: this.mapCategoryToLegacyFormat(question.category),
          intent: question.intent.primary,
          description: `AI生成: ${question.text}\n\n評価観点: ${question.evaluationCriteria.map(c => c.name).join(', ')}`,
          priority: question.difficulty === 'hard' ? 'high' : 'medium',
          estimatedTime: Math.ceil(question.estimatedAnswerTime / 60),
          aiInstructions: this.generateInstructionsFromQuestion(question),
          confidence: 0.90,
          isEdited: false,
        };
        
        enhancedIntents.push(newIntent);
      }
    });

    return enhancedIntents;
  }

  /**
   * 質問意図カテゴリをQuestionCategoryにマッピング
   */
  private mapIntentsToCategories(intents?: AIGeneratedIntent[]): QuestionCategory[] {
    if (!intents || intents.length === 0) {
      return ['self-introduction', 'experience-skills', 'motivation', 'problem-solving'];
    }

    const categoryMap: Record<string, QuestionCategory> = {
      '企業理解・志望動機': 'motivation',
      '職務適性・スキル': 'experience-skills',
      'キャリアビジョン・成長意欲': 'career-goals',
      'チームワーク・協調性': 'teamwork',
      'リーダーシップ': 'leadership',
      '技術的能力': 'technical',
      '問題解決能力': 'problem-solving',
      '企業文化適合性': 'company-culture-fit',
    };

    const mappedCategories = intents
      .map(intent => categoryMap[intent.category] || 'behavioral')
      .filter((category, index, self) => self.indexOf(category) === index) // 重複除去
      .slice(0, 6); // 最大6カテゴリ

    // 最低限のカテゴリを保証
    if (!mappedCategories.includes('self-introduction')) {
      mappedCategories.unshift('self-introduction');
    }

    return mappedCategories as QuestionCategory[];
  }

  /**
   * QuestionCategoryを既存のカテゴリ形式にマッピング
   */
  private mapCategoryToLegacyFormat(category: QuestionCategory): string {
    const reverseMap: Record<QuestionCategory, string> = {
      'self-introduction': '自己紹介・アイスブレイク',
      'experience-skills': '職務適性・スキル',
      'motivation': '企業理解・志望動機',
      'problem-solving': '問題解決能力',
      'teamwork': 'チームワーク・協調性',
      'leadership': 'リーダーシップ',
      'career-goals': 'キャリアビジョン・成長意欲',
      'company-culture-fit': '企業文化適合性',
      'technical': '技術的能力',
      'behavioral': '行動特性・価値観',
      'situational': '状況判断・適応力',
    };

    return reverseMap[category] || '総合評価';
  }

  /**
   * 経験レベルから難易度を決定
   */
  private determineDifficulty(experienceLevel?: string): 'easy' | 'medium' | 'hard' | 'adaptive' {
    switch (experienceLevel) {
      case 'entry': return 'easy';
      case 'mid': return 'medium';
      case 'senior': case 'executive': return 'hard';
      default: return 'adaptive';
    }
  }

  /**
   * 推定所要時間を計算
   */
  private calculateEstimatedDuration(intents?: AIGeneratedIntent[]): number {
    if (!intents || intents.length === 0) return 30;
    
    const totalTime = intents.reduce((sum, intent) => sum + intent.estimatedTime, 0);
    return Math.max(20, Math.min(60, totalTime + 5)); // 20-60分の範囲
  }

  /**
   * 心理的安全性の配慮事項を生成
   */
  private generateSafetyEnhancements(question: GeneratedQuestion): string {
    const enhancements: string[] = [];

    if (question.supportiveElements?.encouragementPrefix) {
      enhancements.push(`・面接開始前: "${question.supportiveElements.encouragementPrefix}"`);
    }

    if (question.supportiveElements?.clarificationNote) {
      enhancements.push(`・回答中の配慮: ${question.supportiveElements.clarificationNote}`);
    }

    if (question.supportiveElements?.reassuranceMessage) {
      enhancements.push(`・安心メッセージ: ${question.supportiveElements.reassuranceMessage}`);
    }

    if (question.difficulty === 'hard') {
      enhancements.push('・難しい質問への配慮: 回答に詰まった場合は、別の角度からの質問を提示する');
    }

    return enhancements.length > 0 
      ? enhancements.join('\n') 
      : '候補者の心理的負担を軽減し、リラックスした環境で自然な回答を引き出す配慮を行う';
  }

  /**
   * AI指示文を強化
   */
  private enhanceAIInstructions(
    originalInstructions: string,
    question: GeneratedQuestion
  ): string {
    const enhancements = [
      originalInstructions,
      '',
      '【心理的安全性への配慮】',
      '・候補者をリラックスさせ、自然な会話を心がける',
      '・否定的な表現や圧迫的な質問は避ける',
      '・回答に詰まった場合は、優しくサポートする',
    ];

    if (question.followUpQuestions && question.followUpQuestions.length > 0) {
      enhancements.push('', '【フォローアップ質問】');
      question.followUpQuestions.forEach((followUp, index) => {
        enhancements.push(`${index + 1}. ${followUp}`);
      });
    }

    if (question.evaluationCriteria.length > 0) {
      enhancements.push('', '【評価ポイント】');
      question.evaluationCriteria.forEach(criteria => {
        enhancements.push(`・${criteria.name}: ${criteria.description}`);
      });
    }

    return enhancements.join('\n');
  }

  /**
   * 質問から指示文を生成
   */
  private generateInstructionsFromQuestion(question: GeneratedQuestion): string {
    const instructions = [
      `以下の質問を使用して候補者を評価してください:`,
      `"${question.text}"`,
      '',
      '【評価の観点】',
    ];

    question.evaluationCriteria.forEach(criteria => {
      instructions.push(`・${criteria.name}: ${criteria.description}`);
    });

    instructions.push('', '【心理的安全性への配慮】');
    instructions.push('・プロダクト憲法第一条に基づき、候補者の心理的安全性を最優先にしてください');
    instructions.push('・回答を評価する際は、成長の可能性と改善点を建設的に伝えてください');

    return instructions.join('\n');
  }

  // ===== 新規追加のヘルパーメソッド =====

  /**
   * 職種名から部署を推測
   */
  private extractDepartmentFromPosition(position: string): string {
    const departmentMap: Record<string, string> = {
      'エンジニア': '技術部',
      'デザイナー': 'デザイン部',
      '営業': '営業部',
      'マーケティング': 'マーケティング部',
      '人事': '人事部',
      '経理': '経理部',
      'PM': 'プロダクト部',
      'QA': '品質保証部',
    };

    for (const [keyword, department] of Object.entries(departmentMap)) {
      if (position.includes(keyword)) {
        return department;
      }
    }

    return 'その他';
  }

  /**
   * 要件からレベルを判定
   */
  private determineLevelFromRequirements(requirements: string[]): 'entry' | 'mid' | 'senior' | 'executive' | 'c-level' {
    const requirementText = requirements.join(' ').toLowerCase();

    if (requirementText.includes('cto') || requirementText.includes('責任者') || requirementText.includes('マネージャー')) {
      return 'executive';
    } else if (requirementText.includes('リーダー') || requirementText.includes('シニア') || requirementText.includes('5年以上')) {
      return 'senior';
    } else if (requirementText.includes('経験') && !requirementText.includes('未経験')) {
      return 'mid';
    } else {
      return 'entry';
    }
  }

  /**
   * 要件から重点評価領域を抽出
   */
  private extractFocusAreas(requirements: string[]): string[] {
    const areas: string[] = [];
    const requirementText = requirements.join(' ');

    if (requirementText.includes('React') || requirementText.includes('フロントエンド')) {
      areas.push('フロントエンド技術');
    }
    if (requirementText.includes('Node.js') || requirementText.includes('バックエンド')) {
      areas.push('バックエンド技術');
    }
    if (requirementText.includes('AWS') || requirementText.includes('インフラ')) {
      areas.push('インフラ・クラウド');
    }
    if (requirementText.includes('チーム') || requirementText.includes('協調')) {
      areas.push('チームワーク');
    }
    if (requirementText.includes('コミュニケーション')) {
      areas.push('コミュニケーション能力');
    }

    return areas.length > 0 ? areas : ['総合評価'];
  }

  /**
   * 質問群から難易度を判定
   */
  private determineDifficultyFromQuestions(questions: GeneratedQuestion[]): 'easy' | 'medium' | 'hard' | 'adaptive' {
    const hardCount = questions.filter(q => q.difficulty === 'hard').length;
    const easyCount = questions.filter(q => q.difficulty === 'easy').length;

    if (hardCount > questions.length / 2) return 'hard';
    if (easyCount > questions.length / 2) return 'easy';
    return 'medium';
  }

  /**
   * 質問からタグを抽出
   */
  private extractTagsFromQuestions(questions: GeneratedQuestion[]): string[] {
    const tags = new Set<string>();

    questions.forEach(q => {
      if (q.category.includes('technical')) tags.add('技術面接');
      if (q.category.includes('behavioral')) tags.add('行動面接');
      if (q.category.includes('leadership')) tags.add('リーダーシップ');
      if (q.difficulty === 'hard') tags.add('高難度');
      if (q.supportiveElements?.encouragementPrefix) tags.add('心理的安全性');
    });

    return Array.from(tags);
  }
}

export default WorkflowIntegrationService;