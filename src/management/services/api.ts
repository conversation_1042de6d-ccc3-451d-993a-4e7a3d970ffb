/**
 * 管理画面用統一APIサービス
 * 求職者画面のAPIサービスと連携し、管理画面特有の機能を提供
 */

// 管理画面用のモックデータを使用
import {
  UnifiedMockDataService
} from '../../shared/services/unifiedMockData';
import type { Question, Feedback } from '../../shared/types';
import type { CandidateApiResponse, CandidateInfo } from '../types/candidate';

// 管理画面特有の型定義
export interface MeetingLink {
  id: string;
  candidateName: string;
  url: string;
  expiresAt: string;
  createdAt: string;
  status: "active" | "expired" | "used";
  companyInfo?: {
    name: string;
    position: string;
    requirements: string[];
  };
}

export interface InterviewLinkCreate {
  candidate_id: string;
  company_id: string;
  expiration_days: number;
  agent_notes?: string;
  interviewer_role?: string;
  interview_type?: string;
  candidate_email?: string;
}

export interface InterviewLinkResponse {
  id: string;
  session_token: string;
  expires_at: string;
  candidate_name: string;
  interview_url?: string;
}

export interface AgentInterviewConfig {
  companyName: string;
  position: string;
  requirements: string[];
  candidateEmail?: string;
  interviewType: 'behavioral' | 'technical' | 'case' | 'mixed';
  estimatedDuration: number;
}

export interface CompanyApiResponse {
  id: string;
  name: string;
  industry?: string;
  description?: string;
  core_values?: string[];
  vision_mission?: string;
  created_at: string;
  total_documents: number;
  last_activity: string;
}

export interface CompanyListApiResponse {
  total: number;
  skip: number;
  limit: number;
  companies: CompanyApiResponse[];
}

export interface CompanyKeypointResponse {
  id: string;
  company_id: string;
  template_id: string;
  custom_name: string;
  custom_description: string;
  position_type: string;
  priority: number;
  order_index: number;
  status: string;
  is_active: boolean;
  created_at: string;
  template: {
    id: string;
    name: string;
    description: string;
    position_type: string;
    industry: string;
    difficulty_level: string;
    estimated_time_minutes: number;
    is_default: boolean;
    is_active: boolean;
    created_at: string;
  };
  questions: any[];
  total_questions: number;
  estimated_total_time: number;
}

/**
 * 管理画面専用APIサービス
 */
export class ManagementApi {
  /**
   * ミーティングリンクを生成
   */
  static async generateMeetingLink(candidateName: string, config?: AgentInterviewConfig, expirationDays: number = 7): Promise<MeetingLink> {
    // モックミーティングリンク生成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const linkId = `mtg_${Date.now()}`;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    
    return {
      id: linkId,
      candidateName,
      url: `${baseUrl}/interview-room?link=${linkId}`,
      expiresAt: new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      status: "active",
      companyInfo: config ? {
        name: config.companyName,
        position: config.position,
        requirements: config.requirements
      } : undefined
    };
  }

  /**
   * 面接リンクを生成 (APIを使用)
   */
  static async generateInterviewLink(data: InterviewLinkCreate): Promise<InterviewLinkResponse> {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
      const response = await fetch(`${baseUrl}/interviews/generate-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result: InterviewLinkResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error generating interview link:', error);
      throw error;
    }
  }

  /**
   * 質問スクリプトを生成
   */
  static async generateQuestionScript(
    companyName: string,
    position: string,
    requirements: string[]
  ): Promise<Question[]> {
    const { questions } = await UnifiedMockDataService.generateQuestionScript({
      companyName,
      position,
      requirements,
      interviewType: 'behavioral',
      estimatedDuration: 900,
    });
    return questions;
  }

  /**
   * エージェント向けフィードバック一覧を取得
   */
  static async getAgentFeedbackList(): Promise<{
    candidateName: string;
    interviewDate: string;
    overallScore: number;
    feedbacks: Feedback[];
  }[]> {
    // モックフィードバックリスト
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const feedbacks = await UnifiedMockDataService.getBasicFeedbacks();
    
    return [
      {
        candidateName: '田中太郎',
        interviewDate: '2024-06-14',
        overallScore: 8.2,
        feedbacks: feedbacks
      },
      {
        candidateName: '佐藤花子',
        interviewDate: '2024-06-13',
        overallScore: 7.5,
        feedbacks: feedbacks.slice(0, 1)
      }
    ];
  }

  /**
   * 面接設定を保存
   */
  static async saveInterviewConfig(config: AgentInterviewConfig): Promise<{ configId: string }> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      configId: `config_${Date.now()}`
    };
  }

  /**
   * アクティブなミーティングリンク一覧を取得
   */
  static async getActiveMeetingLinks(): Promise<MeetingLink[]> {
    await new Promise(resolve => setTimeout(resolve, 600));
    
    return [
      {
        id: 'mtg_001',
        candidateName: '山田一郎',
        url: 'http://localhost:3000/interview-room?link=mtg_001',
        expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        companyInfo: {
          name: '株式会社テックソリューション',
          position: 'フロントエンドエンジニア',
          requirements: ['React経験3年以上', 'TypeScript経験', 'チーム開発経験']
        }
      },
      {
        id: 'mtg_002',
        candidateName: '鈴木美咲',
        url: 'http://localhost:3000/interview-room?link=mtg_002',
        expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        companyInfo: {
          name: '株式会社マーケティングラボ',
          position: 'デジタルマーケター',
          requirements: ['Google Analytics経験', 'SNS運用経験', 'データ分析スキル']
        }
      }
    ];
  }

  /**
   * 企業一覧をAPIから取得
   */
  static async getCompaniesList(skip: number = 0, limit: number = 100): Promise<CompanyListApiResponse> {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
      const response = await fetch(`${baseUrl}/companies/?skip=${skip}&limit=${limit}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: CompanyListApiResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  }
}

export class CompanyApi {
  /**
   * 企業情報からAIキーポイントを生成
   */
  static async generateKeypoints(companyId: string, count: number = 4): Promise<CompanyKeypointResponse[]> {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
      const response = await fetch(`${baseUrl}/companies/${companyId}/generate-keypoints?count=${count}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const keypoints: CompanyKeypointResponse[] = await response.json();
      return keypoints;
    } catch (error) {
      console.error('Error generating keypoints:', error);
      throw error;
    }
  }
}

export class CandidateApi {

  static async getCandidatesList(skip: number = 0, limit: number = 100): Promise<CandidateInfo[]> {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';
      const response = await fetch(`${baseUrl}/candidates/?skip=${skip}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const candidates: CandidateApiResponse[] = await response.json();
      // console.log('getCandidatesList response', candidates);

      // Transform API response to match CandidateInfo interface
      return candidates.map(candidate => ({
        id: candidate.id,
        name: candidate.nameKana, // Using nameKana as display name
        nameKana: candidate.nameKana,
        age: candidate.age,
        currentStatus: candidate.employmentStatus,
        currentEmployer: candidate.company,
        previousEmployer: undefined, // Not available in API
        position: candidate.position,
        address: candidate.address,
        hasResume: candidate.hasResume,
        hasCareerHistory: candidate.hasCareerHistory,
        activeLinks: [], // Will be populated separately if needed
      }));
    } catch (error) {
      console.error('Error fetching candidates:', error);
      throw error;
    }
  }

  static async registerCandidate(formData: FormData) {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';

    const response = await fetch(`${baseUrl}/candidates/register`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '登録に失敗しました');
    }
    return response.json();
  }
}
/**
 * エクスポート用の統合オブジェクト
 */
export default {
  ManagementApi,
  CandidateApi,
};