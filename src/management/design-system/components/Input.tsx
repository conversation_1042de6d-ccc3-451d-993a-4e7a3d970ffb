/**
 * Input Component - 面接君デザインシステム 
 * フォーカス時の視覚的フィードバック強化
 */

import React from 'react';
import {
  FormControl,
  FormLabel,
  Input as ChakraInput,
  FormHelperText,
  FormErrorMessage,
  InputProps as ChakraInputProps,
  FormControlProps,
} from '@chakra-ui/react';
import { colors, motion, borders } from '../index';

export interface InputProps extends ChakraInputProps {
  /** ラベル */
  label?: string;
  /** ヘルパーテキスト */
  helperText?: string;
  /** エラーメッセージ */
  errorMessage?: string;
  /** 必須項目かどうか */
  isRequired?: boolean;
  /** エラー状態 */
  isInvalid?: boolean;
  /** FormControlのプロパティ */
  formControlProps?: FormControlProps;
}

const inputStyles = {
  borderColor: colors.gray[300],
  _hover: {
    borderColor: colors.gray[400],
  },
  _focus: {
    borderColor: colors.primary[500],
    boxShadow: `0 0 0 1px ${colors.primary[500]}`,
  },
  _invalid: {
    borderColor: colors.error[500],
    boxShadow: `0 0 0 1px ${colors.error[500]}`,
  },
  transition: `all ${motion.duration.quick} ${motion.easing.natural}`,
  borderRadius: borders.radius.md,
};

export const Input: React.FC<InputProps> = ({
  label,
  helperText,
  errorMessage,
  isRequired = false,
  isInvalid = false,
  formControlProps,
  ...inputProps
}) => {
  return (
    <FormControl isRequired={isRequired} isInvalid={isInvalid} {...formControlProps}>
      {label && (
        <FormLabel 
          fontSize="sm" 
          fontWeight="medium"
          color={colors.gray[700]}
          mb={2}
        >
          {label}
        </FormLabel>
      )}
      
      <ChakraInput
        {...inputStyles}
        {...inputProps}
      />
      
      {helperText && !isInvalid && (
        <FormHelperText fontSize="xs" color={colors.gray[600]} mt={1}>
          {helperText}
        </FormHelperText>
      )}
      
      {errorMessage && isInvalid && (
        <FormErrorMessage fontSize="xs" mt={1}>
          {errorMessage}
        </FormErrorMessage>
      )}
    </FormControl>
  );
};