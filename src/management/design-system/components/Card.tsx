/**
 * Card Component - 面接君デザインシステム
 * 情報整理のための基本コンポーネント
 */

import React from 'react';
import { Box, BoxProps } from '@chakra-ui/react';
import { colors, shadows, motion, borders } from '../index';

export interface CardProps extends BoxProps {
  /** カードの種類 */
  variant?: 'default' | 'outline' | 'elevated' | 'interactive';
  /** ホバー効果の有無 */
  hoverable?: boolean;
  /** カード内のパディング */
  padding?: 'sm' | 'md' | 'lg';
}

const cardVariants = {
  default: {
    bg: 'white',
    border: 'none',
    boxShadow: shadows.base,
  },
  outline: {
    bg: 'white',
    border: `1px solid ${colors.gray[200]}`,
    boxShadow: 'none',
  },
  elevated: {
    bg: 'white', 
    border: 'none',
    boxShadow: shadows.lg,
  },
  interactive: {
    bg: 'white',
    border: `1px solid ${colors.gray[200]}`,
    boxShadow: shadows.base,
    cursor: 'pointer',
  },
};

const paddingStyles = {
  sm: 4,
  md: 6,
  lg: 8,
};

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  hoverable = false,
  padding = 'md',
  children,
  ...props
}) => {
  const hoverStyles = hoverable || variant === 'interactive' ? {
    _hover: {
      transform: 'translateY(-2px)',
      boxShadow: shadows.lg,
    }
  } : {};

  return (
    <Box
      {...cardVariants[variant]}
      p={paddingStyles[padding]}
      borderRadius={borders.radius.lg}
      transition={`all ${motion.duration.moderate} ${motion.easing.natural}`}
      {...hoverStyles}
      {...props}
    >
      {children}
    </Box>
  );
};