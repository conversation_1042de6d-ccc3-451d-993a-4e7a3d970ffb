/**
 * Button Component - 面接君デザインシステム
 * UIUX憲法に基づく統一ボタンコンポーネント
 */

import React from 'react';
import { Button as ChakraButton, ButtonProps as ChakraButtonProps } from '@chakra-ui/react';
import { colors, motion } from '../index';

export interface ButtonProps extends Omit<ChakraButtonProps, 'variant' | 'size'> {
  /** ボタンの種類 */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  /** ボタンサイズ */
  size?: 'sm' | 'md' | 'lg';
  /** フルワイズ表示 */
  fullWidth?: boolean;
  /** ローディング状態 */
  isLoading?: boolean;
  /** 非活性状態 */
  isDisabled?: boolean;
}

const buttonStyles = {
  primary: {
    bg: colors.primary[500],
    color: 'white',
    _hover: { bg: colors.primary[600] },
    _active: { bg: colors.primary[700] },
    _disabled: { bg: colors.gray[300], color: colors.gray[500] },
  },
  secondary: {
    bg: colors.secondary[500], 
    color: 'white',
    _hover: { bg: colors.secondary[600] },
    _active: { bg: colors.secondary[600] },
    _disabled: { bg: colors.gray[300], color: colors.gray[500] },
  },
  outline: {
    bg: 'transparent',
    color: colors.primary[500],
    border: `1px solid ${colors.primary[500]}`,
    _hover: { bg: colors.primary[50] },
    _active: { bg: colors.primary[100] },
    _disabled: { borderColor: colors.gray[300], color: colors.gray[500] },
  },
  ghost: {
    bg: 'transparent',
    color: colors.gray[700],
    _hover: { bg: colors.gray[100] },
    _active: { bg: colors.gray[200] },
    _disabled: { color: colors.gray[400] },
  },
  destructive: {
    bg: colors.error[500],
    color: 'white', 
    _hover: { bg: '#dc2626' }, // Slightly darker red for hover
    _active: { bg: '#b91c1c' }, // Even darker red for active
    _disabled: { bg: colors.gray[300], color: colors.gray[500] },
  },
};

const sizeStyles = {
  sm: {
    h: 8,
    px: 3,
    fontSize: 'sm',
  },
  md: {
    h: 10,
    px: 4,
    fontSize: 'md',
  },
  lg: {
    h: 12,
    px: 6,
    fontSize: 'lg',
  },
};

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  children,
  isLoading = false,
  isDisabled = false,
  ...props
}) => {
  return (
    <ChakraButton
      {...buttonStyles[variant]}
      {...sizeStyles[size]}
      w={fullWidth ? 'full' : 'auto'}
      isLoading={isLoading}
      isDisabled={isDisabled}
      transition={`all ${motion.duration.quick} ${motion.easing.natural}`}
      fontWeight="medium"
      borderRadius="md"
      {...props}
    >
      {children}
    </ChakraButton>
  );
};