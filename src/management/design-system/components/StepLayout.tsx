/**
 * StepLayout Component - 面接君デザインシステム
 * ステップ型ワークフローの統一レイアウト
 */

import React from 'react';
import {
  VStack,
  HStack,
  Text,
  Progress,
  Stepper,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  StepSeparator,
  Box,
} from '@chakra-ui/react';
import { colors, spacing } from '../index';
import { Card } from './Card';

export interface StepInfo {
  title: string;
  description: string;
  status?: 'complete' | 'active' | 'inactive';
}

export interface StepLayoutProps {
  /** 現在のステップインデックス */
  currentStep: number;
  /** ステップ情報の配列 */
  steps: StepInfo[];
  /** ステップのタイトル */
  title?: string;
  /** プログレスバーの表示 */
  showProgress?: boolean;
  /** 子要素 */
  children: React.ReactNode;
}

export const StepLayout: React.FC<StepLayoutProps> = ({
  currentStep,
  steps,
  title,
  showProgress = true,
  children,
}) => {
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <VStack spacing={spacing[6]} align="stretch" w="full">
      {/* タイトル */}
      {title && (
        <Box textAlign="center">
          <Text 
            fontSize="2xl" 
            fontWeight="bold"
            color={colors.gray[800]}
            mb={spacing[2]}
          >
            {title}
          </Text>
          {showProgress && (
            <Box>
              <Progress 
                value={progress} 
                colorScheme="blue"
                size="sm"
                borderRadius="full"
                bg={colors.gray[200]}
              />
              <Text 
                fontSize="sm" 
                color={colors.gray[600]}
                mt={spacing[1]}
              >
                ステップ {currentStep + 1} / {steps.length}
              </Text>
            </Box>
          )}
        </Box>
      )}

      {/* ステッパー */}
      <Card variant="outline" padding="md">
        <Stepper 
          index={currentStep} 
          colorScheme="blue" 
          size="sm"
          orientation="horizontal"
        >
          {steps.map((step, index) => (
            <Step key={index}>
              <StepIndicator>
                <StepStatus
                  complete={<StepIcon />}
                  incomplete={<StepNumber />}
                  active={<StepNumber />}
                />
              </StepIndicator>
              
              <Box flexShrink="0" ml={spacing[2]}>
                <StepTitle 
                  fontSize="sm"
                  fontWeight="medium"
                  color={index <= currentStep ? colors.primary[600] : colors.gray[500]}
                >
                  {step.title}
                </StepTitle>
                <StepDescription 
                  fontSize="xs"
                  color={colors.gray[600]}
                >
                  {step.description}
                </StepDescription>
              </Box>
              
              <StepSeparator />
            </Step>
          ))}
        </Stepper>
      </Card>

      {/* メインコンテンツ */}
      <Box>
        {children}
      </Box>
    </VStack>
  );
};