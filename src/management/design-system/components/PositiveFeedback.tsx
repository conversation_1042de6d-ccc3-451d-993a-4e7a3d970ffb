/**
 * PositiveFeedback Component - 面接君デザインシステム
 * プロダクト憲法第一条に基づく心理的安全性の保障
 */

import React from 'react';
import { Box, Text, VStack, HStack, Icon } from '@chakra-ui/react';
import { CheckCircleIcon, StarIcon, ArrowUpIcon } from '@chakra-ui/icons';
import { colors, spacing } from '../index';
import { Card } from './Card';

export interface PositiveFeedbackProps {
  /** フィードバックの種類 */
  type?: 'success' | 'growth' | 'potential' | 'encouragement';
  /** メインメッセージ */
  message: string;
  /** 詳細説明（オプション） */
  details?: string;
  /** 次のステップ提案（オプション） */
  nextStep?: string;
  /** アイコンの表示/非表示 */
  showIcon?: boolean;
}

const feedbackConfig = {
  success: {
    icon: CheckCircleIcon,
    iconColor: colors.secondary[500],
    borderColor: colors.secondary[600],
    bgColor: colors.secondary[50],
    prefix: "✨ 素晴らしいポイント",
  },
  growth: {
    icon: ArrowUpIcon,
    iconColor: colors.primary[500], 
    borderColor: colors.primary[600],
    bgColor: colors.primary[50],
    prefix: "🌱 成長のサイン",
  },
  potential: {
    icon: StarIcon,
    iconColor: colors.warning[500],
    borderColor: colors.warning[500], 
    bgColor: colors.warning[50],
    prefix: "💡 可能性の発見",
  },
  encouragement: {
    icon: CheckCircleIcon,
    iconColor: colors.secondary[500],
    borderColor: colors.secondary[600],
    bgColor: colors.secondary[50],
    prefix: "🎯 次のステップ",
  },
};

export const PositiveFeedback: React.FC<PositiveFeedbackProps> = ({
  type = 'success',
  message,
  details,
  nextStep,
  showIcon = true,
}) => {
  const config = feedbackConfig[type];

  return (
    <Card
      variant="outline"
      borderColor={config.borderColor}
      bg={config.bgColor}
      padding="md"
    >
      <VStack align="start" spacing={spacing[3]}>
        <HStack spacing={spacing[2]} align="center">
          {showIcon && (
            <Icon 
              as={config.icon} 
              color={config.iconColor} 
              w={5} 
              h={5} 
            />
          )}
          <Text 
            fontSize="sm" 
            fontWeight="semibold"
            color={config.iconColor}
          >
            {config.prefix}
          </Text>
        </HStack>

        <Text 
          fontSize="md" 
          fontWeight="medium"
          color={colors.gray[800]}
          lineHeight="relaxed"
        >
          {message}
        </Text>

        {details && (
          <Text 
            fontSize="sm" 
            color={colors.gray[600]}
            lineHeight="relaxed"
          >
            {details}
          </Text>
        )}

        {nextStep && (
          <Box 
            p={spacing[3]} 
            bg="white"
            borderRadius="md"
            border={`1px solid ${config.borderColor}`}
            w="full"
          >
            <Text 
              fontSize="sm" 
              fontWeight="medium"
              color={colors.gray[700]}
            >
              💭 ご提案：{nextStep}
            </Text>
          </Box>
        )}
      </VStack>
    </Card>
  );
};