/**
 * useToastPositive Hook - 面接君デザインシステム
 * プロダクト憲法に基づくポジティブトースト通知
 */

import { useToast, UseToastOptions } from '@chakra-ui/react';
import { colors } from '../index';

interface PositiveToastOptions extends Omit<UseToastOptions, 'status'> {
  /** ポジティブフィードバックのタイプ */
  type?: 'success' | 'progress' | 'encouragement' | 'discovery';
}

const toastConfig = {
  success: {
    status: 'success' as const,
    title: '素晴らしい進歩です！',
    icon: '✨',
  },
  progress: {
    status: 'info' as const,
    title: '順調に進んでいます',
    icon: '🌱',
  },
  encouragement: {
    status: 'info' as const,
    title: '一緒に頑張りましょう',
    icon: '💪',
  },
  discovery: {
    status: 'info' as const,
    title: '新しい発見です',
    icon: '💡',
  },
};

export const useToastPositive = () => {
  const toast = useToast();

  const showPositiveToast = ({
    type = 'success',
    title,
    description,
    ...options
  }: PositiveToastOptions) => {
    const config = toastConfig[type];
    
    return toast({
      ...config,
      title: title || config.title,
      description: description ? `${config.icon} ${description}` : undefined,
      duration: 4000,
      isClosable: true,
      position: 'top-right',
      ...options,
    });
  };

  const showSuccess = (description: string, title?: string) => {
    return showPositiveToast({
      type: 'success',
      title,
      description,
    });
  };

  const showProgress = (description: string, title?: string) => {
    return showPositiveToast({
      type: 'progress', 
      title,
      description,
    });
  };

  const showEncouragement = (description: string, title?: string) => {
    return showPositiveToast({
      type: 'encouragement',
      title,
      description,
    });
  };

  const showDiscovery = (description: string, title?: string) => {
    return showPositiveToast({
      type: 'discovery',
      title,
      description,
    });
  };

  return {
    showPositiveToast,
    showSuccess,
    showProgress,
    showEncouragement,
    showDiscovery,
  };
};