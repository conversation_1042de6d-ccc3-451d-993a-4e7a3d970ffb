/**
 * useFormValidation Hook - 面接君デザインシステム
 * 一貫したフォームバリデーション処理
 */

import { useState, useCallback } from 'react';

export interface ValidationRule {
  /** バリデーション関数 */
  validate: (value: any) => boolean;
  /** エラーメッセージ（ポジティブな表現を推奨） */
  message: string;
}

export interface FieldConfig {
  /** 必須項目かどうか */
  required?: boolean;
  /** カスタムバリデーションルール */
  rules?: ValidationRule[];
  /** 初期値 */
  initialValue?: any;
}

export interface UseFormValidationOptions {
  /** フィールド設定 */
  fields: Record<string, FieldConfig>;
  /** ポジティブフィードバックモード */
  positiveMode?: boolean;
}

export const useFormValidation = ({
  fields,
  positiveMode = true,
}: UseFormValidationOptions) => {
  const [values, setValues] = useState<Record<string, any>>(() => {
    const initialValues: Record<string, any> = {};
    Object.entries(fields).forEach(([key, config]) => {
      initialValues[key] = config.initialValue || '';
    });
    return initialValues;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const setValue = useCallback((field: string, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // エラーをクリア
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  const setFieldTouched = useCallback((field: string, isTouched: boolean = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }));
  }, []);

  const validateField = useCallback((field: string, value: any): string => {
    const config = fields[field];
    if (!config) return '';

    // 必須チェック
    if (config.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return positiveMode 
        ? `${field}を入力していただけると助かります`
        : `${field}は必須です`;
    }

    // カスタムルールチェック
    if (config.rules) {
      for (const rule of config.rules) {
        if (!rule.validate(value)) {
          return rule.message;
        }
      }
    }

    return '';
  }, [fields, positiveMode]);

  const validateAll = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    Object.keys(fields).forEach(field => {
      const error = validateField(field, values[field]);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [fields, values, validateField]);

  const reset = useCallback(() => {
    const initialValues: Record<string, any> = {};
    Object.entries(fields).forEach(([key, config]) => {
      initialValues[key] = config.initialValue || '';
    });
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [fields]);

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateField,
    validateAll,
    reset,
    isValid: Object.keys(errors).length === 0,
    isDirty: Object.keys(touched).length > 0,
  };
};