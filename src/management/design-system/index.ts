/**
 * 面接君 デザインシステム V4.0
 * UIUX_DESIGN_GUIDLINES.mdに基づく統合デザインシステム
 */

// カラーシステム
export const colors = {
  primary: {
    50: "#eff6ff",
    100: "#dbeafe", 
    500: "#3b82f6",  // メインブランドカラー
    600: "#2563eb",
    700: "#1d4ed8",
  },
  secondary: {
    50: "#f0fdf4",
    500: "#22c55e",  // 成功・ポジティブ
    600: "#16a34a",
  },
  warning: {
    50: "#fffbeb",
    500: "#f59e0b",   // 注意・改善点
  },
  error: {
    50: "#fef2f2", 
    500: "#ef4444",   // エラー・重要
  },
  gray: {
    50: "#f9fafb",
    100: "#f3f4f6",
    200: "#e5e7eb",
    300: "#d1d5db",
    400: "#9ca3af",
    500: "#6b7280",
    600: "#4b5563",
    700: "#374151",
    800: "#1f2937",
    900: "#111827",
  }
} as const;

// タイポグラフィシステム
export const typography = {
  fontFamily: {
    primary: "'Inter', 'Hiragino Sans', 'Yu Gothic UI', sans-serif",
  },
  fontSize: {
    xs: "0.75rem",    // 12px
    sm: "0.875rem",   // 14px
    base: "1rem",     // 16px
    lg: "1.125rem",   // 18px
    xl: "1.25rem",    // 20px
    "2xl": "1.5rem",  // 24px
    "3xl": "1.875rem", // 30px
  },
  lineHeight: {
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  }
} as const;

// スペーシングシステム
export const spacing = {
  0: "0",
  1: "0.25rem",   // 4px
  2: "0.5rem",    // 8px
  3: "0.75rem",   // 12px
  4: "1rem",      // 16px
  5: "1.25rem",   // 20px
  6: "1.5rem",    // 24px
  8: "2rem",      // 32px
  10: "2.5rem",   // 40px
  12: "3rem",     // 48px
  16: "4rem",     // 64px
  20: "5rem",     // 80px
} as const;

// ブレークポイント
export const breakpoints = {
  sm: "768px",      // タブレット
  md: "1024px",     // デスクトップ小
  lg: "1200px",     // デスクトップ大
  xl: "1440px",     // デスクトップ特大
} as const;

// モーションシステム
export const motion = {
  duration: {
    quick: "150ms",     // ツールチップ、ホバーエフェクト
    moderate: "300ms",  // モーダル表示、アコーディオン
    slow: "500ms",      // 画面全体のトランジション
  },
  easing: {
    natural: "ease-in-out",
    emphasized: "cubic-bezier(0.4, 0, 0.2, 1)",
  }
} as const;

// シャドウシステム
export const shadows = {
  sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
  base: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
  md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
  lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
  xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
} as const;

// 境界線
export const borders = {
  width: {
    0: "0",
    1: "1px", 
    2: "2px",
    4: "4px",
  },
  radius: {
    none: "0",
    sm: "0.125rem",   // 2px
    base: "0.25rem",  // 4px
    md: "0.375rem",   // 6px
    lg: "0.5rem",     // 8px
    xl: "0.75rem",    // 12px
    "2xl": "1rem",    // 16px
    full: "9999px",
  }
} as const;

// プロダクト憲法に基づくトーン
export const tone = {
  positive: {
    primary: colors.secondary[500],
    text: "一緒に成長していきましょう",
    examples: ["ここが素晴らしいポイントです", "次のステップをご提案します"]
  },
  prohibited: {
    words: ["ダメです", "間違っています", "できていません", "合格できません"],
    colors: [colors.error[500]], // 使用禁止色は限定的に
  }
} as const;