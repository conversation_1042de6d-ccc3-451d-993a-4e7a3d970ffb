/**
 * 包括的フィードバックレポートのテストページ
 * 開発・デモ用途
 */
import React, { useState } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Heading, 
  Button, 
  Select, 
  Text,
  Card,
  CardBody,
  CardHeader,
  useColorModeValue 
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// SSRを無効化してハイドレーションエラーを防ぐ
const ComprehensiveFeedbackReport = dynamic(
  () => import('../components/ComprehensiveFeedbackReport'),
  { 
    ssr: false,
    loading: () => <Box p={6}>ロード中...</Box>
  }
);
import {
  mockDataPattern1_NewGradEngineer,
  mockDataPattern2_MidCareerSales,
  mockDataPattern3_CareerChange,
  mockDataPattern4_HighPotentialConsulting,
  mockDataPattern5_Executive,
  ALL_MOCK_PATTERNS
} from '../data/mockEvaluationData';
import { ComprehensiveInterviewReport } from '../types/evaluation';

const TestComprehensiveReport: React.FC = () => {
  const router = useRouter();
  const [selectedPattern, setSelectedPattern] = useState(0);
  const [showReport, setShowReport] = useState(false);

  const patterns = [
    { name: 'パターン1: 新卒・ITエンジニア（優秀層）', data: mockDataPattern1_NewGradEngineer },
    { name: 'パターン2: 中途・営業職（課題あり層）', data: mockDataPattern2_MidCareerSales },
    { name: 'パターン3: 第二新卒・キャリアチェンジ', data: mockDataPattern3_CareerChange },
    { name: 'パターン4: ハイポテンシャル・コンサル志望', data: mockDataPattern4_HighPotentialConsulting },
    { name: 'パターン5: エグゼクティブ・経営層', data: mockDataPattern5_Executive }
  ];

  const handleBackToDashboard = () => {
    router.push('/test-data');
  };

  const handleStartNewPractice = () => {
    router.push('/focused-interview');
  };

  if (showReport) {
    return (
      <ComprehensiveFeedbackReport
        reportData={patterns[selectedPattern].data}
        onClose={() => setShowReport(false)}
        onStartNewPractice={handleStartNewPractice}
      />
    );
  }

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')} py={8}>
      <VStack spacing={8} maxW="4xl" mx="auto" px={6}>
        <Heading size="xl" textAlign="center" color={useColorModeValue('gray.800', 'white')}>
          包括的面接分析レポート - テスト画面
        </Heading>

        <Card w="full" bg={useColorModeValue('white', 'gray.800')}>
          <CardHeader>
            <Heading size="md">レポートパターン選択</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  表示するユーザータイプを選択してください
                </Text>
                <Select 
                  value={selectedPattern} 
                  onChange={(e) => setSelectedPattern(Number(e.target.value))}
                  bg={useColorModeValue('white', 'gray.700')}
                >
                  {patterns.map((pattern, index) => (
                    <option key={index} value={index}>
                      {pattern.name}
                    </option>
                  ))}
                </Select>
              </Box>

              <Box p={4} bg={useColorModeValue('blue.50', 'blue.900')} borderRadius="md">
                <Text fontSize="sm" fontWeight="bold" color="blue.700" mb={2}>
                  選択中のパターン詳細
                </Text>
                <VStack align="start" spacing={1}>
                  <Text fontSize="sm">
                    <strong>候補者:</strong> {patterns[selectedPattern].data.metadata.candidateName}
                  </Text>
                  <Text fontSize="sm">
                    <strong>職種:</strong> {patterns[selectedPattern].data.metadata.position}
                  </Text>
                  <Text fontSize="sm">
                    <strong>総合スコア:</strong> {patterns[selectedPattern].data.overallAssessment.score}点
                  </Text>
                  <Text fontSize="sm">
                    <strong>推薦レベル:</strong> {patterns[selectedPattern].data.overallAssessment.recommendation}
                  </Text>
                </VStack>
              </Box>

              <VStack spacing={4}>
                <Button 
                  colorScheme="blue" 
                  size="lg" 
                  w="full"
                  onClick={() => setShowReport(true)}
                >
                  包括的レポートを表示
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={handleBackToDashboard}
                >
                  テストダッシュボードに戻る
                </Button>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        {/* 機能説明 */}
        <Card w="full" bg={useColorModeValue('green.50', 'green.900')}>
          <CardHeader>
            <Heading size="md" color="green.700">新機能の特徴</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="start" spacing={3}>
              <Text fontSize="sm">
                ✅ <strong>STAR+C評価フレームワーク:</strong> 行動面接の詳細分析
              </Text>
              <Text fontSize="sm">
                ✅ <strong>音声・プロソディ分析:</strong> 話速、フィラーワード、感情分析
              </Text>
              <Text fontSize="sm">
                ✅ <strong>パーソナライズされたアクションプラン:</strong> 段階的な改善計画
              </Text>
              <Text fontSize="sm">
                ✅ <strong>ベンチマーク比較:</strong> 業界・職種・経験レベル別比較
              </Text>
              <Text fontSize="sm">
                ✅ <strong>プログレッシブディスクロージャー:</strong> 段階的な情報開示
              </Text>
              <Text fontSize="sm">
                ✅ <strong>5つのユーザータイプ対応:</strong> 新卒～エグゼクティブまで
              </Text>
            </VStack>
          </CardBody>
        </Card>

        {/* 開発情報 */}
        <Card w="full" bg={useColorModeValue('gray.100', 'gray.700')}>
          <CardHeader>
            <Heading size="sm" color="gray.600">開発者向け情報</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="start" spacing={2}>
              <Text fontSize="xs" color="gray.600">
                📁 コンポーネント: /src/candidate/components/ComprehensiveFeedbackReport.tsx
              </Text>
              <Text fontSize="xs" color="gray.600">
                📁 型定義: /src/candidate/types/evaluation.ts
              </Text>
              <Text fontSize="xs" color="gray.600">
                📁 モックデータ: /src/candidate/data/mockEvaluationData.ts
              </Text>
              <Text fontSize="xs" color="gray.600">
                📁 評価エンジン: /src/candidate/lib/evaluationEngine.ts
              </Text>
              <Text fontSize="xs" color="gray.600">
                🔧 Phase 1実装: モックデータベース、Phase 2でAI統合予定
              </Text>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Box>
  );
};

export default TestComprehensiveReport;