/**
 * 実践的面接練習メインページ
 * シナリオ選択 → 練習実行 → 詳細レポートの流れを管理
 */
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Box } from '@chakra-ui/react';
import { AccessibilityHelper } from '../components/AccessibilityHelper';
import HeaderNavigation, { NavigationItem } from '../components/HeaderNavigation';
import AdvancedPracticeSelector from '../components/AdvancedPracticeSelector';
import AdvancedInterviewFlow from '../components/AdvancedInterviewFlow';
import AdvancedFeedbackReport from '../components/AdvancedFeedbackReport';
import { AdvancedFeedback, getScenarioById } from '../lib/advancedPracticeData';

type PracticeState = 'selection' | 'practicing' | 'report';

const AdvancedPracticePage: React.FC = () => {
  const router = useRouter();
  const [currentState, setCurrentState] = useState<PracticeState>('selection');
  const [selectedScenarioId, setSelectedScenarioId] = useState<string>('');
  const [practiceResults, setPracticeResults] = useState<AdvancedFeedback[]>([]);
  const [user, setUser] = useState<any>(null);

  // ユーザー情報取得
  useEffect(() => {
    const userData = localStorage.getItem('demo_user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role !== 'candidate') {
        router.push('/demo-login');
        return;
      }
      setUser(parsedUser);
    } else {
      router.push('/demo-login');
    }
  }, [router]);

  // URLパラメータから初期状態を設定
  React.useEffect(() => {
    const { scenario } = router.query;
    if (typeof scenario === 'string') {
      setSelectedScenarioId(scenario);
      setCurrentState('practicing');
    }
  }, [router.query]);

  const handleScenarioSelect = (scenarioId: string) => {
    setSelectedScenarioId(scenarioId);
    setCurrentState('practicing');
    
    // URLを更新（ブラウザの戻るボタン対応）
    router.push(`/advanced-practice?scenario=${scenarioId}`, undefined, { shallow: true });
  };

  const handlePracticeComplete = (results: AdvancedFeedback[]) => {
    setPracticeResults(results);
    setCurrentState('report');
    
    // URLを更新
    router.push('/advanced-practice?state=report', undefined, { shallow: true });
  };

  const handleReportClose = () => {
    setCurrentState('selection');
    setSelectedScenarioId('');
    setPracticeResults([]);
    
    // URLをリセット
    router.push('/advanced-practice', undefined, { shallow: true });
  };

  // パンくずリスト生成
  const getBreadcrumbs = (): NavigationItem[] => {
    const breadcrumbs: NavigationItem[] = [
      { label: '実践面接練習', href: '/advanced-practice' }
    ];

    if (currentState === 'practicing' && selectedScenarioId) {
      const scenario = getScenarioById(selectedScenarioId);
      if (scenario) {
        breadcrumbs.push({ 
          label: scenario.name, 
          href: `/advanced-practice?scenario=${selectedScenarioId}`,
          isCurrentPage: true 
        });
      }
    } else if (currentState === 'report') {
      const scenario = getScenarioById(selectedScenarioId);
      if (scenario) {
        breadcrumbs.push({ 
          label: scenario.name, 
          href: `/advanced-practice?scenario=${selectedScenarioId}` 
        });
        breadcrumbs.push({ 
          label: '分析レポート', 
          href: '/advanced-practice?state=report',
          isCurrentPage: true 
        });
      }
    } else {
      breadcrumbs[0].isCurrentPage = true;
    }

    return breadcrumbs;
  };

  // ブラウザの戻るボタン処理
  React.useEffect(() => {
    const handlePopState = () => {
      const { scenario, state } = router.query;
      
      if (state === 'report') {
        setCurrentState('report');
      } else if (typeof scenario === 'string') {
        setCurrentState('practicing');
        setSelectedScenarioId(scenario);
      } else {
        setCurrentState('selection');
        setSelectedScenarioId('');
        setPracticeResults([]);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [router.query]);

  if (!user) {
    return <Box>Loading...</Box>;
  }

  return (
    <Box minH="100vh">
      <AccessibilityHelper />
      <HeaderNavigation
        user={user}
        breadcrumbs={getBreadcrumbs()}
        showBackButton={currentState !== 'selection'}
        backButtonLabel={
          currentState === 'practicing' ? 'シナリオ選択に戻る' :
          currentState === 'report' ? '練習に戻る' : '戻る'
        }
        onBackClick={() => {
          if (currentState === 'practicing') {
            setCurrentState('selection');
            setSelectedScenarioId('');
            router.push('/advanced-practice', undefined, { shallow: true });
          } else if (currentState === 'report') {
            setCurrentState('practicing');
            router.push(`/advanced-practice?scenario=${selectedScenarioId}`, undefined, { shallow: true });
          }
        }}
      />
      
      {currentState === 'selection' && (
        <AdvancedPracticeSelector
          onScenarioSelect={handleScenarioSelect}
          userExperience="mid" // 実際の実装では、ユーザープロファイルから取得
          targetIndustry="tech" // 実際の実装では、ユーザー設定から取得
        />
      )}

      {currentState === 'practicing' && selectedScenarioId && (
        <AdvancedInterviewFlow
          scenarioId={selectedScenarioId}
          onComplete={handlePracticeComplete}
        />
      )}

      {currentState === 'report' && practiceResults.length > 0 && (
        <AdvancedFeedbackReport
          feedbacks={practiceResults}
          scenarioId={selectedScenarioId}
          onClose={handleReportClose}
        />
      )}
    </Box>
  );
};

export default AdvancedPracticePage;