import React from 'react';
import { Box, Container, VStack, Heading, Text } from '@chakra-ui/react';
import { AutoSpeechInput } from '../components/AutoSpeechInput';

const TestSpeechPage: React.FC = () => {
  const handleTranscriptionComplete = (text: string) => {
    console.log('Transcription completed:', text);
    alert(`Transcription: ${text}`);
  };

  const handleError = (error: string) => {
    console.error('Speech error:', error);
    alert(`Error: ${error}`);
  };

  return (
    <Container maxW="container.md" py={8}>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center">
          <Heading size="lg" mb={4}>
            Speech-to-Text Test Page
          </Heading>
          <Text color="gray.600">
            This page is for testing the speech-to-text functionality.
            Open browser console to see detailed logs.
          </Text>
        </Box>

        <AutoSpeechInput
          questionId="test-question-123"
          onTranscriptionComplete={handleTranscriptionComplete}
          onError={handleError}
          autoStart={true}
          placeholder="Start speaking to test speech recognition..."
        />

        <Box p={4} bg="blue.50" borderRadius="md">
          <Text fontSize="sm" color="blue.700">
            <strong>Instructions:</strong>
            <br />
            1. Allow microphone access when prompted
            <br />
            2. Start speaking - the system will automatically detect your voice
            <br />
            3. Stop speaking for 4 seconds to auto-submit
            <br />
            4. Check browser console for detailed logs
            <br />
            5. Check network tab for WebSocket messages
          </Text>
        </Box>

        <Box p={4} bg="yellow.50" borderRadius="md">
          <Text fontSize="sm" color="yellow.700">
            <strong>Troubleshooting:</strong>
            <br />
            • Make sure microphone is connected and working
            <br />
            • Check browser permissions for microphone access
            <br />
            • Ensure Azure Speech Service credentials are configured
            <br />
            • Check WebSocket connection to backend
            <br />
            • Look for errors in browser console
          </Text>
        </Box>
      </VStack>
    </Container>
  );
};

export default TestSpeechPage;
