import React, { useState } from 'react';
import { <PERSON>, Container, VStack, Heading, Text, Tabs, Tab<PERSON>ist, TabPanels, Tab, TabPanel, Divider } from '@chakra-ui/react';
import { AutoSpeechInput } from '../components/AutoSpeechInput';
import { WebSpeechTest } from '../components/WebSpeechTest';

const TestSpeechPage: React.FC = () => {
  const [webSpeechResults, setWebSpeechResults] = useState<string[]>([]);

  const handleTranscriptionComplete = (text: string) => {
    console.log('AutoSpeechInput transcription completed:', text);
    alert(`AutoSpeechInput Transcription: ${text}`);
  };

  const handleError = (error: string) => {
    console.error('AutoSpeechInput error:', error);
    alert(`AutoSpeechInput Error: ${error}`);
  };

  const handleWebSpeechResult = (text: string) => {
    console.log('WebSpeechTest result:', text);
    setWebSpeechResults(prev => [...prev, text]);
  };

  return (
    <Container maxW="container.lg" py={8}>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center">
          <Heading size="lg" mb={4}>
            Speech-to-Text Test Page
          </Heading>
          <Text color="gray.600">
            This page is for testing the speech-to-text functionality.
            Open browser console to see detailed logs.
          </Text>
        </Box>

        <Tabs variant="enclosed" colorScheme="blue">
          <TabList>
            <Tab>AutoSpeechInput (Full Integration)</Tab>
            <Tab>Web Speech API (Direct Test)</Tab>
          </TabList>

          <TabPanels>
            <TabPanel>
              <VStack spacing={4} align="stretch">
                <AutoSpeechInput
                  questionId="test-question-123"
                  onTranscriptionComplete={handleTranscriptionComplete}
                  onError={handleError}
                  autoStart={true}
                  placeholder="Start speaking to test speech recognition..."
                />
              </VStack>
            </TabPanel>

            <TabPanel>
              <VStack spacing={4} align="stretch">
                <WebSpeechTest onResult={handleWebSpeechResult} />

                {webSpeechResults.length > 0 && (
                  <>
                    <Divider />
                    <Box>
                      <Text fontWeight="bold" mb={2}>Web Speech Results History:</Text>
                      {webSpeechResults.map((result, index) => (
                        <Box key={index} p={2} bg="gray.100" borderRadius="md" mb={2}>
                          <Text fontSize="sm" color="gray.600">Result {index + 1}:</Text>
                          <Text>{result}</Text>
                        </Box>
                      ))}
                    </Box>
                  </>
                )}
              </VStack>
            </TabPanel>
          </TabPanels>
        </Tabs>

        <Box p={4} bg="blue.50" borderRadius="md">
          <Text fontSize="sm" color="blue.700">
            <strong>Instructions:</strong>
            <br />
            1. Allow microphone access when prompted
            <br />
            2. Start speaking - the system will automatically detect your voice
            <br />
            3. Stop speaking for 4 seconds to auto-submit
            <br />
            4. Check browser console for detailed logs
            <br />
            5. Check network tab for WebSocket messages
            <br />
            <br />
            <strong>Note:</strong> The system will try Azure Speech Service first,
            then fallback to browser's Web Speech API if Azure fails.
          </Text>
        </Box>

        <Box p={4} bg="yellow.50" borderRadius="md">
          <Text fontSize="sm" color="yellow.700">
            <strong>Troubleshooting:</strong>
            <br />
            • Make sure microphone is connected and working
            <br />
            • Check browser permissions for microphone access
            <br />
            • If Azure fails, Web Speech API will be used automatically
            <br />
            • Web Speech API works in Chrome, Edge, Safari (limited support in Firefox)
            <br />
            • Check WebSocket connection to backend for sending results
            <br />
            • Look for errors in browser console
            <br />
            <br />
            <strong>Supported Languages:</strong>
            <br />
            • Vietnamese (vi-VN) - primary
            <br />
            • English (en-US) - fallback
            <br />
            • Japanese (ja-JP) - fallback
          </Text>
        </Box>
      </VStack>
    </Container>
  );
};

export default TestSpeechPage;
