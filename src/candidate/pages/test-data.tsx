/**
 * エージェントリンク面接ページプロトタイプ
 * 実際のリンクページのプレビュー機能
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Container,
  Heading,
  Badge,
  SimpleGrid,
  useColorModeValue,
  Divider,
  List,
  ListItem,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  Progress,
  useToast,
  Icon,
} from '@chakra-ui/react';
import HeaderNavigation from '../components/HeaderNavigation';
import { mockInterviewLinks } from '../lib/agentLinkData';
import { getLinkStatusMessage, processLinkAccess } from '../lib/linkIntegration';
import DataService from '../lib/unified-data';
import { TimeIcon, CheckCircleIcon, WarningIcon } from '@chakra-ui/icons';
import { generateAdvancedFeedback } from '../lib/advancedPracticeData';

const InterviewLinkPrototype: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const toast = useToast();
  
  const [practiceResults, setPracticeResults] = useState<any[]>([]);
  const [isExpired, setIsExpired] = useState(false);
  

  // シナリオIDを読みやすい名前に変換（統一データシステムを使用）
  const getScenarioName = (scenarioId: string): string => {
    return DataService.getScenarioDisplayName(scenarioId);
  };

  // 仮ユーザー（テスト用）
  const testUser = {
    profile: { name: 'テストユーザー' }
  };

  const breadcrumbs: any[] = [];

  // 期限チェック（2週間）
  useEffect(() => {
    const twoWeeksFromNow = new Date();
    twoWeeksFromNow.setDate(twoWeeksFromNow.getDate() + 14);
    
    const checkExpiration = () => {
      const now = new Date();
      if (now > twoWeeksFromNow) {
        setIsExpired(true);
      }
    };
    
    const interval = setInterval(checkExpiration, 1000 * 60 * 60); // 1時間ごとにチェック
    return () => clearInterval(interval);
  }, []);

  // 面接準備ページに遷移
  const handleStartPractice = (linkId: string, scenarioId: string) => {
    const link = mockInterviewLinks.find(l => l.id === linkId);
    if (!link) return;

    // 面接準備ページに遷移
    window.location.href = `/interview-prep?linkId=${linkId}&scenarioId=${scenarioId}`;
  };


  return (
    <Box minH="100vh" bg={bgColor}>
      <HeaderNavigation
        user={testUser}
        breadcrumbs={breadcrumbs}
        showBackButton={false}
      />
      
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} id="main-content" tabIndex={-1}>
          {/* ヘッダー */}
          <VStack spacing={3} textAlign="center">
            <Heading size="lg" color={useColorModeValue('primary.700', 'primary.300')}>
              エージェント面接練習
            </Heading>
            <Text color={useColorModeValue('gray.600', 'gray.300')}>
              あなた専用の面接練習が用意されています
            </Text>
          </VStack>

          {/* 期限警告 */}
          {!isExpired ? (
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Text fontWeight="bold">このページは2週間後に自動で無効になります</Text>
                <HStack spacing={2}>
                  <Icon as={TimeIcon} />
                  <Text fontSize="sm">
                    期限: {new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toLocaleDateString('ja-JP')}
                  </Text>
                </HStack>
              </VStack>
            </Alert>
          ) : (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <AlertDescription>
                このページは期限切れです。面接練習を行うことはできません。
              </AlertDescription>
            </Alert>
          )}

          {/* 面接リンク一覧 */}
          <VStack spacing={6} w="full">
            {mockInterviewLinks.map((link, index) => {
              const statusInfo = getLinkStatusMessage(link);
              const linkResults = practiceResults.filter(r => r.linkId === link.id);
              
              return (
                <Card key={index} w="full" bg={cardBg} shadow="lg">
                  <CardBody>
                    <VStack spacing={6}>
                      {/* 企業情報ヘッダー */}
                      <HStack justify="space-between" w="full">
                        <VStack align="start" spacing={2}>
                          <Heading size="lg" color={useColorModeValue('primary.700', 'primary.300')}>
                            {link.companyInfo.name}
                          </Heading>
                          <HStack spacing={3} flexWrap="wrap">
                            <Badge colorScheme="blue" fontSize="sm">{link.companyInfo.industry}</Badge>
                            <Badge colorScheme="green" fontSize="sm">{link.companyInfo.position}</Badge>
                            <Badge colorScheme={statusInfo.color} fontSize="sm">
                              {statusInfo.message}
                            </Badge>
                          </HStack>
                        </VStack>
                      </HStack>

                      <Divider />

                      {/* 企業詳細情報 */}
                      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} w="full">
                        <VStack align="start" spacing={4}>
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業概要</Text>
                            <VStack align="start" spacing={2} fontSize="sm">
                              <Text><strong>所在地:</strong> {link.companyInfo.location}</Text>
                              <Text><strong>従業員数:</strong> {link.companyInfo.employeeCount}</Text>
                              <Text><strong>設立:</strong> {link.companyInfo.founded}</Text>
                            </VStack>
                          </Box>
                          
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業説明</Text>
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.300')}>
                              {link.companyInfo.description}
                            </Text>
                          </Box>
                          
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>面接スタイル</Text>
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.300')}>
                              {link.companyInfo.interviewStyle}
                            </Text>
                          </Box>
                        </VStack>

                        <VStack align="start" spacing={4}>
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業文化</Text>
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.300')}>
                              {link.companyInfo.culture}
                            </Text>
                          </Box>
                          
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={3}>🎯 推奨練習シナリオ</Text>
                            <VStack align="start" spacing={3}>
                              {link.recommendedScenarios.map((scenarioId, i) => (
                                <Box key={i} w="full">
                                  <VStack align="start" spacing={2}>
                                    <Text fontSize="sm" fontWeight="medium">
                                      {getScenarioName(scenarioId)}
                                    </Text>
                                    <Button
                                      size="md"
                                      colorScheme="primary"
                                      variant="solid"
                                      isDisabled={isExpired}
                                      onClick={() => handleStartPractice(link.id, scenarioId)}
                                      w="full"
                                      _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                                      _active={{ transform: 'translateY(0)' }}
                                      leftIcon={<Icon as={CheckCircleIcon} />}
                                    >
                                      この面接を練習する
                                    </Button>
                                  </VStack>
                                </Box>
                              ))}
                            </VStack>
                          </Box>
                        </VStack>
                      </SimpleGrid>

                      {/* エージェントからのアドバイス */}
                      <Box w="full">
                        <Text fontSize="md" fontWeight="bold" mb={3}>
                          📝 エージェントからのアドバイス
                        </Text>
                        <Card variant="outline" bg={useColorModeValue('blue.50', 'blue.900')}>
                          <CardBody>
                            <VStack spacing={4}>
                              <Box w="full">
                                <Text fontSize="sm" fontWeight="bold" mb={2}>面接攻略メモ:</Text>
                                <Text fontSize="sm" whiteSpace="pre-line">
                                  {link.agentNotes}
                                </Text>
                              </Box>
                              
                              <Divider />
                              
                              <Box w="full">
                                <Text fontSize="sm" fontWeight="bold" mb={2}>練習推奨:</Text>
                                <Text fontSize="sm">
                                  {link.agentRecommendation}
                                </Text>
                              </Box>
                            </VStack>
                          </CardBody>
                        </Card>
                      </Box>

                      {/* 練習結果表示 */}
                      {linkResults.length > 0 && (
                        <Box w="full">
                          <Text fontSize="md" fontWeight="bold" mb={3}>
                            📊 練習結果 ({linkResults.length}回)
                          </Text>
                          <VStack spacing={3}>
                            {linkResults.map((result, i) => (
                              <Card key={i} variant="outline" w="full">
                                <CardBody>
                                  <HStack justify="space-between">
                                    <VStack align="start" spacing={1}>
                                      <Text fontSize="sm" fontWeight="bold">
                                        {getScenarioName(result.scenarioId)} - {result.practiceDate}
                                      </Text>
                                      <HStack spacing={2}>
                                        <Badge colorScheme="green">
                                          総合スコア: {result.feedback.communicationSkill}/10
                                        </Badge>
                                        <Badge colorScheme="blue">
                                          コミュニケーション: {result.feedback.communicationSkill}/10
                                        </Badge>
                                      </HStack>
                                    </VStack>
                                    <Button 
                                      size="sm" 
                                      colorScheme="primary"
                                      variant="outline"
                                      onClick={() => {
                                        // 結果データをセッションストレージに保存
                                        sessionStorage.setItem('interviewResult', JSON.stringify({
                                          feedbacks: [result.feedback],
                                          resultData: result
                                        }));
                                        // 結果ページに遷移
                                        window.location.href = `/interview-result?linkId=${result.linkId}&scenarioId=${result.scenarioId}`;
                                      }}
                                    >
                                      詳細分析を見る
                                    </Button>
                                  </HStack>
                                </CardBody>
                              </Card>
                            ))}
                          </VStack>
                        </Box>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              );
            })}
          </VStack>

          {/* 新機能テストセクション */}
          <Card w="full" bg={useColorModeValue('purple.50', 'purple.900')} borderWidth={2} borderColor="purple.200">
            <CardHeader>
              <HStack spacing={2}>
                <Icon as={CheckCircleIcon} color="purple.600" />
                <Heading size="md" color="purple.700">🚀 新機能: 包括的面接分析レポート</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="start">
                <Text fontSize="sm" color="purple.700">
                  STAR+C評価フレームワークとAI分析を組み合わせた次世代の面接分析レポートをテストできます。
                </Text>
                <HStack spacing={4}>
                  <Button
                    colorScheme="purple"
                    size="md"
                    onClick={() => window.location.href = '/test-comprehensive-report'}
                  >
                    包括的レポートをテスト
                  </Button>
                  <Badge colorScheme="purple" fontSize="xs">Phase 1実装</Badge>
                </HStack>
                <Text fontSize="xs" color="purple.600">
                  ✨ 5つのユーザータイプパターン、音声分析、パーソナライズされたアクションプランなどを体験
                </Text>
              </VStack>
            </CardBody>
          </Card>

        </VStack>
      </Container>
    </Box>
  );
};

export default InterviewLinkPrototype;