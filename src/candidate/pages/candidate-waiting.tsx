/**
 * 候補者待機画面
 * エージェントからのミーティングリンクを待つ画面
 */

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  Alert,
  AlertIcon,
  Badge,
  Card,
  CardBody,
  useToast,
  useColorModeValue,
  Spinner,
  Input,
  FormControl,
  FormLabel,
  Divider
} from '@chakra-ui/react';
import { TimeIcon, LinkIcon, CheckCircleIcon, InfoIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, slideInFromBottom } from '../motion';

const MotionBox = motion(Box);
const MotionVStack = motion(VStack);

interface CandidateUser {
  profile: {
    name: string;
    email: string;
  };
  role: string;
}

export default function CandidateWaitingPage() {
  const router = useRouter();
  const toast = useToast();
  const [user, setUser] = useState<CandidateUser | null>(null);
  const [directToken, setDirectToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // カラーモード対応
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const infoColor = useColorModeValue('blue.50', 'blue.900');

  /**
   * ユーザー情報の読み込み
   */
  useEffect(() => {
    const userData = localStorage.getItem('demo_user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role === 'candidate') {
        setUser(parsedUser);
      } else {
        // 候補者以外は適切な画面にリダイレクト
        router.push('/demo-login');
      }
    } else {
      router.push('/demo-login');
    }
  }, [router]);

  /**
   * 直接トークン入力での面接開始
   */
  const handleDirectTokenAccess = async () => {
    if (!directToken.trim()) {
      toast({
        title: 'エラー',
        description: 'ミーティングトークンを入力してください',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);

    try {
      // トークンの形式を簡易チェック
      if (directToken.length < 8) {
        throw new Error('無効なトークンです');
      }

      // 面接ページにリダイレクト
      router.push(`/interview-token?token=${directToken}`);
    } catch (error) {
      toast({
        title: 'エラー',
        description: '無効なミーティングトークンです',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * ログアウト処理
   */
  const handleLogout = () => {
    localStorage.removeItem('demo_user');
    router.push('/demo-login');
  };

  /**
   * 練習モードへの移動
   */
  const handlePracticeMode = () => {
    router.push('/candidate-dashboard');
  };

  if (!user) {
    return (
      <Box bg={bgColor} minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <Spinner size="xl" color="blue.500" />
      </Box>
    );
  }

  return (
    <Box bg={bgColor} minH="100vh" py={spacing.sectionSpacing}>
      <Container maxW="container.md">
        <MotionVStack {...fadeIn} spacing={spacing.sectionSpacing} align="stretch">
          
          {/* ヘッダー */}
          <VStack spacing={4} textAlign="center">
            <Badge colorScheme="blue" fontSize="md" px={4} py={2}>
              候補者待機中
            </Badge>
            
            <Heading as="h1" size="2xl" color="blue.600">
              こんにちは、{user.profile.name}様
            </Heading>
            
            <Text {...textStyles.body} color="gray.600" maxW="2xl">
              エージェント様からのミーティングリンクをお待ちください。
              リンクが届き次第、面接を開始いただけます。
            </Text>
          </VStack>

          {/* メイン待機カード */}
          <MotionBox 
            variants={slideInFromBottom}
            initial="initial"
            animate="animate"
            custom={{ delay: 0.2 }}
            exit="exit"
          >
            <Card bg={cardBg} borderColor={borderColor} boxShadow="lg">
              <CardBody p={spacing.cardPadding}>
                <VStack spacing={spacing.cardPadding} align="stretch">
                  
                  <HStack justify="center" spacing={4}>
                    <LinkIcon boxSize={8} color="blue.500" />
                    <Heading as="h2" size="lg" color="blue.600">
                      面接リンク待機中
                    </Heading>
                  </HStack>

                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="bold">
                        エージェント様からのご連絡をお待ちください
                      </Text>
                      <Text fontSize="sm">
                        メールまたはメッセージでミーティングリンクが送信されます
                      </Text>
                    </VStack>
                  </Alert>

                  <VStack spacing={4} align="stretch">
                    <Text {...textStyles.label} textAlign="center">
                      面接の流れ
                    </Text>
                    
                    <VStack spacing={3} align="stretch">
                      <HStack spacing={3}>
                        <Badge colorScheme="blue" variant="solid" borderRadius="full" w={6} h={6} display="flex" alignItems="center" justifyContent="center">1</Badge>
                        <Text fontSize="sm">エージェントがミーティングリンクを発行</Text>
                        <CheckCircleIcon color="green.500" />
                      </HStack>
                      
                      <HStack spacing={3}>
                        <Badge colorScheme="blue" variant="solid" borderRadius="full" w={6} h={6} display="flex" alignItems="center" justifyContent="center">2</Badge>
                        <Text fontSize="sm">メール/メッセージでリンクを受信</Text>
                        <TimeIcon color="yellow.500" />
                      </HStack>
                      
                      <HStack spacing={3}>
                        <Badge colorScheme="gray" variant="solid" borderRadius="full" w={6} h={6} display="flex" alignItems="center" justifyContent="center">3</Badge>
                        <Text fontSize="sm" color="gray.500">リンクをクリックして面接開始</Text>
                      </HStack>
                    </VStack>
                  </VStack>

                </VStack>
              </CardBody>
            </Card>
          </MotionBox>

          {/* 直接アクセス */}
          <MotionBox 
            variants={slideInFromBottom}
            initial="initial"
            animate="animate"
            custom={{ delay: 0.4 }}
            exit="exit"
          >
            <Card bg={infoColor} borderColor="blue.200">
              <CardBody p={spacing.cardPadding}>
                <VStack spacing={4} align="stretch">
                  
                  <HStack justify="center" spacing={3}>
                    <InfoIcon color="blue.600" />
                    <Text {...textStyles.subheading} color="blue.700">
                      ミーティングトークンを直接入力
                    </Text>
                  </HStack>

                  <Text fontSize="sm" color="blue.600" textAlign="center">
                    既にトークンをお持ちの場合は、こちらに入力してください
                  </Text>

                  <FormControl>
                    <FormLabel {...textStyles.label} color="blue.700">
                      ミーティングトークン
                    </FormLabel>
                    <Input
                      value={directToken}
                      onChange={(e) => setDirectToken(e.target.value)}
                      placeholder="例: mtg_token_abc123..."
                      bg="white"
                      borderColor="blue.300"
                      _focus={{ borderColor: 'blue.500' }}
                    />
                  </FormControl>

                  <Button
                    colorScheme="blue"
                    onClick={handleDirectTokenAccess}
                    isLoading={isLoading}
                    loadingText="接続中..."
                    size="lg"
                    isDisabled={!directToken.trim()}
                  >
                    面接を開始する
                  </Button>

                </VStack>
              </CardBody>
            </Card>
          </MotionBox>

          {/* アクションボタン */}
          <MotionBox 
            variants={slideInFromBottom}
            initial="initial"
            animate="animate"
            custom={{ delay: 0.6 }}
            exit="exit"
          >
            <VStack spacing={4}>
              
              <Divider />
              
              <Text {...textStyles.caption} color="gray.500" textAlign="center">
                面接の準備として、事前練習も可能です
              </Text>

              <HStack spacing={4} justify="center">
                <Button
                  variant="outline"
                  colorScheme="blue"
                  onClick={handlePracticeMode}
                >
                  練習モードで準備
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLogout}
                >
                  ログアウト
                </Button>
              </HStack>

            </VStack>
          </MotionBox>

        </MotionVStack>
      </Container>
    </Box>
  );
}