/**
 * 求職者向けダッシュボード
 * 心理的安全性とパーソナライゼーションを重視
 * 
 * 【重要】このページは候補者が直接練習する際のメニュー画面です
 * - 練習モード経由: /candidate-waiting → ここ → /interview-preparation
 * - エージェント経由: /test-data → /interview-prep（別フロー）
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Progress,
  Badge,
  Icon,
  Avatar,
  Heading,
  Container,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useToast,
  useColorModeValue,
  Divider,
  List,
  ListItem,
  ListIcon,
} from '@chakra-ui/react';
import { 
  StarIcon, 
  TimeIcon, 
  CheckCircleIcon,
  ArrowForwardIcon,
} from '@chakra-ui/icons';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { TEST_ACCOUNTS } from '../lib/testAccounts';
import { AccessibilityHelper } from '../components/AccessibilityHelper';

const MotionCard = motion(Card);

const CandidateDashboard: React.FC = () => {
  const router = useRouter();
  const toast = useToast();
  const [user, setUser] = useState<any>(null);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    // ユーザー情報の取得
    const userData = localStorage.getItem('demo_user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role !== 'candidate') {
        router.push('/demo-login');
        return;
      }
      setUser(parsedUser);
    } else {
      router.push('/demo-login');
    }

    // 時間に応じた挨拶
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('おはようございます');
    } else if (hour < 18) {
      setGreeting('こんにちは');
    } else {
      setGreeting('こんばんは');
    }
  }, [router]);

  const handleStartPractice = (type: string) => {
    // 面接練習開始前の準備画面へ
    router.push(`/interview-preparation?type=${type}`);
  };

  const handleAdvancedPractice = () => {
    // 実践的な面接練習画面へ
    router.push('/advanced-practice');
  };

  const handleViewProgress = () => {
    router.push('/my-progress');
  };

  if (!user) {
    return <Box>Loading...</Box>;
  }

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
      {/* アクセシビリティヘルパー */}
      <AccessibilityHelper />
      
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch" id="main-content" tabIndex={-1}>
          {/* ウェルカムヘッダー */}
          <Box bg={useColorModeValue('white', 'gray.800')} p={{ base: 4, md: 6 }} borderRadius="lg" boxShadow="sm">
            <VStack align="stretch" spacing={4}>
              <HStack justify="space-between" align="start">
                <HStack spacing={{ base: 3, md: 4 }} align="start">
                  <Avatar 
                    name={user.profile.name} 
                    size={{ base: "sm", md: "md" }} 
                    bg="primary.500" 
                  />
                  <VStack align="start" spacing={0} flex={1}>
                    <Text 
                      fontSize={{ base: "lg", md: "2xl" }} 
                      fontWeight="bold" 
                      color={useColorModeValue('gray.800', 'gray.100')}
                    >
                      {greeting}、{user.profile.name}さん
                    </Text>
                    <Text 
                      color={useColorModeValue('gray.600', 'gray.400')}
                      fontSize={{ base: "sm", md: "md" }}
                    >
                      今日も一歩ずつ、確実に成長していきましょう！
                    </Text>
                  </VStack>
                </HStack>
                <Button
                  variant="ghost"
                  size={{ base: "sm", md: "md" }}
                  onClick={() => {
                    localStorage.removeItem('demo_user');
                    router.push('/demo-login');
                  }}
                >
                  ログアウト
                </Button>
              </HStack>
            </VStack>
          </Box>

          {/* モチベーションメッセージ */}
          <MotionCard
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            bg={useColorModeValue('primary.50', 'gray.800')}
            borderColor={useColorModeValue('primary.200', 'primary.700')}
            borderWidth={1}
          >
            <CardBody>
              <HStack spacing={3}>
                <Icon as={StarIcon} color="primary.500" boxSize={6} />
                <VStack align="start" spacing={1} flex={1}>
                  <Text fontWeight="bold" color={useColorModeValue('primary.700', 'primary.300')}>
                    今日のモチベーション
                  </Text>
                  <Text fontSize="sm" color={useColorModeValue('primary.600', 'primary.400')}>
                    「完璧を目指さなくていい。昨日の自分より1%でも成長できれば、それは大きな一歩です。」
                  </Text>
                </VStack>
              </HStack>
            </CardBody>
          </MotionCard>

          {/* 進捗サマリー */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={{ base: 4, md: 6 }}>
            <Stat bg={useColorModeValue('white', 'gray.800')} p={{ base: 4, md: 6 }} borderRadius="lg" boxShadow="sm">
              <StatLabel color={useColorModeValue('gray.600', 'gray.400')}>練習回数</StatLabel>
              <StatNumber fontSize={{ base: "2xl", md: "3xl" }} color="primary.600">0回</StatNumber>
              <StatHelpText>
                まだ練習を始めていません
              </StatHelpText>
            </Stat>

            <Stat bg={useColorModeValue('white', 'gray.800')} p={{ base: 4, md: 6 }} borderRadius="lg" boxShadow="sm">
              <StatLabel color={useColorModeValue('gray.600', 'gray.400')}>成長スコア</StatLabel>
              <StatNumber fontSize={{ base: "2xl", md: "3xl" }} color="green.600">-</StatNumber>
              <StatHelpText>
                練習を始めると表示されます
              </StatHelpText>
            </Stat>

            <Stat bg={useColorModeValue('white', 'gray.800')} p={{ base: 4, md: 6 }} borderRadius="lg" boxShadow="sm">
              <StatLabel color={useColorModeValue('gray.600', 'gray.400')}>次の目標</StatLabel>
              <StatNumber fontSize={{ base: "lg", md: "xl" }} color="orange.600">初回練習</StatNumber>
              <StatHelpText>
                まずは気軽に始めてみましょう
              </StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* 練習メニュー */}
          <VStack align="start" spacing={4}>
            <Heading size="md" color="gray.800">
              練習を始める
            </Heading>
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={{ base: 4, md: 6 }} w="full">
              {/* 基本練習カード */}
              <MotionCard
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
                cursor="pointer"
                onClick={() => handleStartPractice('basic')}
              >
                <CardHeader>
                  <HStack justify="space-between">
                    <VStack align="start" spacing={2}>
                      <Badge colorScheme="green">初心者におすすめ</Badge>
                      <Heading size="md">基本的な面接練習</Heading>
                    </VStack>
                    <Icon as={StarIcon} color="yellow.400" boxSize={8} />
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack align="start" spacing={3}>
                    <Text color="gray.600">
                      自己紹介や志望動機など、基本的な質問から始めます
                    </Text>
                    <List spacing={2}>
                      <ListItem fontSize="sm">
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        約15分で完了
                      </ListItem>
                      <ListItem fontSize="sm">
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        優しいフィードバック
                      </ListItem>
                      <ListItem fontSize="sm">
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        緊張をほぐす工夫あり
                      </ListItem>
                    </List>
                    <Button
                      colorScheme="primary"
                      rightIcon={<ArrowForwardIcon />}
                      w="full"
                    >
                      この練習を始める
                    </Button>
                  </VStack>
                </CardBody>
              </MotionCard>

              {/* 実践練習カード */}
              <MotionCard
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
                cursor="pointer"
                onClick={handleAdvancedPractice}
                bg={useColorModeValue('white', 'gray.800')}
                borderWidth={2}
                borderColor={useColorModeValue('purple.200', 'purple.600')}
                _hover={{
                  borderColor: useColorModeValue('purple.400', 'purple.400'),
                  boxShadow: 'lg'
                }}
              >
                <CardHeader>
                  <HStack justify="space-between">
                    <VStack align="start" spacing={2}>
                      <Badge colorScheme="purple">上級者向け</Badge>
                      <Heading size="md" color={useColorModeValue('gray.800', 'gray.100')}>
                        実践的な面接練習
                      </Heading>
                    </VStack>
                    <Icon as={TimeIcon} color="purple.400" boxSize={8} />
                  </HStack>
                </CardHeader>
                <CardBody>
                  <VStack align="start" spacing={3}>
                    <Text color={useColorModeValue('gray.600', 'gray.300')}>
                      業界特化型の高度な面接シミュレーション
                    </Text>
                    <List spacing={2}>
                      <ListItem fontSize="sm" color={useColorModeValue('gray.700', 'gray.200')}>
                        <ListIcon as={CheckCircleIcon} color="purple.500" />
                        業界別シナリオ選択
                      </ListItem>
                      <ListItem fontSize="sm" color={useColorModeValue('gray.700', 'gray.200')}>
                        <ListIcon as={CheckCircleIcon} color="purple.500" />
                        高度な分析レポート
                      </ListItem>
                      <ListItem fontSize="sm" color={useColorModeValue('gray.700', 'gray.200')}>
                        <ListIcon as={CheckCircleIcon} color="purple.500" />
                        プレッシャー面接対応
                      </ListItem>
                    </List>
                    <Button
                      colorScheme="purple"
                      rightIcon={<ArrowForwardIcon />}
                      w="full"
                      variant="solid"
                    >
                      実践練習を始める
                    </Button>
                  </VStack>
                </CardBody>
              </MotionCard>
            </SimpleGrid>
          </VStack>

          {/* 学習リソース */}
          <VStack align="start" spacing={4}>
            <Heading size="md" color="gray.800">
              面接のコツ
            </Heading>
            <Card w="full">
              <CardBody>
                <VStack align="start" spacing={3}>
                  <Text fontWeight="bold" color="primary.600">
                    💡 今日のワンポイントアドバイス
                  </Text>
                  <Text color="gray.700">
                    面接では「完璧な回答」よりも「あなたらしい回答」が大切です。
                    自分の経験や想いを、自分の言葉で伝えることを心がけましょう。
                  </Text>
                  <Divider />
                  <HStack spacing={4}>
                    <Button size="sm" variant="outline">
                      面接のコツを見る
                    </Button>
                    <Button size="sm" variant="outline">
                      よくある質問
                    </Button>
                  </HStack>
                </VStack>
              </CardBody>
            </Card>
          </VStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default CandidateDashboard;