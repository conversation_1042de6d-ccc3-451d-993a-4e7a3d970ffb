import Document, { Html, Head, Main, NextScript } from 'next/document';
import { ColorModeScript } from '@chakra-ui/react';
import { mensetsukuTheme } from '../theme';

export default class MyDocument extends Document {
  render() {
    return (
      <Html lang="ja">
        <Head>
          {/* モバイル最適化 */}
          <meta name="theme-color" content="#2563EB" />
          <meta name="apple-mobile-web-app-capable" content="yes" />
          <meta name="apple-mobile-web-app-status-bar-style" content="default" />
          <meta name="apple-mobile-web-app-title" content="面接君" />
          
          {/* アクセシビリティ対応 */}
          <meta name="description" content="AI面接官による面接練習アプリケーション。音声認識とフィードバック機能付き。" />
          <meta name="keywords" content="面接練習,AI,音声認識,フィードバック,就職活動" />
          
          {/* フォント最適化 */}
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
          <link 
            href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;600;700&display=swap" 
            rel="stylesheet" 
          />
          
          {/* Speech SDK 本体 */}
          <script src="https://aka.ms/csspeech/jsbrowserpackageraw"></script>
          
          {/* アクセシビリティ対応CSS */}
          <link rel="stylesheet" href="/styles/accessibility.css?v=5" />
        </Head>
        <body>
          {/* ColorModeScript をMainより前に配置してちらつきを防ぐ */}
          <ColorModeScript initialColorMode={mensetsukuTheme.config.initialColorMode} />
          
          
          <Main />
          <NextScript />
          
          {/* ライブリージョンの初期化 */}
          <div id="live-announcer" aria-live="polite" aria-atomic="true" className="sr-only"></div>
        </body>
      </Html>
    );
  }
} 