/**
 * 面接準備画面 - エージェントリンク用
 * 企業情報とエージェントアドバイスを確認してから面接に進む
 * 
 * 【重要】このページはエージェント経由のフロー専用です
 * フロー: /test-data → ここ → /interview-room
 * 
 * 直接練習フローは /interview-preparation を使用します
 */
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Progress,
  Container,
  Alert,
  AlertIcon,
  AlertDescription,
  Checkbox,
  Card,
  CardBody,
  CardHeader,
  useToast,
  useColorModeValue,
  Heading,
  Badge,
  Divider,
  SimpleGrid,
  List,
  ListItem,
  ListIcon,
  Input,
  FormControl,
  FormLabel,
} from '@chakra-ui/react';
import {
  CheckCircleIcon,
  ArrowForwardIcon,
  ArrowBackIcon,
  InfoIcon,
} from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import HeaderNavigation from '../components/HeaderNavigation';
import { mockInterviewLinks } from '../lib/agentLinkData';
import DataService from '../lib/unified-data';
import type { 
  InterviewData, 
  BreadcrumbItem, 
  InterviewLink, 
  LinkValidationResult 
} from '../types/interview-prep';

const MotionBox = motion.create(Box);

interface ChecklistItem {
  id: string;
  label: string;
  description: string;
  isChecked: boolean;
  isRequired: boolean;
}

const InterviewPrep: React.FC = () => {
  const router = useRouter();
  const { linkId, scenarioId, status } = router.query;
  const toast = useToast();
  
  // カラーモード値を先頭で定義（Hooks順序を固定）
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const grayText = useColorModeValue('gray.600', 'gray.400');
  const cardBg = useColorModeValue('white', 'gray.800');
  const primaryText = useColorModeValue('primary.700', 'primary.300');
  const grayTextSecondary = useColorModeValue('gray.600', 'gray.300');
  const blueBg = useColorModeValue('blue.50', 'blue.900');
  const primaryBg = useColorModeValue('primary.100', 'primary.800');
  const grayTextDark = useColorModeValue('gray.700', 'gray.300');
  
  const [currentStep, setCurrentStep] = useState(0);
  const [interviewData, setInterviewData] = useState<InterviewData | null>(null);
  const [isTokenRequired, setIsTokenRequired] = useState(false);
  const [inputToken, setInputToken] = useState('');
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([
    {
      id: 'environment',
      label: '静かな環境にいる',
      description: '周りの音が気にならない場所で練習しましょう',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'camera',
      label: 'カメラの許可',
      description: '表情分析のためカメラを使用します（録画はされません）',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'microphone',
      label: 'マイクの許可',
      description: '音声認識のためマイクを使用します',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'mindset',
      label: '企業情報とアドバイスを確認した',
      description: 'エージェントからのアドバイスを理解しました',
      isChecked: false,
      isRequired: true,
    },
  ]);

  const steps = [
    { title: '企業情報確認', description: '面接する企業の情報を確認しましょう' },
    { title: '環境確認', description: '快適な面接環境を整えましょう' },
    { title: '最後の準備', description: '深呼吸をして、リラックスしましょう' },
  ];

  // シナリオIDを読みやすい名前に変換（統一データシステムを使用）
  const getScenarioName = (scenarioId: string): string => {
    return DataService.getScenarioDisplayName(scenarioId);
  };

  useEffect(() => {
    if (linkId) {
      // ステータス別の処理
      if (status === 'expired') {
        setInterviewData({
          linkId: Array.isArray(linkId) ? linkId[0] : linkId,
          candidateName: '',
          candidateEmail: '',
          sessionId: '',
          status: 'expired',
          message: 'このリンクは期限切れです',
          companyInfo: { name: 'テスト企業', position: 'テストポジション' }
        });
        return;
      }
      
      if (status === 'invalid') {
        setInterviewData({
          linkId: Array.isArray(linkId) ? linkId[0] : linkId,
          candidateName: '',
          candidateEmail: '',
          sessionId: '',
          status: 'invalid',
          message: 'このリンクは無効です',
          companyInfo: null
        });
        return;
      }
      
      // 有効なリンクの場合
      if (status === 'active' || !status) {
        // トークン認証が必要
        setIsTokenRequired(true);
        
        // リンク情報の基本データを設定（トークン認証前）
        const link = mockInterviewLinks.find(l => l.id === linkId);
        
        setInterviewData({
          status: 'active',
          requiresToken: true,
          linkId: Array.isArray(linkId) ? linkId[0] : linkId,
          candidateName: '',
          candidateEmail: '',
          sessionId: '',
          scenarioId: scenarioId as string || 'scenario_basic',
          // 基本情報のみ（トークン認証後に詳細を表示）
          companyInfo: link ? {
            name: link.companyInfo.name,
            position: link.companyInfo.position
          } : {
            name: 'テスト株式会社',
            position: 'フロントエンドエンジニア'
          }
        });
      }
    } else {
      // リンクIDがない場合は無効扱い
      setInterviewData({
        linkId: null,
        candidateName: '',
        candidateEmail: '',
        sessionId: '',
        status: 'invalid',
        message: 'リンクIDが指定されていません',
        companyInfo: null
      });
    }
  }, [linkId, scenarioId, status, router, toast]);

  // デバイスのパーミッションチェック
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        
        setChecklist(prev =>
          prev.map(item => {
            if (item.id === 'camera' || item.id === 'microphone') {
              return { ...item, isChecked: true };
            }
            return item;
          })
        );
        
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.log('デバイスアクセスエラー:', error);
      }
    };
    
    if (currentStep === 1) {
      checkPermissions();
    }
  }, [currentStep]);

  const handleChecklistToggle = (id: string) => {
    setChecklist(prev =>
      prev.map(item =>
        item.id === id ? { ...item, isChecked: !item.isChecked } : item
      )
    );
  };

  const canProceed = () => {
    if (currentStep === 0) return true; // 企業情報確認は自由
    if (currentStep === 1) {
      return checklist.filter(item => item.isRequired).every(item => item.isChecked);
    }
    return true;
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // 面接開始
      const effectiveScenarioId = scenarioId || interviewData?.scenarioId || 'scenario_basic';
      router.push(`/focused-interview?linkId=${linkId}&scenarioId=${effectiveScenarioId}`);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.push('/test-data');
    }
  };

  // トークン認証処理
  const handleTokenSubmit = () => {
    // テスト用トークン: demo-token-123
    if (inputToken === 'demo-token-123') {
      setIsTokenValid(true);
      setIsTokenRequired(false);
      
      // 詳細データを読み込み
      const link = mockInterviewLinks.find(l => l.id === linkId);
      
      setInterviewData((prevData: InterviewData | null) => ({
        linkId: prevData?.linkId || null,
        candidateName: prevData?.candidateName || '',
        candidateEmail: prevData?.candidateEmail || '',
        sessionId: prevData?.sessionId || '',
        ...prevData,
        requiresToken: false,
        companyInfo: link ? link.companyInfo : {
          name: 'テスト株式会社',
          position: 'フロントエンドエンジニア',
          industry: 'IT・ソフトウェア',
          size: '100-500名',
          description: 'これはテスト用の企業情報です。実際の面接では、エージェントが設定した企業情報が表示されます。'
        },
        // agentNotes: link ? link.agentNotes : [
        //   'テスト用のエージェントメモです',
        //   '技術的なスキルについて重点的にアピールしてください',
        //   'チームワークの経験についても言及すると良いでしょう'
        // ],
        // agentRecommendation: link ? link.agentRecommendation : 'テスト環境では、様々な状況をシミュレーションできます。'
      }));

      toast({
        title: 'トークン認証成功',
        description: '面接準備を開始できます',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } else {
      toast({
        title: 'トークン認証エラー',
        description: '無効なトークンです。エージェントから送られた正しいトークンを入力してください。',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // ローディング状態
  if (!interviewData) {
    return (
      <Box minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <Text>Loading...</Text>
      </Box>
    );
  }

  // 期限切れ画面
  if (interviewData.status === 'expired') {
    return (
      <Container maxW="container.md" py={8}>
        <VStack spacing={6} textAlign="center">
          <Badge colorScheme="red" fontSize="lg" p={3}>
            リンク期限切れ
          </Badge>
          <Heading size="lg" color="red.600">
            {interviewData.message}
          </Heading>
          <Alert status="warning">
            <AlertIcon />
            <AlertDescription>
              このリンクの有効期限が切れています。新しいリンクをエージェントにお問い合わせください。
            </AlertDescription>
          </Alert>
          {interviewData.companyInfo && (
            <Card w="full">
              <CardHeader>
                <Heading size="md">面接予定企業</Heading>
              </CardHeader>
              <CardBody>
                <Text><strong>企業名:</strong> {interviewData.companyInfo?.name}</Text>
                <Text><strong>ポジション:</strong> {interviewData.companyInfo?.position}</Text>
              </CardBody>
            </Card>
          )}
          <Button colorScheme="blue" onClick={() => router.push('/demo-login')}>
            テストページに戻る
          </Button>
        </VStack>
      </Container>
    );
  }

  // 無効リンク画面
  if (interviewData.status === 'invalid') {
    return (
      <Container maxW="container.md" py={8}>
        <VStack spacing={6} textAlign="center">
          <Badge colorScheme="gray" fontSize="lg" p={3}>
            無効なリンク
          </Badge>
          <Heading size="lg" color="gray.600">
            {interviewData.message}
          </Heading>
          <Alert status="error">
            <AlertIcon />
            <AlertDescription>
              このリンクは無効です。正しいリンクをエージェントに確認してください。
            </AlertDescription>
          </Alert>
          <Button colorScheme="blue" onClick={() => router.push('/demo-login')}>
            テストページに戻る
          </Button>
        </VStack>
      </Container>
    );
  }

  // トークン入力画面
  if (interviewData.status === 'active' && isTokenRequired) {
    return (
      <Container maxW="container.md" py={8}>
        <VStack spacing={6} textAlign="center">
          <Badge colorScheme="blue" fontSize="lg" p={3}>
            面接トークン認証
          </Badge>
          <Heading size="lg" color="blue.600">
            面接アクセストークンを入力してください
          </Heading>
          
          <Card w="full">
            <CardHeader>
              <Heading size="md">面接情報</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={3} align="start">
                <Text><strong>企業名:</strong> {interviewData.companyInfo?.name}</Text>
                <Text><strong>ポジション:</strong> {interviewData.companyInfo?.position}</Text>
              </VStack>
            </CardBody>
          </Card>

          <Alert status="info">
            <AlertIcon />
            <AlertDescription>
              エージェントから送られたトークンを入力してください。リンクと一緒に送付されています。
            </AlertDescription>
          </Alert>

          <VStack spacing={4} w="full" maxW="400px">
            <FormControl>
              <FormLabel>アクセストークン</FormLabel>
              <Input
                value={inputToken}
                onChange={(e) => setInputToken(e.target.value)}
                placeholder="例: demo-token-123"
                size="lg"
                textAlign="center"
              />
            </FormControl>

            <Button
              colorScheme="blue"
              size="lg"
              w="full"
              onClick={handleTokenSubmit}
              isDisabled={!inputToken.trim()}
            >
              認証して面接準備を開始
            </Button>
          </VStack>

          <Box p={4} bg="orange.50" borderRadius="md" w="full">
            <Text fontSize="sm" color="orange.700" fontWeight="bold" mb={2}>
              テスト用トークン:
            </Text>
            <Text fontSize="sm" color="orange.600">
              demo-token-123
            </Text>
          </Box>

          <Button variant="ghost" onClick={() => router.push('/demo-login')}>
            テストページに戻る
          </Button>
        </VStack>
      </Container>
    );
  }

  const testUser = { profile: { name: 'テストユーザー' } };
  // const breadcrumbs: BreadcrumbItem[] = [];

  return (
    <Box minH="100vh" bg={bgColor}>
      <HeaderNavigation
        user={testUser}
        showBackButton={true}
        backButtonLabel="リンクページに戻る"
        onBackClick={() => router.push('/test-data')}
      />
      
      <Container maxW="container.md" py={{ base: 4, md: 8 }} px={{ base: 4, md: 6 }}>
        <VStack spacing={8} id="main-content" tabIndex={-1}>
          {/* 進捗バー */}
          <Box w="full">
            <HStack justify="space-between" mb={2}>
              <Text fontSize="sm" color={grayText}>
                ステップ {currentStep + 1} / {steps.length}
              </Text>
              <Text fontSize="sm" color={grayText}>
                {steps[currentStep].title}
              </Text>
            </HStack>
            <Progress
              value={((currentStep + 1) / steps.length) * 100}
              colorScheme="primary"
              borderRadius="full"
              hasStripe
              isAnimated
            />
          </Box>

          {/* メインコンテンツ */}
          <AnimatePresence mode="wait">
            <MotionBox
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              w="full"
            >
              {currentStep === 0 && (
                <VStack spacing={6}>
                  {/* 企業情報 */}
                  <Card w="full" bg={cardBg}>
                    <CardHeader>
                      <VStack align="start" spacing={2}>
                        <Heading size="md" color={primaryText}>
                          {interviewData.companyInfo?.name}
                        </Heading>
                        <HStack spacing={3} flexWrap="wrap">
                          <Badge colorScheme="blue" fontSize="sm">{interviewData.companyInfo?.industry}</Badge>
                          <Badge colorScheme="green" fontSize="sm">{interviewData.companyInfo?.position}</Badge>
                          <Badge colorScheme="purple" fontSize="sm">{getScenarioName(interviewData.scenarioId || '')}</Badge>
                        </HStack>
                      </VStack>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        <VStack align="start" spacing={4}>
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業概要</Text>
                            <VStack align="start" spacing={2} fontSize="sm">
                              <Text><strong>所在地:</strong> {interviewData.companyInfo?.location}</Text>
                              <Text><strong>従業員数:</strong> {interviewData.companyInfo?.employeeCount}</Text>
                              <Text><strong>設立:</strong> {interviewData.companyInfo?.founded}</Text>
                            </VStack>
                          </Box>
                          
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業説明</Text>
                            <Text fontSize="sm" color={grayTextSecondary}>
                              {interviewData.companyInfo?.description}
                            </Text>
                          </Box>
                        </VStack>

                        <VStack align="start" spacing={4}>
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>面接スタイル</Text>
                            <Text fontSize="sm" color={grayTextSecondary}>
                              {interviewData.companyInfo?.interviewStyle}
                            </Text>
                          </Box>
                          
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>企業文化</Text>
                            <Text fontSize="sm" color={grayTextSecondary}>
                              {interviewData.companyInfo?.culture}
                            </Text>
                          </Box>
                        </VStack>
                      </SimpleGrid>
                    </CardBody>
                  </Card>

                  {/* エージェントアドバイス */}
                  <Card w="full" bg={blueBg}>
                    <CardBody>
                      <VStack spacing={4}>
                        <Box w="full">
                          <Text fontSize="md" fontWeight="bold" mb={3}>
                            📝 エージェントからのアドバイス
                          </Text>
                          <Text fontSize="sm" whiteSpace="pre-line" mb={4}>
                            {/* {interviewData.agentNotes} */}
                            エージェントからのアドバイスがここに表示されます。
                          </Text>
                          
                          <Divider />
                          
                          <Box mt={4}>
                            <Text fontSize="sm" fontWeight="bold" mb={2}>練習推奨:</Text>
                            <Text fontSize="sm">
                              {/* {interviewData.agentRecommendation} */}
                              練習のおすすめポイントがここに表示されます。
                            </Text>
                          </Box>
                        </Box>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              )}

              {currentStep === 1 && (
                <Card bg={cardBg}>
                  <CardBody>
                    <VStack spacing={6} align="start">
                      <VStack align="start" spacing={2}>
                        <Heading size="md" color={primaryText}>
                          面接環境の確認
                        </Heading>
                        <Text color={grayTextSecondary}>
                          より良い面接体験のため、以下の項目を確認してください
                        </Text>
                      </VStack>
                      
                      <VStack spacing={4} w="full">
                        {checklist.map(item => (
                          <Box
                            key={item.id}
                            p={4}
                            borderWidth={1}
                            borderRadius="md"
                            borderColor={item.isChecked ? 'green.300' : 'gray.200'}
                            bg={item.isChecked ? 'green.50' : 'white'}
                            w="full"
                            transition="all 0.3s"
                          >
                            <Checkbox
                              isChecked={item.isChecked}
                              onChange={() => handleChecklistToggle(item.id)}
                              colorScheme="green"
                              size="lg"
                            >
                              <VStack align="start" spacing={1} ml={2}>
                                <HStack>
                                  <Text fontWeight="medium">
                                    {item.label}
                                  </Text>
                                  {item.isRequired && (
                                    <Badge colorScheme="red" fontSize="xs">
                                      必須
                                    </Badge>
                                  )}
                                </HStack>
                                <Text fontSize="sm" color="gray.600">
                                  {item.description}
                                </Text>
                              </VStack>
                            </Checkbox>
                          </Box>
                        ))}
                      </VStack>
                      
                      {!canProceed() && (
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            必須項目をすべてチェックすると次に進めます
                          </AlertDescription>
                        </Alert>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              )}

              {currentStep === 2 && (
                <Card bg={cardBg}>
                  <CardBody>
                    <VStack spacing={6} align="center">
                      <VStack spacing={2} textAlign="center">
                        <Heading size="md" color={primaryText}>
                          面接開始準備完了！
                        </Heading>
                        <Text color={grayTextSecondary}>
                          深呼吸をして、リラックスして面接を始めましょう
                        </Text>
                      </VStack>
                      
                      <Box
                        w="200px"
                        h="200px"
                        borderRadius="full"
                        bg={primaryBg}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                        >
                          <Text fontSize="6xl">🌸</Text>
                        </motion.div>
                      </Box>
                      
                      <VStack spacing={4} w="full">
                        <Text textAlign="center" color={grayTextDark}>
                          面接では以下のことを覚えておいてください：
                        </Text>
                        <List spacing={2}>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            間違えても、言い直しても大丈夫
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            考える時間を取っても問題ありません
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            AIは味方です。厳しい評価はしません
                          </ListItem>
                        </List>
                        
                        <Alert status="success" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            準備ができたら「面接を開始する」ボタンを押してください
                          </AlertDescription>
                        </Alert>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </MotionBox>
          </AnimatePresence>

          {/* アクションボタン */}
          <HStack justify="space-between" w="full">
            <Button
              variant="ghost"
              leftIcon={<ArrowBackIcon />}
              onClick={handleBack}
            >
              {currentStep === 0 ? 'リンクページに戻る' : '前へ'}
            </Button>
            
            <Button
              colorScheme="primary"
              size="lg"
              rightIcon={<ArrowForwardIcon />}
              onClick={handleNext}
              isDisabled={!canProceed()}
              px={8}
            >
              {currentStep === steps.length - 1 ? '面接を開始する' : '次へ'}
            </Button>
          </HStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default InterviewPrep;