/**
 * トークンベース面接ページ
 * ミーティングリンクからアクセスした候補者用の面接実施画面
 */

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  Spinner,
  Alert,
  AlertIcon,
  Badge,
  Progress,
  useToast,
  useColorModeValue,
  Divider
} from '@chakra-ui/react';
import { TimeIcon, CheckCircleIcon, WarningIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { InterviewDataApi } from '../services/api';
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import { fadeIn, slideInFromBottom } from '../motion';
import type { InterviewSessionResponse, Question } from '../../shared/types';

const MotionBox = motion(Box);
const MotionVStack = motion(VStack);

interface TokenInterviewState {
  status: 'loading' | 'ready' | 'in_progress' | 'completed' | 'error';
  interview?: InterviewSessionResponse;
  currentQuestionIndex: number;
  answers: Array<{
    questionId: string;
    answer: string;
    feedback?: any;
  }>;
  timeStarted?: Date;
  error?: string;
}

export default function InterviewTokenPage() {
  const router = useRouter();
  const { token } = router.query;
  const toast = useToast();
  
  const [state, setState] = useState<TokenInterviewState>({
    status: 'loading',
    currentQuestionIndex: 0,
    answers: []
  });

  // カラーモード対応
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const successColor = useColorModeValue('green.50', 'green.900');

  /**
   * トークン認証と面接データ取得
   */
  useEffect(() => {
    const initializeInterview = async () => {
      if (!token || typeof token !== 'string') {
        setState(prev => ({
          ...prev,
          status: 'error',
          error: '無効なアクセスです。正しいリンクからアクセスしてください。'
        }));
        return;
      }

      try {
        console.log('🔐 トークン認証開始:', token);
        
        // 統合APIで面接セッション開始
        const interview = await InterviewDataApi.startInterviewSession(token);
        
        console.log('✅ 面接データ取得完了:', interview);
        
        setState(prev => ({
          ...prev,
          status: 'ready',
          interview,
          answers: new Array(interview.questions.length).fill(null).map((_, index) => ({
            questionId: interview.questions[index].id,
            answer: ''
          }))
        }));

        // 成功トースト
        toast({
          title: '面接準備完了',
          description: `${interview.companyName}の面接を開始できます`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

      } catch (error) {
        console.error('面接初期化エラー:', error);
        
        let errorMessage = 'エラーが発生しました';
        if (error instanceof Error) {
          if (error.message.includes('expired')) {
            errorMessage = 'このリンクは期限切れです。新しいリンクをリクエストしてください。';
          } else if (error.message.includes('used')) {
            errorMessage = 'このリンクは既に使用済みです。';
          } else if (error.message.includes('invalid')) {
            errorMessage = '無効なリンクです。正しいリンクをご確認ください。';
          }
        }
        
        setState(prev => ({
          ...prev,
          status: 'error',
          error: errorMessage
        }));

        toast({
          title: '面接開始エラー',
          description: errorMessage,
          status: 'error',
          duration: 7000,
          isClosable: true,
        });
      }
    };

    initializeInterview();
  }, [token, toast]);

  /**
   * 面接開始ハンドラー
   */
  const handleStartInterview = () => {
    setState(prev => ({
      ...prev,
      status: 'in_progress',
      timeStarted: new Date()
    }));

    toast({
      title: '面接開始',
      description: 'リラックスして、ありのままの自分を表現してください',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  /**
   * 面接ルーム画面へ遷移
   */
  const redirectToInterviewRoom = () => {
    const params = new URLSearchParams({
      token: token as string,
      interviewId: state.interview!.interviewId,
      mode: 'token-based'
    });
    
    router.push(`/interview-room?${params.toString()}`);
  };

  /**
   * エラー画面の表示
   */
  if (state.status === 'error') {
    return (
      <Box bg={bgColor} minH="100vh" py={spacing.sectionSpacing}>
        <Container maxW="container.md">
          <MotionBox {...fadeIn}>
            <VStack spacing={spacing.sectionSpacing} align="center" textAlign="center">
              <WarningIcon boxSize={16} color="red.500" />
              
              <Heading as="h1" size="xl" color="red.600">
                アクセスエラー
              </Heading>
              
              <Alert status="error" borderRadius="md">
                <AlertIcon />
                <Text>{state.error}</Text>
              </Alert>

              <VStack spacing={4}>
                <Text {...textStyles.body} color="gray.600">
                  以下をご確認ください：
                </Text>
                
                <VStack align="start" spacing={2}>
                  <Text fontSize="sm">• エージェントから送信された正しいリンクを使用している</Text>
                  <Text fontSize="sm">• リンクの有効期限が切れていない</Text>
                  <Text fontSize="sm">• 既に面接を完了していない</Text>
                </VStack>

                <Button
                  colorScheme="blue"
                  onClick={() => router.push('/')}
                  px={spacing.buttonPadding.px}
                  py={spacing.buttonPadding.py}
                >
                  ホームに戻る
                </Button>
              </VStack>
            </VStack>
          </MotionBox>
        </Container>
      </Box>
    );
  }

  /**
   * ローディング画面
   */
  if (state.status === 'loading') {
    return (
      <Box bg={bgColor} minH="100vh" py={spacing.sectionSpacing}>
        <Container maxW="container.md">
          <MotionBox {...fadeIn}>
            <VStack spacing={spacing.sectionSpacing} align="center" textAlign="center">
              <Spinner size="xl" color="blue.500" thickness="4px" />
              
              <VStack spacing={4}>
                <Heading as="h1" size="xl" color="blue.600">
                  面接準備中
                </Heading>
                
                <Text {...textStyles.body} color="gray.600">
                  面接データを読み込んでいます...
                </Text>
                
                <Progress 
                  size="lg" 
                  isIndeterminate 
                  colorScheme="blue" 
                  borderRadius="full"
                  w="300px"
                />
              </VStack>
            </VStack>
          </MotionBox>
        </Container>
      </Box>
    );
  }

  /**
   * 面接準備完了画面
   */
  if (state.status === 'ready' && state.interview) {
    const interview = state.interview;
    const estimatedMinutes = Math.round(interview.estimatedDuration / 60);

    return (
      <Box bg={bgColor} minH="100vh" py={spacing.sectionSpacing}>
        <Container maxW="container.lg">
          <MotionVStack {...fadeIn} spacing={spacing.sectionSpacing} align="stretch">
            
            {/* ヘッダー */}
            <VStack spacing={4} textAlign="center">
              <CheckCircleIcon boxSize={12} color="green.500" />
              
              <Heading as="h1" size="2xl" color="blue.600">
                面接準備完了
              </Heading>
              
              <Text {...textStyles.body} color="gray.600" maxW="2xl">
                エージェント様より面接のご案内をいただき、ありがとうございます。
                準備が整いましたので、面接を開始してください。
              </Text>
            </VStack>

            {/* 面接情報カード */}
            <MotionBox 
              {...slideInFromBottom}
              bg={cardBg}
              borderRadius="lg"
              borderWidth="1px"
              borderColor={borderColor}
              p={spacing.cardPadding}
              boxShadow="md"
            >
              <VStack spacing={spacing.cardPadding} align="stretch">
                <HStack justify="space-between" align="center">
                  <Heading as="h2" {...textStyles.heading} color="blue.600">
                    面接詳細情報
                  </Heading>
                  <Badge colorScheme="green" fontSize="md" px={3} py={1}>
                    準備完了
                  </Badge>
                </HStack>

                <Divider />

                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text {...textStyles.label}>企業名</Text>
                    <Text {...textStyles.body} fontWeight="bold">
                      {interview.companyName}
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text {...textStyles.label}>募集ポジション</Text>
                    <Text {...textStyles.body} fontWeight="bold">
                      {interview.position}
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text {...textStyles.label}>質問数</Text>
                    <Text {...textStyles.body} fontWeight="bold">
                      {interview.questions.length}問
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text {...textStyles.label}>予想所要時間</Text>
                    <HStack>
                      <TimeIcon color="gray.500" />
                      <Text {...textStyles.body} fontWeight="bold">
                        約{estimatedMinutes}分
                      </Text>
                    </HStack>
                  </HStack>
                </VStack>
              </VStack>
            </MotionBox>

            {/* 準備事項 */}
            <MotionBox 
              variants={slideInFromBottom}
              initial="initial"
              animate="animate"
              custom={{ delay: 0.2 }}
              exit="exit"
              bg={successColor}
              borderRadius="lg"
              borderWidth="1px"
              borderColor="green.200"
              p={spacing.cardPadding}
            >
              <VStack spacing={4} align="stretch">
                <Heading as="h3" size="md" color="green.700">
                  📋 面接前の準備確認
                </Heading>

                <VStack align="start" spacing={3}>
                  <HStack spacing={3}>
                    <CheckCircleIcon color="green.500" />
                    <Text fontSize="sm">安定したインターネット接続</Text>
                  </HStack>
                  <HStack spacing={3}>
                    <CheckCircleIcon color="green.500" />
                    <Text fontSize="sm">マイク・スピーカーの動作確認</Text>
                  </HStack>
                  <HStack spacing={3}>
                    <CheckCircleIcon color="green.500" />
                    <Text fontSize="sm">静かで集中できる環境</Text>
                  </HStack>
                  <HStack spacing={3}>
                    <CheckCircleIcon color="green.500" />
                    <Text fontSize="sm">十分な時間の確保（中断なし）</Text>
                  </HStack>
                </VStack>

                <Text fontSize="xs" color="green.600" mt={2}>
                  ✨ リラックスして、ありのままの自分を表現してください
                </Text>
              </VStack>
            </MotionBox>

            {/* 開始ボタン */}
            <MotionBox 
              variants={slideInFromBottom}
              initial="initial"
              animate="animate"
              custom={{ delay: 0.4 }}
              exit="exit"
              textAlign="center"
            >
              <Button
                size="lg"
                colorScheme="blue"
                px={spacing.buttonPadding.px}
                py={spacing.buttonPadding.py}
                fontSize="lg"
                fontWeight="bold"
                onClick={redirectToInterviewRoom}
                boxShadow="lg"
                _hover={{ transform: 'translateY(-2px)', boxShadow: 'xl' }}
                transition="all 0.2s ease"
              >
                🚀 面接を開始する
              </Button>
              
              <Text fontSize="xs" color="gray.500" mt={3}>
                クリックすると面接画面に移動します
              </Text>
            </MotionBox>

          </MotionVStack>
        </Container>
      </Box>
    );
  }

  return null;
}