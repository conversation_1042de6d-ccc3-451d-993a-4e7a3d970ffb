import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

import { GoogleMeetInterview, CompanyInfo } from '../components/MeetingLayout';
import { mockInterviewLinks } from '../lib/agentLinkData';
import { InterviewProvider } from '../contexts/InterviewContext';

interface InterviewData {
  companyInfo: CompanyInfo;
  agentNotes: string[];
  scenarioId: string;
}

const FocusedInterview: React.FC = () => {
  const [interviewData, setInterviewData] = useState<InterviewData | null>(
    null
  );
  const router = useRouter();
  const { linkId, scenarioId } = router.query;

  useEffect(() => {
    if (!linkId) return;

    const loadData = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const linkData = mockInterviewLinks.find((link) => link.id === linkId);
      if (linkData) {
        setInterviewData({
          companyInfo: linkData.companyInfo,
          agentNotes: Array.isArray(linkData.agentNotes)
            ? linkData.agentNotes
            : [],
          scenarioId:
            (scenarioId as string) || linkData.recommendedScenarios[0],
        });
      } else {
        router.push('/');
      }
    };

    loadData();
  }, [linkId, scenarioId, router]);

  const handleComplete = (results: any[]) => {
    if (!interviewData) return;

    const result = {
      id: `result_${Date.now()}`,
      linkId: linkId as string,
      scenarioId: interviewData.scenarioId,
      companyName: interviewData.companyInfo.name,
      position: interviewData.companyInfo.position,
      practiceDate: new Date().toISOString(),
      candidateName: 'Test User',
      candidateEmail: '<EMAIL>',
      overallScore: 8.5,
      communicationScore: 8.8,
      technicalScore: 8.2,
      duration: 45,
      feedback: results,
    };

    router.push(`/interview-result?resultId=${result.id}`);
  };

  return interviewData ? (
    <InterviewProvider>
      <GoogleMeetInterview
        scenarioId={interviewData.scenarioId}
        companyInfo={interviewData.companyInfo}
        agentNotes={interviewData.agentNotes}
        onComplete={handleComplete}
        onBack={() => router.back()}
      />
    </InterviewProvider>
  ) : null;
};

export default FocusedInterview;
