import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { Box, Text, Spinner, VStack } from '@chakra-ui/react';

const IndexPage = () => {
  const router = useRouter();

  useEffect(() => {
    // ルートアクセス時は demo-login にリダイレクト
    router.replace('/demo-login');
  }, [router]);

  return (
    <Box minH="100vh" display="flex" alignItems="center" justifyContent="center">
      <VStack spacing={4}>
        <Spinner size="lg" color="blue.500" />
        <Text>面接君にリダイレクト中...</Text>
      </VStack>
    </Box>
  );
};

export default IndexPage; 