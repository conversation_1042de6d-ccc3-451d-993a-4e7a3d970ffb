/**
 * 面接準備画面 - 直接練習用
 * マイクロモーメントを活用した心理的安全性の確保
 * 
 * 【重要】このページは候補者が直接練習する際の準備画面です
 * フロー: /candidate-dashboard → ここ → /interview-room
 * 
 * エージェント経由フローは /interview-prep を使用します
 */
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Progress,
  Icon,
  Container,
  Alert,
  AlertIcon,
  AlertDescription,
  Checkbox,
  Card,
  CardBody,
  useToast,
  useColorModeValue,
  Heading,
  List,
  ListItem,
  ListIcon,
  Badge,
  Divider,
} from '@chakra-ui/react';
import {
  CheckCircleIcon,
  WarningIcon,
  InfoIcon,
  ArrowForwardIcon,
  ArrowBackIcon,
} from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { AccessibilityHelper } from '../components/AccessibilityHelper';
import HeaderNavigation from '../components/HeaderNavigation';

const MotionBox = motion(Box);

interface ChecklistItem {
  id: string;
  label: string;
  description: string;
  isChecked: boolean;
  isRequired: boolean;
}

const InterviewPreparation: React.FC = () => {
  const router = useRouter();
  const toast = useToast();
  const { type } = router.query;
  
  const [currentStep, setCurrentStep] = useState(0);
  const [user, setUser] = useState<any>(null);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([
    {
      id: 'quiet',
      label: '静かな環境にいる',
      description: '周りの音が気にならない場所で練習しましょう',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'camera',
      label: 'カメラの許可',
      description: '表情分析のためカメラを使用します（録画はされません）',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'microphone',
      label: 'マイクの許可',
      description: '音声認識のためマイクを使用します',
      isChecked: false,
      isRequired: true,
    },
    {
      id: 'mindset',
      label: '練習の心構えができている',
      description: '失敗しても大丈夫！練習なので気楽に臨みましょう',
      isChecked: false,
      isRequired: false,
    },
  ]);

  const steps = [
    { title: '環境確認', description: '快適な練習環境を整えましょう' },
    { title: '目標設定', description: '今回の練習で意識することを決めましょう' },
    { title: '最後の準備', description: '深呼吸をして、リラックスしましょう' },
  ];

  const handleChecklistToggle = (id: string) => {
    setChecklist(prev =>
      prev.map(item =>
        item.id === id ? { ...item, isChecked: !item.isChecked } : item
      )
    );
  };

  const canProceed = () => {
    return checklist.filter(item => item.isRequired).every(item => item.isChecked);
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // 面接練習画面へ遷移
      router.push('/interview-room');
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  // ユーザー情報取得
  useEffect(() => {
    const userData = localStorage.getItem('demo_user');
    if (userData) {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role !== 'candidate') {
        router.push('/demo-login');
        return;
      }
      setUser(parsedUser);
    } else {
      router.push('/demo-login');
    }
  }, [router]);

  // デバイスのパーミッションチェック
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        // カメラとマイクの権限をチェック
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        
        // 権限が取得できたら自動的にチェック
        setChecklist(prev =>
          prev.map(item => {
            if (item.id === 'camera' || item.id === 'microphone') {
              return { ...item, isChecked: true };
            }
            return item;
          })
        );
        
        // ストリームを停止
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.log('デバイスアクセスエラー:', error);
      }
    };
    
    if (currentStep === 0) {
      checkPermissions();
    }
  }, [currentStep]);

  if (!user) {
    return <Box>Loading...</Box>;
  }

  const practiceType = type === 'basic' ? '基本面接練習' : '実践面接練習';
  const breadcrumbs = [
    { label: practiceType, href: `/interview-preparation?type=${type}`, isCurrentPage: true }
  ];

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
      {/* アクセシビリティヘルパー */}
      <AccessibilityHelper />
      <HeaderNavigation
        user={user}
        breadcrumbs={breadcrumbs}
        showBackButton={true}
        backButtonLabel="ダッシュボードに戻る"
        onBackClick={() => router.push('/candidate-dashboard')}
      />
      
      <Container maxW="container.md" py={{ base: 4, md: 8 }} px={{ base: 4, md: 6 }}>
        <VStack spacing={8} id="main-content" tabIndex={-1}>
          {/* 進捗バー */}
          <Box w="full">
            <HStack justify="space-between" mb={2}>
              <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                ステップ {currentStep + 1} / {steps.length}
              </Text>
              <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                {steps[currentStep].title}
              </Text>
            </HStack>
            <Progress
              value={((currentStep + 1) / steps.length) * 100}
              colorScheme="primary"
              borderRadius="full"
              hasStripe
              isAnimated
            />
          </Box>

          {/* メインコンテンツ */}
          <AnimatePresence mode="wait">
            <MotionBox
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              w="full"
            >
              {currentStep === 0 && (
                <Card bg={useColorModeValue('white', 'gray.800')} borderColor={useColorModeValue('gray.200', 'gray.700')}>
                  <CardBody>
                    <VStack spacing={6} align="start">
                      <VStack align="start" spacing={2}>
                        <Heading size="md" color={useColorModeValue('primary.700', 'primary.300')}>
                          練習環境の確認
                        </Heading>
                        <Text color={useColorModeValue('gray.600', 'gray.300')}>
                          より良い練習体験のため、以下の項目を確認してください
                        </Text>
                      </VStack>
                      
                      <VStack spacing={4} w="full">
                        {checklist.map(item => (
                          <Box
                            key={item.id}
                            p={{ base: 3, md: 4 }}
                            borderWidth={1}
                            borderRadius="md"
                            borderColor={item.isChecked ? useColorModeValue('green.300', 'green.600') : useColorModeValue('gray.200', 'gray.600')}
                            bg={item.isChecked ? useColorModeValue('green.50', 'green.900') : useColorModeValue('white', 'gray.800')}
                            w="full"
                            transition="all 0.3s"
                          >
                            <Checkbox
                              isChecked={item.isChecked}
                              onChange={() => handleChecklistToggle(item.id)}
                              colorScheme="green"
                              size={{ base: "md", md: "lg" }}
                            >
                              <VStack align="start" spacing={1} ml={2}>
                                <HStack>
                                  <Text fontWeight="medium">
                                    {item.label}
                                  </Text>
                                  {item.isRequired && (
                                    <Badge colorScheme="red" fontSize="xs">
                                      必須
                                    </Badge>
                                  )}
                                </HStack>
                                <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                                  {item.description}
                                </Text>
                              </VStack>
                            </Checkbox>
                          </Box>
                        ))}
                      </VStack>
                      
                      {!canProceed() && (
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            必須項目をすべてチェックすると次に進めます
                          </AlertDescription>
                        </Alert>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              )}

              {currentStep === 1 && (
                <Card bg={useColorModeValue('white', 'gray.800')} borderColor={useColorModeValue('gray.200', 'gray.700')}>
                  <CardBody>
                    <VStack spacing={6} align="start">
                      <VStack align="start" spacing={2}>
                        <Heading size="md" color={useColorModeValue('primary.700', 'primary.300')}>
                          今回の練習目標
                        </Heading>
                        <Text color={useColorModeValue('gray.600', 'gray.300')}>
                          小さな目標を設定して、一歩ずつ成長しましょう
                        </Text>
                      </VStack>
                      
                      <VStack spacing={4} w="full">
                        <Box
                          p={{ base: 3, md: 4 }}
                          borderWidth={2}
                          borderRadius="md"
                          borderColor={useColorModeValue('primary.300', 'primary.600')}
                          bg={useColorModeValue('primary.50', 'gray.800')}
                          w="full"
                        >
                          <VStack align="start" spacing={3}>
                            <HStack>
                              <Icon as={InfoIcon} color="primary.500" />
                              <Text fontWeight="bold" color={useColorModeValue('primary.700', 'primary.300')}>
                                初めての練習におすすめの目標
                              </Text>
                            </HStack>
                            <List spacing={2}>
                              <ListItem>
                                <ListIcon as={CheckCircleIcon} color="primary.500" />
                                最後まで回答を完了する
                              </ListItem>
                              <ListItem>
                                <ListIcon as={CheckCircleIcon} color="primary.500" />
                                緊張しても大丈夫と自分に言い聞かせる
                              </ListItem>
                              <ListItem>
                                <ListIcon as={CheckCircleIcon} color="primary.500" />
                                1つでも自分の強みを伝える
                              </ListItem>
                            </List>
                          </VStack>
                        </Box>
                        
                        <Alert status="success" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            完璧を目指す必要はありません。今の自分のベストを出せれば十分です！
                          </AlertDescription>
                        </Alert>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              )}

              {currentStep === 2 && (
                <Card bg={useColorModeValue('white', 'gray.800')} borderColor={useColorModeValue('gray.200', 'gray.700')}>
                  <CardBody>
                    <VStack spacing={6} align="center">
                      <VStack spacing={2} textAlign="center">
                        <Heading size="md" color={useColorModeValue('primary.700', 'primary.300')}>
                          準備完了！
                        </Heading>
                        <Text color={useColorModeValue('gray.600', 'gray.300')}>
                          深呼吸をして、リラックスして始めましょう
                        </Text>
                      </VStack>
                      
                      <Box
                        w={{ base: "150px", md: "200px" }}
                        h={{ base: "150px", md: "200px" }}
                        borderRadius="full"
                        bg={useColorModeValue('primary.100', 'primary.800')}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        position="relative"
                      >
                        <motion.div
                          animate={{
                            scale: [1, 1.1, 1],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        >
                          <Text fontSize="6xl">🌸</Text>
                        </motion.div>
                      </Box>
                      
                      <VStack spacing={4} w="full">
                        <Text textAlign="center" color={useColorModeValue('gray.700', 'gray.300')}>
                          練習では以下のことを覚えておいてください：
                        </Text>
                        <List spacing={2}>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            間違えても、言い直しても大丈夫
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            考える時間を取っても問題ありません
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="green.500" />
                            AIは味方です。厳しい評価はしません
                          </ListItem>
                        </List>
                        
                        <Divider />
                        
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <AlertDescription>
                            準備ができたら「練習を始める」ボタンを押してください
                          </AlertDescription>
                        </Alert>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </MotionBox>
          </AnimatePresence>

          {/* アクションボタン */}
          <HStack justify="space-between" w="full">
            {currentStep > 0 ? (
              <Button
                variant="ghost"
                leftIcon={<ArrowBackIcon />}
                onClick={handleBack}
              >
                前へ
              </Button>
            ) : (
              <Box /> // 空のスペーサー
            )}
            
            <Button
              colorScheme="primary"
              size="lg"
              rightIcon={<ArrowForwardIcon />}
              onClick={handleNext}
              isDisabled={currentStep === 0 && !canProceed()}
              px={8}
            >
              {currentStep === steps.length - 1 ? '練習を始める' : '次へ'}
            </Button>
          </HStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default InterviewPreparation;