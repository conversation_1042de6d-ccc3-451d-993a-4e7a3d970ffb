/**
 * 面接結果表示ページ
 * 集中用面接完了後の結果表示（包括的レポート対応）
 */
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';
import { Box, Button, VStack, Text, useToast, useColorModeValue, Switch, HStack } from '@chakra-ui/react';
import HeaderNavigation from '../components/HeaderNavigation';

// SSRを無効化してハイドレーションエラーを防ぐ
const ComprehensiveFeedbackReport = dynamic(
  () => import('../components/ComprehensiveFeedbackReport'),
  { 
    ssr: false,
    loading: () => <Box p={6}>ロード中...</Box>
  }
);

const AdvancedFeedbackReport = dynamic(
  () => import('../components/AdvancedFeedbackReport'),
  { 
    ssr: false,
    loading: () => <Box p={6}>ロード中...</Box>
  }
);

// HeaderNavigationもSSR無効化
const DynamicHeaderNavigation = dynamic(
  () => import('../components/HeaderNavigation'),
  { 
    ssr: false,
    loading: () => <Box h={16} bg="white" borderBottomWidth={1} />
  }
);
import { ArrowBackIcon } from '@chakra-ui/icons';
import { getEvaluationDataByProfile, mockDataPattern1_NewGradEngineer } from '../data/mockEvaluationData';
import { CandidateProfile } from '../types/evaluation';

const InterviewResult: React.FC = () => {
  const router = useRouter();
  const { linkId, scenarioId } = router.query;
  const toast = useToast();
  
  // SSR安全な初期化
  const [resultData, setResultData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [useComprehensiveReport, setUseComprehensiveReport] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // クライアントサイドでのみデータ取得
  useEffect(() => {
    setIsMounted(true);
    if (typeof window !== 'undefined') {
      const stored = sessionStorage.getItem('interviewResult');
      if (stored) {
        try {
          const parsedData = JSON.parse(stored);
          setResultData(parsedData);
          setIsLoading(false);
          return;
        } catch (e) {
          console.error('Failed to parse stored data:', e);
        }
      }
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    // SSR安全なチェック
    if (!isMounted || !router.isReady) {
      return;
    }
    
    // データがある場合は何もしない
    if (resultData) {
      return;
    }
    
    console.log('🔍 interview-result ページ - データなしのため確認');
    console.log('🔍 クエリパラメータ:', { linkId, scenarioId });
    
    // データがない場合のみ警告を表示
    if (!toast.isActive('no-result-data')) {
      toast({
        id: 'no-result-data',
        title: '結果が見つかりません',
        description: '面接結果が見つかりません。リンクページに戻ります。',
        status: 'warning',
        duration: 5000,
        isClosable: true,
      });
    }
    
    // 少し遅延させてからリダイレクト
    const timer = setTimeout(() => {
      router.push('/test-data');
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [isMounted, router.isReady, resultData, linkId, scenarioId, toast, router]);

  // SSR中はローディング表示
  if (!isMounted) {
    return (
      <Box 
        minH="100vh" 
        display="flex" 
        alignItems="center" 
        justifyContent="center"
        bg={useColorModeValue('gray.50', 'gray.900')}
      >
        ロード中...
      </Box>
    );
  }

  const handleClose = () => {
    console.log('🗑️ 手動でページを離れるため、セッションストレージをクリア');
    sessionStorage.removeItem('interviewResult');
    router.push('/test-data');
  };

  const handleStartNewPractice = () => {
    router.push('/focused-interview');
  };

  // モックデータの取得（実際の実装では、resultDataから候補者プロフィールを判定）
  const getMockReportData = () => {
    // デフォルトプロフィール
    const defaultProfile: CandidateProfile = {
      type: '新卒',
      position: 'ソフトウェアエンジニア',
      industry: 'IT',
      experienceLevel: 'junior'
    };
    
    return getEvaluationDataByProfile(defaultProfile);
  };

  const testUser = { profile: { name: 'テストユーザー' } };
  const breadcrumbs: any[] = [];

  if (isLoading) {
    return (
      <Box 
        minH="100vh" 
        display="flex" 
        alignItems="center" 
        justifyContent="center"
        bg={useColorModeValue('gray.50', 'gray.900')}
      >
        結果を読み込み中...
      </Box>
    );
  }

  if (!resultData) {
    return (
      <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
        <DynamicHeaderNavigation
          user={testUser}
          breadcrumbs={breadcrumbs}
          showBackButton={true}
          backButtonLabel="リンクページに戻る"
          onBackClick={() => router.push('/test-data')}
        />
        <Box 
          minH="80vh" 
          display="flex" 
          flexDirection="column"
          alignItems="center" 
          justifyContent="center"
          gap={4}
        >
          <Text>面接結果が見つかりません</Text>
          <Button 
            leftIcon={<ArrowBackIcon />} 
            onClick={() => router.push('/test-data')}
          >
            リンクページに戻る
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
      <DynamicHeaderNavigation
        user={testUser}
        breadcrumbs={breadcrumbs}
        showBackButton={true}
        backButtonLabel="リンクページに戻る"
        onBackClick={handleClose}
      />
      
      {/* レポート切り替えスイッチ */}
      <Box p={4} bg={useColorModeValue('white', 'gray.800')} borderBottomWidth={1}>
        <HStack justify="center" spacing={4}>
          <Text fontSize="sm">従来版</Text>
          <Switch
            isChecked={useComprehensiveReport}
            onChange={(e) => setUseComprehensiveReport(e.target.checked)}
            colorScheme="blue"
            size="lg"
          />
          <Text fontSize="sm" fontWeight="bold">包括的分析版 (新機能)</Text>
        </HStack>
      </Box>

      {useComprehensiveReport ? (
        <ComprehensiveFeedbackReport
          reportData={getMockReportData()}
          onClose={handleClose}
          onStartNewPractice={handleStartNewPractice}
        />
      ) : (
        <AdvancedFeedbackReport
          feedbacks={resultData.feedbacks}
          scenarioId={scenarioId as string}
          onClose={handleClose}
        />
      )}
    </Box>
  );
};

export default InterviewResult;