import type { AppProps } from 'next/app';
import { ChakraProvider, useColorMode } from '@chakra-ui/react';
import { useEffect } from 'react';
import { mensetsukuTheme } from '../theme';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { useGlobalErrorHandler } from '../hooks/useError';
import '../styles/toast-override.css';

// ダークモード設定をDOMに反映するコンポーネント
function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { colorMode } = useColorMode();
  
  useEffect(() => {
    // body要素にdata-theme属性を設定
    document.body.setAttribute('data-theme', colorMode);
    document.documentElement.setAttribute('data-theme', colorMode);
  }, [colorMode]);
  
  return <>{children}</>;
}

// グローバルエラーハンドラーコンポーネント
function GlobalErrorHandler({ children }: { children: React.ReactNode }) {
  useGlobalErrorHandler();
  return <>{children}</>;
}

function MyApp({ Component, pageProps }: AppProps) {
  return (
    <ChakraProvider theme={mensetsukuTheme}>
      <ErrorBoundary>
        <GlobalErrorHandler>
          <ThemeProvider>
            <Component {...pageProps} />
          </ThemeProvider>
        </GlobalErrorHandler>
      </ErrorBoundary>
    </ChakraProvider>
  );
}

export default MyApp; 