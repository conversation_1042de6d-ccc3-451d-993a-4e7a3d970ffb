/**
 * 面接練習ルーム
 * 自然な面接体験を提供
 */
import React from 'react';
import { ChakraProvider } from '@chakra-ui/react';
import NaturalInterviewFlow from '../components/NaturalInterviewFlow';
import { mensetsukuTheme } from '../theme';

const InterviewRoomPage: React.FC = () => {
  return (
    <ChakraProvider theme={mensetsukuTheme}>
      <NaturalInterviewFlow />
    </ChakraProvider>
  );
};

export default InterviewRoomPage;