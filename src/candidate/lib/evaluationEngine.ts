/**
 * 面接評価エンジン (Phase 1: MVP実装)
 * OpenAI GPT-4 APIを使用したテキスト分析とスコアリング
 */

import { 
  ComprehensiveInterviewReport, 
  CandidateProfile, 
  EvaluationConfig,
  STARCEvaluation,
  AudioAnalysisResult,
  InterviewEvaluationFramework 
} from '../types/evaluation';
import { getEvaluationDataByProfile } from '../data/mockEvaluationData';
import type { 
  CommunicationAnalysis, 
  EvaluationData, 
  STARAverageScores,
  PersonalizedFeedback,
  AudioVideoAnalysis,
  ActionPlan,
  BenchmarkComparison 
} from '../types/evaluation-engine';

// OpenAI API設定（実際の実装では環境変数から取得）
const OPENAI_API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY || '';
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

interface InterviewData {
  questions: string[];
  answers: string[];
  audioFile?: File;
  metadata: {
    duration: number;
    position: string;
    candidateProfile: CandidateProfile;
  };
}

interface OpenAIResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

/**
 * メインの評価エンジンクラス
 */
export class InterviewEvaluationEngine {
  private config: EvaluationConfig;

  constructor(config: Partial<EvaluationConfig> = {}) {
    this.config = {
      weights: {
        communication: 0.25,
        logicalThinking: 0.20,
        experienceQuality: 0.20,
        culturalFit: 0.15,
        technicalCompetence: 0.10,
        adaptability: 0.05,
        professionalism: 0.05,
      },
      industrySpecific: true,
      roleSpecific: true,
      includeAudioAnalysis: false, // Phase 1では無効
      includeVideoAnalysis: false, // Phase 1では無効
      detailLevel: 'standard',
      ...config
    };
  }

  /**
   * メインの評価実行メソッド
   */
  async evaluateInterview(interviewData: InterviewData): Promise<ComprehensiveInterviewReport> {
    try {
      // Phase 1: モックデータを使用（実際のAI分析は後のフェーズで実装）
      if (process.env.NODE_ENV === 'development') {
        return this.generateMockReport(interviewData);
      }

      // 1. STAR法分析
      const starAnalysis = await this.analyzeSTARResponses(
        interviewData.questions, 
        interviewData.answers
      );

      // 2. コミュニケーション分析
      const communicationAnalysis = await this.analyzeCommunication(
        interviewData.answers.join(' ')
      );

      // 3. 総合スコア計算
      const overallScore = this.calculateOverallScore({
        starAnalysis,
        communicationAnalysis
      });

      // 4. パーソナライズされたフィードバック生成
      const feedback = await this.generatePersonalizedFeedback({
        starAnalysis,
        communicationAnalysis,
        profile: interviewData.metadata.candidateProfile
      });

      // 5. レポート構築
      return this.buildComprehensiveReport({
        interviewData,
        starAnalysis,
        communicationAnalysis,
        overallScore,
        feedback
      });

    } catch (error) {
      console.error('評価エンジンエラー:', error);
      // エラー時はモックデータを返す
      return this.generateMockReport(interviewData);
    }
  }

  /**
   * STAR法による回答分析
   */
  private async analyzeSTARResponses(
    questions: string[], 
    answers: string[]
  ): Promise<STARCEvaluation[]> {
    const analyses: STARCEvaluation[] = [];

    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const answer = answers[i];

      const prompt = this.createSTARAnalysisPrompt(question, answer);
      
      try {
        const response = await this.callOpenAI(prompt);
        const analysis = this.parseSTARResponse(response);
        analyses.push(analysis);
      } catch (error) {
        console.error('STAR分析エラー:', error);
        // フォールバック: デフォルトスコア
        analyses.push(this.getDefaultSTARAnalysis());
      }
    }

    return analyses;
  }

  /**
   * コミュニケーション分析
   */
  private async analyzeCommunication(fullText: string): Promise<CommunicationAnalysis> {
    const prompt = this.createCommunicationAnalysisPrompt(fullText);
    
    try {
      const response = await this.callOpenAI(prompt);
      return this.parseCommunicationResponse(response);
    } catch (error) {
      console.error('コミュニケーション分析エラー:', error);
      return this.getDefaultCommunicationAnalysis();
    }
  }

  /**
   * OpenAI API呼び出し
   */
  private async callOpenAI(prompt: string): Promise<string> {
    if (!OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'あなたは面接評価の専門家です。客観的で建設的なフィードバックを提供してください。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data: OpenAIResponse = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * STAR分析用プロンプト生成
   */
  private createSTARAnalysisPrompt(question: string, answer: string): string {
    return `
以下の面接回答をSTAR+C法の観点から評価してください。

質問: ${question}
回答: ${answer}

以下の項目をそれぞれ0-100点で評価し、JSON形式で返してください：

{
  "situation": {
    "clarity": 0-100, // 状況説明の明確さ
    "relevance": 0-100, // 質問との関連性
    "specificity": 0-100 // 具体性
  },
  "task": {
    "roleClarity": 0-100, // 役割の明確さ
    "challengeLevel": 0-100, // 課題の難易度
    "scope": 0-100 // 影響範囲
  },
  "action": {
    "initiative": 0-100, // 主体性
    "methodology": 0-100, // 方法論の妥当性
    "collaboration": 0-100 // 協働性
  },
  "result": {
    "quantification": 0-100, // 定量的成果
    "impact": 0-100, // インパクト
    "learning": 0-100 // 学びの深さ
  },
  "competency": {
    "transferability": 0-100, // 応用可能性
    "growthMindset": 0-100 // 成長志向
  }
}

評価理由も簡潔に含めてください。
    `;
  }

  /**
   * コミュニケーション分析用プロンプト生成
   */
  private createCommunicationAnalysisPrompt(text: string): string {
    return `
以下のテキストをコミュニケーション能力の観点から分析してください：

テキスト: ${text}

以下の項目を0-100点で評価し、JSON形式で返してください：

{
  "verbalFluency": {
    "score": 0-100,
    "fillerWords": 推定値, // フィラーワード頻度（回/分）
    "speechClarity": 0-100, // 発話の明瞭さ
    "vocabularyRichness": 0-100 // 語彙の豊富さ
  },
  "structuring": {
    "score": 0-100,
    "logicalFlow": 0-100, // 論理的流れ
    "introduction": 0-100, // 導入の明確さ
    "conclusion": 0-100 // 結論の適切さ
  },
  "content": {
    "relevance": 0-100, // 内容の関連性
    "depth": 0-100, // 内容の深さ
    "examples": 0-100 // 具体例の適切さ
  }
}

改善提案も3つ含めてください。
    `;
  }

  /**
   * STAR分析レスポンス解析
   */
  private parseSTARResponse(response: string): STARCEvaluation {
    try {
      // JSONを抽出して解析
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed as STARCEvaluation;
      }
    } catch (error) {
      console.error('STAR応答解析エラー:', error);
    }
    
    return this.getDefaultSTARAnalysis();
  }

  /**
   * コミュニケーション分析レスポンス解析
   */
  private parseCommunicationResponse(response: string): CommunicationAnalysis {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('コミュニケーション応答解析エラー:', error);
    }
    
    return this.getDefaultCommunicationAnalysis();
  }

  /**
   * 総合スコア計算
   */
  private calculateOverallScore(analysisData: EvaluationData): number {
    // 簡単な重み付け平均を使用
    const weights = this.config.weights;
    let totalScore = 0;
    let totalWeight = 0;

    if (analysisData.starAnalysis?.length > 0) {
      const avgSTARScore = analysisData.starAnalysis.reduce((sum: number, star: any) => {
        return sum + this.calculateSTARScore(star);
      }, 0) / analysisData.starAnalysis.length;
      
      totalScore += avgSTARScore * (weights.experienceQuality + weights.logicalThinking);
      totalWeight += weights.experienceQuality + weights.logicalThinking;
    }

    if (analysisData.communicationAnalysis) {
      const commScore = (analysisData.communicationAnalysis as any).verbalFluency?.score || 70;
      totalScore += commScore * weights.communication;
      totalWeight += weights.communication;
    }

    // 残りの重みには平均スコアを使用
    const remainingWeight = 1 - totalWeight;
    if (remainingWeight > 0) {
      totalScore += 70 * remainingWeight; // デフォルト70点
    }

    return Math.round(totalScore);
  }

  /**
   * STARスコア計算
   */
  private calculateSTARScore(starAnalysis: STARCEvaluation): number {
    const situation = (starAnalysis.situation.clarity + starAnalysis.situation.relevance + starAnalysis.situation.specificity) / 3;
    const task = (starAnalysis.task.roleClarity + starAnalysis.task.challengeLevel + starAnalysis.task.scope) / 3;
    const action = (starAnalysis.action.initiative + starAnalysis.action.methodology + starAnalysis.action.collaboration) / 3;
    const result = (starAnalysis.result.quantification + starAnalysis.result.impact + starAnalysis.result.learning) / 3;
    const competency = (starAnalysis.competency.transferability + starAnalysis.competency.growthMindset) / 2;

    return (situation + task + action + result + competency) / 5;
  }

  /**
   * パーソナライズされたフィードバック生成
   */
  private async generatePersonalizedFeedback(data: any): Promise<any> {
    // Phase 1では簡単なルールベースのフィードバック
    const feedback = {
      strengths: [] as string[],
      improvements: [] as string[],
      actionItems: [] as string[]
    };

    // STARスコアに基づく分析
    if (data.starAnalysis?.length > 0) {
      const avgScores = this.calculateAverageSTARScores(data.starAnalysis);
      
      if (avgScores.situation > 80) {
        feedback.strengths.push('状況説明が明確で具体的');
      } else if (avgScores.situation < 60) {
        feedback.improvements.push('状況をより具体的に説明する');
        feedback.actionItems.push('STAR法の練習 - 状況設定を明確にする練習');
      }

      if (avgScores.result > 80) {
        feedback.strengths.push('成果を定量的に表現できている');
      } else if (avgScores.result < 60) {
        feedback.improvements.push('成果をより定量的に説明する');
        feedback.actionItems.push('過去の実績を数値化して整理する');
      }
    }

    // コミュニケーションスコアに基づく分析
    if (data.communicationAnalysis) {
      const commScore = data.communicationAnalysis.verbalFluency?.score || 70;
      
      if (commScore > 80) {
        feedback.strengths.push('コミュニケーション能力が高い');
      } else if (commScore < 60) {
        feedback.improvements.push('話し方の流暢性を向上させる');
        feedback.actionItems.push('音読練習とスピーチトレーニング');
      }
    }

    return feedback;
  }

  /**
   * 包括的レポート構築
   */
  private buildComprehensiveReport(data: any): ComprehensiveInterviewReport {
    const { interviewData, overallScore, feedback } = data;
    
    // Phase 1では基本的なレポート構造を作成
    const report: ComprehensiveInterviewReport = {
      metadata: {
        candidateName: '候補者',
        position: interviewData.metadata.position,
        interviewDate: new Date().toLocaleDateString('ja-JP'),
        duration: `${Math.round(interviewData.metadata.duration / 60)}分`,
        interviewType: 'behavioral'
      },
      overallAssessment: {
        score: overallScore,
        level: this.determineLevel(overallScore),
        recommendation: this.determineRecommendation(overallScore)
      },
      detailedEvaluation: this.createDetailedEvaluation(data),
      audioVideoAnalysis: this.createAudioVideoAnalysis(),
      actionPlan: this.createActionPlan(feedback),
      benchmarkComparison: this.createBenchmarkComparison(overallScore),
      summary: {
        keyStrengths: feedback.strengths || [],
        primaryImprovements: feedback.improvements || [],
        nextSteps: feedback.actionItems || []
      }
    };

    return report;
  }

  /**
   * モックレポート生成（開発用）
   */
  private generateMockReport(interviewData: InterviewData): ComprehensiveInterviewReport {
    return getEvaluationDataByProfile(interviewData.metadata.candidateProfile);
  }

  // ===== ヘルパーメソッド =====

  private getDefaultSTARAnalysis(): STARCEvaluation {
    return {
      situation: { clarity: 70, relevance: 70, specificity: 70 },
      task: { roleClarity: 70, challengeLevel: 70, scope: 70 },
      action: { initiative: 70, methodology: 70, collaboration: 70 },
      result: { quantification: 70, impact: 70, learning: 70 },
      competency: { transferability: 70, growthMindset: 70 }
    };
  }

  private getDefaultCommunicationAnalysis(): any {
    return {
      verbalFluency: {
        score: 70,
        fillerWords: 2.0,
        speechClarity: 70,
        vocabularyRichness: 70
      },
      structuring: {
        score: 70,
        logicalFlow: 70,
        introduction: 70,
        conclusion: 70
      },
      content: {
        relevance: 70,
        depth: 70,
        examples: 70
      }
    };
  }

  private calculateAverageSTARScores(starAnalyses: STARCEvaluation[]): any {
    const totals = {
      situation: 0,
      task: 0,
      action: 0,
      result: 0,
      competency: 0
    };

    starAnalyses.forEach(analysis => {
      totals.situation += (analysis.situation.clarity + analysis.situation.relevance + analysis.situation.specificity) / 3;
      totals.task += (analysis.task.roleClarity + analysis.task.challengeLevel + analysis.task.scope) / 3;
      totals.action += (analysis.action.initiative + analysis.action.methodology + analysis.action.collaboration) / 3;
      totals.result += (analysis.result.quantification + analysis.result.impact + analysis.result.learning) / 3;
      totals.competency += (analysis.competency.transferability + analysis.competency.growthMindset) / 2;
    });

    const count = starAnalyses.length;
    return {
      situation: totals.situation / count,
      task: totals.task / count,
      action: totals.action / count,
      result: totals.result / count,
      competency: totals.competency / count
    };
  }

  private determineLevel(score: number): 'entry' | 'junior' | 'mid' | 'senior' | 'expert' {
    if (score >= 90) return 'expert';
    if (score >= 80) return 'senior';
    if (score >= 70) return 'mid';
    if (score >= 60) return 'junior';
    return 'entry';
  }

  private determineRecommendation(score: number): 'strong_hire' | 'hire' | 'maybe' | 'no_hire' {
    if (score >= 85) return 'strong_hire';
    if (score >= 75) return 'hire';
    if (score >= 60) return 'maybe';
    return 'no_hire';
  }

  private createDetailedEvaluation(data: any): InterviewEvaluationFramework {
    // Phase 1では基本的な評価構造を作成
    const communication = data.communicationAnalysis || this.getDefaultCommunicationAnalysis();
    
    return {
      communication: {
        verbalFluency: {
          score: communication.verbalFluency.score,
          metrics: {
            fillerWords: communication.verbalFluency.fillerWords,
            speechRate: 160, // デフォルト値
            pauseFrequency: 1.0,
            sentenceCoherence: communication.verbalFluency.speechClarity
          }
        },
        structuring: {
          score: communication.structuring.score,
          metrics: {
            introductionClarity: communication.structuring.introduction,
            logicalFlow: communication.structuring.logicalFlow,
            conclusion: communication.structuring.conclusion,
            timeManagement: 75
          }
        },
        nonVerbal: {
          score: 75, // Phase 1では固定値
          metrics: {
            eyeContact: 75,
            facialExpression: 75,
            voiceTone: 75,
            energy: 75
          }
        }
      },
      logicalThinking: {
        problemAnalysis: {
          score: 75,
          metrics: {
            issueIdentification: 75,
            rootCauseAnalysis: 75,
            frameworkUsage: 70,
            dataEvidence: 75
          }
        },
        solutionDesign: {
          score: 75,
          metrics: {
            creativity: 70,
            feasibility: 80,
            comprehensiveness: 75,
            prioritization: 75
          }
        }
      },
      experienceQuality: {
        leadership: {
          score: 75,
          examples: [],
          metrics: {
            teamSize: 0,
            complexity: 75,
            outcomes: 75,
            influence: 75
          }
        },
        problemSolving: {
          score: 75,
          examples: [],
          metrics: {
            challengeLevel: 75,
            innovation: 70,
            stakeholders: 75,
            results: 75
          }
        }
      },
      culturalFit: {
        values: {
          score: 75,
          alignment: {
            companyMission: 75,
            teamDynamics: 75,
            workStyle: 75,
            growthMindset: 80
          }
        },
        motivation: {
          score: 75,
          factors: {
            intrinsic: 80,
            careerGoals: 75,
            companyKnowledge: 70,
            enthusiasm: 80
          }
        }
      },
      technicalCompetence: {
        domainKnowledge: {
          score: 75,
          areas: {
            industryTrends: 75,
            technicalDepth: 75,
            practicalApplication: 75,
            continuousLearning: 80
          }
        },
        roleSpecific: {
          score: 75,
          competencies: []
        }
      },
      adaptability: {
        changeManagement: {
          score: 75,
          examples: [],
          metrics: {
            flexibility: 75,
            resilience: 75,
            learningSpeed: 80,
            ambiguityTolerance: 70
          }
        }
      },
      professionalism: {
        presentation: {
          score: 80,
          metrics: {
            appearance: 80,
            punctuality: 85,
            preparation: 75,
            etiquette: 80
          }
        }
      }
    };
  }

  private createAudioVideoAnalysis(): any {
    return {
      audio: {
        segments: [],
        prosodyAnalysis: {
          averagePitch: 165,
          pitchVariation: 40,
          speakingRate: 160,
          pauseAnalysis: {
            totalPauses: 15,
            averagePauseLength: 0.8,
            inappropriatePauses: 2
          },
          fillerWords: {
            count: 12,
            frequency: 2.0,
            types: { "えー": 5, "あの": 4, "その": 3 }
          }
        },
        emotionAnalysis: {
          overallSentiment: 'positive' as const,
          confidence: 0.75,
          emotions: {
            enthusiasm: 75,
            nervousness: 30,
            confidence: 70,
            frustration: 15
          }
        }
      }
    };
  }

  private createActionPlan(feedback: any): any {
    return {
      immediate: [
        {
          action: "STAR法の基本練習",
          method: "過去の経験をSTAR法で整理する",
          metric: "構造化された回答ができる"
        }
      ],
      shortTerm: [
        {
          action: "コミュニケーション練習",
          method: "模擬面接を週1回実施",
          metric: "流暢で自然な受け答えができる",
          deadline: "2週間後"
        }
      ],
      longTerm: [
        {
          action: "専門知識の深化",
          method: "業界研究と関連書籍の読書",
          metric: "業界の最新トレンドを説明できる",
          deadline: "1ヶ月後"
        }
      ]
    };
  }

  private createBenchmarkComparison(score: number): any {
    return {
      industryAverage: 75,
      roleSpecificAverage: 73,
      experienceLevelAverage: 72,
      userPercentile: Math.min(95, Math.max(5, score - 20)),
      improvementPotential: Math.max(0, 90 - score)
    };
  }
}

/**
 * 評価エンジンのファクトリー関数
 */
export function createEvaluationEngine(config?: Partial<EvaluationConfig>): InterviewEvaluationEngine {
  return new InterviewEvaluationEngine(config);
}

/**
 * 簡単な評価実行関数
 */
export async function evaluateInterview(
  questions: string[],
  answers: string[],
  candidateProfile: CandidateProfile,
  options?: Partial<EvaluationConfig>
): Promise<ComprehensiveInterviewReport> {
  const engine = createEvaluationEngine(options);
  
  const interviewData = {
    questions,
    answers,
    metadata: {
      duration: 2400, // 40分
      position: candidateProfile.position,
      candidateProfile
    }
  };

  return engine.evaluateInterview(interviewData);
}