/**
 * エージェントリンク統合テスト用ユーティリティ
 * 候補者側とエージェント側のデータ連携テスト
 */

import { getInterviewLinkById, incrementAccessCount } from './agentLinkData';
import type { InterviewLink } from './agentLinkData';

// リンクアクセス時の統合処理
export const processLinkAccess = (linkId: string): {
  success: boolean;
  link?: InterviewLink;
  error?: string;
} => {
  try {
    const link = getInterviewLinkById(linkId);
    
    if (!link) {
      return {
        success: false,
        error: 'リンクが見つかりません。URLを確認してください。'
      };
    }
    
    // 有効期限チェック
    if (new Date(link.expiresAt) < new Date()) {
      return {
        success: false,
        error: 'このリンクは有効期限が切れています。エージェントに新しいリンクを依頼してください。'
      };
    }
    
    // ステータスチェック
    if (link.status !== 'active') {
      return {
        success: false,
        error: 'このリンクは無効になっています。'
      };
    }
    
    // アクセス回数を増やす
    incrementAccessCount(linkId);
    
    return {
      success: true,
      link
    };
  } catch (error) {
    return {
      success: false,
      error: 'システムエラーが発生しました。しばらく時間をおいてから再度お試しください。'
    };
  }
};

// リンクの状態表示用ヘルパー
export const getLinkStatusMessage = (link: InterviewLink): {
  message: string;
  color: 'green' | 'yellow' | 'red';
} => {
  const now = new Date();
  const expiresAt = new Date(link.expiresAt);
  const timeUntilExpiry = expiresAt.getTime() - now.getTime();
  const daysUntilExpiry = Math.ceil(timeUntilExpiry / (1000 * 60 * 60 * 24));
  
  if (link.status !== 'active') {
    return {
      message: 'このリンクは無効です',
      color: 'red'
    };
  }
  
  if (timeUntilExpiry <= 0) {
    return {
      message: '',
      color: ''
    };
  }
  
  if (daysUntilExpiry <= 1) {
    return {
      message: '本日中に期限切れ',
      color: 'red'
    };
  }
  
  if (daysUntilExpiry <= 3) {
    return {
      message: `残り${daysUntilExpiry}日`,
      color: 'yellow'
    };
  }
  
  return {
    message: `残り${daysUntilExpiry}日`,
    color: 'green'
  };
};

// 推奨シナリオのマッピング
export const mapRecommendedScenarios = (scenarioIds: string[]) => {
  const scenarioMap: { [key: string]: { name: string; priority: number } } = {
    "scenario_tech_senior": { name: "テック系シニアエンジニア面接", priority: 1 },
    "scenario_consulting_case": { name: "戦略コンサルタント ケース面接", priority: 1 },
    "scenario_pressure_test": { name: "高圧面接シミュレーション", priority: 2 }
  };
  
  return scenarioIds
    .map(id => ({ id, ...scenarioMap[id] }))
    .filter(scenario => scenario.name)
    .sort((a, b) => a.priority - b.priority);
};

// デバッグ用：リンクの詳細情報表示
export const getDebugInfo = (linkId: string) => {
  const link = getInterviewLinkById(linkId);
  if (!link) return null;
  
  return {
    linkId,
    companyName: link.companyInfo.name,
    position: link.companyInfo.position,
    agentName: link.createdByName,
    createdAt: new Date(link.createdAt).toLocaleDateString('ja-JP'),
    expiresAt: new Date(link.expiresAt).toLocaleDateString('ja-JP'),
    accessCount: link.accessCount,
    lastAccessed: link.lastAccessedAt 
      ? new Date(link.lastAccessedAt).toLocaleString('ja-JP')
      : 'なし',
    recommendedScenariosCount: link.recommendedScenarios.length,
    status: link.status
  };
};