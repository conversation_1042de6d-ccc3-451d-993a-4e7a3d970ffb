/**
 * ユーザビリティテスト用のモックアカウント
 * 3つのペルソナに対応した完全なテスト環境
 */

export interface TestAccount {
  email: string;
  password: string;
  role: 'candidate' | 'agent' | 'interviewer';
  profile: any;
  journeyScenario: string;
}

// テスト用アカウント情報
export const TEST_ACCOUNTS: Record<string, TestAccount> = {
  // 求職者: 田中美咲（26歳）
  candidate: {
    email: '<EMAIL>',
    password: 'demo1234',
    role: 'candidate',
    profile: {
      name: '田中 美咲',
      age: 26,
      currentStatus: '在職中（転職活動中）',
      experience: 'フロントエンドエンジニア3年',
      targetIndustry: 'IT・Web系',
      interviewAnxiety: 8, // 1-10スケール
      practiceHistory: [],
      strengths: ['技術力', 'コミュニケーション'],
      weaknesses: ['緊張しやすい', 'アドリブが苦手'],
      goals: [
        '緊張せずに話せるようになりたい',
        '自己PRを上手く伝えたい',
        '想定外の質問にも対応できるようになりたい'
      ]
    },
    journeyScenario: '初めての転職活動で、面接に強い不安を感じている。特に技術面接は経験があるが、人事面接での自己表現に自信がない。'
  },

  // エージェント: 佐藤健太（32歳）
  agent: {
    email: '<EMAIL>',
    password: 'demo1234',
    role: 'agent',
    profile: {
      name: '佐藤 健太',
      age: 32,
      company: '株式会社キャリアサポート',
      position: 'キャリアコンサルタント',
      experienceYears: 5,
      activeClients: 45,
      specialization: 'IT・エンジニア転職',
      averagePreparationTime: '2.5時間/人',
      painPoints: [
        '面接対策の時間が取れない',
        'フィードバックの一貫性が保てない',
        '求職者の成長度合いを可視化できない'
      ],
      goals: [
        '効率的な面接対策を提供したい',
        '客観的なフィードバックを提供したい',
        '求職者の内定率を向上させたい'
      ]
    },
    journeyScenario: '複数の求職者を同時にサポートしており、限られた時間で効果的な面接対策を提供する必要がある。AIツールで業務効率化を図りたい。'
  },

  // 面接官: 鈴木雅子（38歳）
  interviewer: {
    email: '<EMAIL>',
    password: 'demo1234',
    role: 'interviewer',
    profile: {
      name: '鈴木 雅子',
      age: 38,
      company: '株式会社テックイノベーション',
      position: '人事部 採用課長',
      experienceYears: 12,
      monthlyInterviews: 30,
      teamSize: 5,
      interviewTypes: ['一次面接', '最終面接', '役員面接の同席'],
      challenges: [
        '面接官によって評価基準がバラバラ',
        '内定辞退率が高い（30%）',
        '部下の面接スキル向上が進まない'
      ],
      goals: [
        '面接スキルを客観的に振り返りたい',
        '部下の面接官育成を効率化したい',
        '候補者により良い面接体験を提供したい'
      ]
    },
    journeyScenario: '自身の面接スキルを客観視し、改善したい。また、部下の面接官トレーニングにも活用し、採用品質の向上と内定承諾率の改善を目指している。'
  }
};

// ログイン処理のモック
export const mockLogin = async (email: string, password: string): Promise<{
  success: boolean;
  user?: TestAccount;
  error?: string;
}> => {
  // 実際のログイン処理をシミュレート（1秒の遅延）
  await new Promise(resolve => setTimeout(resolve, 1000));

  const account = Object.values(TEST_ACCOUNTS).find(
    acc => acc.email === email && acc.password === password
  );

  if (account) {
    return {
      success: true,
      user: account
    };
  }

  return {
    success: false,
    error: 'メールアドレスまたはパスワードが正しくありません'
  };
};

// ペルソナ別の初期データ
export const getInitialDataForPersona = (role: string) => {
  switch (role) {
    case 'candidate':
      return {
        availableInterviews: [
          {
            id: 'basic-1',
            title: '基本的な自己紹介',
            difficulty: 'easy',
            duration: 15,
            questions: 5
          },
          {
            id: 'behavioral-1',
            title: '行動面接対策',
            difficulty: 'medium',
            duration: 30,
            questions: 8
          }
        ],
        recommendations: [
          'まずは「基本的な自己紹介」から始めることをお勧めします',
          '深呼吸機能を使って、リラックスして臨みましょう'
        ]
      };

    case 'agent':
      return {
        clientList: [
          {
            id: 'client-1',
            name: '山田 太郎',
            status: '面接対策中',
            nextInterview: '2024-06-15',
            practiceCount: 3
          },
          {
            id: 'client-2',
            name: '佐々木 花子',
            status: '新規登録',
            nextInterview: null,
            practiceCount: 0
          }
        ],
        templates: [
          {
            id: 'template-1',
            name: 'IT企業向け面接',
            questionCount: 10
          }
        ]
      };

    case 'interviewer':
      return {
        recentSessions: [
          {
            id: 'session-1',
            date: '2024-06-10',
            candidateType: 'エンジニア',
            score: 7.5,
            insights: ['質問の深堀りが不足', '共感的な対応が好印象']
          }
        ],
        teamMembers: [
          {
            id: 'member-1',
            name: '田中 一郎',
            role: '面接官補佐',
            skillLevel: 'beginner'
          }
        ]
      };

    default:
      return {};
  }
};