/**
 * 実践的な面接練習用データベース
 * 業界別・難易度別・シチュエーション別の質問とシナリオ
 */

export interface AdvancedQuestion {
  id: string;
  text: string;
  category: string;
  difficulty: "medium" | "hard" | "expert";
  industry?: string;
  jobRole?: string;
  estimatedTime: number;
  followUpQuestions?: string[];
  evaluationCriteria: string[];
  scenarioType?: "normal" | "pressure" | "case_study" | "behavioral";
  context?: string;
  hints?: string[];
}

export interface InterviewScenario {
  id: string;
  name: string;
  description: string;
  industry: string;
  jobRole: string;
  difficulty: "medium" | "hard" | "expert";
  estimatedDuration: number;
  questions: AdvancedQuestion[];
  scenarioSettings: {
    pressureLevel: number; // 1-5
    timeConstraints: boolean;
    multipleInterviewers: boolean;
    interruptions: boolean;
  };
}

export interface AdvancedFeedback {
  id: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  
  // 詳細スコア
  technicalAccuracy?: number;
  communicationSkill: number;
  problemSolvingAbility: number;
  leadershipPotential?: number;
  culturalFit: number;
  stressHandling: number;
  
  // 業界特有の評価項目
  industrySpecificScores?: {
    [key: string]: number;
  };
  
  // 改善提案
  strengths: string[];
  improvements: string[];
  nextSteps: string[];
  
  // ベンチマーク
  industryBenchmark?: number;
  levelComparison?: "junior" | "mid" | "senior" | "expert";
}

// 業界別質問データベース
export const industryQuestions: { [industry: string]: AdvancedQuestion[] } = {
  "tech": [
    {
      id: "tech_001",
      text: "大規模なレガシーシステムのモダナイゼーションプロジェクトを任されました。技術的負債が蓄積した状況で、どのようなアプローチで進めますか？",
      category: "技術戦略",
      difficulty: "hard",
      industry: "tech",
      jobRole: "software_engineer",
      estimatedTime: 300,
      followUpQuestions: [
        "ステークホルダーとの合意形成はどう図りますか？",
        "リスク評価とミティゲーション戦略は？"
      ],
      evaluationCriteria: [
        "技術的知見の深さ",
        "プロジェクト管理能力",
        "リスク管理意識",
        "ステークホルダー調整力"
      ],
      scenarioType: "case_study",
      context: "あなたは主任エンジニアとして、創業10年のスタートアップで働いています。急速な成長により、システムの複雑性が増している状況です。",
      hints: [
        "段階的な移行戦略を考える",
        "ビジネス影響を最小化する方法",
        "チーム編成と責任分担"
      ]
    },
    {
      id: "tech_002", 
      text: "本番環境で重大な障害が発生しました。サービスが停止し、顧客からの問い合わせが殺到しています。初期対応から根本解決まで、どのように進めますか？",
      category: "障害対応",
      difficulty: "expert",
      industry: "tech",
      jobRole: "sre",
      estimatedTime: 240,
      followUpQuestions: [
        "再発防止策はどう策定しますか？",
        "チーム内外への情報共有はどうしますか？"
      ],
      evaluationCriteria: [
        "冷静な判断力",
        "優先順位付け能力",
        "コミュニケーション能力",
        "学習・改善意識"
      ],
      scenarioType: "pressure",
      context: "深夜2時、あなたは当直エンジニアとして障害アラートを受信しました。",
      hints: [
        "トリアージの重要性",
        "コミュニケーションプラン",
        "ポストモーテムの価値"
      ]
    }
  ],
  
  "consulting": [
    {
      id: "consulting_001",
      text: "クライアントは小売業界の老舗企業です。デジタル化が遅れており、売上が3年連続で減少しています。どのような戦略提案をしますか？",
      category: "戦略立案",
      difficulty: "hard",
      industry: "consulting", 
      jobRole: "strategy_consultant",
      estimatedTime: 360,
      followUpQuestions: [
        "実装優先順位はどう決めますか？",
        "ROIの測定方法は？",
        "組織変革の課題は？"
      ],
      evaluationCriteria: [
        "論理的思考力",
        "構造化能力",
        "ビジネス理解",
        "実行可能性の検討"
      ],
      scenarioType: "case_study",
      context: "創業80年の老舗デパートチェーン。従業員数3000人、年商500億円。",
      hints: [
        "MECE思考の活用",
        "Quick Winの特定",
        "Change Managementの重要性"
      ]
    }
  ],

  "finance": [
    {
      id: "finance_001",
      text: "新興市場への投資ファンド設立を検討しています。リスク評価から投資戦略まで、どのようなアプローチで進めますか？",
      category: "投資戦略",
      difficulty: "expert",
      industry: "finance",
      jobRole: "investment_banker",
      estimatedTime: 300,
      followUpQuestions: [
        "規制対応はどう考えますか？",
        "ポートフォリオ構成の考え方は？"
      ],
      evaluationCriteria: [
        "金融知識",
        "リスク分析力",
        "市場理解",
        "数値分析能力"
      ],
      scenarioType: "case_study",
      context: "東南アジア市場への投資ファンド（100億円規模）の設立を検討中。",
      hints: [
        "マクロ経済分析",
        "規制環境の把握",
        "分散投資の重要性"
      ]
    }
  ]
};

// 面接シナリオ定義
export const interviewScenarios: InterviewScenario[] = [
  {
    id: "scenario_tech_senior",
    name: "テック系シニアエンジニア面接",
    description: "大手テック企業のシニアエンジニアポジション向けの実践的面接。技術的深度と리더십能力を重視。",
    industry: "tech",
    jobRole: "senior_engineer",
    difficulty: "hard",
    estimatedDuration: 90,
    questions: industryQuestions["tech"],
    scenarioSettings: {
      pressureLevel: 3,
      timeConstraints: true,
      multipleInterviewers: false,
      interruptions: false
    }
  },
  
  {
    id: "scenario_consulting_case",
    name: "戦略コンサルタント ケース面接",
    description: "トップティアコンサルティングファームのケース面接。論理的思考と構造化能力を評価。",
    industry: "consulting", 
    jobRole: "consultant",
    difficulty: "expert",
    estimatedDuration: 120,
    questions: industryQuestions["consulting"],
    scenarioSettings: {
      pressureLevel: 4,
      timeConstraints: true,
      multipleInterviewers: false,
      interruptions: true
    }
  },

  {
    id: "scenario_pressure_test",
    name: "高圧面接シミュレーション",
    description: "意図的にプレッシャーをかける面接環境。ストレス下での判断力とコミュニケーション能力を評価。",
    industry: "general",
    jobRole: "general",
    difficulty: "expert", 
    estimatedDuration: 60,
    questions: [
      {
        id: "pressure_001",
        text: "なぜ前職を辞めたのですか？正直に答えてください。",
        category: "プレッシャー質問",
        difficulty: "hard",
        estimatedTime: 120,
        evaluationCriteria: [
          "誠実性",
          "ストレス耐性",
          "論理的説明能力"
        ],
        scenarioType: "pressure",
        context: "面接官は懐疑的な表情で、厳しい口調で質問します。",
        hints: [
          "正直かつ建設的な回答",
          "学習した点の強調",
          "前向きな姿勢の維持"
        ]
      }
    ],
    scenarioSettings: {
      pressureLevel: 5,
      timeConstraints: true,
      multipleInterviewers: true,
      interruptions: true
    }
  }
];

// 高度な分析機能
export const generateAdvancedFeedback = async (
  question: AdvancedQuestion,
  answer: string,
  scenario: InterviewScenario
): Promise<AdvancedFeedback> => {
  // 実際の実装では、より高度なAI分析を使用
  const analysisResult = await analyzeAdvancedAnswer(question, answer, scenario);
  
  return {
    id: `advanced_feedback_${Date.now()}`,
    questionId: question.id,
    candidateAnswer: answer,
    aiAnalysis: analysisResult.analysis,
    
    communicationSkill: analysisResult.scores.communication,
    problemSolvingAbility: analysisResult.scores.problemSolving,
    culturalFit: analysisResult.scores.culturalFit,
    stressHandling: analysisResult.scores.stressHandling,
    
    technicalAccuracy: question.industry === 'tech' ? analysisResult.scores.technical : undefined,
    leadershipPotential: question.category.includes('リーダー') ? analysisResult.scores.leadership : undefined,
    
    industrySpecificScores: analysisResult.industryScores,
    
    strengths: analysisResult.strengths,
    improvements: analysisResult.improvements,
    nextSteps: analysisResult.nextSteps,
    
    industryBenchmark: analysisResult.benchmark,
    levelComparison: analysisResult.level
  };
};

// モック分析関数（実際の実装では外部APIを使用）
const analyzeAdvancedAnswer = async (
  question: AdvancedQuestion,
  answer: string,
  scenario: InterviewScenario
): Promise<any> => {
  // シミュレーション用の遅延
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  return {
    analysis: "高度な分析による詳細フィードバック。回答の構造化、論理性、業界知識の深さを評価しました。",
    scores: {
      communication: Math.floor(Math.random() * 3) + 7, // 7-10
      problemSolving: Math.floor(Math.random() * 3) + 6, // 6-9
      culturalFit: Math.floor(Math.random() * 2) + 8, // 8-10
      stressHandling: Math.floor(Math.random() * 4) + 6, // 6-10
      technical: question.industry === 'tech' ? Math.floor(Math.random() * 3) + 7 : undefined,
      leadership: Math.floor(Math.random() * 3) + 6
    },
    industryScores: {
      [scenario.industry]: Math.floor(Math.random() * 3) + 7
    },
    strengths: [
      "論理的な構造化が優秀",
      "具体例の使用が効果的", 
      "専門知識の深さが印象的"
    ],
    improvements: [
      "より簡潔な表現を心がける",
      "数値的根拠の提示を強化",
      "ステークホルダー視点の追加"
    ],
    nextSteps: [
      "ケーススタディ練習を継続",
      "業界トレンドの調査強化",
      "プレゼンテーション스킬の向上"
    ],
    benchmark: Math.floor(Math.random() * 20) + 75, // 75-95
    level: ["junior", "mid", "senior"][Math.floor(Math.random() * 3)]
  };
};

// 実践練習用ユーティリティ
export const getQuestionsByIndustry = (industry: string): AdvancedQuestion[] => {
  return industryQuestions[industry] || [];
};

export const getScenarioById = (scenarioId: string): InterviewScenario | null => {
  return interviewScenarios.find(s => s.id === scenarioId) || null;
};

export const getRecommendedScenarios = (
  experience: string,
  targetIndustry: string
): InterviewScenario[] => {
  return interviewScenarios.filter(s => 
    s.industry === targetIndustry || s.industry === "general"
  );
};