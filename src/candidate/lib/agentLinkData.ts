/**
 * エージェントリンク面接用データ構造とモックデータ
 * エージェントが企業情報を設定して求職者に送るリンク機能
 */

import DataService, { 
  type CompanyInfo, 
  type AgentInfo, 
  type InterviewLink,
  COMPANIES,
  AGENTS
} from './unified-data';

// 既存のインターフェースを統一データシステムから再エクスポート
export type { CompanyInfo, AgentInfo, InterviewLink };

// 統一データシステムから企業データとエージェントデータを取得
export const mockCompanies: CompanyInfo[] = DataService.getAllCompanies();
export const mockAgents: AgentInfo[] = DataService.getAllAgents();

// モック面接リンクデータ
export const mockInterviewLinks: InterviewLink[] = [
  {
    id: "link_001",
    companyInfo: DataService.getCompanyById("company_001")!, // テクノロジーアドバンス
    agentNotes: `技術面接では特にアルゴリズムとシステム設計の知識が重視されます。
    
面接官の特徴：
- CTO：技術的深度を重視、論理的説明を求める
- エンジニアリングマネージャー：チームワークと問題解決プロセスを評価
- シニアエンジニア：実装経験と技術的判断力をチェック

注意点：
- コーディング課題が出る可能性が高い
- 「なぜその技術選択をしたか」の理由を明確に説明できるように
- 過去のプロジェクトでの失敗体験とそこから学んだことを準備`,
    agentRecommendation: "技術的な深度が求められるため、「テック系シニアエンジニア面接」での練習を強く推奨します。特に技術的判断力とシステム設計能力を重点的に練習してください。",
    recommendedScenarios: ["scenario_tech_senior"],
    createdBy: "agent_001",
    createdByName: "佐藤健太",
    candidateEmail: "<EMAIL>",
    createdAt: "2025-06-10T10:00:00Z",
    expiresAt: "2025-06-17T23:59:59Z",
    status: "active",
    accessCount: 0
  },
  {
    id: "link_002",
    companyInfo: DataService.getCompanyById("company_002")!, // グローバルコンサルティング
    agentNotes: `ケース面接が中心となります。

面接官の特徴：
- パートナー：戦略的思考と構造化能力を評価
- プリンシパル：論理的思考プロセスと仮説構築力をチェック  
- コンサルタント：計算能力とプレゼンテーション力を確認

想定されるケース：
- 小売業の売上減少要因分析
- 新規事業の市場参入戦略
- M&Aの財務的妥当性評価

準備のポイント：
- フレームワーク思考（MECE、3C、4P等）
- 数値感覚と概算計算スキル
- 仮説思考と論理的説明力`,
    agentRecommendation: "ケース面接対策が必須です。「戦略コンサルタント ケース面接」で練習し、構造化思考と論理的説明力を磨いてください。時間制限内での思考整理が重要です。",
    recommendedScenarios: ["scenario_consulting_case"],
    createdBy: "agent_001", 
    createdByName: "佐藤健太",
    candidateEmail: "<EMAIL>",
    createdAt: "2025-06-10T14:30:00Z",
    expiresAt: "2025-06-17T23:59:59Z",
    status: "active",
    accessCount: 2,
    lastAccessedAt: "2025-06-11T09:15:00Z"
  },
  {
    id: "link_003",
    companyInfo: DataService.getCompanyById("company_005")!, // 伝統商社
    agentNotes: `老舗商社の面接では人柄と継続力が重視されます。

面接官の特徴：
- 役員：厳格で伝統を重視、ストレス耐性をチェック
- 部長：体育会系の雰囲気、チームワークと根性を評価
- 課長：実務能力と顧客対応力を確認

よくある質問：
- 「なぜ商社を選んだのか」
- 「海外勤務への意欲」
- 「困難な状況での対処方法」
- 「長期的なキャリア目標」

面接の雰囲気：
- やや圧迫的な質問あり
- 礼儀作法と敬語の使い方をチェック
- 継続力と忍耐力を重視`,
    agentRecommendation: "この企業は面接でストレス耐性を確認する傾向があります。「高圧面接シミュレーション」での練習を必ず行い、プレッシャー下でも冷静に回答できるよう準備してください。",
    recommendedScenarios: ["scenario_pressure_test"],
    createdBy: "agent_002",
    createdByName: "田中美咲", 
    candidateEmail: "<EMAIL>",
    createdAt: "2025-06-09T16:45:00Z",
    expiresAt: "2025-06-16T23:59:59Z",
    status: "active",
    accessCount: 1,
    lastAccessedAt: "2025-06-10T20:30:00Z"
  }
];

// ユーティリティ関数
export const getInterviewLinkById = (linkId: string): InterviewLink | null => {
  return mockInterviewLinks.find(link => link.id === linkId) || null;
};

export const getActiveLinksForAgent = (agentId: string): InterviewLink[] => {
  return mockInterviewLinks.filter(link => 
    link.createdBy === agentId && link.status === 'active'
  );
};

export const isLinkExpired = (link: InterviewLink): boolean => {
  return new Date(link.expiresAt) < new Date();
};

export const incrementAccessCount = (linkId: string): void => {
  const link = getInterviewLinkById(linkId);
  if (link) {
    link.accessCount++;
    link.lastAccessedAt = new Date().toISOString();
  }
};

export const generateNewLinkId = (): string => {
  return `link_${Math.random().toString(36).substring(2, 15)}`;
};

// シナリオ推奨メッセージ生成（統一データシステムを使用）
export const getScenarioRecommendationMessage = (scenarioId: string): string => {
  const scenario = DataService.getScenarioById(scenarioId);
  if (!scenario) return "この企業の面接スタイルに適した練習を行ってください。";
  
  return scenario.description;
};

// シナリオ名取得（統一データシステムを使用）
export const getScenarioName = (scenarioId: string): string => {
  return DataService.getScenarioDisplayName(scenarioId);
};