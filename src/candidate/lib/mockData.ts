/**
 * mensetsu-kun 候補者画面用 モックデータ
 * UI整理・承認フェーズ用の一元化されたモックデータ
 */

export interface Question {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number; // 想定回答時間（秒）
}

export interface Interview {
  id: string;
  candidateName: string;
  position: string;
  companyName: string;
  status: "pending" | "in_progress" | "completed" | "cancelled";
  createdAt: string;
  scheduledAt?: string;
  questions: Question[];
}

export interface Feedback {
  id: string;
  interviewId: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  emotionScore?: number;
  confidenceScore?: number;
  relevanceScore?: number;
  clarityScore?: number; // 1-10 明瞭性スコア
  overall: number; // 1-10 総合スコア
  overallRating: number; // 1-5 (後方互換性のため残す)
  suggestions: string[];
  timestamp: string;
}

export interface AudioData {
  id: string;
  filename: string;
  duration: number;
  transcription: string;
  confidence: number;
}

// ========== モックデータ定義 ==========

export const mockQuestions: Question[] = [
  {
    id: "q1",
    text: "自己紹介をお願いします。経歴と志望動機を含めて2分程度でお話しください。",
    category: "基本情報",
    difficulty: "easy",
    estimatedTime: 120
  },
  {
    id: "q2", 
    text: "これまでの経験で最も困難だった課題と、それをどのように解決したか教えてください。",
    category: "経験・スキル",
    difficulty: "medium",
    estimatedTime: 180
  },
  {
    id: "q3",
    text: "チームで働く際に重視することは何ですか？具体的なエピソードがあれば教えてください。",
    category: "コミュニケーション",
    difficulty: "medium", 
    estimatedTime: 150
  },
  {
    id: "q4",
    text: "5年後のキャリアビジョンを教えてください。この会社でどのような貢献をしたいですか？",
    category: "将来性",
    difficulty: "medium",
    estimatedTime: 180
  },
  {
    id: "q5",
    text: "技術的な課題に直面した時、どのようなアプローチで解決に取り組みますか？",
    category: "技術力",
    difficulty: "hard",
    estimatedTime: 200
  }
];

export const mockFeedbacks: Feedback[] = [
  {
    id: "feedback_001",
    interviewId: "interview_001",
    questionId: "q1",
    candidateAnswer: "私は3年間フロントエンド開発に従事し、ReactとTypeScriptを中心に業務を行ってきました。特にユーザビリティ向上に興味があり、御社のプロダクトの品質向上に貢献したいと考えています。",
    aiAnalysis: "自己紹介は簡潔で要点が整理されています。技術スタックと志望動機が明確に述べられており、好印象です。",
    emotionScore: 8.2,
    confidenceScore: 7.5,
    relevanceScore: 9.0,
    overall: 8,
    overallRating: 4,
    suggestions: [
      "より具体的な成果や数値を含めると説得力が増します",
      "企業研究の深さをアピールできるとさらに良いでしょう"
    ],
    timestamp: "2025-06-09T14:05:00Z"
  },
  {
    id: "feedback_002",
    interviewId: "interview_001", 
    questionId: "q2",
    candidateAnswer: "レガシーシステムのリニューアルプロジェクトで、要件定義が曖昧な状況がありました。ステークホルダーとの定期的な会議を設定し、プロトタイプを作成して認識を合わせることで解決しました。",
    aiAnalysis: "具体的な課題と解決策が述べられており、問題解決能力の高さが伺えます。コミュニケーション能力も評価できます。",
    emotionScore: 7.8,
    confidenceScore: 8.2,
    relevanceScore: 8.7,
    overall: 8,
    overallRating: 4,
    suggestions: [
      "結果や成果をより詳しく説明すると良いでしょう",
      "チームメンバーとの協力についても触れると完璧です"
    ],
    timestamp: "2025-06-09T14:08:00Z"
  }
];

// ========== データアクセス関数 ==========

/**
 * 質問データを取得する
 */
export const getQuestions = async (): Promise<Question[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockQuestions;
};

/**
 * 特定の質問を取得する
 */
export const getQuestionById = async (id: string): Promise<Question | null> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockQuestions.find(q => q.id === id) || null;
};

/**
 * フィードバックデータを取得する
 */
export const getFeedbacks = async (interviewId?: string): Promise<Feedback[]> => {
  await new Promise(resolve => setTimeout(resolve, 600));
  if (interviewId) {
    return mockFeedbacks.filter(f => f.interviewId === interviewId);
  }
  return mockFeedbacks;
};

/**
 * 音声解析結果をシミュレート
 */
export const analyzeAudio = async (audioFile: File): Promise<{ text: string; feedback: Feedback }> => {
  // サンプル回答の場合は解析時間を短縮
  const isSmallFile = audioFile.size <= 1024;
  const delay = isSmallFile ? 100 : 1000; // サンプルは0.1秒、実際は1秒
  await new Promise(resolve => setTimeout(resolve, delay));
  
  // モック解析結果
  const transcription = "ありがとうございます。私は○○大学を卒業後、△△会社で3年間フロントエンド開発に携わってきました。特にReactとTypeScriptを使用したSPA開発に従事し、ユーザビリティの向上に取り組んでまいりました。";
  
  const feedback: Feedback = {
    id: `feedback_${Date.now()}`,
    interviewId: "current_interview",
    questionId: "current_question", 
    candidateAnswer: transcription,
    aiAnalysis: "回答は明確で構成されており、技術的な経験が適切に説明されています。具体的な技術スタックと業務内容が述べられており、信頼性が高い回答です。",
    emotionScore: 7.8,
    confidenceScore: 8.1,
    relevanceScore: 8.5,
    clarityScore: 8.3,
    overall: 8.2,
    overallRating: 4,
    suggestions: [
      "より具体的なプロジェクトの成果や数値があると説得力が増します",
      "技術選択の理由や学習過程についても言及すると良いでしょう"
    ],
    timestamp: new Date().toISOString()
  };
  
  return { text: transcription, feedback };
};

/**
 * 音声合成をシミュレート（実際は音声ファイルを返す）
 */
export const synthesizeText = async (text: string): Promise<Blob> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 実際の実装では音声ファイルを返すが、ここではダミーのBlobを返す
  const dummyAudio = new Blob(["dummy audio data"], { type: "audio/wav" });
  return dummyAudio;
};