/**
 * 統一データ管理システム
 * 将来的にAPI呼び出しに置き換える際の単一エントリーポイント
 */

// ===== 型定義 =====

export interface CompanyInfo {
  id: string;
  name: string;
  industry: string;
  position: string;
  description: string;
  interviewStyle: string;
  culture: string;
  location: string;
  employeeCount: string;
  founded: string;
  website?: string;
}

export interface AgentInfo {
  id: string;
  name: string;
  email: string;
  company: string;
  specialization: string[];
}

export interface ScenarioConfig {
  id: string;
  name: string;
  description: string;
  industry: string;
  jobRole: string;
  difficulty: "medium" | "hard" | "expert";
  estimatedDuration: number;
  scenarioSettings: {
    pressureLevel: number; // 1-5
    timeConstraints: boolean;
    multipleInterviewers: boolean;
    interruptions: boolean;
  };
  tags: string[];
}

export interface QuestionData {
  id: string;
  text: string;
  category: string;
  difficulty: "medium" | "hard" | "expert";
  industry?: string;
  jobRole?: string;
  estimatedTime: number;
  followUpQuestions?: string[];
  evaluationCriteria: string[];
  scenarioType?: "normal" | "pressure" | "case_study" | "behavioral";
  context?: string;
  hints?: string[];
}

export interface InterviewLink {
  id: string;
  companyInfo: CompanyInfo;
  agentNotes: string;
  agentRecommendation: string;
  recommendedScenarios: string[];
  createdBy: string;
  createdByName: string;
  candidateEmail?: string;
  createdAt: string;
  expiresAt: string;
  status: 'active' | 'used' | 'expired';
  accessCount: number;
  lastAccessedAt?: string;
}

export interface PracticeResult {
  id: string;
  linkId: string;
  scenarioId: string;
  companyName: string;
  position: string;
  practiceDate: string;
  feedback: AdvancedFeedback[]; // 詳細フィードバック配列
  overallScore: number;
  communicationScore: number;
  technicalScore?: number;
  duration: number;
  candidateName?: string;
  candidateEmail?: string;
}

// 高度な分析フィードバック（候補者アプリからの詳細分析）
export interface AdvancedFeedback {
  id: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  communicationSkill: number;
  problemSolvingAbility: number;
  culturalFit: number;
  stressHandling: number;
  technicalAccuracy?: number;
  leadershipPotential?: number;
  strengths: string[];
  improvements: string[];
  nextSteps: string[];
  industryBenchmark?: number;
  levelComparison?: "junior" | "mid" | "senior" | "expert";
}

// ===== マスターデータ =====

export const COMPANIES: CompanyInfo[] = [
  {
    id: "company_001",
    name: "株式会社テクノロジーアドバンス",
    industry: "IT・ソフトウェア",
    position: "シニアエンジニア",
    description: "AIと機械学習に特化したスタートアップ企業。急成長中で技術力と問題解決能力を重視する文化があります。",
    interviewStyle: "技術的深度と論理的思考力を重視。コーディング課題やシステム設計に関する質問が中心。",
    culture: "フラットな組織構造、チャレンジ精神を重視、リモートワーク推進",
    location: "東京都渋谷区",
    employeeCount: "120名",
    founded: "2019年",
    website: "https://tech-advance.co.jp"
  },
  {
    id: "company_002",
    name: "グローバルコンサルティング株式会社",
    industry: "コンサルティング",
    position: "ビジネスアナリスト",
    description: "戦略コンサルティングファーム。大手企業のDX支援と業務改革を手がける。論理的思考と構造化スキルが必須。",
    interviewStyle: "ケース面接中心。論理的思考力、構造化能力、プレゼンテーション力を評価。",
    culture: "成果主義、グローバル志向、継続的学習を重視",
    location: "東京都千代田区",
    employeeCount: "450名",
    founded: "2005年",
    website: "https://global-consulting.co.jp"
  },
  {
    id: "company_003",
    name: "メガバンクシステムズ",
    industry: "金融・銀行",
    position: "システムエンジニア",
    description: "大手銀行のシステム部門。金融システムの安定性とセキュリティを最重要視する保守的な企業文化。",
    interviewStyle: "安定性重視、リスク管理意識、チームワークを評価。やや保守的で慎重な判断を求める。",
    culture: "安定性重視、チームワーク、継続的改善、コンプライアンス重視",
    location: "東京都大手町",
    employeeCount: "2,800名",
    founded: "1985年",
    website: "https://megabank-sys.co.jp"
  },
  {
    id: "company_004",
    name: "株式会社イノベーションラボ",
    industry: "スタートアップ・ベンチャー",
    position: "プロダクトマネージャー",
    description: "EdTech領域のスタートアップ。教育のデジタル化を推進。速いペースでの意思決定と実行力が求められる。",
    interviewStyle: "スピード感、実行力、創造性を重視。実際のプロダクト課題に対する解決策提案が中心。",
    culture: "スピード重視、実験的アプローチ、失敗を恐れない、顧客第一",
    location: "東京都目黒区",
    employeeCount: "85名",
    founded: "2021年",
    website: "https://innovation-lab.jp"
  },
  {
    id: "company_005",
    name: "伝統商社株式会社",
    industry: "商社・貿易",
    position: "営業マネージャー",
    description: "創業100年の老舗商社。海外取引が多く、長期的な関係構築を重視。面接では人柄と継続力を評価される。",
    interviewStyle: "人柄重視、継続力、コミュニケーション能力を評価。やや高圧的な質問でストレス耐性も確認。",
    culture: "伝統と革新の両立、長期的関係重視、海外志向、体育会系気質",
    location: "東京都中央区",
    employeeCount: "1,200名",
    founded: "1924年",
    website: "https://traditional-trading.co.jp"
  }
];

export const AGENTS: AgentInfo[] = [
  {
    id: "agent_001",
    name: "佐藤健太",
    email: "<EMAIL>",
    company: "キャリアサポート株式会社",
    specialization: ["IT・エンジニア", "コンサルティング", "金融"]
  },
  {
    id: "agent_002",
    name: "田中美咲",
    email: "<EMAIL>",
    company: "キャリアサポート株式会社",
    specialization: ["営業・マーケティング", "スタートアップ", "商社"]
  }
];

export const SCENARIOS: ScenarioConfig[] = [
  {
    id: "scenario_basic",
    name: "基本的な面接練習",
    description: "一般的な企業面接の練習。コミュニケーション能力と基本的な受け答えを重視。",
    industry: "general",
    jobRole: "general",
    difficulty: "medium",
    estimatedDuration: 45,
    scenarioSettings: {
      pressureLevel: 2,
      timeConstraints: false,
      multipleInterviewers: false,
      interruptions: false
    },
    tags: ["基本面接", "コミュニケーション", "自己紹介"]
  },
  {
    id: "scenario_tech_senior",
    name: "テック系シニアエンジニア面接",
    description: "大手テック企業のシニアエンジニアポジション向けの実践的面接。技術的深度とリーダーシップ能力を重視。",
    industry: "tech",
    jobRole: "software_engineer",
    difficulty: "hard",
    estimatedDuration: 60,
    scenarioSettings: {
      pressureLevel: 3,
      timeConstraints: true,
      multipleInterviewers: false,
      interruptions: false
    },
    tags: ["技術戦略", "システム設計", "リーダーシップ"]
  },
  {
    id: "scenario_consulting_case",
    name: "戦略コンサルタント ケース面接",
    description: "トップティアコンサルティングファームのケース面接。論理的思考と構造化能力を評価。",
    industry: "consulting",
    jobRole: "consultant",
    difficulty: "expert",
    estimatedDuration: 120,
    scenarioSettings: {
      pressureLevel: 4,
      timeConstraints: true,
      multipleInterviewers: false,
      interruptions: true
    },
    tags: ["ケース分析", "論理的思考", "プレゼンテーション"]
  },
  {
    id: "scenario_pressure_test",
    name: "高圧面接シミュレーション",
    description: "意図的にプレッシャーをかける面接環境。ストレス下での判断力とコミュニケーション能力を評価。",
    industry: "general",
    jobRole: "general",
    difficulty: "expert",
    estimatedDuration: 60,
    scenarioSettings: {
      pressureLevel: 5,
      timeConstraints: false,
      multipleInterviewers: false,
      interruptions: false
    },
    tags: ["プレッシャー対応", "ストレス耐性", "誠実性"]
  }
];

export const QUESTIONS: QuestionData[] = [
  {
    id: "basic_001",
    text: "まずは簡単に自己紹介をお願いします。",
    category: "自己紹介",
    difficulty: "medium",
    industry: "general",
    jobRole: "general",
    estimatedTime: 120,
    followUpQuestions: [
      "その経験の中で最も印象に残っていることは何ですか？",
      "なぜこの業界に興味を持ったのですか？"
    ],
    evaluationCriteria: [
      "コミュニケーション能力",
      "自己表現力",
      "論理的構成",
      "熱意と意欲"
    ],
    scenarioType: "normal",
    context: "リラックスした雰囲気で、あなた自身について教えてください。",
    hints: [
      "簡潔に要点をまとめる",
      "経験と今後の展望を含める",
      "相手の目を見て話す"
    ]
  },
  {
    id: "basic_002", 
    text: "志望動機を教えてください。なぜ弊社を希望されるのですか？",
    category: "志望動機",
    difficulty: "medium",
    industry: "general",
    jobRole: "general",
    estimatedTime: 180,
    followUpQuestions: [
      "他社と比較してどのような点に魅力を感じましたか？",
      "入社後はどのような貢献をしたいと考えていますか？"
    ],
    evaluationCriteria: [
      "企業研究の深さ",
      "動機の明確さ",
      "将来ビジョン",
      "熱意"
    ],
    scenarioType: "normal",
    context: "企業への理解と入社への意欲について確認させていただきます。",
    hints: [
      "企業の特徴を具体的に言及する",
      "自分の経験と関連付ける",
      "将来のキャリアプランを示す"
    ]
  },
  {
    id: "basic_003",
    text: "これまでの経験の中で困難を乗り越えた体験について教えてください。",
    category: "経験・困難克服",
    difficulty: "medium", 
    industry: "general",
    jobRole: "general",
    estimatedTime: 240,
    followUpQuestions: [
      "その時の判断基準は何でしたか？",
      "同じような状況が起きた場合、今度はどう対処しますか？"
    ],
    evaluationCriteria: [
      "問題解決能力",
      "粘り強さ",
      "学習能力",
      "論理的思考"
    ],
    scenarioType: "behavioral",
    context: "具体的なエピソードを通して、あなたの課題解決力を教えてください。",
    hints: [
      "STAR法（状況・課題・行動・結果）で構成する",
      "自分の役割を明確にする",
      "学んだことや成長を強調する"
    ]
  },
  {
    id: "tech_001",
    text: "大",
    category: "技術戦略",
    difficulty: "hard",
    industry: "tech",
    jobRole: "software_engineer",
    estimatedTime: 300,
    followUpQuestions: [
      "ステークホルダーとの合意形成はどう図りますか？",
      "リスク評価とミティゲーション戦略は？"
    ],
    evaluationCriteria: [
      "技術的知見の深さ",
      "プロジェクト管理能力",
      "リスク管理意識",
      "ステークホルダー調整力"
    ],
    scenarioType: "case_study",
    context: "あなたは主任エンジニアとして、創業10年のスタートアップで働いています。急速な成長により、システムの複雑性が増している状況です。",
    hints: [
      "段階的な移行戦略を考える",
      "ビジネス影響を最小化する方法",
      "チーム編成と責任分担"
    ]
  },
  {
    id: "consulting_001",
    text: "小売業界の大手チェーンの売上が3年連続で減少しています。この問題を解決するための戦略を提案してください。",
    category: "ケース分析",
    difficulty: "expert",
    industry: "consulting",
    jobRole: "consultant",
    estimatedTime: 1800,
    followUpQuestions: [
      "実行における主なリスクは何ですか？",
      "ROI試算はどのように行いますか？"
    ],
    evaluationCriteria: [
      "論理的思考力",
      "構造化能力",
      "創意工夫",
      "計算能力"
    ],
    scenarioType: "case_study",
    context: "クライアントは従業員数5万人の小売チェーンです。主力商品は衣料品で、全国に500店舗を展開しています。",
    hints: [
      "売上減少の要因分析を構造化する",
      "市場環境と内部要因を分けて考える",
      "定量的な根拠を示す"
    ]
  },
  {
    id: "pressure_001",
    text: "なぜ前職を辞めたのですか？正直に答えてください。",
    category: "プレッシャー質問",
    difficulty: "hard",
    industry: "general",
    jobRole: "general",
    estimatedTime: 120,
    evaluationCriteria: [
      "誠実性",
      "ストレス耐性",
      "論理的説明能力"
    ],
    scenarioType: "pressure",
    context: "面接官は懐疑的な表情で、厳しい口調で質問します。",
    hints: [
      "正直かつ建設的な回答",
      "学習した点の強調",
      "前向きな姿勢の維持"
    ]
  }
];

// ===== データアクセス関数（将来的にAPI呼び出しに置き換え） =====

export const DataService = {
  // 企業情報
  getCompanyById: (id: string): CompanyInfo | null => {
    return COMPANIES.find(company => company.id === id) || null;
  },

  getCompaniesByIndustry: (industry: string): CompanyInfo[] => {
    return COMPANIES.filter(company => company.industry === industry);
  },

  getAllCompanies: (): CompanyInfo[] => {
    return [...COMPANIES];
  },

  // エージェント情報
  getAgentById: (id: string): AgentInfo | null => {
    return AGENTS.find(agent => agent.id === id) || null;
  },

  getAllAgents: (): AgentInfo[] => {
    return [...AGENTS];
  },

  // シナリオ情報
  getScenarioById: (id: string): ScenarioConfig | null => {
    return SCENARIOS.find(scenario => scenario.id === id) || null;
  },

  getScenariosByIndustry: (industry: string): ScenarioConfig[] => {
    return SCENARIOS.filter(scenario => scenario.industry === industry || scenario.industry === 'general');
  },

  getScenariosByDifficulty: (difficulty: "medium" | "hard" | "expert"): ScenarioConfig[] => {
    return SCENARIOS.filter(scenario => scenario.difficulty === difficulty);
  },

  getAllScenarios: (): ScenarioConfig[] => {
    return [...SCENARIOS];
  },

  // 質問情報
  getQuestionById: (id: string): QuestionData | null => {
    return QUESTIONS.find(question => question.id === id) || null;
  },

  getQuestionsByScenario: (scenarioId: string): QuestionData[] => {
    const scenario = DataService.getScenarioById(scenarioId);
    if (!scenario) return [];
    
    const filteredQuestions = QUESTIONS.filter(question => {
      const industryMatch = question.industry === scenario.industry || scenario.industry === 'general' || question.industry === undefined;
      const jobRoleMatch = question.jobRole === scenario.jobRole || scenario.jobRole === 'general' || question.jobRole === undefined;
      return industryMatch && jobRoleMatch;
    });
    
    return filteredQuestions;
  },

  getQuestionsByCategory: (category: string): QuestionData[] => {
    return QUESTIONS.filter(question => question.category === category);
  },

  getAllQuestions: (): QuestionData[] => {
    return [...QUESTIONS];
  },

  // 面接結果管理（モックデータ - 実際はAPIから取得）
  PRACTICE_RESULTS: [
    {
      id: "result_001",
      linkId: "link_001",
      scenarioId: "scenario_tech_senior",
      companyName: "株式会社テクノロジーアドバンス",
      position: "シニアエンジニア",
      practiceDate: "2025-06-14T10:30:00Z",
      candidateName: "山田太郎",
      candidateEmail: "<EMAIL>",
      overallScore: 8.2,
      communicationScore: 8.5,
      technicalScore: 8.0,
      duration: 45,
      feedback: [
        {
          id: "feedback_001_1",
          questionId: "tech_001",
          candidateAnswer: "レガシーシステムのモダナイゼーションでは、まず現状の技術的負債を詳細に分析し、段階的な移行計画を策定します。ビジネス影響を最小化するため、ストラングラーパターンを採用し...",
          aiAnalysis: "技術的な知見が深く、構造化された回答でした。特にストラングラーパターンの言及は適切で、実務経験の豊富さが感じられます。",
          communicationSkill: 9,
          problemSolvingAbility: 8,
          culturalFit: 8,
          stressHandling: 7,
          technicalAccuracy: 9,
          leadershipPotential: 8,
          strengths: [
            "技術的知見が非常に深い",
            "段階的アプローチが適切",
            "ビジネス影響を考慮した設計"
          ],
          improvements: [
            "より具体的な期間設定があると良い",
            "コスト面の言及が不足",
            "チーム編成についても触れるべき"
          ],
          nextSteps: [
            "アーキテクチャ設計の詳細練習",
            "プロジェクト管理スキルの向上",
            "ステークホルダー調整力の強化"
          ],
          industryBenchmark: 85,
          levelComparison: "senior"
        },
        {
          id: "feedback_001_2", 
          questionId: "tech_002",
          candidateAnswer: "チーム開発においては、まずコードレビューの文化を確立し、ペアプログラミングやモブプログラミングを導入します...",
          aiAnalysis: "チーム開発に対する理解が深く、具体的な施策を提示できています。リーダーシップの資質が感じられる回答です。",
          communicationSkill: 8,
          problemSolvingAbility: 9,
          culturalFit: 9,
          stressHandling: 8,
          technicalAccuracy: 8,
          leadershipPotential: 9,
          strengths: [
            "チーム開発への理解が深い",
            "具体的な改善策を提示",
            "文化醸成の重要性を理解"
          ],
          improvements: [
            "メトリクスによる効果測定の言及",
            "個人のスキル差への対応",
            "ツールチェーンの最適化"
          ],
          nextSteps: [
            "チームビルディング手法の学習",
            "メンタリングスキルの向上",
            "組織改革事例の研究"
          ],
          industryBenchmark: 88,
          levelComparison: "senior"
        }
      ]
    },
    {
      id: "result_002",
      linkId: "link_002", 
      scenarioId: "scenario_consulting_case",
      companyName: "グローバルコンサルティング株式会社",
      position: "ビジネスアナリスト",
      practiceDate: "2025-06-13T14:00:00Z",
      candidateName: "鈴木花子",
      candidateEmail: "<EMAIL>",
      overallScore: 7.8,
      communicationScore: 8.0,
      duration: 90,
      feedback: [
        {
          id: "feedback_002_1",
          questionId: "consulting_001",
          candidateAnswer: "小売業界の売上減少について、まず外部要因と内部要因に分けて分析します。外部要因としては市場環境の変化、競合状況、消費者行動の変化...",
          aiAnalysis: "ケース分析のアプローチが適切で、MECEに要因を整理できています。論理的な思考プロセスが明確に示されている優秀な回答です。",
          communicationSkill: 8,
          problemSolvingAbility: 9,
          culturalFit: 8,
          stressHandling: 7,
          strengths: [
            "MECE思考が身についている",
            "論理的な構造化が優秀",
            "仮説思考が明確"
          ],
          improvements: [
            "定量的な根拠がより具体的だと良い",
            "実行可能性の検討が不足",
            "優先順位付けの基準を明確に"
          ],
          nextSteps: [
            "数値分析スキルの強化",
            "業界知識の深堀り",
            "プレゼンテーション技術の向上"
          ],
          industryBenchmark: 82,
          levelComparison: "mid"
        }
      ]
    }
  ] as PracticeResult[],

  // 結果管理メソッド
  savePracticeResult: (result: PracticeResult): void => {
    DataService.PRACTICE_RESULTS.push(result);
    console.log('📊 面接結果を保存:', result);
  },

  getPracticeResultsByAgent: (agentId: string): PracticeResult[] => {
    // 注意: 実際の実装ではAPIから取得
    return DataService.PRACTICE_RESULTS.filter(result => {
      // linkIdからエージェントIDを特定する処理（実際はAPI経由）
      return true; // 暫定的に全件返す
    });
  },

  getAllPracticeResults: (): PracticeResult[] => {
    return [...DataService.PRACTICE_RESULTS];
  },

  getPracticeResultByLinkId: (linkId: string): PracticeResult | null => {
    return DataService.PRACTICE_RESULTS.find(result => result.linkId === linkId) || null;
  },

  // シナリオ表示用ヘルパー
  getScenarioDisplayName: (scenarioId: string): string => {
    const scenario = DataService.getScenarioById(scenarioId);
    return scenario ? scenario.name : scenarioId;
  },

  getScenarioTags: (scenarioId: string): string[] => {
    const scenario = DataService.getScenarioById(scenarioId);
    return scenario ? scenario.tags : [];
  },

  getDifficultyLabel: (difficulty: "medium" | "hard" | "expert"): string => {
    const labels = {
      medium: "MEDIUM",
      hard: "HARD", 
      expert: "EXPERT"
    };
    return labels[difficulty];
  },

  getPressureLevelDisplay: (level: number): string => {
    return `プレッシャー: ${level}/5`;
  }
};

// ===== 将来のAPI設計インターフェース =====

export interface APIInterface {
  // 企業情報API
  companies: {
    getById: (id: string) => Promise<CompanyInfo>;
    getByIndustry: (industry: string) => Promise<CompanyInfo[]>;
    getAll: () => Promise<CompanyInfo[]>;
  };

  // エージェントAPI  
  agents: {
    getById: (id: string) => Promise<AgentInfo>;
    getAll: () => Promise<AgentInfo[]>;
  };

  // シナリオAPI
  scenarios: {
    getById: (id: string) => Promise<ScenarioConfig>;
    getByIndustry: (industry: string) => Promise<ScenarioConfig[]>;
    getByDifficulty: (difficulty: string) => Promise<ScenarioConfig[]>;
    getAll: () => Promise<ScenarioConfig[]>;
  };

  // 質問API
  questions: {
    getById: (id: string) => Promise<QuestionData>;
    getByScenario: (scenarioId: string) => Promise<QuestionData[]>;
    getByCategory: (category: string) => Promise<QuestionData[]>;
    getAll: () => Promise<QuestionData[]>;
  };

  // 面接リンクAPI
  interviewLinks: {
    getById: (id: string) => Promise<InterviewLink>;
    getByAgent: (agentId: string) => Promise<InterviewLink[]>;
    create: (data: Omit<InterviewLink, 'id' | 'createdAt'>) => Promise<InterviewLink>;
    update: (id: string, data: Partial<InterviewLink>) => Promise<InterviewLink>;
  };

  // 結果API
  results: {
    getByLink: (linkId: string) => Promise<PracticeResult[]>;
    create: (data: Omit<PracticeResult, 'id'>) => Promise<PracticeResult>;
  };
}

// ===== エクスポート =====
export default DataService;