/**
 * エラーハンドリング用カスタムフック
 * アプリケーション全体で統一的なエラー処理を提供
 */

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@chakra-ui/react';
import logger from '../utils/logger';

// エラーの種類を定義
export type ErrorType = 'network' | 'validation' | 'auth' | 'server' | 'unknown';

// エラー情報の型定義
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
}

// エラーメッセージのマッピング
const ERROR_MESSAGES: Record<ErrorType, string> = {
  network: 'ネットワーク接続に問題があります。接続を確認してください。',
  validation: '入力内容に誤りがあります。もう一度確認してください。',
  auth: 'ログインが必要です。再度ログインしてください。',
  server: 'サーバーでエラーが発生しました。しばらくしてからお試しください。',
  unknown: '予期しないエラーが発生しました。',
};

// ユーザーフレンドリーなメッセージに変換
const getUserFriendlyMessage = (error: any): { type: ErrorType; message: string } => {
  // ネットワークエラー
  if (error.code === 'NETWORK_ERROR' || error.name === 'NetworkError' || !navigator.onLine) {
    return { type: 'network', message: ERROR_MESSAGES.network };
  }
  
  // 認証エラー
  if (error.status === 401 || error.code === 'UNAUTHORIZED') {
    return { type: 'auth', message: ERROR_MESSAGES.auth };
  }
  
  // バリデーションエラー
  if (error.status === 400 || error.code === 'VALIDATION_ERROR') {
    return { 
      type: 'validation', 
      message: error.message || ERROR_MESSAGES.validation 
    };
  }
  
  // サーバーエラー
  if (error.status >= 500 || error.code === 'SERVER_ERROR') {
    return { type: 'server', message: ERROR_MESSAGES.server };
  }
  
  // その他のエラー
  return { 
    type: 'unknown', 
    message: error.message || ERROR_MESSAGES.unknown 
  };
};

/**
 * エラーハンドリング用カスタムフック
 */
export const useError = () => {
  const [error, setError] = useState<ErrorInfo | null>(null);
  const [isError, setIsError] = useState(false);
  const toast = useToast();

  // エラーをクリア
  const clearError = useCallback(() => {
    setError(null);
    setIsError(false);
  }, []);

  // エラーを処理
  const handleError = useCallback((error: any, options?: {
    silent?: boolean;  // トーストを表示しない
    fallbackMessage?: string;  // カスタムメッセージ
  }) => {
    const { type, message } = getUserFriendlyMessage(error);
    
    const errorInfo: ErrorInfo = {
      type,
      message: options?.fallbackMessage || message,
      code: error.code,
      details: error,
      timestamp: new Date(),
    };
    
    setError(errorInfo);
    setIsError(true);
    
    // ログに記録
    logger.error('Error occurred', {
      ...errorInfo,
      stack: error.stack,
    });
    
    // サイレントモードでなければトーストを表示
    if (!options?.silent) {
      toast({
        title: 'エラーが発生しました',
        description: errorInfo.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    }
    
    return errorInfo;
  }, [toast]);

  // Promise エラーハンドラー
  const withErrorHandling = useCallback(<T extends any[], R>(
    asyncFn: (...args: T) => Promise<R>,
    options?: {
      silent?: boolean;
      fallbackMessage?: string;
      onError?: (error: ErrorInfo) => void;
    }
  ) => {
    return async (...args: T): Promise<R | undefined> => {
      try {
        clearError();
        return await asyncFn(...args);
      } catch (error) {
        const errorInfo = handleError(error, options);
        options?.onError?.(errorInfo);
        return undefined;
      }
    };
  }, [clearError, handleError]);

  // リトライ機能付きエラーハンドラー
  const withRetry = useCallback(<T extends any[], R>(
    asyncFn: (...args: T) => Promise<R>,
    options?: {
      maxRetries?: number;
      retryDelay?: number;
      shouldRetry?: (error: any, attempt: number) => boolean;
    }
  ) => {
    const maxRetries = options?.maxRetries || 3;
    const retryDelay = options?.retryDelay || 1000;
    const shouldRetry = options?.shouldRetry || ((error) => error.type === 'network');

    return async (...args: T): Promise<R | undefined> => {
      let lastError: any;
      
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          clearError();
          return await asyncFn(...args);
        } catch (error) {
          lastError = error;
          
          if (attempt < maxRetries && shouldRetry(error, attempt)) {
            logger.info(`Retrying... (attempt ${attempt + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
          } else {
            break;
          }
        }
      }
      
      // 全てのリトライが失敗した場合
      handleError(lastError);
      return undefined;
    };
  }, [clearError, handleError]);

  return {
    error,
    isError,
    clearError,
    handleError,
    withErrorHandling,
    withRetry,
  };
};

// グローバルエラーハンドラー用フック
export const useGlobalErrorHandler = () => {
  const { handleError } = useError();

  useEffect(() => {
    // 未処理のPromiseエラーをキャッチ
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      handleError(event.reason);
      event.preventDefault();
    };

    // 通常のエラーをキャッチ
    const handleError_global = (event: ErrorEvent) => {
      handleError(event.error);
      event.preventDefault();
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError_global);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError_global);
    };
  }, [handleError]);
};