/**
 * インテリジェント面接フック
 * 候補者側での質問生成システムの統合フック
 */
import { useState, useCallback, useRef } from 'react';
import {
  IntelligentInterviewService,
  InterviewSession,
  InterviewSessionConfig,
} from '../services/intelligentInterviewService';
import {
  GeneratedQuestion,
  SessionAnalysis,
} from '../../shared/types/intelligent-questions';
import { useError, ErrorInfo } from './useError';

export interface UseIntelligentInterviewReturn {
  // セッション状態
  currentSession: InterviewSession | null;
  currentQuestion: GeneratedQuestion | null;
  currentQuestionIndex: number;
  totalQuestions: number;
  isSessionActive: boolean;
  isSessionCompleted: boolean;
  
  // セッション制御
  startSession: (config: InterviewSessionConfig) => Promise<void>;
  completeSession: () => InterviewSession | null;
  moveToNext: () => void;
  recordResponse: (response: string, responseTime: number, confidenceLevel?: number) => void;
  
  // 支援機能
  getEncouragementMessage: () => string | null;
  getSessionAnalysis: () => SessionAnalysis | null;
  getSupportiveMessage: (type: 'opening' | 'transition' | 'closing') => string;
  
  // 状態管理
  isLoading: boolean;
  error: ErrorInfo | null;
  clearError: () => void;
}

export const useIntelligentInterview = (): UseIntelligentInterviewReturn => {
  const [currentSession, setCurrentSession] = useState<InterviewSession | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<GeneratedQuestion | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const serviceRef = useRef<IntelligentInterviewService>(new IntelligentInterviewService());
  const { error, handleError, clearError } = useError();

  const startSession = useCallback(async (config: InterviewSessionConfig) => {
    setIsLoading(true);
    clearError();
    
    try {
      const service = serviceRef.current;
      const session = await service.startIntelligentSession(config);
      
      setCurrentSession(session);
      
      // 最初の質問を設定
      const firstQuestion = service.getNextQuestion();
      setCurrentQuestion(firstQuestion);
      
    } catch (err) {
      handleError(err, {
        fallbackMessage: '面接セッションの開始に失敗しました'
      });
    } finally {
      setIsLoading(false);
    }
  }, [clearError, handleError]);

  const completeSession = useCallback(() => {
    const service = serviceRef.current;
    const completedSession = service.completeSession();
    
    if (completedSession) {
      setCurrentSession(completedSession);
      setCurrentQuestion(null);
    }
    
    return completedSession;
  }, []);

  const moveToNext = useCallback(() => {
    const service = serviceRef.current;
    
    // 次の質問に進む
    service.moveToNextQuestion();
    
    // 新しい現在の質問を取得
    const nextQuestion = service.getNextQuestion();
    setCurrentQuestion(nextQuestion);
    
    // セッション状態を更新
    const updatedSession = service.getCurrentSession();
    if (updatedSession) {
      setCurrentSession({ ...updatedSession });
    }
  }, []);

  const recordResponse = useCallback((
    response: string,
    responseTime: number,
    confidenceLevel: number = 5
  ) => {
    if (!currentSession || !currentQuestion) return;
    
    const service = serviceRef.current;
    service.recordResponse(currentQuestion.id, response, responseTime, confidenceLevel);
    
    // セッション状態を更新
    const updatedSession = service.getCurrentSession();
    if (updatedSession) {
      setCurrentSession({ ...updatedSession });
    }
  }, [currentSession, currentQuestion]);

  const getEncouragementMessage = useCallback(() => {
    const service = serviceRef.current;
    return service.getEncouragementMessage();
  }, []);

  const getSessionAnalysis = useCallback(() => {
    const service = serviceRef.current;
    return service.analyzeSession();
  }, []);

  const getSupportiveMessage = useCallback((type: 'opening' | 'transition' | 'closing') => {
    if (!currentSession) return '';
    
    const { supportiveMessaging } = currentSession;
    
    switch (type) {
      case 'opening':
        return supportiveMessaging.openingMessage;
      case 'transition':
        const messages = supportiveMessaging.transitionMessages;
        return messages[Math.floor(Math.random() * messages.length)] || '';
      case 'closing':
        return supportiveMessaging.closingMessage;
      default:
        return '';
    }
  }, [currentSession]);

  // 計算されたプロパティ
  const currentQuestionIndex = currentSession?.currentQuestionIndex || 0;
  const totalQuestions = currentSession?.questions.length || 0;
  const isSessionActive = Boolean(currentSession && !currentSession.completedAt);
  const isSessionCompleted = Boolean(currentSession?.completedAt);

  return {
    // セッション状態
    currentSession,
    currentQuestion,
    currentQuestionIndex,
    totalQuestions,
    isSessionActive,
    isSessionCompleted,
    
    // セッション制御
    startSession,
    completeSession,
    moveToNext,
    recordResponse,
    
    // 支援機能
    getEncouragementMessage,
    getSessionAnalysis,
    getSupportiveMessage,
    
    // 状態管理
    isLoading,
    error,
    clearError,
  };
};

export default useIntelligentInterview;