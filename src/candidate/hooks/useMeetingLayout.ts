/**
 * Modern Meeting Layout Hook
 * Enhanced với animations và modern features
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { useToast } from '@chakra-ui/react';

interface UseMeetingLayoutProps {
  onComplete: (results: any[]) => void;
  onBack: () => void;
}

interface MeetingAnalytics {
  totalDuration: number;
  questionsAnswered: number;
  averageResponseTime: number;
  engagementLevel: number;
}

export const useMeetingLayout = ({ onComplete, onBack }: UseMeetingLayoutProps) => {
  // Core states
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  
  // UI states
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSidePanel, setShowSidePanel] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [networkQuality, setNetworkQuality] = useState<'excellent' | 'good' | 'poor'>('excellent');
  
  // Meeting states
  const [meetingDuration, setMeetingDuration] = useState(0);
  const [isInterviewActive, setIsInterviewActive] = useState(false);
  const [participantCount, setParticipantCount] = useState(2); // User + AI
  
  // Analytics
  const [analytics, setAnalytics] = useState<MeetingAnalytics>({
    totalDuration: 0,
    questionsAnswered: 0,
    averageResponseTime: 0,
    engagementLevel: 0
  });

  const toast = useToast();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const analyticsRef = useRef<MeetingAnalytics>(analytics);

  // Device and network detection
  useEffect(() => {
    const checkDeviceAndNetwork = () => {
      // Mobile detection
      setIsMobile(window.innerWidth < 768);
      
      // Network quality simulation (in real app, use Network Information API)
      const connection = (navigator as any).connection;
      if (connection) {
        const { effectiveType } = connection;
        if (effectiveType === '4g') {
          setNetworkQuality('excellent');
        } else if (effectiveType === '3g') {
          setNetworkQuality('good');
        } else {
          setNetworkQuality('poor');
        }
      }
    };
    
    checkDeviceAndNetwork();
    window.addEventListener('resize', checkDeviceAndNetwork);
    window.addEventListener('online', () => setNetworkQuality('excellent'));
    window.addEventListener('offline', () => setNetworkQuality('poor'));
    
    return () => {
      window.removeEventListener('resize', checkDeviceAndNetwork);
      window.removeEventListener('online', () => setNetworkQuality('excellent'));
      window.removeEventListener('offline', () => setNetworkQuality('poor'));
    };
  }, []);

  // Enhanced timer with analytics
  useEffect(() => {
    if (isInterviewActive) {
      timerRef.current = setInterval(() => {
        setMeetingDuration(prev => {
          const newDuration = prev + 1;
          
          // Update analytics
          setAnalytics(prevAnalytics => ({
            ...prevAnalytics,
            totalDuration: newDuration,
            engagementLevel: Math.min(100, prevAnalytics.engagementLevel + 0.1)
          }));
          
          return newDuration;
        });
      }, 1000);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isInterviewActive]);

  // Enhanced keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Prevent if user is typing in input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch(event.key.toLowerCase()) {
        case 'm':
          event.preventDefault();
          handleMuteToggle();
          break;
        case 'v':
          event.preventDefault();
          handleVideoToggle();
          break;
        case 'c':
          event.preventDefault();
          setShowChat(prev => !prev);
          break;
        case 'f':
          event.preventDefault();
          handleFullscreenToggle();
          break;
        case 'escape':
          if (isFullscreen) {
            setIsFullscreen(false);
          }
          break;
        case ' ':
          event.preventDefault();
          // Space for push-to-talk functionality
          break;
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.key === ' ') {
        // Release push-to-talk
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [isFullscreen]);

  // Enhanced control handlers with haptic feedback
  const handleMuteToggle = useCallback(() => {
    setIsMuted(prev => {
      const newMuted = !prev;
      
      // Haptic feedback on mobile
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      // Enhanced toast with emoji
      toast({
        title: newMuted ? "🔇 Microphone muted" : "🎤 Microphone unmuted",
        description: newMuted ? "Press M to unmute" : "Press M to mute",
        status: newMuted ? "warning" : "success",
        duration: 2000,
        isClosable: true,
        position: 'top',
      });
      
      // Analytics update
      setAnalytics(prev => ({
        ...prev,
        engagementLevel: Math.max(0, prev.engagementLevel - (newMuted ? 5 : 0))
      }));
      
      return newMuted;
    });
  }, [toast]);

  const handleVideoToggle = useCallback(() => {
    setIsVideoOff(prev => {
      const newVideoOff = !prev;
      
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      toast({
        title: newVideoOff ? "📷 Camera turned off" : "📹 Camera turned on",
        description: newVideoOff ? "Press V to turn on camera" : "Press V to turn off camera",
        status: newVideoOff ? "warning" : "success",
        duration: 2000,
        isClosable: true,
        position: 'top',
      });
      
      return newVideoOff;
    });
  }, [toast]);

  const handleRecordingToggle = useCallback(() => {
    setIsRecording(prev => {
      const newRecording = !prev;
      
      if ('vibrate' in navigator) {
        navigator.vibrate(newRecording ? [100, 50, 100] : 200);
      }
      
      toast({
        title: newRecording ? "🔴 Recording started" : "⏹️ Recording stopped",
        description: newRecording ? "Interview session is being recorded" : "Recording saved successfully",
        status: newRecording ? "info" : "success",
        duration: 3000,
        isClosable: true,
        position: 'top-right',
      });
      
      return newRecording;
    });
  }, [toast]);

  const handleEndCall = useCallback(() => {
    const shouldEnd = confirm('End the interview session?\n\nYour progress will be saved and analyzed.');
    if (shouldEnd) {
      setIsInterviewActive(false);
      setIsRecording(false);
      
      toast({
        title: "📊 Generating Report",
        description: "Please wait while we analyze your interview...",
        status: 'info',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      
      // Pass analytics to completion handler
      setTimeout(() => {
        onComplete([{
          analytics: analyticsRef.current,
          networkQuality,
          duration: meetingDuration
        }]);
      }, 1500);
    }
  }, [onComplete, networkQuality, meetingDuration]);

  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(prev => {
      const newFullscreen = !prev;
      
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      
      return newFullscreen;
    });
  }, []);

  const startInterview = useCallback(() => {
    setIsInterviewActive(true);
    setIsRecording(true);
    
    toast({
      title: "🚀 Interview Started",
      description: "Good luck! Remember to speak clearly and confidently.",
      status: "success",
      duration: 4000,
      isClosable: true,
      position: 'top',
    });
    
    // Initialize analytics
    setAnalytics({
      totalDuration: 0,
      questionsAnswered: 0,
      averageResponseTime: 0,
      engagementLevel: 100
    });
  }, [toast]);

  const updateAnalytics = useCallback((updates: Partial<MeetingAnalytics>) => {
    setAnalytics(prev => ({ ...prev, ...updates }));
    analyticsRef.current = { ...analyticsRef.current, ...updates };
  }, []);

  // Enhanced time formatter with hours
  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Network quality indicator
  const getNetworkIndicator = useCallback(() => {
    switch(networkQuality) {
      case 'excellent': return { color: 'green', bars: 4, label: 'Excellent' };
      case 'good': return { color: 'yellow', bars: 3, label: 'Good' };
      case 'poor': return { color: 'red', bars: 1, label: 'Poor' };
      default: return { color: 'gray', bars: 0, label: 'Unknown' };
    }
  }, [networkQuality]);

  return {
    // Core states
    isMuted,
    isVideoOff,
    isRecording,
    
    // UI states
    isFullscreen,
    showSidePanel,
    showChat,
    isMobile,
    networkQuality,
    
    // Meeting states
    meetingDuration,
    isInterviewActive,
    participantCount,
    analytics,
    
    // Setters
    setShowSidePanel,
    setShowChat,
    setIsInterviewActive,
    
    // Enhanced handlers
    handleMuteToggle,
    handleVideoToggle,
    handleRecordingToggle,
    handleEndCall,
    handleFullscreenToggle,
    startInterview,
    updateAnalytics,
    
    // Utils
    formatTime,
    getNetworkIndicator,
    
    // Shortcuts info for help
    shortcuts: {
      'M': 'Toggle microphone',
      'V': 'Toggle camera',
      'C': 'Toggle chat',
      'F': 'Toggle fullscreen',
      'Space': 'Push to talk',
      'Esc': 'Exit fullscreen'
    }
  };
}; 