import { useState, useEffect, useCallback, useRef } from 'react';
import { speechToTextService, SpeechRecognitionResult, SpeechToTextConfig } from '../services/speechToTextService';
import { interviewSocket } from '../services/interviewSocket';

export interface UseSpeechToTextOptions extends SpeechToTextConfig {
  autoStart?: boolean;
  questionId?: string;
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
}

export interface UseSpeechToTextReturn {
  isListening: boolean;
  isInitializing: boolean;
  currentText: string;
  finalText: string;
  confidence: number;
  error: string | null;
  isSpeaking: boolean;
  startListening: () => Promise<boolean>;
  stopListening: () => Promise<void>;
  resetTranscription: () => void;
  sendResponse: () => void;
}

export const useSpeechToText = (options: UseSpeechToTextOptions = {}): UseSpeechToTextReturn => {
  const [isListening, setIsListening] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [finalText, setFinalText] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);

  const finalTextRef = useRef('');
  const isListeningRef = useRef(false);
  const optionsRef = useRef(options);

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // Initialize service with callbacks
  useEffect(() => {
    const initializeService = async () => {
      setIsInitializing(true);
      setError(null);

      try {
        speechToTextService.setCallbacks({
          onRecognizing: (result: SpeechRecognitionResult) => {
            setCurrentText(result.text);
            setConfidence(result.confidence);
          },
          
          onRecognized: (result: SpeechRecognitionResult) => {
            if (result.text.trim()) {
              const newFinalText = finalTextRef.current + (finalTextRef.current ? ' ' : '') + result.text.trim();
              setFinalText(newFinalText);
              finalTextRef.current = newFinalText;
              setCurrentText('');
              setConfidence(result.confidence);
            }
          },
          
          onError: (errorMessage: string) => {
            setError(errorMessage);
            setIsListening(false);
            isListeningRef.current = false;
            optionsRef.current.onError?.(errorMessage);
          },
          
          onSessionStarted: () => {
            setIsListening(true);
            isListeningRef.current = true;
            setError(null);
          },
          
          onSessionStopped: () => {
            setIsListening(false);
            isListeningRef.current = false;
            setIsSpeaking(false);
            
            // Send final transcription if available
            if (finalTextRef.current.trim()) {
              optionsRef.current.onTranscriptionComplete?.(finalTextRef.current);
            }
          },
          
          onSpeechStart: () => {
            setIsSpeaking(true);
          },
          
          onSpeechEnd: () => {
            setIsSpeaking(false);
          }
        });

        const initialized = await speechToTextService.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize speech recognition service');
        }

        // Auto start if enabled
        if (options.autoStart) {
          await startListening();
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown initialization error';
        setError(errorMessage);
        options.onError?.(errorMessage);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeService();

    // Cleanup on unmount
    return () => {
      speechToTextService.dispose();
    };
  }, []); // Empty dependency array - only run once

  const startListening = useCallback(async (): Promise<boolean> => {
    if (isListeningRef.current || isInitializing) {
      return false;
    }

    setError(null);
    setCurrentText('');
    
    try {
      const success = await speechToTextService.startContinuousRecognition();
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start listening';
      setError(errorMessage);
      options.onError?.(errorMessage);
      return false;
    }
  }, [isInitializing, options]);

  const stopListening = useCallback(async (): Promise<void> => {
    if (!isListeningRef.current) {
      return;
    }

    try {
      await speechToTextService.stopRecognition();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop listening';
      setError(errorMessage);
      options.onError?.(errorMessage);
    }
  }, [options]);

  const resetTranscription = useCallback(() => {
    setCurrentText('');
    setFinalText('');
    finalTextRef.current = '';
    setConfidence(0);
    setError(null);
  }, []);

  const sendResponse = useCallback(() => {
    const textToSend = finalText.trim();
    if (!textToSend) {
      setError('No transcription available to send');
      return;
    }

    if (!options.questionId) {
      setError('Question ID is required to send response');
      return;
    }

    try {
      const success = interviewSocket.send({
        type: 'response_answer',
        question_id: options.questionId,
        text: textToSend,
        confidence: confidence,
        method: 'speech_to_text',
        timestamp: new Date().toISOString()
      });

      if (!success) {
        setError('Failed to send response - socket not connected');
      } else {
        // Reset after successful send
        resetTranscription();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send response';
      setError(errorMessage);
      options.onError?.(errorMessage);
    }
  }, [finalText, confidence, options.questionId, options, resetTranscription]);

  // Auto-stop listening when component unmounts
  useEffect(() => {
    return () => {
      if (isListeningRef.current) {
        speechToTextService.stopRecognition();
      }
    };
  }, []);

  return {
    isListening,
    isInitializing,
    currentText,
    finalText,
    confidence,
    error,
    isSpeaking,
    startListening,
    stopListening,
    resetTranscription,
    sendResponse
  };
};
