import { useState, useEffect, useCallback, useRef } from 'react';
import { speechToTextService, SpeechRecognitionResult, SpeechToTextConfig } from '../services/speechToTextService';
import { interviewSocket } from '../services/interviewSocket';

export interface UseSpeechToTextOptions extends SpeechToTextConfig {
  autoStart?: boolean;
  questionId?: string;
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
}

export interface UseSpeechToTextReturn {
  isListening: boolean;
  isInitializing: boolean;
  currentText: string;
  finalText: string;
  confidence: number;
  error: string | null;
  isSpeaking: boolean;
  startListening: () => Promise<boolean>;
  stopListening: () => Promise<void>;
  resetTranscription: () => void;
  sendResponse: () => void;
}

export const useSpeechToText = (options: UseSpeechToTextOptions = {}): UseSpeechToTextReturn => {
  const [isListening, setIsListening] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [finalText, setFinalText] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);

  const finalTextRef = useRef('');
  const isListeningRef = useRef(false);
  const optionsRef = useRef(options);

  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  useEffect(() => {
    const initializeService = async () => {
      setIsInitializing(true);
      setError(null);

      try {
        speechToTextService.setCallbacks({
          onRecognizing: (result: SpeechRecognitionResult) => {
            setCurrentText(result.text);
            setConfidence(result.confidence);
          },

          onRecognized: (result: SpeechRecognitionResult) => {
            if (result.text.trim()) {
              const newFinalText = finalTextRef.current + (finalTextRef.current ? ' ' : '') + result.text.trim();
              setFinalText(newFinalText);
              finalTextRef.current = newFinalText;
              setCurrentText('');
              setConfidence(result.confidence);

              if (newFinalText.length > 10 && result.confidence > 0.7) {
                console.log('Auto-sending transcription:', newFinalText);
                setTimeout(() => {
                  if (finalTextRef.current === newFinalText) {
                    optionsRef.current.onTranscriptionComplete?.(newFinalText);
                    resetTranscription();
                  }
                }, 2000);
              }
            }
          },

          onError: (errorMessage: string) => {
            console.error('Speech recognition error:', errorMessage);

            // Don't show error for certain non-critical errors
            if (errorMessage.includes('no-speech') ||
              errorMessage.includes('aborted') ||
              errorMessage.includes('network')) {
              console.log('Non-critical error, continuing:', errorMessage);
              return;
            }

            setError(errorMessage);
            setIsListening(false);
            isListeningRef.current = false;
            optionsRef.current.onError?.(errorMessage);
          },

          onSessionStarted: () => {
            console.log('Speech recognition session started');
            setIsListening(true);
            isListeningRef.current = true;
            setError(null);
          },

          onSessionStopped: () => {
            console.log('Speech recognition session stopped');
            setIsListening(false);
            isListeningRef.current = false;
            setIsSpeaking(false);

            // Send final transcription if available and not already sent
            if (finalTextRef.current.trim() && finalTextRef.current.length > 5) {
              console.log('Sending final transcription on session stop:', finalTextRef.current);
              optionsRef.current.onTranscriptionComplete?.(finalTextRef.current);
              resetTranscription();
            }
          },

          onSpeechStart: () => {
            console.log('Speech started');
            setIsSpeaking(true);
          },

          onSpeechEnd: () => {
            console.log('Speech ended');
            setIsSpeaking(false);
          }
        });

        const initialized = await speechToTextService.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize speech recognition service');
        }

        console.log('Speech-to-text service initialized successfully');

        // Auto start if enabled
        if (options.autoStart) {
          console.log('Auto-starting speech recognition');
          await startListening();
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown initialization error';
        console.error('Speech-to-text initialization error:', errorMessage);
        setError(errorMessage);
        options.onError?.(errorMessage);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeService();

    return () => {
      console.log('Cleaning up speech-to-text service');
      speechToTextService.dispose();
    };
  }, []); // Empty dependency array - only run once

  const startListening = useCallback(async (): Promise<boolean> => {
    if (isListeningRef.current || isInitializing) {
      return false;
    }

    setError(null);
    setCurrentText('');

    try {
      const success = await speechToTextService.startContinuousRecognition();
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start listening';
      setError(errorMessage);
      options.onError?.(errorMessage);
      return false;
    }
  }, [isInitializing, options]);

  const stopListening = useCallback(async (): Promise<void> => {
    if (!isListeningRef.current) {
      return;
    }

    try {
      await speechToTextService.stopRecognition();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop listening';
      setError(errorMessage);
      options.onError?.(errorMessage);
    }
  }, [options]);

  const resetTranscription = useCallback(() => {
    setCurrentText('');
    setFinalText('');
    finalTextRef.current = '';
    setConfidence(0);
    setError(null);
  }, []);

  const sendResponse = useCallback(() => {
    const textToSend = finalText.trim();
    if (!textToSend) {
      setError('No transcription available to send');
      return false;
    }

    if (!options.questionId) {
      setError('Question ID is required to send response');
      return false;
    }

    try {
      let success = interviewSocket.sendResponseAnswer({
        question_id: options.questionId,
        text: textToSend,
        confidence: confidence,
        method: 'speech_to_text'
      });

      if (!success) {
        success = interviewSocket.send({
          type: 'response_answer',
          question_id: options.questionId,
          text: textToSend,
          confidence: confidence,
          method: 'speech_to_text',
          timestamp: new Date().toISOString()
        });
      }

      // If that also fails, try with candidate_message as fallback
      if (!success) {
        console.warn('response_answer failed, trying candidate_message as fallback');
        success = interviewSocket.send({
          type: 'candidate_message',
          message: textToSend,
          question_id: options.questionId,
          timestamp: new Date().toISOString()
        });
      }

      if (!success) {
        setError('Failed to send response - socket not connected');
        return false;
      } else {
        console.log('Response sent successfully');
        // Reset after successful send
        resetTranscription();
        return true;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send response';
      console.error('Error sending response:', errorMessage);
      setError(errorMessage);
      options.onError?.(errorMessage);
      return false;
    }
  }, [finalText, confidence, options.questionId, options, resetTranscription]);

  // Auto-stop listening when component unmounts
  useEffect(() => {
    return () => {
      if (isListeningRef.current) {
        speechToTextService.stopRecognition();
      }
    };
  }, []);

  return {
    isListening,
    isInitializing,
    currentText,
    finalText,
    confidence,
    error,
    isSpeaking,
    startListening,
    stopListening,
    resetTranscription,
    sendResponse
  };
};
