export interface SocketMessage {
  type: string;
  timestamp: string;
  [key: string]: any;
}

export interface SystemMessage extends SocketMessage {
  type: 'system_message';
  message: string;
}

export interface ErrorMessage extends SocketMessage {
  type: 'error';
  message: string;
}

export interface PingMessage extends SocketMessage {
  type: 'ping';
}

export interface AnswerSubmittedMessage extends SocketMessage {
  type: 'answer_submitted';
  question_id: string;
  question_text: string;
} 