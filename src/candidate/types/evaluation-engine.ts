/**
 * Evaluation Engine で使用される型定義
 * any型を排除して型安全性を向上
 */

import type { STARCEvaluation } from './evaluation';

// コミュニケーション分析の結果型
export interface CommunicationAnalysis {
  clarity: number;
  structure: number;
  engagement: number;
  confidence: number;
  articulation: number;
  listening: number;
  summary: string;
  strengths: string[];
  improvements: string[];
  verbalFluency?: {
    score: number;
  };
}

// STAR分析の詳細型
export interface STARAnalysisItem {
  situation: number;
  task: number;
  action: number;
  result: number;
  context: number;
  overall: number;
  feedback: string;
}

// パーソナライズドフィードバック型
export interface PersonalizedFeedback {
  strengths: string[];
  improvements: string[];
  specificAdvice: string[];
  nextSteps: string[];
  motivationalMessage: string;
}

// オーディオビデオ分析型
export interface AudioVideoAnalysis {
  speechClarity: number;
  pace: number;
  volume: number;
  eyeContact: number;
  bodyLanguage: number;
  facialExpressions: number;
  professionalAppearance: number;
  overallPresence: number;
  insights: string[];
  recommendations: string[];
}

// アクションプラン型
export interface ActionPlan {
  immediate: Array<{
    action: string;
    timeline: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  shortTerm: Array<{
    action: string;
    timeline: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  longTerm: Array<{
    action: string;
    timeline: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}

// ベンチマーク比較型
export interface BenchmarkComparison {
  userScore: number;
  industryAverage: number;
  topPerformers: number;
  percentile: number;
  category: string;
  insights: string[];
}

// 評価データの統合型
export interface EvaluationData {
  communicationAnalysis: CommunicationAnalysis;
  starAnalysis: STARCEvaluation[]; // 既存の型を使用
  personalizedFeedback?: PersonalizedFeedback;
  audioVideoAnalysis?: AudioVideoAnalysis;
  actionPlan?: ActionPlan;
  benchmarkComparison?: BenchmarkComparison;
  overallScore?: number;
}

// STAR平均スコア型
export interface STARAverageScores {
  situation: number;
  task: number;
  action: number;
  result: number;
  context: number;
  overall: number;
}