/**
 * Azure Speech SDK の型定義
 * Microsoftの公式SDKに対する型安全性を提供
 */

// Azure Speech SDK のグローバル型定義
export interface SpeechConfig {
  speechSynthesisVoiceName: string;
}

export interface AvatarVideoFormat {
  // ビデオフォーマットの設定
}

export interface AvatarConfig {
  backgroundColor: string;
}

export interface AvatarEvent {
  type: 'videoFrameReceived' | 'speechStarted' | 'speechEnded' | 'connectionEstablished' | 'connectionClosed';
  data?: {
    mediaStream?: MediaStream;
    message?: string;
    timestamp?: number;
  };
  timestamp: number;
}

export interface ChatAvatarConnection {
  avatarEventReceived: ((event: AvatarEvent) => void) | null;
  start(): Promise<void>;
  close(): void;
  speakTextAsync(text: string): Promise<void>;
}

export interface SpeechSDKStatic {
  SpeechConfig: {
    fromSubscription(key: string, region: string): SpeechConfig;
  };
  AvatarVideoFormat: new () => AvatarVideoFormat;
  AvatarConfig: new (avatarId: string, style: string, videoFormat: AvatarVideoFormat) => AvatarConfig;
  ChatAvatarConnection: new (speechConfig: SpeechConfig, avatarConfig: AvatarConfig) => ChatAvatarConnection;
}

// WindowにSpeechSDKを追加
declare global {
  interface Window {
    SpeechSDK: SpeechSDKStatic;
  }
}