/**
 * 面接準備ページで使用される型定義
 * コーディング規約に従い、any型を排除して型安全性を向上
 */

// 企業情報の型定義
export interface CompanyInfo {
  name: string;
  position: string;
  industry?: string;
  location?: string;
  employeeCount?: string;
  founded?: string;
  description?: string;
  interviewStyle?: string;
  culture?: string;
}

// 面接データの型定義
export interface InterviewData {
  linkId: string | null;
  candidateName: string;
  candidateEmail: string;
  sessionId: string;
  scenarioId?: string;
  companyInfo: CompanyInfo | null;
  status?: 'active' | 'expired' | 'invalid';
  expiresAt?: string;
  message?: string;
  requiresToken?: boolean;
}

// パンくずリストの項目型定義
export interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

// 面接リンクの型定義
export interface InterviewLink {
  id: string;
  companyInfo: CompanyInfo;
  status: 'active' | 'expired' | 'invalid';
  expiresAt: string;
  candidateName?: string;
  candidateEmail?: string;
}

// リンク検証結果の型定義
export interface LinkValidationResult {
  isValid: boolean;
  status: 'active' | 'expired' | 'invalid';
  link?: InterviewLink;
  error?: string;
}