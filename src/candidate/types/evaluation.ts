/**
 * 面接評価フレームワーク - TypeScriptインターフェース定義
 * STAR+C評価モデルと包括的評価軸を含む
 */

// ========================================
// 1. STAR+C評価モデル
// ========================================
export interface STARCEvaluation {
  situation: {
    clarity: number; // 状況説明の明確さ (0-100)
    relevance: number; // 質問との関連性
    specificity: number; // 具体性
  };
  task: {
    roleClarity: number; // 役割の明確さ
    challengeLevel: number; // 課題の難易度
    scope: number; // 影響範囲
  };
  action: {
    initiative: number; // 主体性
    methodology: number; // 方法論の妥当性
    collaboration: number; // 協働性
  };
  result: {
    quantification: number; // 定量的成果
    impact: number; // インパクト
    learning: number; // 学びの深さ
  };
  competency: {
    transferability: number; // 応用可能性
    growthMindset: number; // 成長志向
  };
}

export interface STARCExample {
  question: string;
  response: {
    situation: string;
    task: string;
    action: string;
    result: string;
    competency: string; // 得られた能力・学び
  };
  evaluation: STARCEvaluation;
  improvements: {
    suggestion: string;
    examplePhrase: string;
    practicePrompt: string;
  }[];
}

// ========================================
// 2. 包括的評価軸（7つの主要カテゴリ）
// ========================================
export interface InterviewEvaluationFramework {
  // 1. コミュニケーション能力
  communication: {
    verbalFluency: {
      score: number;
      metrics: {
        fillerWords: number; // フィラーワード（あー、えー）の頻度
        speechRate: number; // 話速（words/min）
        pauseFrequency: number; // 不自然な間の頻度
        sentenceCoherence: number; // 文章の論理的つながり
      };
    };
    structuring: {
      score: number;
      metrics: {
        introductionClarity: number; // 導入の明確さ
        logicalFlow: number; // 論理の流れ
        conclusion: number; // まとめの適切さ
        timeManagement: number; // 時間配分
      };
    };
    nonVerbal: {
      score: number;
      metrics: {
        eyeContact: number; // アイコンタクト維持率
        facialExpression: number; // 表情の豊かさ
        voiceTone: number; // 声のトーン変化
        energy: number; // エネルギーレベル
      };
    };
  };

  // 2. 論理的思考力
  logicalThinking: {
    problemAnalysis: {
      score: number;
      metrics: {
        issueIdentification: number; // 問題特定力
        rootCauseAnalysis: number; // 原因分析
        frameworkUsage: number; // フレームワーク活用
        dataEvidence: number; // データ根拠
      };
    };
    solutionDesign: {
      score: number;
      metrics: {
        creativity: number; // 創造性
        feasibility: number; // 実現可能性
        comprehensiveness: number; // 網羅性
        prioritization: number; // 優先順位付け
      };
    };
  };

  // 3. 経験の質と深さ
  experienceQuality: {
    leadership: {
      score: number;
      examples: STARCExample[];
      metrics: {
        teamSize: number; // チーム規模
        complexity: number; // 複雑性
        outcomes: number; // 成果
        influence: number; // 影響力
      };
    };
    problemSolving: {
      score: number;
      examples: STARCExample[];
      metrics: {
        challengeLevel: number; // 課題難易度
        innovation: number; // 革新性
        stakeholders: number; // ステークホルダー管理
        results: number; // 結果
      };
    };
  };

  // 4. 文化適合性
  culturalFit: {
    values: {
      score: number;
      alignment: {
        companyMission: number; // 企業ミッションとの合致
        teamDynamics: number; // チーム文化との適合
        workStyle: number; // 働き方の適合
        growthMindset: number; // 成長志向
      };
    };
    motivation: {
      score: number;
      factors: {
        intrinsic: number; // 内発的動機
        careerGoals: number; // キャリア目標の合致
        companyKnowledge: number; // 企業理解度
        enthusiasm: number; // 熱意
      };
    };
  };

  // 5. 技術的能力（業界別カスタマイズ）
  technicalCompetence: {
    domainKnowledge: {
      score: number;
      areas: {
        industryTrends: number; // 業界動向理解
        technicalDepth: number; // 技術的深さ
        practicalApplication: number; // 実践応用力
        continuousLearning: number; // 継続学習
      };
    };
    roleSpecific: {
      score: number;
      competencies: RoleSpecificCompetency[];
    };
  };

  // 6. 適応力と学習能力
  adaptability: {
    changeManagement: {
      score: number;
      examples: AdaptabilityExample[];
      metrics: {
        flexibility: number; // 柔軟性
        resilience: number; // レジリエンス
        learningSpeed: number; // 学習速度
        ambiguityTolerance: number; // 曖昧さへの耐性
      };
    };
  };

  // 7. プロフェッショナリズム
  professionalism: {
    presentation: {
      score: number;
      metrics: {
        appearance: number; // 身だしなみ（ビデオ面接の場合）
        punctuality: number; // 時間管理
        preparation: number; // 準備度
        etiquette: number; // マナー
      };
    };
  };
}

// ========================================
// 3. 補助的インターフェース
// ========================================
export interface RoleSpecificCompetency {
  name: string;
  score: number;
  evidence: string[];
  gaps: string[];
  developmentPlan: string;
}

export interface AdaptabilityExample {
  context: string;
  challenge: string;
  approach: string;
  outcome: string;
  learningPoints: string[];
}

// ========================================
// 4. 音声・映像分析結果
// ========================================
export interface AudioAnalysisResult {
  segments: AudioSegment[];
  prosodyAnalysis: ProsodyAnalysis;
  emotionAnalysis: EmotionAnalysis;
}

export interface AudioSegment {
  timestamp: string;
  type: 'excellent' | 'improvement' | 'neutral';
  transcript: string;
  feedback: string;
  confidence: number;
}

export interface ProsodyAnalysis {
  averagePitch: number;
  pitchVariation: number;
  speakingRate: number; // words per minute
  pauseAnalysis: {
    totalPauses: number;
    averagePauseLength: number;
    inappropriatePauses: number;
  };
  fillerWords: {
    count: number;
    frequency: number; // per minute
    types: { [word: string]: number };
  };
}

export interface EmotionAnalysis {
  overallSentiment: 'positive' | 'neutral' | 'negative';
  confidence: number;
  emotions: {
    enthusiasm: number;
    nervousness: number;
    confidence: number;
    frustration: number;
  };
}

// ========================================
// 5. 包括的レポート構造
// ========================================
export interface ComprehensiveInterviewReport {
  // 基本情報
  metadata: {
    candidateName: string;
    position: string;
    interviewDate: string;
    duration: string;
    interviewType: 'behavioral' | 'technical' | 'case' | 'mixed';
  };

  // 総合評価
  overallAssessment: {
    score: number; // 0-100
    level: 'entry' | 'junior' | 'mid' | 'senior' | 'expert';
    recommendation: 'strong_hire' | 'hire' | 'maybe' | 'no_hire';
  };

  // 詳細評価
  detailedEvaluation: InterviewEvaluationFramework;

  // 音声・映像分析
  audioVideoAnalysis: {
    audio: AudioAnalysisResult;
    video?: VideoAnalysisResult;
  };

  // 改善アクションプラン
  actionPlan: PersonalizedActionPlan;

  // ベンチマーク比較
  benchmarkComparison: BenchmarkComparison;

  // 強み・改善点サマリー
  summary: {
    keyStrengths: string[];
    primaryImprovements: string[];
    nextSteps: string[];
  };
}

export interface VideoAnalysisResult {
  facialExpressions: {
    dominant: string;
    confidence: number;
    engagement: number;
  };
  bodyLanguage: {
    posture: number;
    gestures: number;
    eyeContact: number;
  };
}

export interface PersonalizedActionPlan {
  immediate: ActionItem[]; // 今すぐ
  shortTerm: ActionItem[]; // 1週間
  longTerm: ActionItem[]; // 1ヶ月
}

export interface ActionItem {
  action: string;
  method: string;
  metric: string;
  deadline?: string;
  resources?: Resource[];
}

export interface Resource {
  type: 'book' | 'course' | 'article' | 'tool' | 'exercise';
  title: string;
  description: string;
  url?: string;
  estimatedTime: string;
}

export interface BenchmarkComparison {
  industryAverage: number;
  roleSpecificAverage: number;
  experienceLevelAverage: number;
  userPercentile: number;
  improvementPotential: number;
}

// ========================================
// 6. ユーザープロファイル
// ========================================
export interface CandidateProfile {
  type: '新卒' | '第二新卒' | '中途' | 'エグゼクティブ';
  position: string;
  industry: string;
  experienceLevel: 'entry' | 'junior' | 'mid' | 'senior' | 'expert';
  yearsOfExperience?: number;
  previousIndustry?: string;
  targetIndustry?: string;
  specializations?: string[];
}

// ========================================
// 7. 評価生成設定
// ========================================
export interface EvaluationConfig {
  weights: {
    communication: number;
    logicalThinking: number;
    experienceQuality: number;
    culturalFit: number;
    technicalCompetence: number;
    adaptability: number;
    professionalism: number;
  };
  industrySpecific: boolean;
  roleSpecific: boolean;
  includeAudioAnalysis: boolean;
  includeVideoAnalysis: boolean;
  detailLevel: 'basic' | 'standard' | 'comprehensive';
}

// ========================================
// 8. 業界別カスタマイズ
// ========================================
export interface IndustrySpecificQuestions {
  [industry: string]: {
    technical: string[];
    behavioral: string[];
    caseStudy?: string[];
  };
}

export const INDUSTRY_QUESTIONS: IndustrySpecificQuestions = {
  IT: {
    technical: [
      "最新の技術トレンドについてどう考えていますか？",
      "技術的な課題をどのように解決しましたか？"
    ],
    behavioral: [
      "チーム開発での役割は？",
      "技術選定の基準は？"
    ]
  },
  finance: {
    technical: [
      "リスク管理についての考え方は？",
      "規制変更への対応経験は？"
    ],
    behavioral: [
      "プレッシャー下での意思決定は？",
      "ステークホルダーとの調整経験は？"
    ]
  },
  sales: {
    technical: [
      "営業プロセスの改善経験は？",
      "顧客セグメンテーションの方法は？"
    ],
    behavioral: [
      "最も困難だった商談は？",
      "チーム目標達成への貢献は？"
    ]
  },
  consulting: {
    technical: [
      "問題解決のフレームワークは？",
      "データ分析手法について説明してください"
    ],
    behavioral: [
      "クライアントとの難しい状況をどう乗り越えましたか？",
      "チームの意見が分かれた時の対処法は？"
    ],
    caseStudy: [
      "売上が減少している企業の課題を分析してください",
      "新規事業の収益性を評価してください"
    ]
  }
};