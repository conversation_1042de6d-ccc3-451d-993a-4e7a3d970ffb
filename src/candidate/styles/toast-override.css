/* Toast テキスト色のオーバーライド */

/* ライトモード（デフォルト） */
[id^="toast-"] {
  color: #1a202c !important; /* gray.900 */
}

[id^="toast-"] * {
  color: #1a202c !important; /* gray.900 */
}

[id^="toast-"] .chakra-toast__inner {
  color: #1a202c !important; /* gray.900 */
}

/* ダークモード */
[data-theme="dark"] [id^="toast-"],
.chakra-ui-dark [id^="toast-"] {
  color: #f7fafc !important; /* gray.100 */
}

[data-theme="dark"] [id^="toast-"] *,
.chakra-ui-dark [id^="toast-"] * {
  color: #f7fafc !important; /* gray.100 */
}

[data-theme="dark"] [id^="toast-"] .chakra-toast__inner,
.chakra-ui-dark [id^="toast-"] .chakra-toast__inner {
  color: #f7fafc !important; /* gray.100 */
}

/* Success トースト */
[id^="toast-"][data-status="success"] {
  background-color: #f0fff4 !important; /* green.50 */
  color: #1a202c !important; /* green.900 */
}

[data-theme="dark"] [id^="toast-"][data-status="success"],
.chakra-ui-dark [id^="toast-"][data-status="success"] {
  background-color: #22543d !important; /* green.800 */
  color: #f0fff4 !important; /* green.100 */
}

/* Error トースト */
[id^="toast-"][data-status="error"] {
  background-color: #fff5f5 !important; /* red.50 */
  color: #742a2a !important; /* red.900 */
}

[data-theme="dark"] [id^="toast-"][data-status="error"],
.chakra-ui-dark [id^="toast-"][data-status="error"] {
  background-color: #742a2a !important; /* red.800 */
  color: #fff5f5 !important; /* red.100 */
}

/* Warning トースト */
[id^="toast-"][data-status="warning"] {
  background-color: #fffaf0 !important; /* orange.50 */
  color: #7b341e !important; /* orange.900 */
}

[data-theme="dark"] [id^="toast-"][data-status="warning"],
.chakra-ui-dark [id^="toast-"][data-status="warning"] {
  background-color: #7b341e !important; /* orange.800 */
  color: #fffaf0 !important; /* orange.100 */
}

/* Info トースト */
[id^="toast-"][data-status="info"] {
  background-color: #ebf8ff !important; /* blue.50 */
  color: #1a365d !important; /* blue.900 */
}

[data-theme="dark"] [id^="toast-"][data-status="info"],
.chakra-ui-dark [id^="toast-"][data-status="info"] {
  background-color: #1a365d !important; /* blue.800 */
  color: #ebf8ff !important; /* blue.100 */
}