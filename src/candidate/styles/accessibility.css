/* アクセシビリティ対応のCSS */

/* フォントサイズ変数 */
:root {
  --font-size-base: 16px;
  --motion-reduce: no-preference;
}

/* フォントサイズの動的調整 */
html {
  font-size: var(--font-size-base);
}

/* 高コントラストモード */
.high-contrast {
  filter: contrast(150%);
}

.high-contrast button,
.high-contrast input,
.high-contrast textarea {
  border: 2px solid #000 !important;
}

.high-contrast text {
  color: #000 !important;
}

/* アニメーション軽減 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* カスタムのモーション軽減 */
.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* フォーカス表示の強化 */
*:focus {
  outline: 2px solid #2563EB !important;
  outline-offset: 2px !important;
}

/* スクリーンリーダー用テキスト */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* スキップリンク */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #2563EB;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
  outline: 3px solid rgba(37, 99, 235, 0.3);
}

/* キーボードナビゲーション支援 */
[tabindex="-1"]:focus {
  outline: none;
}

/* タッチターゲットのサイズ確保 */
button,
input,
select,
textarea,
a {
  min-height: 44px;
  min-width: 44px;
}

/* 色に依存しない情報伝達 */
.error::before {
  content: "⚠ ";
  font-weight: bold;
}

.success::before {
  content: "✓ ";
  font-weight: bold;
}

.warning::before {
  content: "⚠ ";
  font-weight: bold;
}

/* 読みやすさの向上 */
p, li, div {
  line-height: 1.5;
  max-width: 70ch; /* 読みやすい行長 */
}

/* コントラスト比の確保 */
.low-contrast-fix {
  color: #1a1a1a !important;
  background: #ffffff !important;
}

/* 音声読み上げ用の一時停止 */
.pause-speech {
  speak: never;
}

/* ランドマーク要素の明確化 */
main[role="main"],
nav[role="navigation"],
aside[role="complementary"],
header[role="banner"],
footer[role="contentinfo"] {
  outline: 1px solid transparent; /* フォーカス時のみ表示 */
}

main[role="main"]:focus,
nav[role="navigation"]:focus,
aside[role="complementary"]:focus,
header[role="banner"]:focus,
footer[role="contentinfo"]:focus {
  outline-color: #2563EB;
}