/**
 * インテリジェント面接サービス
 * 候補者側での質問生成システムの統合
 */
import { IntelligentQuestionGenerator } from '../../shared/services/intelligentQuestionGenerator';
import {
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  GeneratedQuestion,
  QuestionCategory,
  CandidateState,
  SessionAnalysis,
} from '../../shared/types/intelligent-questions';
import { DataService, CompanyInfo, QuestionData } from '../lib/unified-data';

export interface InterviewSessionConfig {
  companyInfo: CompanyInfo;
  candidateProfile?: {
    experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
    skills: string[];
    background: string[];
    previousInterviews: number;
  };
  useIntelligentGeneration: boolean;
  adaptiveSettings: {
    personalizeForCandidate: boolean;
    adjustForAnxiety: boolean;
    emphasizeGrowth: boolean;
    avoidNegativeLanguage: boolean;
  };
}

export interface InterviewSession {
  id: string;
  config: InterviewSessionConfig;
  questions: GeneratedQuestion[];
  currentQuestionIndex: number;
  candidateResponses: {
    questionId: string;
    response: string;
    responseTime: number;
    confidenceLevel: number;
    timestamp: string;
  }[];
  sessionAnalysis: Partial<SessionAnalysis>;
  startedAt: string;
  completedAt?: string;
  supportiveMessaging: {
    openingMessage: string;
    transitionMessages: string[];
    closingMessage: string;
  };
}

export class IntelligentInterviewService {
  private questionGenerator: IntelligentQuestionGenerator;
  private currentSession: InterviewSession | null = null;

  constructor() {
    this.questionGenerator = new IntelligentQuestionGenerator();
  }

  /**
   * インテリジェント面接セッションの開始
   */
  async startIntelligentSession(config: InterviewSessionConfig): Promise<InterviewSession> {
    const sessionId = this.generateSessionId();
    
    let questions: GeneratedQuestion[];
    let supportiveMessaging;

    if (config.useIntelligentGeneration) {
      // AI質問生成を使用
      const generationRequest: QuestionGenerationRequest = {
        companyInfo: {
          name: config.companyInfo.name,
          industry: config.companyInfo.industry,
          position: config.companyInfo.position,
          requirements: [],
          culture: config.companyInfo.culture,
          interviewStyle: config.companyInfo.interviewStyle,
        },
        candidateProfile: config.candidateProfile,
        questionSettings: {
          totalQuestions: 5,
          categories: this.selectOptimalCategories(config.companyInfo.industry),
          difficulty: this.determineDifficulty(config.candidateProfile?.experienceLevel || 'mid'),
          estimatedDuration: 30,
          includeWarmup: true,
          includeFollowUp: true,
        },
        adaptiveSettings: config.adaptiveSettings,
      };

      const response = await this.questionGenerator.generateQuestions(generationRequest);
      questions = response.questions;
      supportiveMessaging = response.supportiveMessaging;
    } else {
      // 従来の静的質問を使用
      const staticQuestions = this.getStaticQuestions(config.companyInfo);
      questions = this.convertToGeneratedQuestions(staticQuestions);
      supportiveMessaging = this.generateBasicSupportiveMessages(config.companyInfo);
    }

    const session: InterviewSession = {
      id: sessionId,
      config,
      questions,
      currentQuestionIndex: 0,
      candidateResponses: [],
      sessionAnalysis: {},
      startedAt: new Date().toISOString(),
      supportiveMessaging,
    };

    this.currentSession = session;
    return session;
  }

  /**
   * 次の質問を取得
   */
  getNextQuestion(): GeneratedQuestion | null {
    if (!this.currentSession) return null;
    
    const { questions, currentQuestionIndex } = this.currentSession;
    
    if (currentQuestionIndex >= questions.length) {
      return null; // セッション終了
    }

    return questions[currentQuestionIndex];
  }

  /**
   * 候補者の回答を記録
   */
  recordResponse(
    questionId: string,
    response: string,
    responseTime: number,
    confidenceLevel: number = 5
  ): void {
    if (!this.currentSession) return;

    const responseRecord = {
      questionId,
      response,
      responseTime,
      confidenceLevel,
      timestamp: new Date().toISOString(),
    };

    this.currentSession.candidateResponses.push(responseRecord);
  }

  /**
   * 次の質問に進む
   */
  moveToNextQuestion(): void {
    if (!this.currentSession) return;
    this.currentSession.currentQuestionIndex++;
  }

  /**
   * 適応的な励ましメッセージを取得
   */
  getEncouragementMessage(): string | null {
    if (!this.currentSession) return null;

    const { questions, currentQuestionIndex, candidateResponses } = this.currentSession;
    const currentQuestion = questions[currentQuestionIndex];
    
    if (!currentQuestion) return null;

    // 難しい質問の後
    if (currentQuestion.difficulty === 'hard' && candidateResponses.length > 0) {
      const lastResponse = candidateResponses[candidateResponses.length - 1];
      if (lastResponse.confidenceLevel < 3) {
        return 'チャレンジングな質問でしたが、よく考えて答えてくださいました。';
      }
    }

    // セッション中盤
    if (currentQuestionIndex === Math.floor(questions.length / 2)) {
      return 'ここまでの回答、とても充実していますね。この調子で続けましょう。';
    }

    return null;
  }

  /**
   * セッションの分析
   */
  analyzeSession(): SessionAnalysis | null {
    if (!this.currentSession || this.currentSession.candidateResponses.length === 0) {
      return null;
    }

    const { questions, candidateResponses } = this.currentSession;
    
    const questionPerformance = candidateResponses.map(response => {
      const question = questions.find(q => q.id === response.questionId);
      return {
        questionId: response.questionId,
        responseTime: response.responseTime,
        qualityScore: this.calculateResponseQuality(response, question),
        confidenceLevel: response.confidenceLevel,
        needsImprovement: response.confidenceLevel < 3,
      };
    });

    const averageResponseTime = questionPerformance.reduce(
      (sum, p) => sum + p.responseTime, 0
    ) / questionPerformance.length;

    const averageConfidence = candidateResponses.reduce(
      (sum, r) => sum + r.confidenceLevel, 0
    ) / candidateResponses.length;

    const improvementTrend = this.calculateImprovementTrend(candidateResponses);

    const analysis: SessionAnalysis = {
      questionPerformance,
      overallMetrics: {
        averageResponseTime,
        consistencyScore: this.calculateConsistencyScore(candidateResponses),
        improvementTrend,
        recommendedNextLevel: this.recommendNextLevel(questionPerformance),
      },
      adaptiveRecommendations: {
        nextSessionDifficulty: this.recommendNextDifficulty(averageConfidence),
        focusAreas: this.identifyFocusAreas(questionPerformance),
        encouragementNeeds: averageConfidence < 4,
        practiceRecommendations: this.generatePracticeRecommendations(questionPerformance),
      },
    };

    this.currentSession.sessionAnalysis = analysis;
    return analysis;
  }

  /**
   * セッション完了
   */
  completeSession(): InterviewSession | null {
    if (!this.currentSession) return null;

    this.currentSession.completedAt = new Date().toISOString();
    this.currentSession.sessionAnalysis = this.analyzeSession() || {};

    const completedSession = { ...this.currentSession };
    this.currentSession = null;
    
    return completedSession;
  }

  /**
   * 現在のセッション取得
   */
  getCurrentSession(): InterviewSession | null {
    return this.currentSession;
  }

  // ===== プライベートメソッド =====

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private selectOptimalCategories(industry: string): QuestionCategory[] {
    const baseCategories: QuestionCategory[] = ['self-introduction', 'motivation'];
    
    if (industry.includes('IT') || industry.includes('ソフトウェア')) {
      return [...baseCategories, 'technical', 'problem-solving', 'teamwork'];
    } else if (industry.includes('コンサル')) {
      return [...baseCategories, 'problem-solving', 'leadership', 'behavioral'];
    } else if (industry.includes('金融')) {
      return [...baseCategories, 'behavioral', 'situational', 'teamwork'];
    } else {
      return [...baseCategories, 'experience-skills', 'teamwork', 'career-goals'];
    }
  }

  private determineDifficulty(experienceLevel: string): 'easy' | 'medium' | 'hard' | 'adaptive' {
    switch (experienceLevel) {
      case 'entry': return 'easy';
      case 'mid': return 'medium';
      case 'senior': return 'hard';
      case 'executive': return 'hard';
      default: return 'adaptive';
    }
  }

  private getStaticQuestions(companyInfo: CompanyInfo): QuestionData[] {
    // 既存のDataServiceから質問を取得
    return DataService.getAllQuestions().slice(0, 5);
  }

  private convertToGeneratedQuestions(staticQuestions: QuestionData[]): GeneratedQuestion[] {
    return staticQuestions.map((q, index) => ({
      id: `static_${q.id}`,
      text: q.text,
      category: this.mapCategoryToQuestionCategory(q.category),
      intent: {
        primary: q.category,
        secondary: [],
        skillAssessment: q.evaluationCriteria,
        personalityTraits: [],
      },
      difficulty: q.difficulty === 'expert' ? 'hard' : q.difficulty as 'easy' | 'medium' | 'hard',
      estimatedAnswerTime: q.estimatedTime,
      followUpQuestions: q.followUpQuestions,
      evaluationCriteria: q.evaluationCriteria.map(criteria => ({
        name: criteria,
        description: criteria,
        weight: 0.25,
        scoringGuideline: {
          excellent: '優秀',
          good: '良好',
          acceptable: '可',
          needsImprovement: '要改善',
        },
      })),
      supportiveElements: {},
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'template',
      },
    }));
  }

  private mapCategoryToQuestionCategory(category: string): QuestionCategory {
    const categoryMap: Record<string, QuestionCategory> = {
      '自己紹介': 'self-introduction',
      '志望動機': 'motivation',
      '経験・困難克服': 'experience-skills',
      '技術戦略': 'technical',
      'ケース分析': 'problem-solving',
      'プレッシャー質問': 'behavioral',
    };
    
    return categoryMap[category] || 'behavioral';
  }

  private generateBasicSupportiveMessages(companyInfo: CompanyInfo) {
    return {
      openingMessage: `${companyInfo.name}での面接練習を始めましょう。リラックスして、ご自身のペースで進めてください。`,
      transitionMessages: [
        '素晴らしい回答でした。次の質問に移りましょう。',
        'とても興味深いお話をありがとうございます。続いて、',
      ],
      closingMessage: '面接練習お疲れさまでした。今日の経験は必ずあなたの成長につながります。',
      encouragementTriggers: [],
    };
  }

  private calculateResponseQuality(
    response: any,
    question: GeneratedQuestion | undefined
  ): number {
    if (!question) return 5;
    
    const responseLength = response.response.length;
    const expectedTime = question.estimatedAnswerTime;
    const actualTime = response.responseTime;
    
    let score = 5; // ベーススコア
    
    // 回答の長さ評価
    if (responseLength < 50) score -= 1;
    else if (responseLength > 500) score += 1;
    
    // 時間効率評価
    const timeRatio = actualTime / expectedTime;
    if (timeRatio < 0.5) score -= 0.5; // 短すぎる
    else if (timeRatio > 2) score -= 1; // 長すぎる
    else if (timeRatio >= 0.8 && timeRatio <= 1.2) score += 0.5; // 適切
    
    // 信頼度評価
    score += (response.confidenceLevel - 5) * 0.2;
    
    return Math.max(1, Math.min(10, score));
  }

  private calculateConsistencyScore(responses: any[]): number {
    if (responses.length < 2) return 1;
    
    const confidenceLevels = responses.map(r => r.confidenceLevel);
    const mean = confidenceLevels.reduce((sum, val) => sum + val, 0) / confidenceLevels.length;
    const variance = confidenceLevels.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / confidenceLevels.length;
    
    // 分散が小さいほど一貫性が高い
    return Math.max(0, 1 - variance / 10);
  }

  private calculateImprovementTrend(responses: any[]): 'improving' | 'stable' | 'declining' {
    if (responses.length < 3) return 'stable';
    
    const firstHalf = responses.slice(0, Math.floor(responses.length / 2));
    const secondHalf = responses.slice(Math.floor(responses.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, r) => sum + r.confidenceLevel, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, r) => sum + r.confidenceLevel, 0) / secondHalf.length;
    
    if (secondAvg > firstAvg + 0.5) return 'improving';
    if (secondAvg < firstAvg - 0.5) return 'declining';
    return 'stable';
  }

  private recommendNextLevel(performance: any[]): QuestionCategory[] {
    const lowPerformanceCategories = performance
      .filter(p => p.qualityScore < 5)
      .map(p => {
        // 質問IDからカテゴリを推測（実装では適切なマッピングが必要）
        return 'experience-skills' as QuestionCategory;
      });
    
    return lowPerformanceCategories.slice(0, 3);
  }

  private recommendNextDifficulty(averageConfidence: number): 'easy' | 'medium' | 'hard' {
    if (averageConfidence < 3) return 'easy';
    if (averageConfidence < 6) return 'medium';
    return 'hard';
  }

  private identifyFocusAreas(performance: any[]): string[] {
    const areas: string[] = [];
    
    const avgResponseTime = performance.reduce((sum, p) => sum + p.responseTime, 0) / performance.length;
    if (avgResponseTime > 300) { // 5分以上
      areas.push('回答の簡潔性');
    }
    
    const lowConfidenceCount = performance.filter(p => p.confidenceLevel < 4).length;
    if (lowConfidenceCount > performance.length / 2) {
      areas.push('自信を持った表現');
    }
    
    const needsImprovementCount = performance.filter(p => p.needsImprovement).length;
    if (needsImprovementCount > 0) {
      areas.push('基礎的なコミュニケーション');
    }
    
    return areas;
  }

  private generatePracticeRecommendations(performance: any[]): string[] {
    const recommendations: string[] = [];
    
    const avgQuality = performance.reduce((sum, p) => sum + p.qualityScore, 0) / performance.length;
    
    if (avgQuality < 5) {
      recommendations.push('基本的な面接対応の練習');
      recommendations.push('STAR法を使った回答構成の練習');
    } else if (avgQuality < 7) {
      recommendations.push('具体例を交えた回答の練習');
      recommendations.push('企業研究の深堀り');
    } else {
      recommendations.push('高度な技術質問の対策');
      recommendations.push('リーダーシップ経験のアピール方法');
    }
    
    return recommendations;
  }
}

export default IntelligentInterviewService;