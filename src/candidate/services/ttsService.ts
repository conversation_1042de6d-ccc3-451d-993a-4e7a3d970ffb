// TTS Service - Clean and optimized
export interface TTSRequest {
    text: string;
    voice?: string;
}

export interface TTSResponse {
    type: 'tts_response' | 'tts_error';
    text?: string;
    voice?: string;
    audio?: string;
    error?: string;
    timestamp: string;
}

export class TTSService {
    private audioCache = new Map<string, { data: string; timestamp: number }>();
    private useWebSpeechFallback = false;
    private readonly maxCacheSize = 50;
    private readonly cacheExpiry = 30 * 60 * 1000;

    constructor(private sendMessage: (message: any) => boolean) {}

    async requestSpeech(text: string, voice: string = "ja-JP-NanamiNeural"): Promise<void> {
        if (!text?.trim()) return;

        this.cleanExpiredCache();

        const cacheKey = `${text}_${voice}`;
        const cached = this.audioCache.get(cacheKey);
        
        if (cached && !this.useWebSpeechFallback) {
            this.playAudio(cached.data);
            return;
        }

        if (this.useWebSpeechFallback) {
            this.useWebSpeechAPI(text);
            return;
        }

        const success = this.sendMessage({
            type: "generate_speech",
            text,
            voice
        });

        if (!success) {
            this.switchToWebSpeechFallback(text);
        }
    }

    handleTTSResponse(response: TTSResponse): void {
        if (response.type === 'tts_response' && response.audio) {
            this.cacheAudio(response.text || '', response.voice || '', response.audio);
            this.playAudio(response.audio);
        } else if (response.type === 'tts_error') {
            console.warn('TTS Error:', response.error);
            this.switchToWebSpeechFallback(response.text || '');
        }
    }

    private playAudio(audioBase64: string): void {
        try {
            const audioData = atob(audioBase64);
            const audioArray = new Uint8Array(audioData.length);
            
            for (let i = 0; i < audioData.length; i++) {
                audioArray[i] = audioData.charCodeAt(i);
            }

            const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);

            audio.oncanplaythrough = () => {
                audio.play().catch(() => this.cleanup(audioUrl));
            };

            audio.onerror = () => this.cleanup(audioUrl);
            audio.onended = () => this.cleanup(audioUrl);

            setTimeout(() => this.cleanup(audioUrl), 10000);

        } catch (error) {
            console.warn('Audio playback failed, fallback to Web Speech');
            this.switchToWebSpeechFallback();
        }
    }

    private useWebSpeechAPI(text: string): void {
        if (!('speechSynthesis' in window) || !text) {
            console.error('Web Speech API is not available');
            return;
        }

        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'ja-JP';
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;

        const voices = speechSynthesis.getVoices();
        const japaneseVoice = voices.find(voice => 
            voice.lang.startsWith('ja') || voice.lang.includes('JP')
        );

        if (japaneseVoice) {
            utterance.voice = japaneseVoice;
        }

        speechSynthesis.speak(utterance);
    }

    private cacheAudio(text: string, voice: string, audio: string): void {
        const cacheKey = `${text}_${voice}`;
        
        if (this.audioCache.size >= this.maxCacheSize) {
            const oldestKey = this.audioCache.keys().next().value;
            if (oldestKey) {
                this.audioCache.delete(oldestKey);
            }
        }

        this.audioCache.set(cacheKey, {
            data: audio,
            timestamp: Date.now()
        });
    }

    private cleanExpiredCache(): void {
        const now = Date.now();
        for (const [key, value] of this.audioCache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                this.audioCache.delete(key);
            }
        }
    }

    private switchToWebSpeechFallback(text?: string): void {
        this.useWebSpeechFallback = true;
        if (text?.trim()) {
            this.useWebSpeechAPI(text.trim());
        }
    }

    private cleanup(audioUrl: string): void {
        URL.revokeObjectURL(audioUrl);
    }

    clearCache(): void {
        this.audioCache.clear();
        this.useWebSpeechFallback = false;
    }

    getCacheSize(): number {
        return this.audioCache.size;
    }
}
