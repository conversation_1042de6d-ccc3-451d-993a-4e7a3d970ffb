import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

export interface SpeechToTextConfig {
  language?: string;
  autoDetectSourceLanguage?: boolean;
  enableVoiceActivityDetection?: boolean;
  silenceTimeoutMs?: number;
}

export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  duration: number;
  isFinal: boolean;
}

export interface SpeechToTextCallbacks {
  onRecognizing?: (result: SpeechRecognitionResult) => void;
  onRecognized?: (result: SpeechRecognitionResult) => void;
  onError?: (error: string) => void;
  onSessionStarted?: () => void;
  onSessionStopped?: () => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
}

class SpeechToTextService {
  private recognizer: SpeechSDK.SpeechRecognizer | null = null;
  private audioConfig: SpeechSDK.AudioConfig | null = null;
  private speechConfig: SpeechSDK.SpeechConfig | null = null;
  private isRecognizing = false;
  private callbacks: SpeechToTextCallbacks = {};
  private silenceTimer: NodeJS.Timeout | null = null;
  private lastSpeechTime = 0;
  private config: SpeechToTextConfig;

  constructor(config: SpeechToTextConfig = {}) {
    this.config = {
      language: 'vi-VN',
      autoDetectSourceLanguage: false,
      enableVoiceActivityDetection: true,
      silenceTimeoutMs: 3000,
      ...config
    };
  }

  async initialize(): Promise<boolean> {
    try {
      // Skip config validation since we're using hardcoded credentials
      // if (!config.azure.speechKey || !config.azure.speechRegion) {
      //   throw new Error('Azure Speech configuration not found');
      // }

      // Create speech config
      this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
        "2qBYgxqzaLNQTidH1ye1V3bh7Ek73xJv2tzXmWbS7ewOwTw4J7OIJQQJ99BFACqBBLyXJ3w3AAAYACOGJwSq",
        "southeastasia"
        // config.azure.speechKey,
        // config.azure.speechRegion
      );

      if (this.config.autoDetectSourceLanguage) {
        this.speechConfig.setProperty(
          SpeechSDK.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,
          ['vi-VN', 'en-US', 'ja-JP'].join(',')
        );
      } else {
        this.speechConfig.speechRecognitionLanguage = this.config.language!;
      }

      // Configure for better accuracy
      this.speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "5000");
      this.speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "3000");
      this.speechConfig.setProperty(SpeechSDK.PropertyId.Speech_SegmentationSilenceTimeoutMs, "3000");

      // Create audio config from microphone
      this.audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();

      return true;
    } catch (error) {
      console.error('Failed to initialize Speech-to-Text service:', error);
      this.callbacks.onError?.(`Initialization failed: ${error}`);
      return false;
    }
  }

  async startContinuousRecognition(): Promise<boolean> {
    if (this.isRecognizing) {
      return true;
    }

    try {
      if (!this.speechConfig || !this.audioConfig) {
        const initialized = await this.initialize();
        if (!initialized) {
          return false;
        }
      }

      // Create recognizer
      this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig!, this.audioConfig!);

      // Set up event handlers
      this.setupEventHandlers();

      // Start continuous recognition
      this.recognizer.startContinuousRecognitionAsync(
        () => {
          this.isRecognizing = true;
          this.callbacks.onSessionStarted?.();
        },
        (error) => {
          console.error('Failed to start recognition:', error);
          this.callbacks.onError?.(`Failed to start recognition: ${error}`);
          this.cleanup();
        }
      );

      return true;
    } catch (error) {
      console.error('Error starting continuous recognition:', error);
      this.callbacks.onError?.(`Start recognition error: ${error}`);
      return false;
    }
  }

  async stopRecognition(): Promise<void> {
    if (!this.isRecognizing || !this.recognizer) {
      return;
    }

    return new Promise((resolve) => {
      this.recognizer!.stopContinuousRecognitionAsync(
        () => {
          this.cleanup();
          this.callbacks.onSessionStopped?.();
          resolve();
        },
        (error) => {
          console.error('Error stopping recognition:', error);
          this.cleanup();
          resolve();
        }
      );
    });
  }

  setCallbacks(callbacks: SpeechToTextCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  isActive(): boolean {
    return this.isRecognizing;
  }

  private setupEventHandlers(): void {
    if (!this.recognizer) return;

    // Recognizing event (intermediate results)
    this.recognizer.recognizing = (sender, event) => {
      if (event.result.text) {
        this.callbacks.onRecognizing?.({
          text: event.result.text,
          confidence: 0.5, // Intermediate result
          duration: event.result.duration / 10000, // Convert to seconds
          isFinal: false
        });
      }
    };

    // Recognized event (final results)
    this.recognizer.recognized = (sender, event) => {
      if (event.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && event.result.text) {
        this.lastSpeechTime = Date.now();
        this.resetSilenceTimer();

        this.callbacks.onRecognized?.({
          text: event.result.text,
          confidence: event.result.properties?.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)
            ? JSON.parse(event.result.properties.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)).NBest?.[0]?.Confidence || 0.8
            : 0.8,
          duration: event.result.duration / 10000,
          isFinal: true
        });
      } else if (event.result.reason === SpeechSDK.ResultReason.NoMatch) {
        // Handle silence or no speech detected
        this.startSilenceTimer();
      }
    };

    // Speech start/end events
    this.recognizer.speechStartDetected = () => {
      this.callbacks.onSpeechStart?.();
      this.resetSilenceTimer();
    };

    this.recognizer.speechEndDetected = () => {
      this.callbacks.onSpeechEnd?.();
      this.startSilenceTimer();
    };

    // Session events
    this.recognizer.sessionStarted = () => {
      console.log('Speech recognition session started');
    };

    this.recognizer.sessionStopped = () => {
      console.log('Speech recognition session stopped');
      this.cleanup();
    };

    // Error handling
    this.recognizer.canceled = (sender, event) => {
      if (event.reason === SpeechSDK.CancellationReason.Error) {
        this.callbacks.onError?.(`Recognition canceled: ${event.errorDetails}`);
      }
      this.cleanup();
    };
  }

  private startSilenceTimer(): void {
    if (!this.config.enableVoiceActivityDetection) return;

    this.resetSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      if (this.isRecognizing && Date.now() - this.lastSpeechTime > this.config.silenceTimeoutMs!) {
        console.log('Silence timeout reached, stopping recognition');
        this.stopRecognition();
      }
    }, this.config.silenceTimeoutMs);
  }

  private resetSilenceTimer(): void {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }

  private cleanup(): void {
    this.isRecognizing = false;
    this.resetSilenceTimer();

    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }
  }

  dispose(): void {
    this.stopRecognition();
    this.callbacks = {};
  }
}

export const speechToTextService = new SpeechToTextService();
export default SpeechToTextService;
