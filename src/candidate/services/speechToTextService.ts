import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

// Web Speech API types
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  abort(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

export interface SpeechToTextConfig {
  language?: string;
  autoDetectSourceLanguage?: boolean;
  enableVoiceActivityDetection?: boolean;
  silenceTimeoutMs?: number;
}

export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  duration: number;
  isFinal: boolean;
}

export interface SpeechToTextCallbacks {
  onRecognizing?: (result: SpeechRecognitionResult) => void;
  onRecognized?: (result: SpeechRecognitionResult) => void;
  onError?: (error: string) => void;
  onSessionStarted?: () => void;
  onSessionStopped?: () => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
}

class SpeechToTextService {
  private recognizer: SpeechSDK.SpeechRecognizer | null = null;
  private audioConfig: SpeechSDK.AudioConfig | null = null;
  private speechConfig: SpeechSDK.SpeechConfig | null = null;
  private webSpeechRecognizer: SpeechRecognition | null = null;
  private isRecognizing = false;
  private callbacks: SpeechToTextCallbacks = {};
  private silenceTimer: NodeJS.Timeout | null = null;
  private lastSpeechTime = 0;
  private config: SpeechToTextConfig;
  private useWebSpeechFallback = false;

  constructor(config: SpeechToTextConfig = {}) {
    this.config = {
      language: 'vi-VN',
      autoDetectSourceLanguage: false,
      enableVoiceActivityDetection: true,
      silenceTimeoutMs: 3000,
      ...config
    };
  }

  async initialize(): Promise<boolean> {
    try {
      // Try Azure Speech SDK first
      console.log('Attempting to initialize Azure Speech SDK...');

      // Create speech config
      this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
        "2qBYgxqzaLNQTidH1ye1V3bh7Ek73xJv2tzXmWbS7ewOwTw4J7OIJQQJ99BFACqBBLyXJ3w3AAAYACOGJwSq",
        "southeastasia"
      );

      if (this.config.autoDetectSourceLanguage) {
        this.speechConfig.setProperty(
          SpeechSDK.PropertyId.SpeechServiceConnection_AutoDetectSourceLanguages,
          ['vi-VN', 'en-US', 'ja-JP'].join(',')
        );
      } else {
        this.speechConfig.speechRecognitionLanguage = this.config.language!;
      }

      // Configure for better accuracy
      this.speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "5000");
      this.speechConfig.setProperty(SpeechSDK.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "3000");
      this.speechConfig.setProperty(SpeechSDK.PropertyId.Speech_SegmentationSilenceTimeoutMs, "3000");

      // Create audio config from microphone
      this.audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();

      console.log('Azure Speech SDK initialized successfully');
      return true;
    } catch (error) {
      console.warn('Azure Speech SDK failed, falling back to Web Speech API:', error);
      return this.initializeWebSpeechAPI();
    }
  }

  private initializeWebSpeechAPI(): boolean {
    try {
      // Check if Web Speech API is available
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        throw new Error('Web Speech API not supported in this browser');
      }

      this.webSpeechRecognizer = new SpeechRecognition();
      this.webSpeechRecognizer.continuous = true;
      this.webSpeechRecognizer.interimResults = true;
      this.webSpeechRecognizer.lang = this.config.language || 'vi-VN';
      this.webSpeechRecognizer.maxAlternatives = 1;

      this.useWebSpeechFallback = true;
      console.log('Web Speech API initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize Web Speech API:', error);
      this.callbacks.onError?.(`Both Azure and Web Speech API failed: ${error}`);
      return false;
    }
  }

  async startContinuousRecognition(): Promise<boolean> {
    if (this.isRecognizing) {
      return true;
    }

    try {
      if (!this.speechConfig || !this.audioConfig) {
        const initialized = await this.initialize();
        if (!initialized) {
          return false;
        }
      }

      // If we're using Web Speech API fallback
      if (this.useWebSpeechFallback && this.webSpeechRecognizer) {
        return this.startWebSpeechRecognition();
      }

      // Create Azure recognizer
      this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig!, this.audioConfig!);

      // Set up event handlers
      this.setupEventHandlers();

      // Start continuous recognition
      return new Promise<boolean>((resolve) => {
        this.recognizer!.startContinuousRecognitionAsync(
          () => {
            this.isRecognizing = true;
            this.callbacks.onSessionStarted?.();
            resolve(true);
          },
          (error) => {
            console.error('Failed to start Azure recognition:', error);

            // Try Web Speech API as fallback
            console.log('Trying Web Speech API as fallback...');
            this.cleanup();

            if (this.initializeWebSpeechAPI()) {
              this.startWebSpeechRecognition().then(resolve).catch(() => resolve(false));
            } else {
              this.callbacks.onError?.(`Failed to start recognition: ${error}`);
              resolve(false);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error starting continuous recognition:', error);
      this.callbacks.onError?.(`Start recognition error: ${error}`);

      // Try Web Speech API as fallback
      try {
        if (this.initializeWebSpeechAPI()) {
          return this.startWebSpeechRecognition();
        }
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }

      return false;
    }
  }

  private startWebSpeechRecognition(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.webSpeechRecognizer) {
        resolve(false);
        return;
      }

      // Set up event handlers
      this.webSpeechRecognizer.onstart = () => {
        console.log('Web Speech recognition started');
        this.isRecognizing = true;
        this.callbacks.onSessionStarted?.();
        resolve(true);
      };

      this.webSpeechRecognizer.onend = () => {
        console.log('Web Speech recognition ended');
        this.isRecognizing = false;
        this.callbacks.onSessionStopped?.();
      };

      this.webSpeechRecognizer.onspeechstart = () => {
        console.log('Web Speech detected speech start');
        this.callbacks.onSpeechStart?.();
        this.resetSilenceTimer();
      };

      this.webSpeechRecognizer.onspeechend = () => {
        console.log('Web Speech detected speech end');
        this.callbacks.onSpeechEnd?.();
        this.startSilenceTimer();
      };

      this.webSpeechRecognizer.onresult = (event) => {
        const results = event.results;
        const resultIndex = event.resultIndex;

        for (let i = resultIndex; i < results.length; i++) {
          const result = results[i];
          const transcript = result[0].transcript;
          const confidence = result[0].confidence;

          if (result.isFinal) {
            console.log('Web Speech final result:', transcript);
            this.callbacks.onRecognized?.({
              text: transcript,
              confidence: confidence,
              duration: 0, // Not available in Web Speech API
              isFinal: true
            });
            this.lastSpeechTime = Date.now();
          } else {
            console.log('Web Speech interim result:', transcript);
            this.callbacks.onRecognizing?.({
              text: transcript,
              confidence: confidence,
              duration: 0,
              isFinal: false
            });
          }
        }
      };

      this.webSpeechRecognizer.onerror = (event) => {
        console.error('Web Speech recognition error:', event.error);
        this.callbacks.onError?.(`Web Speech error: ${event.error} - ${event.message}`);
        resolve(false);
      };

      // Start recognition
      try {
        this.webSpeechRecognizer.start();
      } catch (error) {
        console.error('Failed to start Web Speech recognition:', error);
        this.callbacks.onError?.(`Failed to start Web Speech: ${error}`);
        resolve(false);
      }
    });
  }

  async stopRecognition(): Promise<void> {
    if (!this.isRecognizing) {
      return;
    }

    // Stop Web Speech API if we're using it
    if (this.useWebSpeechFallback && this.webSpeechRecognizer) {
      try {
        this.webSpeechRecognizer.stop();
        // onend event will call cleanup and callbacks
      } catch (error) {
        console.error('Error stopping Web Speech recognition:', error);
        this.cleanup();
      }
      return;
    }

    // Stop Azure Speech SDK
    if (this.recognizer) {
      return new Promise((resolve) => {
        this.recognizer!.stopContinuousRecognitionAsync(
          () => {
            this.cleanup();
            this.callbacks.onSessionStopped?.();
            resolve();
          },
          (error) => {
            console.error('Error stopping recognition:', error);
            this.cleanup();
            resolve();
          }
        );
      });
    }
  }

  setCallbacks(callbacks: SpeechToTextCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  isActive(): boolean {
    return this.isRecognizing;
  }

  private setupEventHandlers(): void {
    if (!this.recognizer) return;

    // Recognizing event (intermediate results)
    this.recognizer.recognizing = (sender, event) => {
      if (event.result.text) {
        this.callbacks.onRecognizing?.({
          text: event.result.text,
          confidence: 0.5, // Intermediate result
          duration: event.result.duration / 10000, // Convert to seconds
          isFinal: false
        });
      }
    };

    // Recognized event (final results)
    this.recognizer.recognized = (sender, event) => {
      if (event.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && event.result.text) {
        this.lastSpeechTime = Date.now();
        this.resetSilenceTimer();

        this.callbacks.onRecognized?.({
          text: event.result.text,
          confidence: event.result.properties?.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)
            ? JSON.parse(event.result.properties.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)).NBest?.[0]?.Confidence || 0.8
            : 0.8,
          duration: event.result.duration / 10000,
          isFinal: true
        });
      } else if (event.result.reason === SpeechSDK.ResultReason.NoMatch) {
        // Handle silence or no speech detected
        this.startSilenceTimer();
      }
    };

    // Speech start/end events
    this.recognizer.speechStartDetected = () => {
      this.callbacks.onSpeechStart?.();
      this.resetSilenceTimer();
    };

    this.recognizer.speechEndDetected = () => {
      this.callbacks.onSpeechEnd?.();
      this.startSilenceTimer();
    };

    // Session events
    this.recognizer.sessionStarted = () => {
      console.log('Speech recognition session started');
    };

    this.recognizer.sessionStopped = () => {
      console.log('Speech recognition session stopped');
      this.cleanup();
    };

    // Error handling
    this.recognizer.canceled = (sender, event) => {
      if (event.reason === SpeechSDK.CancellationReason.Error) {
        this.callbacks.onError?.(`Recognition canceled: ${event.errorDetails}`);
      }
      this.cleanup();
    };
  }

  private startSilenceTimer(): void {
    if (!this.config.enableVoiceActivityDetection) return;

    this.resetSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      if (this.isRecognizing && Date.now() - this.lastSpeechTime > this.config.silenceTimeoutMs!) {
        console.log('Silence timeout reached, stopping recognition');
        this.stopRecognition();
      }
    }, this.config.silenceTimeoutMs);
  }

  private resetSilenceTimer(): void {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }

  private cleanup(): void {
    this.isRecognizing = false;
    this.resetSilenceTimer();

    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }

    if (this.webSpeechRecognizer) {
      try {
        this.webSpeechRecognizer.abort();
      } catch (error) {
        // Ignore errors when aborting
      }
      this.webSpeechRecognizer = null;
    }
  }

  dispose(): void {
    this.stopRecognition();
    this.callbacks = {};
    this.useWebSpeechFallback = false;
  }
}

export const speechToTextService = new SpeechToTextService();
export default SpeechToTextService;
