import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

// Web Speech API types
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  abort(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

export interface SpeechToTextConfig {
  language?: string;
  autoDetectSourceLanguage?: boolean;
  enableVoiceActivityDetection?: boolean;
  silenceTimeoutMs?: number;
}

export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  duration: number;
  isFinal: boolean;
}

export interface SpeechToTextCallbacks {
  onRecognizing?: (result: SpeechRecognitionResult) => void;
  onRecognized?: (result: SpeechRecognitionResult) => void;
  onError?: (error: string) => void;
  onSessionStarted?: () => void;
  onSessionStopped?: () => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
}

class SpeechToTextService {
  private recognizer: SpeechSDK.SpeechRecognizer | null = null;
  private audioConfig: SpeechSDK.AudioConfig | null = null;
  private speechConfig: SpeechSDK.SpeechConfig | null = null;
  private webSpeechRecognizer: SpeechRecognition | null = null;
  private isRecognizing = false;
  private callbacks: SpeechToTextCallbacks = {};
  private silenceTimer: NodeJS.Timeout | null = null;
  private lastSpeechTime = 0;
  private config: SpeechToTextConfig;
  private useWebSpeechFallback = false;

  constructor(config: SpeechToTextConfig = {}) {
    this.config = {
      language: 'vi-VN',
      autoDetectSourceLanguage: false,
      enableVoiceActivityDetection: true,
      silenceTimeoutMs: 3000,
      ...config
    };
  }

  async initialize(): Promise<boolean> {
    return this.initializeWebSpeechAPI();
  }

  private initializeWebSpeechAPI(): boolean {
    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        throw new Error('Web Speech API not supported in this browser');
      }

      this.webSpeechRecognizer = new SpeechRecognition();
      this.webSpeechRecognizer.continuous = true;
      this.webSpeechRecognizer.interimResults = true;
      this.webSpeechRecognizer.lang = this.config.language || 'vi-VN';
      this.webSpeechRecognizer.maxAlternatives = 1;

      this.useWebSpeechFallback = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Web Speech API:', error);
      this.callbacks.onError?.(`Both Azure and Web Speech API failed: ${error}`);
      return false;
    }
  }

  async startContinuousRecognition(): Promise<boolean> {
    if (this.isRecognizing) {
      return true;
    }

    try {
      if (!this.useWebSpeechFallback && !this.webSpeechRecognizer) {
        const initialized = await this.initialize();
        if (!initialized) {
          return false;
        }
      }

      if (this.useWebSpeechFallback || !this.speechConfig) {
        return this.startWebSpeechRecognition();
      }

      return this.startWebSpeechRecognition();
    } catch (error) {
      console.error('Error starting continuous recognition:', error);
      this.callbacks.onError?.(`Start recognition error: ${error}`);

      try {
        if (this.initializeWebSpeechAPI()) {
          return this.startWebSpeechRecognition();
        }
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }

      return false;
    }
  }

  private startWebSpeechRecognition(): Promise<boolean> {
    return new Promise((resolve) => {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.error('SpeechRecognition not available');
        resolve(false);
        return;
      }

      if (this.webSpeechRecognizer) {
        try {
          this.webSpeechRecognizer.abort();
        } catch (e) {
          console.warn('Error aborting previous recognition:', e);
        }
        this.webSpeechRecognizer = null;
      }

      this.isRecognizing = false;

      this.webSpeechRecognizer = new SpeechRecognition();
      this.webSpeechRecognizer.continuous = true;
      this.webSpeechRecognizer.interimResults = true;
      this.webSpeechRecognizer.lang = this.config.language || 'vi-VN';
      this.webSpeechRecognizer.maxAlternatives = 1;

      this.webSpeechRecognizer.onstart = () => {
        console.log('Web Speech recognition started');
        this.isRecognizing = true;
        this.callbacks.onSessionStarted?.();
        resolve(true);
      };

      this.webSpeechRecognizer.onend = () => {
        console.log('Web Speech recognition ended, isRecognizing:', this.isRecognizing);

        if (this.isRecognizing && this.webSpeechRecognizer) {
          console.log('Recognition ended unexpectedly, attempting restart...');

          setTimeout(() => {
            if (this.isRecognizing && this.webSpeechRecognizer) {
              try {
                this.startWebSpeechRecognition();
              } catch (e) {
                console.error('Failed to restart recognition:', e);
                this.isRecognizing = false;
                this.callbacks.onSessionStopped?.();
              }
            }
          }, 500);
        } else {
          this.isRecognizing = false;
          this.callbacks.onSessionStopped?.();
        }
      };

      this.webSpeechRecognizer.onspeechstart = () => {
        console.log('Web Speech detected speech start');
        this.callbacks.onSpeechStart?.();
        this.resetSilenceTimer();
      };

      this.webSpeechRecognizer.onspeechend = () => {
        console.log('Web Speech detected speech end');
        this.callbacks.onSpeechEnd?.();
        this.startSilenceTimer();
      };

      this.webSpeechRecognizer.onresult = (event) => {
        const results = event.results;
        const resultIndex = event.resultIndex;

        for (let i = resultIndex; i < results.length; i++) {
          const result = results[i];
          const transcript = result[0].transcript;
          const confidence = result[0].confidence;

          if (result.isFinal) {
            console.log('Web Speech final result:', transcript);
            this.callbacks.onRecognized?.({
              text: transcript,
              confidence: confidence,
              duration: 0,
              isFinal: true
            });
            this.lastSpeechTime = Date.now();
          } else {
            console.log('Web Speech interim result:', transcript);
            this.callbacks.onRecognizing?.({
              text: transcript,
              confidence: confidence,
              duration: 0,
              isFinal: false
            });
          }
        }
      };

      this.webSpeechRecognizer.onerror = (event) => {
        console.error('Web Speech recognition error:', event.error);

        // Handle specific error types
        if (event.error === 'aborted') {
          console.log('Recognition was aborted, attempting to restart...');
          setTimeout(() => {
            try {
              if (this.isRecognizing) {
                this.webSpeechRecognizer?.start();
              }
            } catch (e) {
              console.error('Failed to restart after abort:', e);
              this.callbacks.onError?.(`Failed to restart recognition: ${e}`);
              this.isRecognizing = false;
            }
          }, 500);
        } else if (event.error === 'network') {
          this.callbacks.onError?.('Network error occurred. Please check your internet connection.');
        } else if (event.error === 'no-speech') {
          // No speech detected - this is normal, don't show error to user
          console.log('No speech detected');
        } else {
          this.callbacks.onError?.(`Web Speech error: ${event.error}`);
        }

        // Only resolve as false for fatal errors
        if (['audio-capture', 'not-allowed', 'service-not-allowed'].includes(event.error)) {
          resolve(false);
        }
      };

      // Start recognition
      try {
        this.webSpeechRecognizer.start();
      } catch (error) {
        console.error('Failed to start Web Speech recognition:', error);
        this.callbacks.onError?.(`Failed to start Web Speech: ${error}`);
        resolve(false);
      }
    });
  }

  async stopRecognition(): Promise<void> {
    if (!this.isRecognizing) {
      return;
    }

    // Set flag first to prevent auto-restart in onend handler
    this.isRecognizing = false;

    // Stop Web Speech API if we're using it
    if ((this.useWebSpeechFallback || !this.recognizer) && this.webSpeechRecognizer) {
      try {
        console.log('Stopping Web Speech recognition...');

        // Use abort() instead of stop() to immediately cancel
        // stop() waits for speech end which can cause issues
        this.webSpeechRecognizer.abort();

        // Manually trigger cleanup since abort() might not trigger onend reliably
        setTimeout(() => {
          if (this.webSpeechRecognizer) {
            this.cleanup();
            this.callbacks.onSessionStopped?.();
          }
        }, 300);
      } catch (error) {
        console.error('Error stopping Web Speech recognition:', error);
        this.cleanup();
        this.callbacks.onSessionStopped?.();
      }
      return;
    }

    // Stop Azure Speech SDK
    if (this.recognizer) {
      return new Promise((resolve) => {
        this.recognizer!.stopContinuousRecognitionAsync(
          () => {
            this.cleanup();
            this.callbacks.onSessionStopped?.();
            resolve();
          },
          (error) => {
            console.error('Error stopping recognition:', error);
            this.cleanup();
            this.callbacks.onSessionStopped?.();
            resolve();
          }
        );
      });
    }
  }

  setCallbacks(callbacks: SpeechToTextCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  isActive(): boolean {
    return this.isRecognizing;
  }

  private setupEventHandlers(): void {
    if (!this.recognizer) return;

    // Recognizing event (intermediate results)
    this.recognizer.recognizing = (sender, event) => {
      if (event.result.text) {
        this.callbacks.onRecognizing?.({
          text: event.result.text,
          confidence: 0.5, // Intermediate result
          duration: event.result.duration / 10000, // Convert to seconds
          isFinal: false
        });
      }
    };

    // Recognized event (final results)
    this.recognizer.recognized = (sender, event) => {
      if (event.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && event.result.text) {
        this.lastSpeechTime = Date.now();
        this.resetSilenceTimer();

        this.callbacks.onRecognized?.({
          text: event.result.text,
          confidence: event.result.properties?.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)
            ? JSON.parse(event.result.properties.getProperty(SpeechSDK.PropertyId.SpeechServiceResponse_JsonResult)).NBest?.[0]?.Confidence || 0.8
            : 0.8,
          duration: event.result.duration / 10000,
          isFinal: true
        });
      } else if (event.result.reason === SpeechSDK.ResultReason.NoMatch) {
        // Handle silence or no speech detected
        this.startSilenceTimer();
      }
    };

    // Speech start/end events
    this.recognizer.speechStartDetected = () => {
      this.callbacks.onSpeechStart?.();
      this.resetSilenceTimer();
    };

    this.recognizer.speechEndDetected = () => {
      this.callbacks.onSpeechEnd?.();
      this.startSilenceTimer();
    };

    // Session events
    this.recognizer.sessionStarted = () => {
      console.log('Speech recognition session started');
    };

    this.recognizer.sessionStopped = () => {
      console.log('Speech recognition session stopped');
      this.cleanup();
    };

    // Error handling
    this.recognizer.canceled = (sender, event) => {
      if (event.reason === SpeechSDK.CancellationReason.Error) {
        this.callbacks.onError?.(`Recognition canceled: ${event.errorDetails}`);
      }
      this.cleanup();
    };
  }

  private startSilenceTimer(): void {
    if (!this.config.enableVoiceActivityDetection) return;

    this.resetSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      if (this.isRecognizing && Date.now() - this.lastSpeechTime > this.config.silenceTimeoutMs!) {
        console.log('Silence timeout reached, stopping recognition');
        this.stopRecognition();
      }
    }, this.config.silenceTimeoutMs);
  }

  private resetSilenceTimer(): void {
    if (this.silenceTimer) {
      clearTimeout(this.silenceTimer);
      this.silenceTimer = null;
    }
  }

  private cleanup(): void {
    this.isRecognizing = false;
    this.resetSilenceTimer();

    if (this.recognizer) {
      this.recognizer.close();
      this.recognizer = null;
    }

    if (this.webSpeechRecognizer) {
      try {
        this.webSpeechRecognizer.abort();
      } catch (error) {
        // Ignore errors when aborting
      }
      this.webSpeechRecognizer = null;
    }
  }

  dispose(): void {
    this.stopRecognition();
    this.callbacks = {};
    this.useWebSpeechFallback = false;
  }
}

export const speechToTextService = new SpeechToTextService();
export default SpeechToTextService;
