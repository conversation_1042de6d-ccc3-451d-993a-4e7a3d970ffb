// Interview Service - Combines Socket, TTS, and WebRTC functionality
import { interviewSocket, SocketMessage } from './interviewSocket';
import { TTSService, TTSResponse } from './ttsService';
import { webrtcService, WebRTCMessage } from './webrtcService';

export interface InterviewMessage {
    type: string;
    data: any;
    timestamp: string;
}

export interface VideoAnalysisData {
    question_id: string;
    video_analysis: {
        analysis_id: string;
        scores: {
            communication_effectiveness: number;
            non_verbal_communication: number;
            content_quality: number;
            overall_impression: number;
            question_specific_score: number;
        };
        insights: {
            confidence_level: string;
            engagement_quality: string;
            authenticity: string;
            stress_indicators: string;
            communication_style: string;
        };
        feedback: string;
        strengths: string[];
        improvements: string[];
    };
    text_analysis: {
        content: string;
        length: number;
        word_count: number;
    };
    combined_evaluation: {
        overall_score: number;
        content_score: number;
        video_score: number;
        weights_used: {
            content: number;
            video: number;
        };
        evaluation_method: string;
    };
    timestamp: string;
}

export interface VideoRecordingData {
    segment_id: string;
    question_id: string;
    duration_seconds: number;
    video_size_bytes: number;
    frame_count: number;
    timestamp: string;
}

export class InterviewService {
    private ttsService: TTSService;
    private messageHandlers: Map<string, (data: any) => void> = new Map();

    constructor() {
        // Initialize TTS service with socket send function
        this.ttsService = new TTSService((message) => interviewSocket.send(message));

        // Set up WebRTC service with socket signaling
        webrtcService.onSignalingMessage((message: WebRTCMessage) => {
            this.handleWebRTCSignaling(message);
        });

        // Set up socket message handling
        interviewSocket.onMessage((message: SocketMessage) => {
            this.handleSocketMessage(message);
        });
    }

    // Connection management
    async connect(sessionId: string): Promise<boolean> {
        return interviewSocket.connect(sessionId);
    }

    disconnect(): void {
        interviewSocket.disconnect();
        this.ttsService.clearCache();
        webrtcService.cleanup();
    }

    // Message sending methods
    sendInterviewStart(scenarioId: string, companyInfo: any, agentNotes: string[]): boolean {
        return interviewSocket.send({
            type: "connection_request",
            scenario_id: scenarioId,
            company_info: companyInfo,
            agent_notes: agentNotes
        });
    }

    sendVideoData(type: string, data: any): boolean {
        return interviewSocket.send({
            type,
            ...data
        });
    }

    // TTS methods
    async requestSpeech(question: any, voice: string = "vi-VN-HoaiMyNeural"): Promise<void> {
        return this.ttsService.requestSpeech(question, voice);
    }

    // Event handlers
    onMessage(handler: (message: SocketMessage) => void): () => void {
        return interviewSocket.onMessage(handler);
    }

    onError(handler: (error: string) => void): () => void {
        return interviewSocket.onError(handler);
    }

    onStatus(handler: (connected: boolean) => void): () => void {
        return interviewSocket.onStatus(handler);
    }

    onInterviewStart(handler: (data: any) => void): void {
        this.messageHandlers.set('interview_started', handler);
    }

    onQuestionReady(handler: (data: any) => void): void {
        this.messageHandlers.set('question_ready', handler);
    }

    onFeedbackReady(handler: (data: any) => void): void {
        this.messageHandlers.set('feedback_ready', handler);
    }

    onInterviewComplete(handler: (data: any) => void): void {
        this.messageHandlers.set('interview_complete', handler);
    }

    onTTSResponse(handler: (data: TTSResponse) => void): void {
        this.messageHandlers.set('tts_response', handler);
    }

    onTTSError(handler: (data: TTSResponse) => void): void {
        this.messageHandlers.set('tts_error', handler);
    }

    // Video analysis handlers
    onVideoAnalysisComplete(handler: (data: VideoAnalysisData) => void): void {
        this.messageHandlers.set('answer_analysis_complete', handler);
    }

    onVideoRecordingStarted(handler: (data: VideoRecordingData) => void): void {
        this.messageHandlers.set('video_recording_started', handler);
    }

    onVideoRecordingEnded(handler: (data: VideoRecordingData) => void): void {
        this.messageHandlers.set('video_recording_ended', handler);
    }

    onProcessingStatus(handler: (data: { message: string; stage?: string }) => void): void {
        this.messageHandlers.set('processing_status', handler);
    }

    // WebRTC methods
    async initializeWebRTC(sessionId: string, isInitiator: boolean = false): Promise<boolean> {
        return webrtcService.initializeConnection(sessionId, isInitiator);
    }

    async createWebRTCOffer(): Promise<RTCSessionDescriptionInit | null> {
        return webrtcService.createOffer();
    }

    onWebRTCRemoteStream(handler: (stream: MediaStream) => void): void {
        webrtcService.onRemoteStream(handler);
    }

    onWebRTCConnectionState(handler: (state: string) => void): void {
        webrtcService.onConnectionState(handler);
    }

    getWebRTCLocalStream(): MediaStream | null {
        return webrtcService.getLocalStream();
    }

    getWebRTCRemoteStream(): MediaStream | null {
        return webrtcService.getRemoteStream();
    }

    getWebRTCConnectionState(): string {
        return webrtcService.getConnectionState();
    }

    // Connection status monitoring
    getConnectionStatus(): string {
        if (!this.socket) return 'disconnected';

        switch (this.socket.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }

    private handleSocketMessage(message: SocketMessage): void {
        if (message.type === 'tts_response' || message.type === 'tts_error') {
            this.ttsService.handleTTSResponse(message as TTSResponse);
            const handler = this.messageHandlers.get(message.type);
            if (handler) handler(message);
            return;
        }

        // Handle WebRTC signaling messages
        if (message.type.startsWith('webrtc_')) {
            console.log('Received WebRTC signaling message:', message.type);
            
            let data;
            const type = message.type.replace('webrtc_', '') as any;
            
            // Extract correct data based on message type
            switch(type) {
                case 'offer':
                    data = message.offer;
                    break;
                case 'answer':
                    data = message.answer;
                    break;
                case 'candidate':
                    data = message.candidate;
                    break;
                default:
                    data = message;
            }
            
            const webrtcMessage: WebRTCMessage = {
                type: type,
                data: data,
                sessionId: message.sessionId || '',
                timestamp: message.timestamp
            };
            
            console.log('Processing WebRTC message of type:', type);
            webrtcService.handleSignalingMessage(webrtcMessage);
            return;
        }

        // Handle other message types
        const handler = this.messageHandlers.get(message.type);
        if (handler) {
            // Pass message.message if it exists, otherwise pass the whole message
            const data = message.message || message;
            handler(data);
        }
    }

    private handleWebRTCSignaling(message: WebRTCMessage): void {
        // Send WebRTC signaling messages through socket
        switch (message.type) {
            case 'offer':
                interviewSocket.sendWebRTCOffer(message.data);
                break;
            case 'answer':
                interviewSocket.sendWebRTCAnswer(message.data);
                break;
            case 'candidate':
                interviewSocket.sendWebRTCCandidate(message.data);
                break;
            case 'ready':
                interviewSocket.sendWebRTCReady();
                break;
        }
    }
}

export const interviewService = new InterviewService(); 