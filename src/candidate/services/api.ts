/**
 * 統一APIサービス
 * 全てのデータアクセスを一元管理し、実際のAPIとモックデータを切り替え可能にする
 */

import { 
  ComprehensiveInterviewReport, 
  STARCExample, 
  CandidateProfile,
  EvaluationConfig 
} from '../types/evaluation';

// 統合型定義をインポート
import type {
  InterviewSessionResponse,
  AnswerSubmissionRequest,
  AnswerSubmissionResponse,
  AudioAnalysisResponse,
  Question
} from '../../shared/types';

// 統合モックサービス
import { UnifiedMockDataService } from '../../shared/services/unifiedMockData';
import config from '../config';
import logger from '../utils/logger';

// 環境設定 - 設定ファイルから取得
const API_BASE_URL = config.api.baseUrl;
const USE_MOCK_DATA = config.api.useMockData;

/**
 * APIレスポンスの共通型
 */
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

/**
 * エラーハンドリング用のカスタムエラー
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * HTTP リクエストのベースクラス
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  /**
   * HTTP GET リクエスト
   */
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          await response.text()
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Network request failed', 0, error);
    }
  }

  /**
   * HTTP POST リクエスト
   */
  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          await response.text()
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Network request failed', 0, error);
    }
  }
}

// API クライアントのシングルトンインスタンス
const apiClient = new ApiClient(API_BASE_URL);

/**
 * 面接評価レポート API
 */
export class InterviewReportApi {
  /**
   * 包括的な面接レポートを取得
   */
  static async getComprehensiveReport(
    candidateId: string,
    interviewId: string
  ): Promise<ComprehensiveInterviewReport> {
    if (USE_MOCK_DATA) {
      // モックデータを返す
      const { generateMockComprehensiveReport } = await import('../data/mockApiData');
      return generateMockComprehensiveReport(candidateId, interviewId);
    }

    try {
      const response = await apiClient.get<ComprehensiveInterviewReport>(
        `/api/interviews/${interviewId}/reports/comprehensive?candidateId=${candidateId}`
      );
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch comprehensive report', error);
      // フォールバックとしてモックデータを返す
      const { generateMockComprehensiveReport } = await import('../data/mockApiData');
      return generateMockComprehensiveReport(candidateId, interviewId);
    }
  }

  /**
   * STAR+C 分析例を取得
   */
  static async getSTARExamples(
    candidateId: string,
    questionIds: string[]
  ): Promise<STARCExample[]> {
    if (USE_MOCK_DATA) {
      const { generateMockSTARExamples } = await import('../data/mockApiData');
      return generateMockSTARExamples(questionIds);
    }

    try {
      const response = await apiClient.post<STARCExample[]>(
        `/api/candidates/${candidateId}/star-analysis`,
        { questionIds }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch STAR examples:', error);
      const { generateMockSTARExamples } = await import('../data/mockApiData');
      return generateMockSTARExamples(questionIds);
    }
  }

  /**
   * 候補者プロファイルを取得
   */
  static async getCandidateProfile(candidateId: string): Promise<CandidateProfile> {
    if (USE_MOCK_DATA) {
      const { generateMockCandidateProfile } = await import('../data/mockApiData');
      return generateMockCandidateProfile(candidateId);
    }

    try {
      const response = await apiClient.get<CandidateProfile>(
        `/api/candidates/${candidateId}/profile`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch candidate profile:', error);
      const { generateMockCandidateProfile } = await import('../data/mockApiData');
      return generateMockCandidateProfile(candidateId);
    }
  }
}

/**
 * 面接データ API
 */
export class InterviewDataApi {
  /**
   * 面接質問一覧を取得
   */
  static async getQuestions(
    scenarioId: string,
    candidateProfile: CandidateProfile
  ): Promise<string[]> {
    if (USE_MOCK_DATA) {
      const questions = await UnifiedMockDataService.getBasicQuestions();
      return questions.map(q => q.text);
    }

    try {
      const response = await apiClient.post<string[]>(
        `/api/scenarios/${scenarioId}/questions`,
        { candidateProfile }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      const questions = await UnifiedMockDataService.getBasicQuestions();
      return questions.map(q => q.text);
    }
  }

  /**
   * ミーティングトークンで面接セッション開始
   */
  static async startInterviewSession(token: string): Promise<InterviewSessionResponse> {
    if (USE_MOCK_DATA) {
      return UnifiedMockDataService.startInterviewSession(token);
    }

    try {
      const response = await apiClient.get<InterviewSessionResponse>(
        `/api/candidate/interview?token=${token}`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to start interview session:', error);
      return UnifiedMockDataService.startInterviewSession(token);
    }
  }

  /**
   * 回答送信
   */
  static async submitAnswer(request: AnswerSubmissionRequest): Promise<AnswerSubmissionResponse> {
    if (USE_MOCK_DATA) {
      return UnifiedMockDataService.submitAnswer(request);
    }

    try {
      const formData = new FormData();
      formData.append('questionId', request.questionId);
      formData.append('textAnswer', request.textAnswer);
      if (request.audioFile) {
        formData.append('audioFile', request.audioFile);
      }
      if (request.duration) {
        formData.append('duration', request.duration.toString());
      }

      const response = await fetch(`${API_BASE_URL}/api/candidate/answers`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new ApiError(`HTTP ${response.status}`, response.status);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Failed to submit answer:', error);
      return UnifiedMockDataService.submitAnswer(request);
    }
  }

  /**
   * 音声解析
   */
  static async analyzeAudio(audioFile: File): Promise<AudioAnalysisResponse> {
    if (USE_MOCK_DATA) {
      return UnifiedMockDataService.analyzeAudio(audioFile);
    }

    try {
      const formData = new FormData();
      formData.append('audioFile', audioFile);
      formData.append('language', 'ja-JP');

      const response = await fetch(`${API_BASE_URL}/api/audio/analyze`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new ApiError(`HTTP ${response.status}`, response.status);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Failed to analyze audio:', error);
      return UnifiedMockDataService.analyzeAudio(audioFile);
    }
  }

  /**
   * 評価設定を取得
   */
  static async getEvaluationConfig(
    position: string,
    industry: string
  ): Promise<EvaluationConfig> {
    if (USE_MOCK_DATA) {
      const { generateMockEvaluationConfig } = await import('../data/mockApiData');
      return generateMockEvaluationConfig(position, industry);
    }

    try {
      const response = await apiClient.get<EvaluationConfig>(
        `/api/evaluation-config?position=${position}&industry=${industry}`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch evaluation config:', error);
      const { generateMockEvaluationConfig } = await import('../data/mockApiData');
      return generateMockEvaluationConfig(position, industry);
    }
  }
}

/**
 * ユーティリティ関数
 */
export class ApiUtils {
  /**
   * API の健全性をチェック
   */
  static async healthCheck(): Promise<boolean> {
    if (USE_MOCK_DATA) {
      return true;
    }

    try {
      const response = await apiClient.get<{ status: string }>('/api/health');
      return response.data.status === 'ok';
    } catch (error) {
      console.warn('API health check failed:', error);
      return false;
    }
  }

  /**
   * 現在の設定情報を取得
   */
  static getApiConfig() {
    return {
      baseUrl: API_BASE_URL,
      useMockData: USE_MOCK_DATA,
      environment: process.env.NODE_ENV,
    };
  }
}

// デフォルトエクスポート
export default {
  InterviewReportApi,
  InterviewDataApi,
  ApiUtils,
  ApiError,
};

// 統合モックサービスのエクスポート
export { UnifiedMockDataService };