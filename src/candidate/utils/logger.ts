/**
 * ロギングユーティリティ
 * 環境に応じてログ出力を制御
 */

import config from '../config';
import RemoteLogService from './remoteLogService';

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

const logLevels: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

class Logger {
  private minLevel: number;
  private enableConsole: boolean;

  constructor() {
    this.minLevel = logLevels[config.log.level as LogLevel] || 0;
    this.enableConsole = config.log.enableConsoleLog;
  }

  private shouldLog(level: LogLevel): boolean {
    return this.enableConsole && logLevels[level] >= this.minLevel;
  }

  private formatMessage(level: LogLevel, message: string, context?: any): string {
    const timestamp = new Date().toISOString();
    const contextStr = context ? ` | ${JSON.stringify(context)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${contextStr}`;
  }

  debug(message: string, context?: any): void {
    if (this.shouldLog('debug')) {
      console.log(this.formatMessage('debug', message, context));
    }
  }

  info(message: string, context?: any): void {
    if (this.shouldLog('info')) {
      console.info(this.formatMessage('info', message, context));
    }
  }

  warn(message: string, context?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message, context));
    }
  }

  error(message: string, error?: Error | any): void {
    if (this.shouldLog('error')) {
      const errorInfo = error instanceof Error ? {
        message: error.message,
        stack: error.stack,
      } : error;
      console.error(this.formatMessage('error', message, errorInfo));
    }

    // プロダクション環境ではリモートログ送信
    if (config.log.enableRemoteLogging && error) {
      this.sendToRemote('error', message, error);
    }
  }

  // グループログ（デバッグ時に便利）
  group(label: string): void {
    if (this.shouldLog('debug')) {
      console.group(label);
    }
  }

  groupEnd(): void {
    if (this.shouldLog('debug')) {
      console.groupEnd();
    }
  }

  // タイマー機能（パフォーマンス測定）
  time(label: string): void {
    if (this.shouldLog('debug')) {
      console.time(label);
    }
  }

  timeEnd(label: string): void {
    if (this.shouldLog('debug')) {
      console.timeEnd(label);
    }
  }

  // テーブル表示（デバッグ時に便利）
  table(data: any): void {
    if (this.shouldLog('debug')) {
      console.table(data);
    }
  }

  // リモートログ送信（将来的な実装用）
  private async sendToRemote(level: LogLevel, message: string, context?: any): Promise<void> {
    try {
      await RemoteLogService.send({ level, message, context });
    } catch (e) {
      if (this.shouldLog('warn')) {
        console.warn(this.formatMessage('warn', 'Failed to send remote log', e));
      }
    }
  }
}

// シングルトンインスタンス
const logger = new Logger();

export default logger;