export interface RemoteLogPayload {
  level: string;
  message: string;
  context?: any;
}

import config from "../config";

export default class RemoteLogService {
  static async send(payload: RemoteLogPayload): Promise<void> {
    if (!config.log.enableRemoteLogging) return;
    try {
      await fetch(`${config.api.baseUrl}/logs`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
    } catch (err) {
      // avoid endless loop
      console.error("Remote log send failed", err);
    }
  }
}
