import React, { useState, useRef } from 'react';
import {
  Box,
  Textarea,
  Button,
  VStack,
  HStack,
  Text,
  useToast,
  VisuallyHidden,
} from '@chakra-ui/react';

interface AnswerInputProps {
  onSubmit: (audioBlob: Blob) => void;
  isLoading: boolean;
}

const AnswerInput: React.FC<AnswerInputProps> = ({ onSubmit, isLoading }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const toast = useToast();

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          sampleSize: 16,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      
      streamRef.current = stream;
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('録音データのサイズ:', event.data.size, 'bytes');
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        let audioContext: AudioContext | null = null;
        try {
          if (audioChunksRef.current.length === 0) {
            throw new Error('録音データが空です');
          }

          // WebM形式の音声データを取得
          const webmBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          console.log('録音データのサイズ:', webmBlob.size, 'bytes');
          
          // WebMをWAVに変換
          audioContext = new AudioContext({
            sampleRate: 16000,
            latencyHint: 'interactive'
          });
          const audioData = await webmBlob.arrayBuffer();
          const audioBuffer = await audioContext.decodeAudioData(audioData);
          
          // 音声データの正規化
          const normalizedBuffer = await normalizeAudio(audioBuffer);
          
          // WAVファイルの生成
          const wavBlob = await convertToWav(normalizedBuffer);
          console.log('WAVデータのサイズ:', wavBlob.size, 'bytes');
          console.log('WAVデータの形式:', wavBlob.type);
          
          onSubmit(wavBlob);
        } catch (error: unknown) {
          console.error('音声処理エラー:', error);
          const errorMessage = error instanceof Error ? error.message : '不明なエラーが発生しました';
          toast({
            title: 'エラー',
            description: '音声の処理に失敗しました: ' + errorMessage,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        } finally {
          // ストリームの停止
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
          }
          // AudioContextのクローズ
          if (audioContext) {
            await audioContext.close();
          }
        }
      };

      mediaRecorder.start(1000); // 1秒ごとにデータを取得
      setIsRecording(true);
    } catch (error: unknown) {
      console.error('録音開始エラー:', error);
      const errorMessage = error instanceof Error ? error.message : '不明なエラーが発生しました';
      toast({
        title: 'エラー',
        description: 'マイクへのアクセスに失敗しました: ' + errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // AudioBufferをWAVに変換する関数
  const convertToWav = async (audioBuffer: AudioBuffer): Promise<Blob> => {
    const numChannels = 1; // モノラル
    const sampleRate = 16000; // 16kHz
    const format = 1; // PCM
    const bitDepth = 16;
    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = audioBuffer.length * blockAlign;
    const buffer = new ArrayBuffer(44 + dataSize);
    const view = new DataView(buffer);

    // WAVヘッダーの書き込み
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    // RIFFヘッダー
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    writeString(8, 'WAVE');

    // fmt チャンク
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true); // fmt チャンクのサイズ
    view.setUint16(20, format, true); // フォーマットタイプ（PCM）
    view.setUint16(22, numChannels, true); // チャンネル数
    view.setUint32(24, sampleRate, true); // サンプルレート
    view.setUint32(28, byteRate, true); // バイトレート
    view.setUint16(32, blockAlign, true); // ブロックアライメント
    view.setUint16(34, bitDepth, true); // ビット深度

    // data チャンク
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    // 音声データの書き込み
    const offset = 44;
    const channelData = audioBuffer.getChannelData(0); // モノラルなので最初のチャンネルのみ使用

    for (let i = 0; i < audioBuffer.length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset + (i * bytesPerSample), value, true);
    }

    // WAVファイルの生成
    const wavBlob = new Blob([buffer], { type: 'audio/wav' });
    
    // デバッグ用：WAVヘッダーの検証（修正版）
    const headerView = new DataView(buffer.slice(0, 44));
    console.log('WAVヘッダー検証:', {
      riff: String.fromCharCode(...Array.from(new Uint8Array(buffer.slice(0, 4)))),
      wave: String.fromCharCode(...Array.from(new Uint8Array(buffer.slice(8, 12)))),
      fmt: String.fromCharCode(...Array.from(new Uint8Array(buffer.slice(12, 16)))),
      data: String.fromCharCode(...Array.from(new Uint8Array(buffer.slice(36, 40)))),
      channels: headerView.getUint16(22, true),
      sampleRate: headerView.getUint32(24, true),
      bitDepth: headerView.getUint16(34, true)
    });

    return wavBlob;
  };

  // 音声データの正規化
  const normalizeAudio = async (audioBuffer: AudioBuffer): Promise<AudioBuffer> => {
    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;

    // ゲインの設定
    const gainNode = offlineContext.createGain();
    gainNode.gain.value = 1.0;

    // コンプレッサーの設定
    const compressor = offlineContext.createDynamicsCompressor();
    compressor.threshold.value = -24;
    compressor.knee.value = 30;
    compressor.ratio.value = 12;
    compressor.attack.value = 0.003;
    compressor.release.value = 0.25;

    // 接続
    source.connect(gainNode);
    gainNode.connect(compressor);
    compressor.connect(offlineContext.destination);

    source.start(0);
    return await offlineContext.startRendering();
  };

  // モック回答を送信する関数
  const sendMockAnswer = () => {
    // モック音声データを作成
    const mockAudioData = new ArrayBuffer(1024);
    const mockBlob = new Blob([mockAudioData], { type: 'audio/wav' });
    onSubmit(mockBlob);
  };

  return (
    <VStack spacing={{ base: 3, md: 4 }} w="full" maxW="400px" mx="auto">
      <VStack spacing={{ base: 2, md: 3 }}>
        <Text 
          fontSize={{ base: "md", md: "lg" }} 
          fontWeight="bold" 
          color="primary.700"
          textAlign="center"
        >
          回答方法を選択してください
        </Text>
        
        {/* モバイルでは縦並び、デスクトップでは横並び */}
        <VStack spacing={3} w="full">
          <Button
            colorScheme="primary"
            onClick={sendMockAnswer}
            isLoading={isLoading}
            size={{ base: "md", md: "lg" }}
            leftIcon={<Text role="img" aria-label="サンプル">✨</Text>}
            w="full"
            py={{ base: 6, md: 4 }}
            fontSize={{ base: "sm", md: "md" }}
            minH={{ base: "50px", md: "auto" }}
            bgColor="primary.500"
            color="white"
            _hover={{ bgColor: "primary.600" }}
            _active={{ bgColor: "primary.700" }}
            _focus={{
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
            boxShadow="md"
            aria-label="サンプル回答で進む - クリックすると模擬回答で次のステップに進みます"
          >
            サンプル回答で進む
            <VisuallyHidden>
              模擬的な回答で面接を進め、UIの確認をすることができます
            </VisuallyHidden>
          </Button>
          
          <Text 
            fontSize={{ base: "xs", md: "sm" }} 
            color="neutral.500" 
            textAlign="center"
          >
            または
          </Text>
          
          <Button
            colorScheme={isRecording ? 'red' : 'primary'}
            onClick={isRecording ? stopRecording : startRecording}
            isLoading={isLoading}
            size={{ base: "md", md: "lg" }}
            leftIcon={
              <Text role="img" aria-label={isRecording ? "録音中" : "マイク"}>
                {isRecording ? '🔴' : '🎤'}
              </Text>
            }
            w="full"
            py={{ base: 6, md: 4 }}
            fontSize={{ base: "sm", md: "md" }}
            minH={{ base: "50px", md: "auto" }}
            variant="outline"
            _focus={{
              borderColor: isRecording ? "red.500" : "primary.500",
              boxShadow: isRecording ? 
                "0 0 0 3px rgba(239, 68, 68, 0.3)" : 
                "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
            aria-label={isRecording ? 
              "録音を停止して音声回答を送信します" : 
              "音声録音を開始して面接の回答を行います"
            }
            aria-live="polite"
            aria-describedby="recording-status"
          >
            {isRecording ? '録音停止' : '音声で回答'}
            <VisuallyHidden id="recording-status">
              {isRecording ? 
                "現在録音中です。クリックすると録音を停止します。" : 
                "クリックすると音声録音を開始します。"
              }
            </VisuallyHidden>
          </Button>
        </VStack>
        
        <Text 
          fontSize={{ base: "xs", md: "sm" }} 
          color="neutral.600" 
          textAlign="center"
          px={{ base: 4, md: 0 }}
        >
          💡 サンプル回答を使ってスムーズにUIを確認できます
        </Text>
      </VStack>
      
      {transcript && (
        <Box 
          p={{ base: 3, md: 4 }} 
          bg="gray.50" 
          borderRadius="md" 
          w="full"
        >
          <Text fontSize={{ base: "sm", md: "md" }}>{transcript}</Text>
        </Box>
      )}
    </VStack>
  );
};

export default AnswerInput;