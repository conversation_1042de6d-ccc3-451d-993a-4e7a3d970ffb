/**
 * 面接開始前の深呼吸プロンプトコンポーネント
 * マイクロモーメント最適化: 緊張緩和のための呼吸エクササイズ
 */
import { useState, useEffect } from "react";
import { Box, VStack, Text, Button, Progress, Icon, VisuallyHidden } from "@chakra-ui/react";
import { motion, useAnimation } from "framer-motion";

interface BreathingPromptProps {
  onComplete: () => void;
}

export const BreathingPrompt: React.FC<BreathingPromptProps> = ({ onComplete }) => {
  const [breathCount, setBreathCount] = useState(0);
  const [phase, setPhase] = useState<"inhale" | "hold" | "exhale">("inhale");
  const [isBreathing, setIsBreathing] = useState(false);
  const controls = useAnimation();

  const startBreathing = () => {
    setIsBreathing(true);
    setBreathCount(0);
    performBreathCycle();
  };

  const performBreathCycle = async () => {
    try {
      // 吸う (3秒)
      setPhase("inhale");
      await controls.start({
        scale: 1.2,
        transition: { duration: 3, ease: [0.4, 0, 0.2, 1] }
      });
      
      // 止める (2秒)
      setPhase("hold");
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 吐く (3秒)
      setPhase("exhale");
      await controls.start({
        scale: 1,
        transition: { duration: 3, ease: [0.4, 0, 0.2, 1] }
      });
      
      // サイクル完了
      setBreathCount(prev => prev + 1);
    } catch (error) {
      console.log("呼吸サイクルが中断されました");
    }
  };

  useEffect(() => {
    if (isBreathing && breathCount > 0 && breathCount < 3) {
      const timer = setTimeout(() => {
        performBreathCycle();
      }, 1000);
      return () => clearTimeout(timer);
    } else if (breathCount >= 3) {
      setIsBreathing(false);
    }
  }, [breathCount]);

  // スキップ機能を追加
  const skipBreathing = () => {
    setBreathCount(3);
    setIsBreathing(false);
    controls.stop();
  };

  const getPhaseText = () => {
    switch (phase) {
      case "inhale":
        return "ゆっくり息を吸って...";
      case "hold":
        return "そのまま止めて...";
      case "exhale":
        return "ゆっくり息を吐いて...";
    }
  };

  const getPhaseColor = () => {
    switch (phase) {
      case "inhale":
        return "primary.100";
      case "hold":
        return "primary.200";
      case "exhale":
        return "primary.50";
    }
  };

  return (
    <VStack 
      spacing={{ base: 6, md: 8 }} 
      p={{ base: 4, md: 8 }} 
      maxW={{ base: "100%", md: "md" }} 
      mx="auto"
      minH="100vh"
      justify="center"
      role="region"
      aria-label="面接前の深呼吸エクササイズ"
      aria-live="polite"
    >
      {/* タイトル */}
      <VStack spacing={{ base: 2, md: 3 }}>
        <Text 
          fontSize={{ base: "xl", md: "2xl" }} 
          fontWeight="bold" 
          color="primary.700"
          textAlign="center"
          role="heading"
          aria-level={1}
        >
          面接前の準備
        </Text>
        <Text 
          fontSize={{ base: "sm", md: "md" }} 
          color="neutral.600" 
          textAlign="center"
          px={{ base: 4, md: 0 }}
        >
          深呼吸をして、心を落ち着けましょう
        </Text>
      </VStack>

      {/* 呼吸サークル */}
      <Box position="relative">
        <motion.div
          animate={controls}
          initial={{ scale: 1 }}
        >
          <Box
            w={{ base: "160px", md: "200px" }}
            h={{ base: "160px", md: "200px" }}
            borderRadius="full"
            bg={getPhaseColor()}
            display="flex"
            alignItems="center"
            justifyContent="center"
            position="relative"
            boxShadow="lg"
            transition="all 0.5s ease-in-out"
          >
            {/* 内側のサークル */}
            <Box
              w={{ base: "120px", md: "150px" }}
              h={{ base: "120px", md: "150px" }}
              borderRadius="full"
              bg="white"
              display="flex"
              alignItems="center"
              justifyContent="center"
              flexDirection="column"
              boxShadow="inner"
            >
              {!isBreathing && breathCount === 0 ? (
                <VStack spacing={1}>
                  <Text fontSize={{ base: "3xl", md: "4xl" }}>🧘</Text>
                  <Text fontSize={{ base: "xs", md: "sm" }} color="primary.600">
                    準備OK？
                  </Text>
                </VStack>
              ) : breathCount >= 3 ? (
                <VStack spacing={1}>
                  <Text fontSize={{ base: "3xl", md: "4xl" }}>✨</Text>
                  <Text fontSize={{ base: "xs", md: "sm" }} color="support.600" fontWeight="bold">
                    準備完了！
                  </Text>
                </VStack>
              ) : (
                <VStack spacing={1}>
                  <Text fontSize={{ base: "2xl", md: "3xl" }} color="primary.600" fontWeight="bold">
                    {breathCount + 1}/3
                  </Text>
                  <Text fontSize={{ base: "2xs", md: "xs" }} color="neutral.600">
                    呼吸
                  </Text>
                </VStack>
              )}
            </Box>
          </Box>
        </motion.div>

      </Box>

      {/* プログレスバー */}
      <Box w="full" maxW={{ base: "300px", md: "400px" }}>
        <Text 
          fontSize={{ base: "xs", md: "sm" }} 
          color="neutral.600" 
          mb={2}
          textAlign="center"
        >
          進捗: {breathCount}/3 回
        </Text>
        <Progress 
          value={(breathCount / 3) * 100} 
          colorScheme="primary" 
          size={{ base: "sm", md: "md" }}
          borderRadius="full"
          hasStripe
          isAnimated
        />
      </Box>

      {/* アクションボタン */}
      <VStack spacing={{ base: 3, md: 4 }} w="full" maxW="300px">
        {!isBreathing && breathCount === 0 && (
          <VStack spacing={3} w="full">
            <motion.div 
              whileHover={{ scale: 1.02 }} 
              whileTap={{ scale: 0.98 }}
              style={{ width: "100%" }}
            >
              <Button
                onClick={startBreathing}
                colorScheme="primary"
                size={{ base: "md", md: "lg" }}
                borderRadius="full"
                px={{ base: 6, md: 8 }}
                py={{ base: 6, md: 4 }}
                w="full"
                fontSize={{ base: "sm", md: "md" }}
                minH={{ base: "50px", md: "auto" }}
                aria-label="深呼吸エクササイズを開始する"
                _focus={{
                  boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
                  outline: "none"
                }}
              >
                深呼吸を始める
                <VisuallyHidden>
                  3回の深呼吸エクササイズを開始します。画面の指示に従って呼吸してください。
                </VisuallyHidden>
              </Button>
            </motion.div>
            <Button
              onClick={onComplete}
              variant="ghost"
              size={{ base: "sm", md: "md" }}
              color="neutral.600"
              fontSize={{ base: "xs", md: "sm" }}
              aria-label="深呼吸をスキップして面接を開始"
              _focus={{
                boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
                outline: "none"
              }}
            >
              スキップして始める
            </Button>
          </VStack>
        )}
        
        {isBreathing && breathCount < 3 && (
          <VStack spacing={3} w="full">
            <Text 
              fontSize={{ base: "sm", md: "md" }} 
              color="primary.600" 
              fontWeight="medium"
              textAlign="center"
              aria-live="assertive"
              role="status"
            >
              {getPhaseText()}
              <VisuallyHidden>
                深呼吸の現在のフェーズ: {getPhaseText()} サークルの動きに合わせて呼吸してください。
              </VisuallyHidden>
            </Text>
            <Button
              onClick={skipBreathing}
              variant="ghost"
              size={{ base: "sm", md: "md" }}
              color="neutral.600"
              fontSize={{ base: "xs", md: "sm" }}
              aria-label="深呼吸エクササイズをスキップ"
              _focus={{
                boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
                outline: "none"
              }}
            >
              スキップ
            </Button>
          </VStack>
        )}
        
        {breathCount >= 3 && (
          <VStack spacing={4} w="full">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.5 }}
            >
              <Text 
                fontSize={{ base: "md", md: "lg" }} 
                color="support.600" 
                fontWeight="bold"
                textAlign="center"
              >
                素晴らしい！準備が整いました
              </Text>
            </motion.div>
            <motion.div 
              whileHover={{ scale: 1.02 }} 
              whileTap={{ scale: 0.98 }}
              style={{ width: "100%" }}
            >
              <Button
                onClick={onComplete}
                colorScheme="support"
                size={{ base: "md", md: "lg" }}
                borderRadius="full"
                px={{ base: 6, md: 8 }}
                py={{ base: 6, md: 4 }}
                w="full"
                fontSize={{ base: "sm", md: "md" }}
                minH={{ base: "50px", md: "auto" }}
                rightIcon={<Text>→</Text>}
              >
                面接を始める
              </Button>
            </motion.div>
          </VStack>
        )}
      </VStack>

      {/* ヒントテキスト */}
      {!isBreathing && breathCount === 0 && (
        <Text fontSize="sm" color="neutral.500" textAlign="center">
          💡 深呼吸は緊張を和らげ、最高のパフォーマンスを発揮する助けになります
        </Text>
      )}
    </VStack>
  );
};