import React from "react";
import {
  Box,
  Card,
  CardBody,
  CardHeader,
  <PERSON>ing,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  useColorModeValue
} from "@chakra-ui/react";
import { StarIcon } from "@chakra-ui/icons";
import { spacing, textStyles } from "./CommonStyles";
import type { ActionItem } from "../types/evaluation";
import { motion } from "framer-motion";

const MotionBox = motion(Box);

export interface ActionPlanPhaseProps {
  title: string;
  color: string;
  items: ActionItem[];
  phaseIndex: number;
}

const ActionPlanPhase: React.FC<ActionPlanPhaseProps> = ({ title, color, items, phaseIndex }) => (
  <MotionBox
    initial={{ opacity: 0, x: -20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ delay: phaseIndex * 0.2 }}
    position="relative"
  >
    <Box
      position="absolute"
      left="8"
      top="6"
      width="4"
      height="4"
      bg={`${color}.500`}
      borderRadius="full"
      transform="translateX(-50%)"
      borderWidth="3px"
      borderColor={useColorModeValue("white", "gray.800")}
    />
    <Card ml="16">
      <CardHeader>
        <Heading size="md" color={`${color}.600`}>{title}</Heading>
      </CardHeader>
      <CardBody>
        <VStack spacing={spacing.cardPadding.base} align="stretch">
          {items.map((item, itemIndex) => (
            <Box key={itemIndex} p={4} bg={useColorModeValue("gray.50", "gray.700")} borderRadius="md">
              <HStack justify="space-between" align="start" mb={2}>
                <Text fontWeight="bold" flex={1}>{item.action}</Text>
                <Button size="xs" colorScheme={color} variant="outline">
                  開始
                </Button>
              </HStack>
              <Text {...textStyles.body} color="gray.600" mb={2}>
                {item.method}
              </Text>
              <HStack spacing={spacing.cardPadding.base}>
                <Icon as={StarIcon} color={`${color}.500`} boxSize={3} />
                <Text fontSize="xs" color={`${color}.600`} fontWeight="medium">
                  目標: {item.metric}
                </Text>
              </HStack>
              {item.resources && item.resources.length > 0 && (
                <Box mt={3}>
                  <Text fontSize="xs" fontWeight="bold" mb={1}>推奨リソース:</Text>
                  <VStack spacing={1} align="start">
                    {item.resources.map((resource, resourceIndex) => (
                      <Text key={resourceIndex} fontSize="xs" color="blue.600">
                        📚 {resource.title} ({resource.estimatedTime})
                      </Text>
                    ))}
                  </VStack>
                </Box>
              )}
            </Box>
          ))}
        </VStack>
      </CardBody>
    </Card>
  </MotionBox>
);

export default ActionPlanPhase;
