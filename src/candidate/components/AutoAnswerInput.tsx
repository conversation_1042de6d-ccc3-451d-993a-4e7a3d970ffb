/**
 * 自動回答検知機能付きAnswerInput
 * 音声の無音期間を検知して自動で次の質問へ進む
 */
import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  useToast,
  useColorModeValue,
  Progress,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';

interface AutoAnswerInputProps {
  onSubmit: (audioBlob: Blob) => void;
  isLoading: boolean;
}

const AutoAnswerInput: React.FC<AutoAnswerInputProps> = ({ onSubmit, isLoading }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [silenceTime, setSilenceTime] = useState(0);
  const [hasStartedSpeaking, setHasStartedSpeaking] = useState(false);
  const [isProcessingAnswer, setIsProcessingAnswer] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  
  const toast = useToast();

  // 音声レベル検知の設定
  const SILENCE_THRESHOLD = 30; // 音声レベルの閾値
  const SILENCE_DURATION = 3000; // 3秒の無音で自動終了
  const MAX_RECORDING_TIME = 180; // 最大3分

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });
      
      streamRef.current = stream;
      
      // 音声分析の設定
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      analyser.smoothingTimeConstant = 0.8;
      analyser.fftSize = 1024;
      microphone.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setIsProcessingAnswer(true);
        
        try {
          if (audioChunksRef.current.length === 0) {
            throw new Error('録音データが空です');
          }

          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          console.log('回答完了 - 音声データのサイズ:', audioBlob.size, 'bytes');
          
          onSubmit(audioBlob);
        } catch (error: unknown) {
          console.error('音声処理エラー:', error);
          const errorMessage = error instanceof Error ? error.message : '不明なエラーが発生しました';
          toast({
            id: 'audio-processing-error',
            title: 'エラー',
            description: '音声の処理に失敗しました: ' + errorMessage,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        } finally {
          cleanup();
        }
      };

      mediaRecorder.start(1000);
      setIsRecording(true);
      setRecordingTime(0);
      setSilenceTime(0);
      setHasStartedSpeaking(false);
      
      // 録音時間のカウント
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          if (newTime >= MAX_RECORDING_TIME) {
            stopRecording();
          }
          return newTime;
        });
      }, 1000);
      
      // 音声レベルの監視
      startVoiceDetection();
      
    } catch (error: unknown) {
      console.error('録音開始エラー:', error);
      const errorMessage = error instanceof Error ? error.message : '不明なエラーが発生しました';
      toast({
        id: 'mic-access-error',
        title: 'エラー',
        description: 'マイクへのアクセスに失敗しました: ' + errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const startVoiceDetection = () => {
    const detectVoice = () => {
      if (!analyserRef.current || !isRecording) return;
      
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      analyserRef.current.getByteFrequencyData(dataArray);
      
      // 音声レベルの計算
      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
      
      if (average > SILENCE_THRESHOLD) {
        // 音声検知
        setHasStartedSpeaking(true);
        setSilenceTime(0);
        
        // 無音タイマーをリセット
        if (silenceTimerRef.current) {
          clearTimeout(silenceTimerRef.current);
          silenceTimerRef.current = null;
        }
      } else if (hasStartedSpeaking) {
        // 無音検知（話し始めた後のみ）
        setSilenceTime(prev => prev + 100);
        
        if (!silenceTimerRef.current) {
          silenceTimerRef.current = setTimeout(() => {
            console.log('無音期間を検知 - 自動的に回答を終了します');
            stopRecording();
          }, SILENCE_DURATION);
        }
      }
      
      requestAnimationFrame(detectVoice);
    };
    
    detectVoice();
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const cleanup = () => {
    // タイマーのクリーンアップ
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }
    
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    // ストリームの停止
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    // AudioContextのクローズ
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    setIsProcessingAnswer(false);
  };

  // モック回答を送信する関数
  const sendMockAnswer = () => {
    const mockAudioData = new ArrayBuffer(1024);
    const mockBlob = new Blob([mockAudioData], { type: 'audio/wav' });
    
    // サンプル回答の場合は即座に次へ進む
    onSubmit(mockBlob);
  };

  // 手動で回答終了
  const handleManualFinish = () => {
    if (isRecording) {
      stopRecording();
    }
  };

  // コンポーネントのクリーンアップ
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isProcessingAnswer) {
    return (
      <VStack spacing={4} w="full" maxW="400px" mx="auto">
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        >
          <Text fontSize={{ base: "xl", md: "2xl" }}>🤔</Text>
        </motion.div>
        <Text fontSize={{ base: "md", md: "lg" }} color={useColorModeValue('primary.600', 'primary.400')} textAlign="center">
          回答を分析中です...
        </Text>
        <Progress size="sm" isIndeterminate colorScheme="primary" w="full" />
      </VStack>
    );
  }

  return (
    <VStack spacing={{ base: 4, md: 6 }} w="full" maxW={{ base: "full", md: "500px", lg: "600px" }} mx="auto">
      {!isRecording ? (
        // 録音開始前
        <VStack spacing={4} w="full">
          <Text 
            fontSize={{ base: "md", md: "lg" }} 
            fontWeight="bold" 
            color={useColorModeValue('primary.700', 'primary.300')}
            textAlign="center"
          >
            準備ができたら回答を開始してください
          </Text>
          
          <VStack spacing={3} w="full">
            <Button
              colorScheme="primary"
              onClick={startRecording}
              isLoading={isLoading}
              size={{ base: "md", md: "lg" }}
              leftIcon={<Text role="img" aria-label="マイク">🎤</Text>}
              w="full"
              py={{ base: 6, md: 4 }}
              fontSize={{ base: "sm", md: "md" }}
              minH={{ base: "50px", md: "auto" }}
            >
              回答を開始
            </Button>
            
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')} textAlign="center">
              または
            </Text>
            
            <Button
              colorScheme="primary"
              onClick={sendMockAnswer}
              isLoading={isLoading}
              size={{ base: "md", md: "lg" }}
              leftIcon={<Text role="img" aria-label="サンプル">✨</Text>}
              w="full"
              py={{ base: 6, md: 4 }}
              fontSize={{ base: "sm", md: "md" }}
              minH={{ base: "50px", md: "auto" }}
              variant="outline"
            >
              サンプル回答で進む
            </Button>
          </VStack>
          
          <Alert status="info" borderRadius="md" bg={useColorModeValue('blue.50', 'blue.900')} borderColor={useColorModeValue('blue.200', 'blue.700')}>
            <AlertIcon color={useColorModeValue('blue.500', 'blue.300')} />
            <AlertDescription fontSize="sm" color={useColorModeValue('blue.700', 'blue.200')}>
              話し終えると自動的に次の質問に進みます（約3秒の無音で検知）
            </AlertDescription>
          </Alert>
        </VStack>
      ) : (
        // 録音中
        <VStack spacing={4} w="full">
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Text fontSize="4xl">🎙️</Text>
          </motion.div>
          
          <VStack spacing={2}>
            <Text fontSize={{ base: "md", md: "lg" }} fontWeight="bold" color="red.600">
              録音中...
            </Text>
            <Text fontSize={{ base: "sm", md: "md" }} color={useColorModeValue('gray.600', 'gray.400')}>
              {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}
            </Text>
          </VStack>
          
          <Box w="full">
            <Progress 
              value={(recordingTime / MAX_RECORDING_TIME) * 100} 
              colorScheme="red" 
              size="md"
              borderRadius="full"
            />
          </Box>
          
          {hasStartedSpeaking && (
            <Alert status="success" borderRadius="md" bg={useColorModeValue('green.50', 'green.900')} borderColor={useColorModeValue('green.200', 'green.700')}>
              <AlertIcon color={useColorModeValue('green.500', 'green.300')} />
              <AlertDescription fontSize="sm" color={useColorModeValue('green.700', 'green.200')}>
                音声を検知しました。話し終えると自動的に次へ進みます
              </AlertDescription>
            </Alert>
          )}
          
          <VStack spacing={2}>
            <Button
              colorScheme="red"
              onClick={handleManualFinish}
              size="lg"
              leftIcon={<Text>⏹️</Text>}
            >
              回答を終了
            </Button>
            <Text fontSize="xs" color={useColorModeValue('gray.500', 'gray.400')} textAlign="center">
              話し終えて約3秒待つか、上記ボタンで終了できます
            </Text>
          </VStack>
        </VStack>
      )}
    </VStack>
  );
};

export default AutoAnswerInput;