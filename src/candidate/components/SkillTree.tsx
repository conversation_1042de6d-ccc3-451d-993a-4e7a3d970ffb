/**
 * スキルツリービジュアライゼーションコンポーネント
 * ユーザーの成長を可視化
 */
import React from "react";
import {
  Box,
  VStack,
  Text,
  SimpleGrid,
  CircularProgress,
  CircularProgressLabel,
  Badge,
  HStack,
  Icon,
  Tooltip,
  Progress,
  VisuallyHidden,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import { StarIcon, CheckCircleIcon } from "@chakra-ui/icons";

export interface UserSkills {
  communication: number;
  logic: number;
  confidence: number;
  creativity: number;
  timeManagement: number;
  emotionalControl: number;
}

interface Skill {
  id: keyof UserSkills;
  name: string;
  icon: string;
  description: string;
  milestones: { threshold: number; title: string }[];
}

interface SkillTreeProps {
  userSkills: UserSkills;
  totalInterviews?: number;
  averageScore?: number;
}

export const SkillTree: React.FC<SkillTreeProps> = ({
  userSkills,
  totalInterviews = 0,
  averageScore = 0,
}) => {
  const skills: Skill[] = [
    {
      id: "communication",
      name: "コミュニケーション",
      icon: "💬",
      description: "明確で効果的な意思疎通",
      milestones: [
        { threshold: 30, title: "初級スピーカー" },
        { threshold: 60, title: "中級コミュニケーター" },
        { threshold: 90, title: "マスターコミュニケーター" },
      ],
    },
    {
      id: "logic",
      name: "論理的思考",
      icon: "🧠",
      description: "構造的で理路整然とした回答",
      milestones: [
        { threshold: 30, title: "論理の基礎" },
        { threshold: 60, title: "構造化思考" },
        { threshold: 90, title: "ロジカルマスター" },
      ],
    },
    {
      id: "confidence",
      name: "自信",
      icon: "💪",
      description: "堂々とした態度と発言",
      milestones: [
        { threshold: 30, title: "自信の芽生え" },
        { threshold: 60, title: "確固たる自信" },
        { threshold: 90, title: "カリスマ性" },
      ],
    },
    {
      id: "creativity",
      name: "創造性",
      icon: "🎨",
      description: "独創的な視点と回答",
      milestones: [
        { threshold: 30, title: "アイデアの種" },
        { threshold: 60, title: "創造的思考" },
        { threshold: 90, title: "イノベーター" },
      ],
    },
    {
      id: "timeManagement",
      name: "時間管理",
      icon: "⏱️",
      description: "適切な回答時間の管理",
      milestones: [
        { threshold: 30, title: "時間意識" },
        { threshold: 60, title: "効率的な話者" },
        { threshold: 90, title: "タイムマスター" },
      ],
    },
    {
      id: "emotionalControl",
      name: "感情制御",
      icon: "😌",
      description: "落ち着きと感情の安定",
      milestones: [
        { threshold: 30, title: "感情の理解" },
        { threshold: 60, title: "感情の調整" },
        { threshold: 90, title: "感情の達人" },
      ],
    },
  ];

  const getSkillLevel = (value: number) => {
    if (value >= 90) return { level: "マスター", color: "purple" };
    if (value >= 70) return { level: "上級", color: "green" };
    if (value >= 50) return { level: "中級", color: "blue" };
    if (value >= 30) return { level: "初級", color: "yellow" };
    return { level: "入門", color: "gray" };
  };

  const getCurrentMilestone = (skill: Skill, value: number) => {
    const achieved = skill.milestones.filter((m) => value >= m.threshold);
    return achieved[achieved.length - 1];
  };

  const getNextMilestone = (skill: Skill, value: number) => {
    return skill.milestones.find((m) => value < m.threshold);
  };

  return (
    <VStack spacing={6} w="full" maxW="800px" mx="auto">
      {/* ヘッダー統計 */}
      <Box
        w="full"
        p={{ base: 4, md: 6 }}
        bg="primary.50"
        borderRadius="lg"
        borderWidth={1}
        borderColor="primary.100"
      >
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
          <VStack>
            <Text fontSize={{ base: "xs", md: "sm" }} color="neutral.600">
              練習回数
            </Text>
            <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color="primary.700">
              {totalInterviews}
            </Text>
          </VStack>
          <VStack>
            <Text fontSize={{ base: "xs", md: "sm" }} color="neutral.600">
              平均スコア
            </Text>
            <HStack spacing={1}>
              <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color="primary.700">
                {averageScore.toFixed(1)}
              </Text>
              <Icon as={StarIcon} color="yellow.400" boxSize={{ base: 4, md: 5 }} />
            </HStack>
          </VStack>
          <VStack display={{ base: "none", md: "flex" }}>
            <Text fontSize="sm" color="neutral.600">
              総合レベル
            </Text>
            <Badge
              colorScheme="primary"
              fontSize="md"
              px={3}
              py={1}
              borderRadius="full"
            >
              レベル {Math.floor(totalInterviews / 5) + 1}
            </Badge>
          </VStack>
        </SimpleGrid>
      </Box>

      {/* スキルツリータイトル */}
      <VStack spacing={1}>
        <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color="primary.700">
          スキルツリー
        </Text>
        <Text fontSize={{ base: "sm", md: "md" }} color="neutral.600">
          あなたの成長を可視化
        </Text>
      </VStack>

      {/* スキルグリッド */}
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={{ base: 4, md: 6 }} w="full">
        {skills.map((skill) => {
          const value = userSkills[skill.id];
          const { level, color } = getSkillLevel(value);
          const currentMilestone = getCurrentMilestone(skill, value);
          const nextMilestone = getNextMilestone(skill, value);

          return (
            <motion.div
              key={skill.id}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <VStack
                spacing={3}
                p={{ base: 4, md: 5 }}
                bg="white"
                borderRadius="lg"
                borderWidth={2}
                borderColor={value === 100 ? "purple.300" : "neutral.200"}
                boxShadow={value === 100 ? "lg" : "sm"}
                h="full"
                justify="space-between"
                role="region"
                aria-label={`${skill.name}スキル: ${value}パーセント達成, レベル: ${level}`}
                tabIndex={0}
                _focus={{
                  borderColor: "primary.500",
                  boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.1)",
                  outline: "none"
                }}
              >
                {/* スキルアイコンと進捗 */}
                <VStack spacing={2}>
                  <Box position="relative">
                    <CircularProgress
                      value={value}
                      size={{ base: "80px", md: "100px" }}
                      color={`${color}.400`}
                      thickness={6}
                      aria-label={`${skill.name}の進捗: ${value}パーセント`}
                    >
                      <CircularProgressLabel>
                        <VStack spacing={0}>
                          <Text 
                            fontSize={{ base: "xl", md: "2xl" }}
                            role="img"
                            aria-label={skill.description}
                          >
                            {skill.icon}
                          </Text>
                          <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="bold">
                            {value}%
                          </Text>
                        </VStack>
                      </CircularProgressLabel>
                    </CircularProgress>
                    {value === 100 && (
                      <Box position="absolute" top={-2} right={-2}>
                        <Icon as={CheckCircleIcon} color="purple.500" boxSize={6} />
                      </Box>
                    )}
                  </Box>

                  {/* スキル名とレベル */}
                  <VStack spacing={1}>
                    <Text
                      fontWeight="bold"
                      fontSize={{ base: "sm", md: "md" }}
                      textAlign="center"
                    >
                      {skill.name}
                    </Text>
                    <Badge
                      colorScheme={color}
                      fontSize={{ base: "xs", md: "sm" }}
                      px={2}
                      borderRadius="full"
                    >
                      {level}
                    </Badge>
                  </VStack>
                </VStack>

                {/* マイルストーン進捗 */}
                <Tooltip
                  label={
                    nextMilestone
                      ? `次: ${nextMilestone.title} (${nextMilestone.threshold}%)`
                      : "マスター達成！"
                  }
                  placement="top"
                >
                  <Box w="full">
                    {currentMilestone && (
                      <Text
                        fontSize={{ base: "xs", md: "xs" }}
                        color="neutral.600"
                        textAlign="center"
                        noOfLines={1}
                      >
                        {currentMilestone.title}
                      </Text>
                    )}
                    {nextMilestone && (
                      <Progress
                        value={((value - (currentMilestone?.threshold || 0)) / 
                                (nextMilestone.threshold - (currentMilestone?.threshold || 0))) * 100}
                        size="xs"
                        colorScheme={color}
                        borderRadius="full"
                        mt={1}
                      />
                    )}
                  </Box>
                </Tooltip>
                
                {/* スクリーンリーダー用の詳細情報 */}
                <VisuallyHidden>
                  スキル詳細: {skill.description}. 
                  現在の進捗: {value}パーセント. 
                  レベル: {level}. 
                  {currentMilestone && `達成マイルストーン: ${currentMilestone.title}.`}
                  {nextMilestone && `次の目標: ${nextMilestone.title} (あと${nextMilestone.threshold - value}パーセント).`}
                </VisuallyHidden>
              </VStack>
            </motion.div>
          );
        })}
      </SimpleGrid>

      {/* モチベーションメッセージ */}
      <Box
        w="full"
        p={4}
        bg="support.50"
        borderRadius="md"
        borderWidth={1}
        borderColor="support.200"
      >
        <HStack spacing={2}>
          <Text fontSize="lg">🌟</Text>
          <Text fontSize={{ base: "sm", md: "md" }} color="support.700">
            素晴らしい成長です！次の目標に向けて練習を続けましょう。
          </Text>
        </HStack>
      </Box>
    </VStack>
  );
};