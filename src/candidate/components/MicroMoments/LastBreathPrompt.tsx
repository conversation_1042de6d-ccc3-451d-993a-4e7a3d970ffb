/**
 * 最後の深呼吸プロンプト - マイクロモーメント最適化
 * プロダクト憲法第一条「心理的安全性の絶対的保障」に基づく
 * 面接開始前の緊張緩和とリラックス効果を提供
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  Text,
  Button,
  useColorModeValue,
  Container,
  HStack,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { getRandomEncouragement } from '../../shared/constants/language';

const MotionBox = motion.create(Box);
const MotionText = motion.create(Text);

interface LastBreathPromptProps {
  onComplete: () => void;
  onSkip?: () => void;
  showSkipOption?: boolean;
}

export const LastBreathPrompt: React.FC<LastBreathPromptProps> = ({
  onComplete,
  onSkip,
  showSkipOption = true,
}) => {
  const [isPulsing, setIsPulsing] = useState(true);
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause'>('inhale');
  const [cycleCount, setCycleCount] = useState(0);
  const [encouragementText, setEncouragementText] = useState('');
  const [isActive, setIsActive] = useState(false);
  
  // カラーモード対応
  const bgColor = useColorModeValue('gradient.calm', 'gradient.calmDark');
  const textColor = useColorModeValue('primary.700', 'primary.200');
  const accentColor = useColorModeValue('primary.100', 'primary.800');
  const breathingCircleColor = useColorModeValue('primary.200', 'primary.600');

  useEffect(() => {
    setEncouragementText(getRandomEncouragement());
  }, []);

  // 呼吸サイクルの管理
  useEffect(() => {
    if (!isActive) return;

    const breathingCycle = async () => {
      const phases = [
        { phase: 'inhale', duration: 4000 },
        { phase: 'hold', duration: 2000 },
        { phase: 'exhale', duration: 6000 },
        { phase: 'pause', duration: 2000 },
      ] as const;

      for (const { phase, duration } of phases) {
        setBreathingPhase(phase);
        await new Promise(resolve => setTimeout(resolve, duration));
      }
      
      setCycleCount(prev => prev + 1);
    };

    const interval = setInterval(breathingCycle, 14000); // 14秒サイクル
    
    return () => clearInterval(interval);
  }, [isActive]);

  // 3サイクル完了で自動終了
  useEffect(() => {
    if (cycleCount >= 3) {
      setIsActive(false);
      setTimeout(onComplete, 1000);
    }
  }, [cycleCount, onComplete]);

  const handleStart = () => {
    setIsActive(true);
    setIsPulsing(false);
  };

  const getBreathingInstructions = () => {
    switch (breathingPhase) {
      case 'inhale':
        return '鼻から静かに息を吸って';
      case 'hold':
        return '少し息を止めて';
      case 'exhale':
        return '口からゆっくり息を吐いて';
      case 'pause':
        return '自然に呼吸を整えて';
      default:
        return '準備ができたら始めましょう';
    }
  };

  const getCircleScale = () => {
    switch (breathingPhase) {
      case 'inhale':
        return 1.3;
      case 'hold':
        return 1.3;
      case 'exhale':
        return 0.8;
      case 'pause':
        return 1.0;
      default:
        return 1.0;
    }
  };

  return (
    <Container maxW="container.sm" py={8}>
      <VStack spacing={8} textAlign="center">
        <AnimatePresence mode="wait">
          {!isActive ? (
            <MotionBox
              key="initial"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <VStack spacing={6}>
                <MotionText
                  fontSize="xl"
                  color={textColor}
                  fontWeight="medium"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  面接を始める前に、一緒に深呼吸をしませんか？
                </MotionText>
                
                <MotionBox
                  w="150px"
                  h="150px"
                  borderRadius="full"
                  bg={accentColor}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  animate={isPulsing ? { 
                    scale: [1, 1.05, 1],
                    boxShadow: [
                      '0 0 20px rgba(0, 0, 0, 0.1)',
                      '0 0 40px rgba(0, 0, 0, 0.2)',
                      '0 0 20px rgba(0, 0, 0, 0.1)'
                    ]
                  } : {}}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                >
                  <Text fontSize="4xl">🌸</Text>
                </MotionBox>

                <Text
                  fontSize="md"
                  color={useColorModeValue('gray.600', 'gray.300')}
                  maxW="400px"
                  lineHeight="1.6"
                >
                  {encouragementText}<br />
                  リラックスした状態で最高のパフォーマンスを発揮しましょう。
                </Text>

                <HStack spacing={4}>
                  <Button
                    colorScheme="primary"
                    size="lg"
                    onClick={handleStart}
                    px={8}
                    fontWeight="medium"
                  >
                    深呼吸を始める
                  </Button>
                  
                  {showSkipOption && (
                    <Button
                      variant="ghost"
                      size="lg"
                      onClick={onSkip}
                      color={useColorModeValue('gray.600', 'gray.400')}
                    >
                      スキップ
                    </Button>
                  )}
                </HStack>
              </VStack>
            </MotionBox>
          ) : (
            <MotionBox
              key="breathing"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <VStack spacing={8}>
                <VStack spacing={4}>
                  <Text
                    fontSize="lg"
                    color={textColor}
                    fontWeight="medium"
                  >
                    {getBreathingInstructions()}
                  </Text>
                  
                  <Text
                    fontSize="sm"
                    color={useColorModeValue('gray.500', 'gray.400')}
                  >
                    {cycleCount + 1} / 3 回目
                  </Text>
                </VStack>

                <MotionBox
                  w="200px"
                  h="200px"
                  borderRadius="full"
                  bg={breathingCircleColor}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  animate={{
                    scale: getCircleScale(),
                    opacity: breathingPhase === 'hold' ? 1 : 0.8,
                  }}
                  transition={{
                    duration: breathingPhase === 'inhale' ? 4 : 
                              breathingPhase === 'exhale' ? 6 : 2,
                    ease: "easeInOut"
                  }}
                  border="3px solid"
                  borderColor={useColorModeValue('primary.300', 'primary.500')}
                >
                  <Text fontSize="5xl">🫁</Text>
                </MotionBox>

                <VStack spacing={2}>
                  <Text
                    fontSize="sm"
                    color={useColorModeValue('gray.600', 'gray.300')}
                  >
                    自然なペースで呼吸に集中してください
                  </Text>
                  
                  {cycleCount >= 2 && (
                    <MotionText
                      fontSize="sm"
                      color="green.500"
                      fontWeight="medium"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      もうすぐ完了です。とてもよくできています！
                    </MotionText>
                  )}
                </VStack>
              </VStack>
            </MotionBox>
          )}
        </AnimatePresence>
      </VStack>
    </Container>
  );
};