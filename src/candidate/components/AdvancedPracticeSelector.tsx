/**
 * 実践練習選択画面
 * 業界別・難易度別・シナリオ別の練習を選択
 */
import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Badge,
  useColorModeValue,
  Heading,
  Select,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Icon,
  List,
  ListItem,
  ListIcon,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  InterviewScenario,
  interviewScenarios,
  getRecommendedScenarios
} from '../lib/advancedPracticeData';
import { 
  StarIcon, 
  TimeIcon, 
  WarningIcon, 
  CheckCircleIcon,
  ArrowForwardIcon
} from '@chakra-ui/icons';

interface AdvancedPracticeSelectorProps {
  onScenarioSelect: (scenarioId: string) => void;
  userExperience?: string;
  targetIndustry?: string;
}

const MotionCard = motion(Card);

const AdvancedPracticeSelector: React.FC<AdvancedPracticeSelectorProps> = ({
  onScenarioSelect,
  userExperience = "mid",
  targetIndustry = "tech"
}) => {
  const [selectedIndustry, setSelectedIndustry] = useState(targetIndustry);
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>("all");
  const [filteredScenarios, setFilteredScenarios] = useState<InterviewScenario[]>(
    interviewScenarios
  );

  // フィルタリング処理
  React.useEffect(() => {
    let filtered = interviewScenarios;
    
    if (selectedIndustry !== "all") {
      filtered = filtered.filter(s => 
        s.industry === selectedIndustry || s.industry === "general"
      );
    }
    
    if (selectedDifficulty !== "all") {
      filtered = filtered.filter(s => s.difficulty === selectedDifficulty);
    }
    
    setFilteredScenarios(filtered);
  }, [selectedIndustry, selectedDifficulty]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "medium": return "yellow";
      case "hard": return "orange"; 
      case "expert": return "red";
      default: return "gray";
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case "medium": return "中級";
      case "hard": return "上級";
      case "expert": return "エキスパート";
      default: return difficulty;
    }
  };

  const getPressureLevelText = (level: number) => {
    switch (level) {
      case 1: case 2: return "リラックス";
      case 3: case 4: return "適度なプレッシャー";
      case 5: return "高プレッシャー";
      default: return "不明";
    }
  };

  const getPressureLevelColor = (level: number) => {
    switch (level) {
      case 1: case 2: return "green";
      case 3: case 4: return "yellow";
      case 5: return "red";
      default: return "gray";
    }
  };

  const getRecommendationBadge = (scenario: InterviewScenario) => {
    // ユーザーの経験レベルと目標業界に基づく推奨度判定
    const isTargetIndustry = scenario.industry === targetIndustry;
    const isDifficultyMatch = (
      (userExperience === "junior" && scenario.difficulty === "medium") ||
      (userExperience === "mid" && (scenario.difficulty === "medium" || scenario.difficulty === "hard")) ||
      (userExperience === "senior" && scenario.difficulty === "hard") ||
      (userExperience === "expert" && scenario.difficulty === "expert")
    );
    
    if (isTargetIndustry && isDifficultyMatch) {
      return <Badge colorScheme="green" fontSize="xs">おすすめ</Badge>;
    } else if (isTargetIndustry || isDifficultyMatch) {
      return <Badge colorScheme="blue" fontSize="xs">適合</Badge>;
    }
    return null;
  };

  return (
    <Box w="full" px={{ base: 4, md: 6, lg: 8 }} py={{ base: 4, md: 8 }}>
      <VStack spacing={8} maxW="container.xl" mx="auto">
        {/* ヘッダー */}
        <VStack spacing={2} textAlign="center">
          <Heading size={{ base: "lg", md: "xl" }} color={useColorModeValue('primary.700', 'primary.300')}>
            実践的な面接練習
          </Heading>
          <Text color={useColorModeValue('gray.600', 'gray.300')} fontSize={{ base: "md", md: "lg" }}>
            業界特化型の高度な面接シミュレーションで実力を磨こう
          </Text>
        </VStack>

        {/* フィルター */}
        <Card w="full" bg={useColorModeValue('white', 'gray.800')}>
          <CardHeader>
            <Heading size="md">練習内容を選択</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <VStack align="start" spacing={2}>
                <Text fontWeight="medium">業界</Text>
                <Select 
                  value={selectedIndustry} 
                  onChange={(e) => setSelectedIndustry(e.target.value)}
                  bg={useColorModeValue('white', 'gray.700')}
                >
                  <option value="all">すべての業界</option>
                  <option value="tech">テクノロジー</option>
                  <option value="consulting">コンサルティング</option>
                  <option value="finance">金融</option>
                  <option value="general">一般</option>
                </Select>
              </VStack>
              
              <VStack align="start" spacing={2}>
                <Text fontWeight="medium">難易度</Text>
                <Select 
                  value={selectedDifficulty} 
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  bg={useColorModeValue('white', 'gray.700')}
                >
                  <option value="all">すべての難易度</option>
                  <option value="medium">中級</option>
                  <option value="hard">上級</option>
                  <option value="expert">エキスパート</option>
                </Select>
              </VStack>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* 練習シナリオ一覧 */}
        <VStack spacing={6} w="full">
          <Heading size="md" alignSelf="start">
            利用可能な練習シナリオ ({filteredScenarios.length}件)
          </Heading>
          
          {filteredScenarios.length === 0 ? (
            <Alert status="info">
              <AlertIcon />
              <AlertDescription>
                選択した条件に合うシナリオが見つかりません。フィルターを調整してください。
              </AlertDescription>
            </Alert>
          ) : (
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} w="full">
              {filteredScenarios.map((scenario, index) => (
                <MotionCard
                  key={scenario.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  cursor="pointer"
                  bg={useColorModeValue('white', 'gray.800')}
                  borderWidth={2}
                  borderColor={useColorModeValue('gray.200', 'gray.700')}
                  _hover={{
                    borderColor: useColorModeValue('primary.300', 'primary.500'),
                    boxShadow: 'lg'
                  }}
                >
                  <CardHeader>
                    <VStack align="start" spacing={3}>
                      <HStack justify="space-between" w="full">
                        <VStack align="start" spacing={1}>
                          <HStack spacing={2}>
                            <Heading size="md" color={useColorModeValue('gray.800', 'gray.100')}>
                              {scenario.name}
                            </Heading>
                            {getRecommendationBadge(scenario)}
                          </HStack>
                          <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                            {scenario.description}
                          </Text>
                        </VStack>
                      </HStack>
                      
                      <HStack spacing={2} flexWrap="wrap">
                        <Badge colorScheme="blue" fontSize="xs">
                          {scenario.industry === "general" ? "一般" : scenario.industry}
                        </Badge>
                        <Badge 
                          colorScheme={getDifficultyColor(scenario.difficulty)} 
                          fontSize="xs"
                        >
                          {getDifficultyText(scenario.difficulty)}
                        </Badge>
                        <Badge 
                          colorScheme={getPressureLevelColor(scenario.scenarioSettings.pressureLevel)} 
                          fontSize="xs"
                        >
                          {getPressureLevelText(scenario.scenarioSettings.pressureLevel)}
                        </Badge>
                      </HStack>
                    </VStack>
                  </CardHeader>
                  
                  <CardBody>
                    <VStack spacing={4}>
                      {/* シナリオ詳細 */}
                      <SimpleGrid columns={2} spacing={4} w="full">
                        <VStack spacing={1}>
                          <HStack>
                            <Icon as={TimeIcon} color="gray.500" boxSize={4} />
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                              所要時間
                            </Text>
                          </HStack>
                          <Text fontWeight="medium">
                            約{scenario.estimatedDuration}分
                          </Text>
                        </VStack>
                        
                        <VStack spacing={1}>
                          <HStack>
                            <Icon as={StarIcon} color="yellow.500" boxSize={4} />
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                              質問数
                            </Text>
                          </HStack>
                          <Text fontWeight="medium">
                            {scenario.questions.length}問
                          </Text>
                        </VStack>
                      </SimpleGrid>
                      
                      {/* 特徴 */}
                      <VStack align="start" spacing={2} w="full">
                        <Text fontSize="sm" fontWeight="medium">このシナリオの特徴:</Text>
                        <List spacing={1}>
                          {scenario.scenarioSettings.timeConstraints && (
                            <ListItem>
                              <ListIcon as={CheckCircleIcon} color="green.500" />
                              <Text fontSize="xs">時間制限あり</Text>
                            </ListItem>
                          )}
                          {scenario.scenarioSettings.multipleInterviewers && (
                            <ListItem>
                              <ListIcon as={CheckCircleIcon} color="blue.500" />
                              <Text fontSize="xs">複数面接官対応</Text>
                            </ListItem>
                          )}
                          {scenario.scenarioSettings.interruptions && (
                            <ListItem>
                              <ListIcon as={WarningIcon} color="orange.500" />
                              <Text fontSize="xs">割り込み質問あり</Text>
                            </ListItem>
                          )}
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="purple.500" />
                            <Text fontSize="xs">詳細分析レポート</Text>
                          </ListItem>
                        </List>
                      </VStack>
                      
                      {/* 開始ボタン */}
                      <Button
                        colorScheme="primary"
                        w="full"
                        rightIcon={<ArrowForwardIcon />}
                        onClick={() => onScenarioSelect(scenario.id)}
                        size="lg"
                      >
                        この練習を開始
                      </Button>
                    </VStack>
                  </CardBody>
                </MotionCard>
              ))}
            </SimpleGrid>
          )}
        </VStack>

        {/* 注意事項 */}
        <Alert status="warning" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            実践練習は基本練習よりも高い難易度です。充分な準備をしてから挑戦することをお勧めします。
          </AlertDescription>
        </Alert>
      </VStack>
    </Box>
  );
};

export default AdvancedPracticeSelector;