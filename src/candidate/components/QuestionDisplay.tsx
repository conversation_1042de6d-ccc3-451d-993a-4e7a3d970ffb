import React, { useEffect, useRef } from "react";
import { Box, Text, Spinner, Center, VStack, VisuallyHidden, useColorModeValue } from "@chakra-ui/react";
import { UnifiedMockDataService } from "../data/mockApiData";

/**
 * 質問データの型定義
 */
interface Question {
  /** 質問のID */
  id: string;
  /** 質問のテキスト */
  text: string;
}

/**
 * 質問表示コンポーネントのプロパティ
 */
interface QuestionDisplayProps {
  /** 表示する質問 */
  question: Question | null;
  /** 読み込み中フラグ */
  isLoading: boolean;
}

/**
 * 質問を表示し、音声合成による読み上げを行うコンポーネント
 * @param question - 表示する質問データ
 * @param isLoading - 読み込み中フラグ
 * @returns 質問表示コンポーネント
 */
const QuestionDisplay: React.FC<QuestionDisplayProps> = ({ question, isLoading }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioUrlRef = useRef<string | null>(null);

  /**
   * モック音声合成を使用して音声を取得・再生する
   * @param text - 合成する音声のテキスト
   */
  const synthesizeAndPlayAudio = async (text: string): Promise<void> => {
    try {
      console.log(`🎤 音声合成開始: "${text.substring(0, 30)}..."`);
      
      // 統合API音声合成を呼び出し
      const audioBlob = await UnifiedMockDataService.synthesizeText(text);
      const url = URL.createObjectURL(audioBlob);
      
      // 前回のURLを解放
      if (audioUrlRef.current) {
        URL.revokeObjectURL(audioUrlRef.current);
      }
      
      audioUrlRef.current = url;
      
      if (audioRef.current) {
        audioRef.current.src = url;
        // 音声の読み込みが完了してから再生
        audioRef.current.oncanplaythrough = (): void => {
          console.log("🔊 音声再生開始");
          audioRef.current?.play().catch((error: Error) => {
            console.error("音声再生エラー:", error.message);
          });
        };
      }
      
      console.log("✅ 音声合成完了");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("音声合成エラー:", errorMessage);
    }
  };

  useEffect(() => {
    if (question) {
      synthesizeAndPlayAudio(question.text);
    }

    // クリーンアップ関数
    return (): void => {
      if (audioUrlRef.current) {
        URL.revokeObjectURL(audioUrlRef.current);
      }
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = "";
      }
    };
  }, [question]);

  if (isLoading) {
    return (
      <Center p={{ base: 4, md: 6 }} role="status" aria-label="質問を読み込み中">
        <Spinner size={{ base: "md", md: "lg" }} color="primary.500" />
        <VisuallyHidden>質問を読み込んでいます。しばらくお待ちください。</VisuallyHidden>
      </Center>
    );
  }

  if (!question) {
    return (
      <Box p={{ base: 4, md: 6 }} role="status" aria-label="質問を読み込み中">
        <Text 
          fontSize={{ base: "sm", md: "md" }}
          color={useColorModeValue('neutral.600', 'neutral.400')}
          textAlign="center"
        >
          質問を読み込み中...
        </Text>
        <VisuallyHidden>面接の質問を準備しています。しばらくお待ちください。</VisuallyHidden>
      </Box>
    );
  }

  return (
    <Box 
      p={{ base: 4, md: 6 }} 
      bg={useColorModeValue('primary.50', 'gray.800')}
      borderRadius="lg"
      borderWidth={1}
      borderColor={useColorModeValue('primary.100', 'gray.700')}
      w="full"
      role="region"
      aria-label="面接の質問"
      tabIndex={0}
      _focus={{
        borderColor: "primary.500",
        boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.1)",
        outline: "none"
      }}
    >
      <VStack spacing={3} align="start">
        <Text 
          fontSize={{ base: "xs", md: "sm" }}
          color={useColorModeValue('primary.600', 'primary.400')}
          fontWeight="medium"
          role="heading"
          aria-level={2}
        >
          質問 {question.id}
        </Text>
        <Text 
          fontSize={{ base: "md", md: "lg", lg: "xl" }}
          fontWeight="medium"
          color={useColorModeValue('neutral.800', 'neutral.100')}
          lineHeight="tall"
          role="main"
          aria-label="面接の質問内容"
        >
          {question.text}
        </Text>
        <Text 
          fontSize={{ base: "xs", md: "sm" }}
          color={useColorModeValue('neutral.500', 'neutral.400')}
          role="note"
        >
          <Text as="span" role="img" aria-label="ヒント">💡</Text> リラックスして、自分の言葉で回答してください
        </Text>
      </VStack>
      
      {/* スクリーンリーダー用の詳細情報 */}
      <VisuallyHidden>
        この質問について、音声で回答するかサンプル回答で進むか選択できます。音声回答の場合はマイクのアクセス許可が必要です。
      </VisuallyHidden>
      
      <audio 
        ref={audioRef} 
        style={{ display: "none" }}
        preload="auto"
        aria-label="質問の音声読み上げ"
      />
    </Box>
  );
};

export default QuestionDisplay; 