/**
 * エラーハンドリング使用例
 * 実際のコンポーネントでの使用方法を示すサンプル
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  VStack,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
} from '@chakra-ui/react';
import { useError } from '../hooks/useError';
import { InterviewReportApi } from '../services/api';

interface Props {
  candidateId: string;
  interviewId: string;
}

export const ErrorHandlingExample: React.FC<Props> = ({ candidateId, interviewId }) => {
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { error, isError, clearError, withErrorHandling, withRetry } = useError();
  const toast = useToast();

  // 基本的なエラーハンドリング
  const handleBasicFetch = withErrorHandling(async () => {
    setIsLoading(true);
    const report = await InterviewReportApi.getComprehensiveReport(candidateId, interviewId);
    setData(report);
    setIsLoading(false);
  });

  // リトライ機能付きエラーハンドリング
  const handleRetryFetch = withRetry(
    async () => {
      setIsLoading(true);
      const report = await InterviewReportApi.getComprehensiveReport(candidateId, interviewId);
      setData(report);
      setIsLoading(false);
    },
    {
      maxRetries: 3,
      retryDelay: 1000,
      shouldRetry: (error, attempt) => {
        // ネットワークエラーの場合のみリトライ
        return error.type === 'network' && attempt < 3;
      },
    }
  );

  // カスタムエラーハンドリング
  const handleCustomErrorFetch = withErrorHandling(
    async () => {
      setIsLoading(true);
      // 意図的にエラーを発生させる例
      throw new Error('カスタムエラーメッセージ');
    },
    {
      fallbackMessage: 'データの取得に失敗しました。しばらくしてからお試しください。',
      onError: (errorInfo) => {
        // カスタムエラー処理
        console.log('Custom error handling:', errorInfo);
      },
    }
  );

  // サイレントエラーハンドリング（トーストなし）
  const handleSilentFetch = withErrorHandling(
    async () => {
      setIsLoading(true);
      throw new Error('サイレントエラー');
    },
    {
      silent: true,
      onError: (errorInfo) => {
        // カスタムトーストを表示
        toast({
          title: 'カスタムエラー通知',
          description: 'サイレントモードでエラーが処理されました',
          status: 'warning',
          duration: 3000,
        });
      },
    }
  );

  return (
    <Box p={6} maxW="600px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold">
          エラーハンドリング使用例
        </Text>

        {/* エラー表示 */}
        {isError && error && (
          <Alert status="error">
            <AlertIcon />
            <AlertDescription>
              {error.message}
              <Button size="xs" ml={2} onClick={clearError}>
                エラーをクリア
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* データ表示 */}
        {data && (
          <Alert status="success">
            <AlertIcon />
            <AlertDescription>
              データの取得に成功しました: {JSON.stringify(data, null, 2)}
            </AlertDescription>
          </Alert>
        )}

        {/* ボタングループ */}
        <VStack spacing={2}>
          <Button
            onClick={handleBasicFetch}
            isLoading={isLoading}
            colorScheme="blue"
            w="full"
          >
            基本的なエラーハンドリング
          </Button>

          <Button
            onClick={handleRetryFetch}
            isLoading={isLoading}
            colorScheme="green"
            w="full"
          >
            リトライ機能付き
          </Button>

          <Button
            onClick={handleCustomErrorFetch}
            isLoading={isLoading}
            colorScheme="orange"
            w="full"
          >
            カスタムエラーメッセージ
          </Button>

          <Button
            onClick={handleSilentFetch}
            isLoading={isLoading}
            colorScheme="purple"
            w="full"
          >
            サイレントエラーハンドリング
          </Button>
        </VStack>

        {/* 使用方法の説明 */}
        <Box p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" fontWeight="bold" mb={2}>
            使用方法:
          </Text>
          <VStack align="start" spacing={1} fontSize="xs">
            <Text>• withErrorHandling: 基本的なエラーハンドリング</Text>
            <Text>• withRetry: リトライ機能付きエラーハンドリング</Text>
            <Text>• silent: true でトーストを非表示</Text>
            <Text>• fallbackMessage でカスタムメッセージ</Text>
            <Text>• onError でカスタム処理を追加</Text>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};