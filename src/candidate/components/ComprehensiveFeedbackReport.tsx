/**
 * 包括的面接分析レポートコンポーネント
 * STAR+C評価フレームワークとプログレッシブディスクロージャー設計を実装
 */

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Flex,
  Heading,
  Text,
  Card,
  CardBody,
  Badge,
  Button,
  IconButton,
  Tab,
  Tabs,
  TabList,
  TabPanel,
  TabPanels,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  useColorModeValue,
  CircularProgress,
  CircularProgressLabel,
  Spinner,
  Icon,
  Tooltip,
  SimpleGrid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Collapse,
  useDisclosure,
} from '@chakra-ui/react';
import { CustomCircularScore, CircularScore, commonGridStyles, scoreColors, scoreSizes, commonStyles, spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';
import {
  ChevronDownIcon,
  CheckCircleIcon,
  StarIcon,
  InfoIcon,
  WarningIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  TimeIcon,
  RepeatIcon,
  ViewIcon,
} from '@chakra-ui/icons';
import MetricProgress from './MetricProgress';
import ActionPlanPhase from './ActionPlanPhase';
import { motion, AnimatePresence } from 'framer-motion';
import { ComprehensiveInterviewReport } from '../types/evaluation';

interface ComprehensiveFeedbackReportProps {
  reportData: ComprehensiveInterviewReport;
  onClose: () => void;
  onStartNewPractice?: () => void;
}

const MotionCard = motion.create(Card);
const MotionBox = motion.create(Box);

const ComprehensiveFeedbackReport: React.FC<ComprehensiveFeedbackReportProps> = ({
  reportData,
  onClose,
  onStartNewPractice
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // スコア評価のヘルパー関数
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'blue';
    if (score >= 40) return 'yellow';
    return 'red';
  };

  const getScoreLevel = (score: number) => {
    if (score >= 90) return { level: "優秀", color: "green" };
    if (score >= 80) return { level: "良好", color: "blue" };
    if (score >= 60) return { level: "標準", color: "yellow" };
    if (score >= 40) return { level: "要改善", color: "orange" };
    return { level: "要強化", color: "red" };
  };

  const getRecommendationBadge = (recommendation: string) => {
    const config = {
      'strong_hire': { text: '強く推薦', color: 'green' },
      'hire': { text: '採用推薦', color: 'blue' },
      'maybe': { text: '要検討', color: 'yellow' },
      'no_hire': { text: '見送り', color: 'red' }
    };
    return config[recommendation as keyof typeof config] || config.maybe;
  };

  // タブ設定
  const tabs = [
    { id: 'overview', label: '総合評価', shortLabel: '総合', icon: ViewIcon },
    { id: 'communication', label: 'コミュニケーション能力', shortLabel: 'コミュ', icon: InfoIcon },
    { id: 'experience', label: '経験・実績', shortLabel: '経験', icon: StarIcon },
    { id: 'audio', label: '音声・表情', shortLabel: '音声', icon: RepeatIcon },
    { id: 'action', label: '改善アクション', shortLabel: 'プラン', icon: TimeIcon }
  ];

  return (
    <Box 
      w="full" 
      px={{ base: 4, md: 6 }} 
      py={{ base: 4, md: 6 }} 
      bg={useColorModeValue('gray.50', 'gray.900')} 
      minH="100vh"
    >
      <Box maxW="7xl" mx="auto">
        <VStack spacing={spacing.sectionSpacing} align="stretch">
        {/* ヘッダー - レスポンシブ対応 */}
        <MotionCard
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          w="full"
          bg={useColorModeValue('white', 'gray.800')}
        >
          <CardBody p={spacing.cardPadding}>
            <VStack 
              spacing={spacing.sectionSpacing}
              align={{ base: "center", md: "stretch" }}
            >
              <HStack 
                justify={{ base: "center", md: "space-between" }} 
                align={{ base: "center", md: "start" }}
                w="full"
                flexDirection={{ base: "column", md: "row" }}
                spacing={spacing.sectionSpacing}
              >
                <VStack 
                  align={{ base: "center", md: "start" }} 
                  spacing={spacing.cardPadding.base}
                  textAlign={{ base: "center", md: "left" }}
                >
                  <Heading 
                    {...textStyles.heading} 
                    color={useColorModeValue('gray.900', 'white')}
                  >
                    面接評価レポート
                  </Heading>
                  <Text color="gray.600" {...textStyles.body}>
                    {reportData.metadata.candidateName} - {reportData.metadata.position}
                  </Text>
                  <Text {...textStyles.caption} color="gray.500">
                    {reportData.metadata.interviewDate} | {reportData.metadata.duration}
                  </Text>
                  <HStack spacing={spacing.cardPadding.base} justify={{ base: "center", md: "flex-start" }}>
                    <Badge colorScheme="blue" {...textStyles.caption} px={spacing.badgePadding.px} py={spacing.badgePadding.py}>
                      {reportData.metadata.duration}
                    </Badge>
                    <Badge 
                      colorScheme={getRecommendationBadge(reportData.overallAssessment.recommendation).color}
                      {...textStyles.caption}
                      px={spacing.badgePadding.px} 
                      py={spacing.badgePadding.py}
                    >
                      {getRecommendationBadge(reportData.overallAssessment.recommendation).text}
                    </Badge>
                  </HStack>
                </VStack>
                <Box mt={{ base: 2, md: 0 }}>
                  <CustomCircularScore 
                    score={reportData.overallAssessment.score} 
                    size={scoreSizes.large}
                    label="総合スコア"
                  />
                </Box>
              </HStack>
            </VStack>
          </CardBody>
        </MotionCard>

        {/* タブナビゲーション */}
        <Card w="full" bg={useColorModeValue('white', 'gray.800')}>
          <CardBody p={0}>
            <Tabs index={tabs.findIndex(tab => tab.id === activeTab)} onChange={(index) => setActiveTab(tabs[index].id)}>
              <Box 
                overflowX="auto" 
                overflowY="hidden"
                sx={commonStyles.scrollbar}
              >
                <TabList 
                  flexWrap="nowrap" 
                  minW="max-content"
                  borderBottomColor={useColorModeValue('gray.200', 'gray.600')}
                >
                  {tabs.map(tab => (
                    <Tab 
                      key={tab.id} 
                      py={spacing.cardPadding}
                      px={spacing.cardPadding}
                      minW="max-content"
                      whiteSpace="nowrap"
                      fontSize={{ base: "sm", md: "md" }}
                      fontWeight="medium"
                      transition={commonStyles.transitions.default}
                      _hover={{
                        color: useColorModeValue('gray.900', 'gray.100'),
                        bg: useColorModeValue('gray.50', 'gray.700')
                      }}
                    >
                      <HStack spacing={spacing.cardPadding.base}>
                        <Icon as={tab.icon} boxSize={spacing.iconBoxSize} />
                        <Text fontSize={{ base: "xs", md: "sm" }} whiteSpace="nowrap">{tab.label}</Text>
                      </HStack>
                    </Tab>
                  ))}
                </TabList>
              </Box>
              
              <TabPanels>
                {/* 総合評価タブ */}
                <TabPanel p={spacing.cardPadding}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    {/* サマリーカード */}
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={spacing.sectionSpacing}>
                      <Card bg={useColorModeValue('green.50', 'green.900')} borderColor="green.200" borderWidth={1}>
                        <CardHeader pb={2} p={spacing.cardPadding}>
                          <HStack>
                            <Icon as={CheckCircleIcon} color="green.600" boxSize={spacing.iconBoxSize} />
                            <Heading {...textStyles.subheading} color="green.700">強み</Heading>
                          </HStack>
                        </CardHeader>
                        <CardBody pt={0} p={spacing.cardPadding}>
                          <List spacing={spacing.cardPadding.base}>
                            {reportData.summary.keyStrengths.slice(0, 4).map((strength, i) => (
                              <ListItem key={i} {...textStyles.caption}>
                                <ListIcon as={CheckCircleIcon} color="green.500" />
                                {strength}
                              </ListItem>
                            ))}
                          </List>
                        </CardBody>
                      </Card>

                      <Card bg={useColorModeValue('blue.50', 'blue.900')} borderColor="blue.200" borderWidth={1}>
                        <CardHeader pb={2} p={spacing.cardPadding}>
                          <HStack>
                            <Icon as={InfoIcon} color="blue.600" boxSize={spacing.iconBoxSize} />
                            <Heading {...textStyles.subheading} color="blue.700">改善ポイント</Heading>
                          </HStack>
                        </CardHeader>
                        <CardBody pt={0} p={spacing.cardPadding}>
                          <List spacing={spacing.cardPadding.base}>
                            {reportData.summary.primaryImprovements.slice(0, 4).map((improvement, i) => (
                              <ListItem key={i} {...textStyles.caption}>
                                <ListIcon as={WarningIcon} color="blue.500" />
                                {improvement}
                              </ListItem>
                            ))}
                          </List>
                        </CardBody>
                      </Card>

                      <Card bg={useColorModeValue('purple.50', 'purple.900')} borderColor="purple.200" borderWidth={1}>
                        <CardHeader pb={2} p={spacing.cardPadding}>
                          <HStack>
                            <Icon as={StarIcon} color="purple.600" boxSize={spacing.iconBoxSize} />
                            <Heading {...textStyles.subheading} color="purple.700">次のステップ</Heading>
                          </HStack>
                        </CardHeader>
                        <CardBody pt={0} p={spacing.cardPadding}>
                          <List spacing={spacing.cardPadding.base}>
                            {reportData.summary.nextSteps.slice(0, 4).map((step, i) => (
                              <ListItem key={i} {...textStyles.caption}>
                                <ListIcon as={ArrowUpIcon} color="purple.500" />
                                {step}
                              </ListItem>
                            ))}
                          </List>
                        </CardBody>
                      </Card>
                    </SimpleGrid>

                    {/* カテゴリー別スコア - レスポンシブ二段表示 */}
                    <Card>
                      <CardHeader>
                        <Heading size="md">詳細評価スコア</Heading>
                      </CardHeader>
                      <CardBody>
                        <SimpleGrid {...commonGridStyles.scoreGrid}>
                          <CustomCircularScore 
                            score={Math.round((
                              reportData.detailedEvaluation.communication.verbalFluency.score +
                              reportData.detailedEvaluation.communication.structuring.score +
                              reportData.detailedEvaluation.communication.nonVerbal.score
                            ) / 3)}
                            size={scoreSizes.large}
                            label="コミュニケーション"
                            color={scoreColors.communication}
                          />
                          <CustomCircularScore 
                            score={Math.round((
                              reportData.detailedEvaluation.logicalThinking.problemAnalysis.score +
                              reportData.detailedEvaluation.logicalThinking.solutionDesign.score
                            ) / 2)}
                            size={scoreSizes.large}
                            label="論理的思考"
                            color={scoreColors.logic}
                          />
                          <CustomCircularScore 
                            score={Math.round((
                              reportData.detailedEvaluation.experienceQuality.leadership.score +
                              reportData.detailedEvaluation.experienceQuality.problemSolving.score
                            ) / 2)}
                            size={scoreSizes.large}
                            label="経験・実績"
                            color={scoreColors.experience}
                          />
                          <CustomCircularScore 
                            score={Math.round((
                              reportData.detailedEvaluation.culturalFit.values.score +
                              reportData.detailedEvaluation.culturalFit.motivation.score
                            ) / 2)}
                            size={scoreSizes.large}
                            label="文化適合性"
                            color={scoreColors.culture}
                          />
                          <CustomCircularScore 
                            score={reportData.detailedEvaluation.technicalCompetence.domainKnowledge.score}
                            size={scoreSizes.large}
                            label="技術的能力"
                            color={scoreColors.technical}
                          />
                          <CustomCircularScore 
                            score={reportData.detailedEvaluation.adaptability.changeManagement.score}
                            size={scoreSizes.large}
                            label="適応力"
                            color={scoreColors.adaptability}
                          />
                        </SimpleGrid>
                      </CardBody>
                    </Card>

                    {/* ベンチマーク比較 */}
                    <Card>
                      <CardHeader>
                        <Heading size="md">ベンチマーク比較</Heading>
                      </CardHeader>
                      <CardBody>
                        <Alert status="info" mb={4}>
                          <AlertIcon />
                          <AlertDescription>
                            同じ業界・職種・経験レベルの候補者と比較した結果です
                          </AlertDescription>
                        </Alert>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing.sectionSpacing}>
                          <VStack spacing={spacing.cardPadding.base}>
                            <HStack justify="space-between" w="full">
                              <Text {...textStyles.body}>あなたのスコア</Text>
                              <Text fontWeight="bold">{reportData.overallAssessment.score}点</Text>
                            </HStack>
                            <HStack justify="space-between" w="full">
                              <Text {...textStyles.body}>業界平均</Text>
                              <Text>{reportData.benchmarkComparison.industryAverage}点</Text>
                            </HStack>
                            <HStack justify="space-between" w="full">
                              <Text {...textStyles.body}>あなたの順位</Text>
                              <Badge colorScheme="blue">上位{100 - reportData.benchmarkComparison.userPercentile}%</Badge>
                            </HStack>
                          </VStack>
                          <VStack align="start" spacing={spacing.cardPadding.base}>
                            <Text {...textStyles.body} fontWeight="medium">改善ポテンシャル</Text>
                            <Progress 
                              value={reportData.benchmarkComparison.improvementPotential} 
                              colorScheme="green" 
                              size="lg" 
                              w="full"
                            />
                            <Text fontSize="xs" color="gray.600">
                              +{reportData.benchmarkComparison.improvementPotential}ポイントの向上が期待できます
                            </Text>
                          </VStack>
                        </SimpleGrid>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                {/* コミュニケーションタブ */}
                <TabPanel p={spacing.cardPadding}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <Heading {...textStyles.heading} mb={4}>コミュニケーション能力の詳細分析</Heading>
                    
                    {/* 言語流暢性 - 詳細説明付き */}
                    <Card>
                      <CardHeader>
                        <HStack justify="space-between">
                          <Heading size="md">言語流暢性</Heading>
                          <Badge colorScheme={getScoreColor(reportData.detailedEvaluation.communication.verbalFluency.score)}>
                            {reportData.detailedEvaluation.communication.verbalFluency.score}点
                          </Badge>
                        </HStack>
                      </CardHeader>
                      <CardBody>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing.sectionSpacing}>
                          <Box>
                            <Stat mb={4}>
                              <StatLabel>フィラーワード頻度</StatLabel>
                              <StatNumber>{reportData.detailedEvaluation.communication.verbalFluency.metrics.fillerWords}回/分</StatNumber>
                              <StatHelpText>
                                {reportData.detailedEvaluation.communication.verbalFluency.metrics.fillerWords <= 2 ? '優秀' : '改善推奨'}
                              </StatHelpText>
                            </Stat>
                            <Text {...textStyles.body} color="gray.600">
                              「えー」「あの」などの無意味な言葉の使用頻度。
                              2回/分以下が理想的で、
                              3回/分以上は改善が必要です。
                            </Text>
                          </Box>
                          <Box>
                            <Stat mb={4}>
                              <StatLabel>話速</StatLabel>
                              <StatNumber>{reportData.detailedEvaluation.communication.verbalFluency.metrics.speechRate}語/分</StatNumber>
                              <StatHelpText>
                                {reportData.detailedEvaluation.communication.verbalFluency.metrics.speechRate >= 150 && 
                                 reportData.detailedEvaluation.communication.verbalFluency.metrics.speechRate <= 180 ? '最適' : '調整推奨'}
                              </StatHelpText>
                            </Stat>
                            <Text {...textStyles.body} color="gray.600">
                              面接での適切な話速は150-180語/分です。
                              速すぎると理解が困難、
                              遅すぎると退屈な印象を与えます。
                            </Text>
                          </Box>
                        </SimpleGrid>
                      </CardBody>
                    </Card>

                    {/* 構造化 - 統一コンポーネント使用 */}
                    <Card>
                      <CardHeader>
                        <HStack justify="space-between">
                          <Heading size="md">回答の構造化</Heading>
                          <Badge colorScheme={getScoreColor(reportData.detailedEvaluation.communication.structuring.score)}>
                            {reportData.detailedEvaluation.communication.structuring.score}点
                          </Badge>
                        </HStack>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={spacing.cardPadding.base} align="stretch">
                          <MetricProgress 
                            label="導入明確さ" 
                            value={reportData.detailedEvaluation.communication.structuring.metrics.introductionClarity}
                            description="質問の意図を理解し、回答の方向性を明確に示しているか"
                          />
                          <MetricProgress 
                            label="論理的流れ" 
                            value={reportData.detailedEvaluation.communication.structuring.metrics.logicalFlow}
                            description="話の筋道が一貫しており、理解しやすい構成になっているか"
                          />
                          <MetricProgress 
                            label="結論の適切さ" 
                            value={reportData.detailedEvaluation.communication.structuring.metrics.conclusion}
                            description="質問に対する明確な答えと、要点を繰り返しているか"
                          />
                          <MetricProgress 
                            label="時間管理" 
                            value={reportData.detailedEvaluation.communication.structuring.metrics.timeManagement}
                            description="制限時間内で適切に情報を伝えられているか"
                          />
                        </VStack>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                {/* 経験・実績タブ */}
                <TabPanel p={spacing.cardPadding}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <Heading {...textStyles.heading} mb={4}>経験・実績の詳細分析</Heading>
                    
                    {/* STAR+C分析 */}
                    {reportData.detailedEvaluation.experienceQuality.leadership.examples.map((example, index) => (
                      <Card key={index}>
                        <CardHeader>
                          <VStack align="start" spacing={spacing.cardPadding.base}>
                            <Heading size="md">STAR+C分析 #{index + 1}</Heading>
                            <Text {...textStyles.body} color="gray.600">{example.question}</Text>
                          </VStack>
                        </CardHeader>
                        <CardBody>
                          <SimpleGrid 
                            {...commonGridStyles.starFrameworkGrid}
                            mb={spacing.sectionSpacing}
                          >
                            <VStack spacing={spacing.cardPadding.base} align="center">
                              <HStack spacing={1} justify="center">
                                <Text fontSize="xs" color={`${scoreColors.situation}.600`} fontWeight="bold">Situation</Text>
                                <Text fontSize="xs" color="gray.500">-</Text>
                                <Text fontSize="xs" color="gray.600">明確さ</Text>
                              </HStack>
                              <CustomCircularScore
                                score={example.evaluation.situation.clarity}
                                size={scoreSizes.large}
                                color={scoreColors.situation}
                                maxScore={100}
                              />
                            </VStack>
                            
                            <VStack spacing={spacing.cardPadding.base} align="center">
                              <HStack spacing={1} justify="center">
                                <Text fontSize="xs" color={`${scoreColors.task}.600`} fontWeight="bold">Task</Text>
                                <Text fontSize="xs" color="gray.500">-</Text>
                                <Text fontSize="xs" color="gray.600">役割明確さ</Text>
                              </HStack>
                              <CustomCircularScore
                                score={example.evaluation.task.roleClarity}
                                size={scoreSizes.large}
                                color={scoreColors.task}
                                maxScore={100}
                              />
                            </VStack>
                            
                            <VStack spacing={spacing.cardPadding.base} align="center">
                              <HStack spacing={1} justify="center">
                                <Text fontSize="xs" color={`${scoreColors.action}.600`} fontWeight="bold">Action</Text>
                                <Text fontSize="xs" color="gray.500">-</Text>
                                <Text fontSize="xs" color="gray.600">主体性</Text>
                              </HStack>
                              <CustomCircularScore
                                score={example.evaluation.action.initiative}
                                size={scoreSizes.large}
                                color={scoreColors.action}
                                maxScore={100}
                              />
                            </VStack>
                            
                            <VStack spacing={spacing.cardPadding.base} align="center">
                              <HStack spacing={1} justify="center">
                                <Text fontSize="xs" color={`${scoreColors.result}.600`} fontWeight="bold">Result</Text>
                                <Text fontSize="xs" color="gray.500">-</Text>
                                <Text fontSize="xs" color="gray.600">定量性</Text>
                              </HStack>
                              <CustomCircularScore
                                score={example.evaluation.result.quantification}
                                size={scoreSizes.large}
                                color={scoreColors.result}
                                maxScore={100}
                              />
                            </VStack>
                            
                            <VStack spacing={spacing.cardPadding.base} align="center">
                              <HStack spacing={1} justify="center">
                                <Text fontSize="xs" color={`${scoreColors.competency}.600`} fontWeight="bold">Competency</Text>
                                <Text fontSize="xs" color="gray.500">-</Text>
                                <Text fontSize="xs" color="gray.600">応用可能性</Text>
                              </HStack>
                              <CustomCircularScore
                                score={example.evaluation.competency.transferability}
                                size={scoreSizes.large}
                                color={scoreColors.competency}
                                maxScore={100}
                              />
                            </VStack>
                          </SimpleGrid>

                          <Divider mb={4} />

                          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={spacing.sectionSpacing}>
                            <Card bg={useColorModeValue('gray.50', 'gray.700')} p={spacing.cardPadding}>
                              <Text {...textStyles.label} mb={2}>回答内容</Text>
                              <VStack align="start" spacing={spacing.cardPadding.base}>
                                <Text {...textStyles.body}><strong>状況:</strong> {example.response.situation}</Text>
                                <Text {...textStyles.body}><strong>課題:</strong> {example.response.task}</Text>
                                <Text {...textStyles.body}><strong>行動:</strong> {example.response.action}</Text>
                                <Text {...textStyles.body}><strong>結果:</strong> {example.response.result}</Text>
                                <Text {...textStyles.body}><strong>学び:</strong> {example.response.competency}</Text>
                              </VStack>
                            </Card>

                            <Card bg={useColorModeValue('blue.50', 'blue.700')} p={spacing.cardPadding}>
                              <Text {...textStyles.label} mb={2}>改善提案</Text>
                              <List spacing={spacing.cardPadding.base}>
                                {example.improvements.map((improvement, i) => (
                                  <ListItem key={i}>
                                    <ListIcon as={InfoIcon} color="blue.500" />
                                    <Text {...textStyles.body}>{improvement.suggestion}</Text>
                                    {improvement.examplePhrase && (
                                      <Text {...textStyles.caption} ml={6} mt={1}>
                                        例: {improvement.examplePhrase}
                                      </Text>
                                    )}
                                  </ListItem>
                                ))}
                              </List>
                            </Card>
                          </SimpleGrid>
                        </CardBody>
                      </Card>
                    ))}
                  </VStack>
                </TabPanel>

                {/* 音声・表情分析タブ */}
                <TabPanel p={spacing.cardPadding}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <Heading {...textStyles.heading} mb={4}>音声・表情分析結果</Heading>
                    
                    {/* 音声品質サマリー - 統一コンポーネント使用 */}
                    <Card>
                      <CardHeader>
                        <Heading size="md">音声品質の総合評価</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={spacing.cardPadding.base} align="stretch">
                          <Box p={4} bg={useColorModeValue('blue.50', 'blue.900')} borderRadius="md">
                            <HStack justify="space-between" mb={2}>
                              <Text fontWeight="bold">全体的な話し方の印象</Text>
                              <Badge colorScheme="blue" textTransform="capitalize">
                                {reportData.audioVideoAnalysis.audio.emotionAnalysis.overallSentiment}
                              </Badge>
                            </HStack>
                            <Text {...textStyles.body} color="gray.600">
                              AIが分析した音声からの全体的な感情と話し方の印象
                              （信頼度: {Math.round(reportData.audioVideoAnalysis.audio.emotionAnalysis.confidence * 100)}%）
                            </Text>
                          </Box>
                          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing.cardPadding.base}>
                            <Box>
                              <MetricProgress 
                                label="話速適切性" 
                                value={reportData.audioVideoAnalysis.audio.prosodyAnalysis.speakingRate >= 150 && 
                                      reportData.audioVideoAnalysis.audio.prosodyAnalysis.speakingRate <= 180 ? 90 : 60}
                                description={`現在: ${reportData.audioVideoAnalysis.audio.prosodyAnalysis.speakingRate}語/分 （理想: 150-180語/分）`}
                              />
                            </Box>
                            <Box>
                              <MetricProgress 
                                label="フィラーワードコントロール" 
                                value={Math.max(0, 100 - (reportData.audioVideoAnalysis.audio.prosodyAnalysis.fillerWords.frequency * 20))}
                                color={reportData.audioVideoAnalysis.audio.prosodyAnalysis.fillerWords.frequency <= 2 ? 'green' : 'orange'}
                                description={`出現頻度: ${reportData.audioVideoAnalysis.audio.prosodyAnalysis.fillerWords.frequency}回/分 （総数: ${reportData.audioVideoAnalysis.audio.prosodyAnalysis.fillerWords.count}回）`}
                              />
                            </Box>
                          </SimpleGrid>
                        </VStack>
                      </CardBody>
                    </Card>

                    {/* 感情・音声特徴分析 */}
                    <Card>
                      <CardHeader>
                        <Heading size="md">感情・音声特徴の詳細</Heading>
                      </CardHeader>
                      <CardBody>
                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing.sectionSpacing}>
                          <VStack spacing={spacing.cardPadding.base} align="stretch">
                            <Text fontWeight="bold" mb={2}>感情の内訳</Text>
                            {Object.entries(reportData.audioVideoAnalysis.audio.emotionAnalysis.emotions).map(([emotion, value]) => {
                              const emotionLabels: { [key: string]: string } = {
                                enthusiasm: '熱意・情熱',
                                nervousness: '緊張・不安', 
                                confidence: '自信・落ち着き',
                                frustration: 'イライラ・怖思'
                              };
                              return (
                                <MetricProgress 
                                  key={emotion}
                                  label={emotionLabels[emotion] || emotion}
                                  value={value}
                                  color={emotion === 'confidence' || emotion === 'enthusiasm' ? 'green' : 
                                        emotion === 'nervousness' || emotion === 'frustration' ? 'orange' : 'blue'}
                                />
                              );
                            })}
                          </VStack>
                          <VStack spacing={spacing.cardPadding.base} align="stretch">
                            <Text fontWeight="bold" mb={2}>音声特徴の分析</Text>
                            <Box p={4} bg={useColorModeValue('gray.50', 'gray.700')} borderRadius="md">
                              <VStack spacing={spacing.cardPadding.base} align="start">
                                <HStack justify="space-between" w="full">
                                  <Text {...textStyles.body} color="gray.600">平均ピッチ</Text>
                                  <Text {...textStyles.body} fontWeight="medium">
                                    {reportData.audioVideoAnalysis.audio.prosodyAnalysis.averagePitch}Hz
                                  </Text>
                                </HStack>
                                <Text fontSize="xs" color="gray.500">
                                  高いピッチは緊張や恐れ、低いピッチは自信や落ち着きを表します
                                </Text>
                                <Divider />
                                <HStack justify="space-between" w="full">
                                  <Text {...textStyles.body} color="gray.600">ピッチ変動幅</Text>
                                  <Text {...textStyles.body} fontWeight="medium">
                                    {reportData.audioVideoAnalysis.audio.prosodyAnalysis.pitchVariation}Hz
                                  </Text>
                                </HStack>
                                <Text fontSize="xs" color="gray.500">
                                  適度な変動は表現力を示し、変動が小さいと単調な印象を与えます
                                </Text>
                                <Divider />
                                <HStack justify="space-between" w="full">
                                  <Text {...textStyles.body} color="gray.600">適切なポーズ</Text>
                                  <Text {...textStyles.body} fontWeight="medium">
                                    {reportData.audioVideoAnalysis.audio.prosodyAnalysis.pauseAnalysis.totalPauses}回
                                  </Text>
                                </HStack>
                                <Text fontSize="xs" color="gray.500">
                                  適切なポーズは聞き手の理解を助け、話し方のリズムを整えます
                                </Text>
                              </VStack>
                            </Box>
                          </VStack>
                        </SimpleGrid>
                      </CardBody>
                    </Card>

                    {/* 音声セグメント分析 */}
                    <Card>
                      <CardHeader>
                        <Heading size="md">重要なセグメント</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={spacing.cardPadding.base} align="stretch">
                          {reportData.audioVideoAnalysis.audio.segments.map((segment, index) => (
                            <Box 
                              key={index}
                              p={4}
                              borderRadius="md"
                              bg={
                                segment.type === 'excellent' ? useColorModeValue('green.50', 'green.900') :
                                segment.type === 'improvement' ? useColorModeValue('orange.50', 'orange.900') :
                                useColorModeValue('gray.50', 'gray.700')
                              }
                              borderWidth={1}
                              borderColor={
                                segment.type === 'excellent' ? 'green.200' :
                                segment.type === 'improvement' ? 'orange.200' :
                                'gray.200'
                              }
                            >
                              <HStack justify="space-between" mb={2}>
                                <HStack spacing={spacing.cardPadding.base}>
                                  <Button 
                                    size="sm" 
                                    variant="ghost"
                                    onClick={() => setPlayingAudio(playingAudio === segment.timestamp ? null : segment.timestamp)}
                                  >
                                    {playingAudio === segment.timestamp ? '⏸️' : '▶️'}
                                  </Button>
                                  <Text {...textStyles.body} fontWeight="medium">{segment.timestamp}</Text>
                                </HStack>
                                <Badge 
                                  colorScheme={
                                    segment.type === 'excellent' ? 'green' :
                                    segment.type === 'improvement' ? 'orange' : 'gray'
                                  }
                                >
                                  {segment.type === 'excellent' ? '優れた回答' : 
                                   segment.type === 'improvement' ? '改善ポイント' : '標準'}
                                </Badge>
                              </HStack>
                              <Text {...textStyles.body} fontStyle="italic" color="gray.600" mb={2}>
                                "{segment.transcript}"
                              </Text>
                              <Text {...textStyles.body}>{segment.feedback}</Text>
                            </Box>
                          ))}
                        </VStack>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                {/* 改善のためのアクションプランタブ */}
                <TabPanel p={spacing.cardPadding}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <VStack spacing={spacing.cardPadding.base} textAlign="center">
                      <Heading {...textStyles.heading}>改善アクションプラン</Heading>
                      <Text {...textStyles.body} color="gray.600">
                        あなたの面接スキル向上のための段階的な改善プランです
                      </Text>
                    </VStack>
                    
                    {/* タイムライン形式のプラン */}
                    <Box position="relative">
                      <Box 
                        position="absolute" 
                        left="8" 
                        top="0" 
                        bottom="0" 
                        width="2px" 
                        bg="blue.200"
                      />
                      
                      <VStack spacing={8} align="stretch">
                        {[
                          { timeframe: 'immediate', title: '今すぐ始める', color: 'red', items: reportData.actionPlan.immediate },
                          { timeframe: 'shortTerm', title: '1週間以内', color: 'orange', items: reportData.actionPlan.shortTerm },
                        ].map(({ timeframe, title, color, items }, phaseIndex) => (
                          <ActionPlanPhase
                            key={timeframe}
                            title={title}
                            color={color}
                            items={items}
                            phaseIndex={phaseIndex}
                          />
                        ))}

                      </VStack>
                    </Box>

                    {/* 進捗追跡 */}
                    <Card bg={useColorModeValue('blue.50', 'blue.900')}>
                      <CardBody>
                        <HStack spacing={spacing.cardPadding.base} mb={3}>
                          <Icon as={InfoIcon} color="blue.600" />
                          <Text fontWeight="bold" color="blue.700">
                            アクションプラン活用のヒント
                          </Text>
                        </HStack>
                        <List spacing={spacing.cardPadding.base}>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="blue.500" />
                            <Text {...textStyles.body}>各アクションは段階的に実行し、前の段階が完了してから次に進みましょう</Text>
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="blue.500" />
                            <Text {...textStyles.body}>週1回の振り返りを行い、進捗を確認してください</Text>
                          </ListItem>
                          <ListItem>
                            <ListIcon as={CheckCircleIcon} color="blue.500" />
                            <Text {...textStyles.body}>困難に感じる項目があれば、より小さなステップに分解しましょう</Text>
                          </ListItem>
                        </List>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </CardBody>
        </Card>

        {/* アクションボタン */}
        <VStack 
          spacing={spacing.sectionSpacing} 
          w="full" 
          align="center"
          direction={{ base: "column", md: "row" }}
        >
          <Button 
            colorScheme="blue" 
            size={{ base: "md", md: "lg" }}
            w={{ base: "full", md: "auto" }}
            {...spacing.buttonPadding}
            onClick={onStartNewPractice}
          >
            新しい練習を開始
          </Button>
          <HStack spacing={spacing.sectionSpacing} w={{ base: "full", md: "auto" }}>
            <Button 
              variant="outline" 
              size={{ base: "md", md: "lg" }}
              flex={{ base: 1, md: "none" }}
              {...spacing.buttonPadding}
              onClick={onClose}
            >
              ダッシュボードに戻る
            </Button>
            <Button 
              variant="outline" 
              size={{ base: "md", md: "lg" }}
              flex={{ base: 1, md: "none" }}
              {...spacing.buttonPadding}
              onClick={() => window.print()}
            >
              レポートを印刷
            </Button>
          </HStack>
        </VStack>
        </VStack>
      </Box>
    </Box>
  );
};

export default ComprehensiveFeedbackReport;