import React, { useEffect, useState } from "react";
import { Box, Text, VStack, Button, Image, Flex, Badge } from "@chakra-ui/react";
import logger from "../utils/logger";

/**
 * インタビューアバターコンポーネントのプロパティ
 */
interface AvatarProps {
  /** フィードバックメッセージ */
  feedback?: string;
  /** 発話中フラグ */
  isSpeaking?: boolean;
}

/**
 * アバターの状態を表す型
 */
type AvatarStatus = "ready" | "speaking" | "listening";

/**
 * モック化されたインタビューアバターコンポーネント
 * UI整理・承認フェーズ用の静的アバター表示
 * @param feedback - アバターが表示するフィードバックメッセージ
 * @param isSpeaking - 発話中フラグ（アニメーション制御用）
 * @returns モックアバターコンポーネント
 */
const InterviewAvatar: React.FC<AvatarProps> = ({ feedback, isSpeaking = false }) => {
  const [status, setStatus] = useState<AvatarStatus>("ready");
  const [currentMessage, setCurrentMessage] = useState<string>("");
  const [logs, setLogs] = useState<string[]>([]);

  /**
   * ログメッセージを記録し、UIに表示する
   * @param message - ログメッセージ
   */
  const log = (message: string): void => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `${timestamp}: ${message}`;
    logger.debug(logMessage);
    setLogs(prev => [...prev.slice(-5), logMessage]);
  };

  /**
   * アバターの発話状態をシミュレート
   */
  useEffect(() => {
    if (feedback) {
      log(`🎤 Avatar speaking: "${feedback.substring(0, 30)}..."`);
      setStatus("speaking");
      setCurrentMessage(feedback);
      
      // 3秒後に発話完了
      const timer = setTimeout(() => {
        setStatus("ready");
        log("✅ Speech completed");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [feedback]);

  /**
   * 初期化ログ
   */
  useEffect(() => {
    log("🎭 Mock avatar initialized successfully");
    setStatus("ready");
  }, []);

  /**
   * アバター再起動（デモ用）
   */
  const restartAvatar = (): void => {
    log("🔄 Avatar restarting...");
    setStatus("ready");
    setCurrentMessage("");
    setLogs([]);
    
    setTimeout(() => {
      log("✅ Avatar ready for interview");
    }, 1000);
  };

  return (
    <VStack spacing={4} w="100%" h="100%">
      {/* メインアバター表示エリア */}
      <Box
        w="100%"
        h="80%"
        position="relative"
        bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        borderRadius="lg"
        overflow="hidden"
        border="3px solid"
        borderColor={
          status === "speaking" ? "green.500" :
          status === "listening" ? "blue.500" : 
          "gray.300"
        }
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        {/* アバター画像プレースホルダー */}
        <VStack spacing={4}>
          <Box
            w="120px"
            h="120px"
            borderRadius="full"
            bg="white"
            display="flex"
            alignItems="center"
            justifyContent="center"
            boxShadow="lg"
            border={status === "speaking" ? "4px solid" : "2px solid"}
            borderColor={status === "speaking" ? "green.400" : "gray.200"}
            animation={status === "speaking" ? "pulse 1.5s infinite" : undefined}
          >
            {/* アバター表情アイコン */}
            <Text fontSize="4xl" role="img" aria-label="avatar">
              {status === "speaking" ? "😊" : 
               status === "listening" ? "🤔" : "👨‍💼"}
            </Text>
          </Box>
          
          {/* アバター名 */}
          <Text color="white" fontSize="xl" fontWeight="bold">
            AIアシスタント
          </Text>
          
          {/* 状態表示 */}
          <Badge
            colorScheme={
              status === "speaking" ? "green" :
              status === "listening" ? "blue" : "gray"
            }
            fontSize="sm"
            px={3}
            py={1}
            borderRadius="full"
          >
            {status === "speaking" && "発話中"}
            {status === "listening" && "聞き取り中"}
            {status === "ready" && "待機中"}
          </Badge>
        </VStack>

        {/* 発話内容表示 */}
        {currentMessage && status === "speaking" && (
          <Box
            position="absolute"
            bottom="4"
            left="4"
            right="4"
            bg="rgba(0,0,0,0.8)"
            color="white"
            p="3"
            borderRadius="md"
            fontSize="sm"
            maxH="100px"
            overflow="auto"
          >
            <Text>{currentMessage}</Text>
          </Box>
        )}

        {/* ステータス表示 */}
        <Box
          position="absolute"
          top="4"
          right="4"
          px="3"
          py="2"
          bg={
            status === "speaking" ? "green.500" :
            status === "listening" ? "blue.500" : "gray.500"
          }
          color="white"
          borderRadius="md"
          fontSize="sm"
          fontWeight="bold"
        >
          {status === "speaking" && "🎤 発話中"}
          {status === "listening" && "👂 聞き取り中"}
          {status === "ready" && "✅ 準備完了"}
        </Box>

        {/* モック表示マーク */}
        <Box
          position="absolute"
          top="4"
          left="4"
          px="2"
          py="1"
          bg="rgba(255,255,255,0.9)"
          color="gray.700"
          borderRadius="sm"
          fontSize="xs"
          fontWeight="bold"
        >
          MOCK AVATAR
        </Box>
      </Box>

      {/* コントロールパネル */}
      <Box
        w="100%"
        h="20%"
        bg="gray.50"
        p="3"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
      >
        <Flex justify="space-between" align="center" mb="2">
          <Text fontWeight="bold" fontSize="sm">
            アバター制御パネル
          </Text>
          <Button size="xs" colorScheme="blue" onClick={restartAvatar}>
            再起動
          </Button>
        </Flex>
        
        {/* ログ表示 */}
        <Box
          bg="white"
          p="2"
          borderRadius="sm"
          fontSize="xs"
          fontFamily="monospace"
          maxH="60px"
          overflow="auto"
          border="1px solid"
          borderColor="gray.200"
        >
          {logs.length > 0 ? (
            logs.map((log, index) => (
              <Text key={index} color="gray.600" mb="1">
                {log}
              </Text>
            ))
          ) : (
            <Text color="gray.400">ログが表示されます...</Text>
          )}
        </Box>
      </Box>

      {/* CSS アニメーション */}
      <style jsx>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      `}</style>
    </VStack>
  );
};

export default InterviewAvatar;