import { QuestionData, ScenarioConfig, CompanyInfo } from '../../lib/unified-data';
import { UserState } from '../AdaptiveAvatar';

// Re-export CompanyInfo từ unified-data
export type { CompanyInfo };

export interface MeetingState {
  scenario: ScenarioConfig | null;
  questions: QuestionData[];
  currentQuestionIndex: number;
  isInterviewActive: boolean;
  meetingDuration: number;
  allFeedbacks: any[];
}

export interface ControlsState {
  isMuted: boolean;
  isVideoOff: boolean;
  showChat: boolean;
  showInfo: boolean;
  isProcessingAnswer: boolean;
  isRecording: boolean;
}

export interface AvatarState {
  userState: UserState;
  avatarSpeaking: boolean;
}

export interface MeetingHandlers {
  onMuteToggle: () => void;
  onVideoToggle: () => void;
  onEndCall: () => void;
  onAnswerQuestion: () => void;
  onShowInfo: (show: boolean) => void;
  onShowChat: (show: boolean) => void;
} 