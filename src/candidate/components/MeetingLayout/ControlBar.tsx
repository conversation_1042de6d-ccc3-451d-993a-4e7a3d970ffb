import React from 'react';
import { 
  Box, 
  HStack, 
  IconButton, 
  Button, 
  Tooltip,
  useBreakpointValue 
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaMicrophone,
  FaMicrophoneSlash,
  FaVideo,
  FaVideoSlash,
  FaPhone,
  FaComments,
  FaUsers,
  FaDesktop,
  FaEllipsisV
} from 'react-icons/fa';

interface ControlBarProps {
  isMuted: boolean;
  isVideoOff: boolean;
  showChat: boolean;
  onMuteToggle: () => void;
  onVideoToggle: () => void;
  onChatToggle: () => void;
  onEndCall: () => void;
}

export const ControlBar: React.FC<ControlBarProps> = ({
  isMuted,
  isVideoOff,
  showChat,
  onMuteToggle,
  onVideoToggle,
  onChatToggle,
  onEndCall
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <motion.div
      initial={{ y: 200, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 1.6, duration: 0.8, type: "spring", stiffness: 200 }}
    >
      <Box
        position="fixed"
        bottom={12}
        left="50%"
        transform="translateX(-50%)"
        zIndex={40}
      >
        <HStack
          bg="rgba(15, 23, 42, 0.8)"
          backdropFilter="blur(30px) saturate(180%)"
          borderRadius="full"
          px={12}
          py={6}
          spacing={10}
          border="2px solid rgba(255, 255, 255, 0.1)"
          boxShadow="0 25px 50px rgba(0, 0, 0, 0.4)"
        >
          {/* Microphone */}
          <Tooltip label={isMuted ? "Unmute" : "Mute"} hasArrow placement="top">
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <IconButton
                aria-label="Toggle microphone"
                icon={isMuted ? <FaMicrophoneSlash /> : <FaMicrophone />}
                size={isMobile ? "lg" : "xl"}
                borderRadius="full"
                bg={isMuted ? "linear-gradient(45deg, #ef4444, #dc2626)" : "rgba(255, 255, 255, 0.15)"}
                color="white"
                _hover={{
                  bg: isMuted ? "linear-gradient(45deg, #dc2626, #b91c1c)" : "rgba(255, 255, 255, 0.25)",
                  boxShadow: isMuted ? "0 10px 25px rgba(239, 68, 68, 0.4)" : "0 10px 25px rgba(255, 255, 255, 0.2)"
                }}
                transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                onClick={onMuteToggle}
                border="1px solid rgba(255, 255, 255, 0.2)"
              />
            </motion.div>
          </Tooltip>
          
          {/* Camera */}
          <Tooltip label={isVideoOff ? "Turn on camera" : "Turn off camera"} hasArrow placement="top">
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <IconButton
                aria-label="Toggle camera"
                icon={isVideoOff ? <FaVideoSlash /> : <FaVideo />}
                size={isMobile ? "lg" : "xl"}
                borderRadius="full"
                bg={isVideoOff ? "linear-gradient(45deg, #ef4444, #dc2626)" : "rgba(255, 255, 255, 0.15)"}
                color="white"
                _hover={{
                  bg: isVideoOff ? "linear-gradient(45deg, #dc2626, #b91c1c)" : "rgba(255, 255, 255, 0.25)",
                  boxShadow: isVideoOff ? "0 10px 25px rgba(239, 68, 68, 0.4)" : "0 10px 25px rgba(255, 255, 255, 0.2)"
                }}
                transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                onClick={onVideoToggle}
                border="1px solid rgba(255, 255, 255, 0.2)"
              />
            </motion.div>
          </Tooltip>
          
          {!isMobile && (
            <>
              {/* Share Screen */}
              <Tooltip label="Share screen" hasArrow placement="top">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <IconButton
                    aria-label="Share screen"
                    icon={<FaDesktop />}
                    size="xl"
                    borderRadius="full"
                    bg="rgba(255, 255, 255, 0.15)"
                    color="white"
                    _hover={{ 
                      bg: "rgba(255, 255, 255, 0.25)",
                      boxShadow: "0 10px 25px rgba(255, 255, 255, 0.2)"
                    }}
                    transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                    border="1px solid rgba(255, 255, 255, 0.2)"
                  />
                </motion.div>
              </Tooltip>
              
              {/* Participants */}
              <Tooltip label="Participants" hasArrow placement="top">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <IconButton
                    aria-label="Participants"
                    icon={<FaUsers />}
                    size="xl"
                    borderRadius="full"
                    bg="rgba(255, 255, 255, 0.15)"
                    color="white"
                    _hover={{ 
                      bg: "rgba(255, 255, 255, 0.25)",
                      boxShadow: "0 10px 25px rgba(255, 255, 255, 0.2)"
                    }}
                    transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                    border="1px solid rgba(255, 255, 255, 0.2)"
                  />
                </motion.div>
              </Tooltip>
              
              {/* Chat */}
              <Tooltip label="Chat" hasArrow placement="top">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <IconButton
                    aria-label="Chat"
                    icon={<FaComments />}
                    size="xl"
                    borderRadius="full"
                    bg={showChat ? "linear-gradient(45deg, #3b82f6, #8b5cf6)" : "rgba(255, 255, 255, 0.15)"}
                    color="white"
                    _hover={{ 
                      bg: showChat ? "linear-gradient(45deg, #2563eb, #7c3aed)" : "rgba(255, 255, 255, 0.25)",
                      boxShadow: showChat ? "0 10px 25px rgba(59, 130, 246, 0.4)" : "0 10px 25px rgba(255, 255, 255, 0.2)"
                    }}
                    transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                    onClick={onChatToggle}
                    border="1px solid rgba(255, 255, 255, 0.2)"
                  />
                </motion.div>
              </Tooltip>
            </>
          )}
          
          {/* End Call */}
          <Tooltip label="End interview" hasArrow placement="top">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                leftIcon={<FaPhone />}
                bg="linear-gradient(45deg, #ef4444, #dc2626)"
                color="white"
                size={isMobile ? "lg" : "xl"}
                borderRadius="full"
                px={isMobile ? 6 : 12}
                fontSize="lg"
                fontWeight="bold"
                _hover={{ 
                  bg: "linear-gradient(45deg, #dc2626, #b91c1c)",
                  boxShadow: "0 15px 30px rgba(239, 68, 68, 0.5)"
                }}
                transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                onClick={onEndCall}
                boxShadow="0 10px 25px rgba(239, 68, 68, 0.4)"
                border="1px solid rgba(255, 255, 255, 0.2)"
              >
                End
              </Button>
            </motion.div>
          </Tooltip>
          
          {!isMobile && (
            /* More options */
            <Tooltip label="More options" hasArrow placement="top">
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <IconButton
                  aria-label="More options"
                  icon={<FaEllipsisV />}
                  size="xl"
                  borderRadius="full"
                  bg="rgba(255, 255, 255, 0.15)"
                  color="white"
                  _hover={{ 
                    bg: "rgba(255, 255, 255, 0.25)",
                    boxShadow: "0 10px 25px rgba(255, 255, 255, 0.2)"
                  }}
                  transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                  border="1px solid rgba(255, 255, 255, 0.2)"
                />
              </motion.div>
            </Tooltip>
          )}
        </HStack>
      </Box>
    </motion.div>
  );
}; 