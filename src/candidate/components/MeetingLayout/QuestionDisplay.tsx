import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Progress,
  IconButton,
  useBreakpointValue,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaMicrophone, FaVolumeUp } from 'react-icons/fa';
import { HiSparkles } from 'react-icons/hi';
import { useInterview } from '../../contexts/InterviewContext';
import { AutoSpeechInput } from '../AutoSpeechInput';

interface Question {
  id: string;
  text: string;
  category?: string;
  difficulty?: string;
}

interface QuestionDisplayProps {
  currentQuestion: Question | string;
  currentQuestionIndex: number;
  totalQuestions: number;
  isProcessingAnswer: boolean;
  onSpeechResponse?: (text: string) => void;
}

export const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  currentQuestion,
  currentQuestionIndex,
  totalQuestions,
  isProcessingAnswer,
  onSpeechResponse,
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const { speakText } = useInterview();

  const getQuestionText = () => {
    return typeof currentQuestion === 'string'
      ? currentQuestion
      : currentQuestion?.text || '';
  };

  const getQuestionId = () => {
    return typeof currentQuestion === 'string'
      ? 'question-' + currentQuestionIndex
      : currentQuestion?.id || 'question-' + currentQuestionIndex;
  };

  const getQuestionCategory = () => {
    return typeof currentQuestion === 'string'
      ? 'Interview'
      : currentQuestion?.category || 'Interview';
  };

  const getQuestionDifficulty = () => {
    return typeof currentQuestion === 'string'
      ? 'hard'
      : currentQuestion?.difficulty || 'hard';
  };

  const handleSpeakQuestion = () => {
    speakText(getQuestionText());
  };

  const handleSpeechTranscription = (text: string) => {
    if (onSpeechResponse) {
      onSpeechResponse(text);
    }
  };

  return (
    <AnimatePresence>
      {currentQuestion && (
        <motion.div
          initial={{ x: -100, opacity: 0, scale: 0.9 }}
          animate={{ x: 0, opacity: 1, scale: 1 }}
          exit={{ x: 100, opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.6, type: 'spring', stiffness: 300 }}
          style={{
            position: 'fixed',
            bottom: '180px',
            left: '40px',
            width: '400px',
            zIndex: 20,
          }}
        >
          <Box
            bg="rgba(15, 23, 42, 0.8)"
            backdropFilter="blur(30px) saturate(180%)"
            borderRadius="3xl"
            p={8}
            border="2px solid rgba(59, 130, 246, 0.3)"
            boxShadow="0 25px 50px rgba(0, 0, 0, 0.4)"
            position="relative"
            overflow="hidden"
          >
            {/* Background Glow Effect */}
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              bgGradient="linear(45deg, transparent, rgba(59, 130, 246, 0.1), transparent)"
              borderRadius="3xl"
              zIndex={-1}
            />

            <VStack spacing={6} align="stretch">
              <HStack justify="space-between" flexWrap="wrap">
                <HStack spacing={3}>
                  <Badge
                    bg="linear-gradient(45deg, #3b82f6, #8b5cf6)"
                    color="white"
                    fontSize="sm"
                    px={4}
                    py={2}
                    borderRadius="full"
                    fontWeight="bold"
                    boxShadow="0 4px 15px rgba(59, 130, 246, 0.3)"
                  >
                    <HiSparkles style={{ marginRight: '6px' }} />
                    {getQuestionCategory()}
                  </Badge>
                  <Badge
                    bg={
                      getQuestionDifficulty() === 'expert'
                        ? 'linear-gradient(45deg, #ef4444, #dc2626)'
                        : getQuestionDifficulty() === 'hard'
                        ? 'linear-gradient(45deg, #f97316, #ea580c)'
                        : 'linear-gradient(45deg, #eab308, #ca8a04)'
                    }
                    color="white"
                    fontSize="sm"
                    px={4}
                    py={2}
                    borderRadius="full"
                    fontWeight="bold"
                    boxShadow="0 4px 15px rgba(234, 179, 8, 0.3)"
                  >
                    {getQuestionDifficulty().toUpperCase()}
                  </Badge>
                </HStack>
                <Text color="whiteAlpha.800" fontSize="sm" fontWeight="medium">
                  {currentQuestionIndex + 1} / {totalQuestions}
                </Text>
              </HStack>

              <HStack align="flex-start" spacing={3}>
                <Text
                  color="white"
                  fontSize={isMobile ? 'lg' : 'xl'}
                  fontWeight="600"
                  lineHeight="tall"
                  letterSpacing="wide"
                  flex="1"
                >
                  {getQuestionText()}
                </Text>
                <IconButton
                  aria-label="Speak question"
                  icon={<FaVolumeUp />}
                  size="sm"
                  bg="rgba(59, 130, 246, 0.2)"
                  color="white"
                  borderRadius="full"
                  _hover={{
                    bg: 'rgba(59, 130, 246, 0.4)',
                    transform: 'scale(1.1)',
                  }}
                  onClick={handleSpeakQuestion}
                />
              </HStack>

              <Box>
                <Progress
                  value={((currentQuestionIndex + 1) / totalQuestions) * 100}
                  bg="rgba(255, 255, 255, 0.1)"
                  borderRadius="full"
                  size="lg"
                  sx={{
                    '& > div': {
                      background:
                        'linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899)',
                      borderRadius: 'full',
                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)',
                    },
                  }}
                />
                <Text
                  color="whiteAlpha.600"
                  fontSize="xs"
                  mt={2}
                  textAlign="center"
                >
                  Interview Progress •{' '}
                  {Math.round(
                    ((currentQuestionIndex + 1) / totalQuestions) * 100
                  )}
                  % Complete
                </Text>
              </Box>

              <Box w="full" maxW="600px">
                <AutoSpeechInput
                  questionId={getQuestionId()}
                  onTranscriptionComplete={handleSpeechTranscription}
                  disabled={isProcessingAnswer}
                  autoStart={true}
                  placeholder="質問に答えるために話し始めてください…"
                />
              </Box>
            </VStack>
          </Box>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
