import React from 'react';
import { Box, HStack, VStack, Text, Avatar, Badge, Progress } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { HiLightningBolt, HiMicrophone } from 'react-icons/hi';
import { FaBrain, FaEye } from 'react-icons/fa';
import { AdaptiveAvatar } from '../AdaptiveAvatar';
import { UserState } from '../AdaptiveAvatar';
import { CompanyInfo } from './types';

interface MainVideoAreaProps {
  companyInfo: CompanyInfo;
  userState: UserState;
  avatarSpeaking: boolean;
  currentQuestionText?: string;
}

export const MainVideoArea: React.FC<MainVideoAreaProps> = ({
  companyInfo,
  userState,
  avatarSpeaking,
  currentQuestionText
}) => {
  return (
    <Box flex="1" position="relative" h="100vh" pt="80px">
      {/* Main Avatar Container */}
      <motion.div
        initial={{ scale: 0.7, opacity: 0, y: 50 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        transition={{ duration: 1, delay: 0.4, type: "spring", stiffness: 200 }}
        style={{
          position: 'absolute',
          top: '15%',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '600px',
          height: '600px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <Box 
          w="100%" 
          h="100%" 
          position="relative"
          bg="rgba(15, 23, 42, 0.4)"
          backdropFilter="blur(25px) saturate(180%)"
          borderRadius="3xl"
          border="2px solid rgba(255, 255, 255, 0.1)"
          boxShadow="0 25px 50px rgba(0, 0, 0, 0.25)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          overflow="hidden"
        >
          {/* Gradient Border Effect */}
          <Box
            position="absolute"
            top={0} left={0} right={0} bottom={0}
            borderRadius="3xl"
            p="2px"
            bgGradient="linear(45deg, #3b82f6, #8b5cf6, #ec4899, #3b82f6)"
            zIndex={0}
          >
            <Box
              w="100%" h="100%"
              bg="rgba(15, 23, 42, 0.8)"
              borderRadius="3xl"
            />
          </Box>

          {/* AI Status Panel */}
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 1, duration: 0.6 }}
            style={{
              position: 'absolute',
              top: 24,
              left: 24,
              zIndex: 10
            }}
          >
            <Box
              bg="rgba(0, 0, 0, 0.6)"
              backdropFilter="blur(15px)"
              borderRadius="2xl"
              px={6}
              py={4}
              border="1px solid rgba(255, 255, 255, 0.1)"
              minW="200px"
            >
              <VStack spacing={3} align="start">
                <HStack spacing={3}>
                  <Avatar size="sm" bg="blue.500" icon={<HiLightningBolt />} />
                  <VStack spacing={0} align="start">
                    <Text color="white" fontSize="md" fontWeight="bold">
                      AI Interviewer
                    </Text>
                    <Text color="whiteAlpha.700" fontSize="xs">
                      {companyInfo.industry}
                    </Text>
                  </VStack>
                </HStack>
                
                <HStack spacing={4} w="100%">
                  {avatarSpeaking ? (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 0.5, repeat: Infinity }}
                    >
                      <Badge colorScheme="red" size="sm" borderRadius="full">
                        <HiMicrophone style={{ marginRight: '4px' }} />
                        Speaking
                      </Badge>
                    </motion.div>
                  ) : (
                    <Badge colorScheme="green" size="sm" borderRadius="full">
                      <FaEye style={{ marginRight: '4px' }} />
                      Listening
                    </Badge>
                  )}
                </HStack>
              </VStack>
            </Box>
          </motion.div>

          {/* Confidence Meter */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            style={{
              position: 'absolute',
              top: 24,
              right: 24,
              zIndex: 10
            }}
          >
            <Box
              bg="rgba(0, 0, 0, 0.6)"
              backdropFilter="blur(15px)"
              borderRadius="2xl"
              px={6}
              py={4}
              border="1px solid rgba(255, 255, 255, 0.1)"
              minW="180px"
            >
              <VStack spacing={3}>
                <HStack spacing={2}>
                  <FaBrain color="#8b5cf6" />
                  <Text color="white" fontSize="sm" fontWeight="bold">
                    AI Analysis
                  </Text>
                </HStack>
                <Box w="100%">
                  <HStack justify="space-between" mb={2}>
                    <Text color="whiteAlpha.700" fontSize="xs">Confidence</Text>
                    <Text color="white" fontSize="sm" fontWeight="bold">
                      {Math.round(userState.confidence * 100)}%
                    </Text>
                  </HStack>
                  <Progress 
                    value={userState.confidence * 100}
                    bg="rgba(255, 255, 255, 0.1)"
                    borderRadius="full"
                    size="sm"
                    sx={{
                      '& > div': {
                        background: userState.confidence > 0.7 
                          ? 'linear-gradient(90deg, #22c55e, #16a34a)'
                          : userState.confidence > 0.4
                          ? 'linear-gradient(90deg, #eab308, #ca8a04)'
                          : 'linear-gradient(90deg, #ef4444, #dc2626)',
                        borderRadius: 'full'
                      }
                    }}
                  />
                </Box>
              </VStack>
            </Box>
          </motion.div>

          {/* Main Avatar */}
          <motion.div
            animate={avatarSpeaking ? { scale: [1, 1.02, 1] } : {}}
            transition={{ duration: 0.5, repeat: Infinity }}
            style={{ position: 'relative', zIndex: 5 }}
          >
            <AdaptiveAvatar 
              userState={userState}
              isSpeaking={avatarSpeaking}
              feedback={currentQuestionText || ""}
            />
          </motion.div>

          {/* Speaking Indicator Ring */}
          {avatarSpeaking && (
            <motion.div
              animate={{ scale: [1, 1.3, 1], opacity: [0.5, 0.8, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              style={{
                position: 'absolute',
                width: '400px',
                height: '400px',
                border: '3px solid rgba(59, 130, 246, 0.6)',
                borderRadius: '50%',
                zIndex: 1
              }}
            />
          )}
        </Box>
      </motion.div>

      {/* AI Status Footer */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 1.4 }}
        style={{
          position: 'absolute',
          bottom: 180,
          left: '50%',
          transform: 'translateX(-50%)'
        }}
      >
        <Box
          bg="rgba(0, 0, 0, 0.4)"
          backdropFilter="blur(20px)"
          color="white"
          px={8}
          py={4}
          borderRadius="2xl"
          border="1px solid rgba(255, 255, 255, 0.1)"
          boxShadow="0 20px 40px rgba(0, 0, 0, 0.3)"
        >
          <Text fontSize="lg" fontWeight="bold" textAlign="center">
            {avatarSpeaking ? "AI is asking you a question..." : "Ready for your response"}
          </Text>
          {userState.emotionalState && (
            <Text fontSize="sm" color="whiteAlpha.700" textAlign="center" mt={1}>
              Detected mood: {userState.emotionalState}
            </Text>
          )}
        </Box>
      </motion.div>
    </Box>
  );
}; 