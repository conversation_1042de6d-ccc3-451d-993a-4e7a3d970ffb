import React from 'react';
import { Box, Container, VStack, Text, Progress } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { HiSparkles } from 'react-icons/hi';
import { CompanyInfo } from './types';

interface LoadingScreenProps {
  companyInfo: CompanyInfo;
  isLoading: boolean;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  companyInfo,
  isLoading,
}) => {
  if (!isLoading) {
    return null;
  }
  return (
    <Box h="100vh" bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" 
         display="flex" alignItems="center" justifyContent="center">
      <Container maxW="md" textAlign="center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <VStack spacing={8}>
            <Box position="relative">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <HiSparkles size={60} color="white" />
              </motion.div>
            </Box>
            <VStack spacing={4}>
              <Text color="white" fontSize="2xl" fontWeight="bold">
                Starting AI Interview
              </Text>
              <Text color="whiteAlpha.800" fontSize="lg">
                Connecting to {companyInfo.name}...
              </Text>
              <Progress 
                size="lg" 
                isIndeterminate 
                colorScheme="whiteAlpha" 
                w="300px"
                borderRadius="full"
                bg="whiteAlpha.200"
              />
            </VStack>
          </VStack>
        </motion.div>
      </Container>
    </Box>
  );
}; 