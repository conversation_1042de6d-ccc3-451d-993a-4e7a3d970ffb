/**
 * Modern Google Meet Style Interview Interface - Optimized Version
 * Chỉ sử dụng message từ socket, không mock data
 */
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { useToast, Box } from '@chakra-ui/react';

// Import services và types
import { CompanyInfo, MeetingState, ControlsState, AvatarState } from './types';

// Import components
import { TopHeaderBar } from './TopHeaderBar';
import { SelfVideoView } from './SelfVideoView';
import { QuestionDisplay } from './QuestionDisplay';
import { ControlBar } from './ControlBar';
import { MeetingInfoModal } from './MeetingInfoModal';
import { LoadingScreen } from './LoadingScreen';
import { Background } from './Background';
import { interviewService } from '@/services/interviewService';
import { interviewSocket } from '@/services/interviewSocket';

// Constants
const QUESTION_SPEAK_DELAY = 1000;
const TOAST_DURATION = 3000;

interface GoogleMeetInterviewProps {
  scenarioId: string;
  companyInfo: CompanyInfo;
  agentNotes: string[];
  onComplete: (results: any[]) => void;
  onBack: () => void;
}

const useInterviewState = (scenarioId: string) => {
  const [meetingState, setMeetingState] = useState<MeetingState>({
    scenario: null,
    questions: [],
    currentQuestionIndex: 0,
    isInterviewActive: false,
    meetingDuration: 0,
    allFeedbacks: [],
  });

  const [controlsState, setControlsState] = useState<ControlsState>({
    isMuted: false,
    isVideoOff: false,
    showChat: false,
    showInfo: false,
    isProcessingAnswer: false,
    isRecording: false,
  });

  const [avatarState, setAvatarState] = useState<AvatarState>({
    userState: {
      confidence: 0.8,
      isPaused: false,
      speakingSpeed: 'normal',
      emotionalState: 'confident',
    },
    avatarSpeaking: false,
  });

  return {
    meetingState,
    setMeetingState,
    controlsState,
    setControlsState,
    avatarState,
    setAvatarState,
  };
};

const useInterviewHandlers = (
  meetingState: MeetingState,
  setMeetingState: React.Dispatch<React.SetStateAction<MeetingState>>,
  setControlsState: React.Dispatch<React.SetStateAction<ControlsState>>,
  setAvatarState: React.Dispatch<React.SetStateAction<AvatarState>>,
  isConnected: boolean,
  currentQuestion: any,
  onComplete: (results: any[]) => void,
  toast: any
) => {
  const handleMuteToggle = useCallback(() => {
    setControlsState((prev) => {
      const newMuted = !prev.isMuted;
      toast({
        title: newMuted ? '🔇 Microphone muted' : '🎤 Microphone unmuted',
        status: newMuted ? 'warning' : 'success',
        duration: TOAST_DURATION,
        isClosable: true,
        position: 'top',
      });
      return { ...prev, isMuted: newMuted };
    });
  }, [setControlsState, toast]);

  const handleVideoToggle = useCallback(() => {
    setControlsState((prev) => ({
      ...prev,
      isVideoOff: !prev.isVideoOff,
    }));
  }, [setControlsState]);

  const handleChatToggle = useCallback(() => {
    setControlsState((prev) => ({
      ...prev,
      showChat: !prev.showChat,
    }));
  }, [setControlsState]);

  const handleShowInfo = useCallback(
    (show: boolean) => {
      setControlsState((prev) => ({
        ...prev,
        showInfo: show,
      }));
    },
    [setControlsState]
  );

  const handleEndCall = useCallback(() => {
    setMeetingState((prev) => ({
      ...prev,
      isInterviewActive: false,
    }));
    setControlsState((prev) => ({
      ...prev,
      isRecording: false,
    }));
    onComplete(meetingState.allFeedbacks);
  }, [
    setMeetingState,
    setControlsState,
    onComplete,
    meetingState.allFeedbacks,
  ]);

  const handleAnswerQuestion = useCallback(async () => {
    if (!currentQuestion) return;

    setControlsState((prev) => ({ ...prev, isProcessingAnswer: true }));
    setAvatarState((prev) => ({ ...prev, avatarSpeaking: true }));

    try {
      // Send answer to backend via interviewService
      if (isConnected) {
        interviewService.sendAnswerSubmission(
          currentQuestion.id,
          currentQuestion.text
        );
      }

      // Wait for AI processing response from socket
      // The processing will be handled by socket message handlers
    } catch (error) {
      console.error('Error processing answer:', error);
      toast({
        title: '⚠️ Processing Error',
        description: 'Please try again or contact support',
        status: 'error',
        duration: 4000,
        isClosable: true,
        position: 'top',
      });
      setControlsState((prev) => ({ ...prev, isProcessingAnswer: false }));
      setAvatarState((prev) => ({ ...prev, avatarSpeaking: false }));
    }
  }, [currentQuestion, isConnected, setControlsState, setAvatarState, toast]);

  // Handler for speech-to-text response
  const handleSpeechResponse = useCallback((text: string) => {
    if (!currentQuestion || !text.trim()) return;

    console.log('Speech response received:', text);
    setControlsState((prev) => ({ ...prev, isProcessingAnswer: true }));
    setAvatarState((prev) => ({ ...prev, avatarSpeaking: true }));

    try {
      // Send speech response to backend via socket
      if (isConnected) {
        interviewSocket.sendResponseAnswer({
          question_id: currentQuestion.id,
          text: text,
          method: 'speech_to_text',
          confidence: 0.8
        });

        // Also send as regular answer submission for backward compatibility
        interviewService.sendAnswerSubmission(
          currentQuestion.id,
          text
        );
      }

      toast({
        title: '🎤 Speech Recognized',
        description: text.length > 50 ? text.substring(0, 50) + '...' : text,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Processing will be handled by socket message handlers
    } catch (error) {
      console.error('Error processing speech response:', error);
      toast({
        title: '⚠️ Speech Processing Error',
        description: 'Please try again or use manual answer',
        status: 'error',
        duration: 4000,
        isClosable: true,
        position: 'top',
      });
      setControlsState((prev) => ({ ...prev, isProcessingAnswer: false }));
      setAvatarState((prev) => ({ ...prev, avatarSpeaking: false }));
    }
  }, [currentQuestion, isConnected, setControlsState, setAvatarState, toast]);

  return {
    handleMuteToggle,
    handleVideoToggle,
    handleChatToggle,
    handleShowInfo,
    handleEndCall,
    handleAnswerQuestion,
    handleSpeechResponse,
  };
};

const GoogleMeetInterview: React.FC<GoogleMeetInterviewProps> = ({
  scenarioId,
  companyInfo,
  agentNotes,
  onComplete,
  onBack,
}) => {
  const toast = useToast();

  // Custom hooks
  const {
    meetingState,
    setMeetingState,
    controlsState,
    setControlsState,
    avatarState,
    setAvatarState,
  } = useInterviewState(scenarioId);

  // Socket connection state
  const [isConnected, setIsConnected] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionId, setSessionId] = useState<string>('');
  
  // WebRTC states
  const [webrtcConnected, setWebrtcConnected] = useState(false);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  
  // Memoized values
  const currentQuestion = useMemo(
    () => meetingState.questions[meetingState.currentQuestionIndex] || null,
    [meetingState.questions, meetingState.currentQuestionIndex]
  );

  const safeAgentNotes = useMemo(
    () => (Array.isArray(agentNotes) ? agentNotes : []),
    [agentNotes]
  );

  // Timer ref
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const initRef = useRef(false);

  // Handlers
  const handlers = useInterviewHandlers(
    meetingState,
    setMeetingState,
    setControlsState,
    setAvatarState,
    isConnected,
    currentQuestion,
    onComplete,
    toast
  );

  // Socket message handlers setup
  useEffect(() => {
    // Set up message handlers
    interviewService.onInterviewStart((data) => {
      setMeetingState((prev) => ({
        ...prev,
        isInterviewActive: true,
      }));
      setControlsState((prev) => ({
        ...prev,
        isRecording: true,
      }));
    });

    interviewService.onQuestionReady((data) => {
      if (data.message) {
        setMeetingState((prev) => ({
          ...prev,
          questions: [...prev.questions, data.message],
        }));
        setIsInitialized(true);
      }
    });

    interviewService.onInterviewComplete((data) => {
      console.log('Interview complete:', data);
      if (data.results) {
        setMeetingState((prev) => ({
          ...prev,
          allFeedbacks: data.results || [],
        }));
        handlers.handleEndCall();
      }
    });

    // Video analysis handlers
    interviewService.onVideoAnalysisComplete((data) => {
      console.log('Video analysis complete:', data);
      setControlsState((prev) => ({ ...prev, isProcessingAnswer: false }));
      setAvatarState((prev) => ({ ...prev, avatarSpeaking: false }));
      
      // Show analysis results
      if (data.video_analysis) {
        toast({
          title: '🎯 Phân tích video hoàn tất',
          description: `Điểm số: ${data.video_analysis.scores?.overall_impression || 'N/A'}/10`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
    });

    interviewService.onVideoRecordingStarted((data) => {
      console.log('Video recording started:', data);
      toast({
        title: '📹 Bắt đầu ghi hình',
        description: 'Video đang được ghi lại cho phân tích AI',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    });

    interviewService.onVideoRecordingEnded((data) => {
      console.log('Video recording ended:', data);
      toast({
        title: '✅ Ghi hình hoàn tất',
        description: `Đã ghi ${data.frame_count || 0} frames trong ${data.duration_seconds || 0}s`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    });

    // WebRTC handlers
    interviewService.onWebRTCRemoteStream((stream) => {
      setRemoteStream(stream);
      toast({
        title: '📹 Kết nối video thành công',
        description: 'Đã kết nối video call với AI',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    });

    interviewService.onWebRTCConnectionState((state) => {
      setWebrtcConnected(state === 'connected');
      if (state === 'connected') {
        toast({
          title: '🔗 WebRTC kết nối thành công',
          description: 'Video call đã sẵn sàng',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    });

    // Set up connection status handlers
    const unsubscribeStatus = interviewService.onStatus((connected) => {
      setIsConnected(connected);
      // set loading screen to false
      setIsLoading(false);
      if (connected) {
        toast({
          title: 'Kết nối thành công!',
          description: 'Hệ thống AI đã sẵn sàng cho cuộc phỏng vấn',
          status: 'success',
          duration: TOAST_DURATION,
          isClosable: true,
        });
      }
    });

    const unsubscribeError = interviewService.onError((error) => {
      setLastError(error);

      // Enhanced error handling with recovery options
      const isConnectionError = error.toLowerCase().includes('connection') ||
                               error.toLowerCase().includes('websocket') ||
                               error.toLowerCase().includes('webrtc');

      if (isConnectionError) {
        toast({
          title: '⚠️ Connection Lost',
          description: `${error}. Attempting to reconnect...`,
          status: 'warning',
          duration: 8000,
          isClosable: true,
        });

        // Attempt automatic reconnection after a brief delay
        setTimeout(() => {
          if (interviewService.getConnectionStatus() !== 'connected') {
            toast({
              title: '🔄 Reconnection Failed',
              description: 'Please refresh the page to continue your interview.',
              status: 'error',
              duration: null, // Don't auto-dismiss
              isClosable: true,
            });
          }
        }, 5000);
      } else {
        toast({
          title: '⚠️ Interview Error',
          description: error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }

      setControlsState((prev) => ({ ...prev, isProcessingAnswer: false }));
      setAvatarState((prev) => ({ ...prev, avatarSpeaking: false }));
    });

    return () => {
      unsubscribeStatus();
      unsubscribeError();
    };
  }, [
    toast,
    setMeetingState,
    setControlsState,
    setAvatarState,
    handlers,
    meetingState.currentQuestionIndex,
    meetingState.questions.length,
  ]);

  useEffect(() => {
    if (!scenarioId || initRef.current) return;

    const initializeInterview = async () => {
      try {
        initRef.current = true;

        const connected = await interviewService.connect(scenarioId);
        if (!connected) return;

        // Generate session ID
        const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        setSessionId(newSessionId);

        interviewService.sendInterviewStart(
          scenarioId,
          companyInfo,
          safeAgentNotes
        );

        // Initialize WebRTC after socket connection
        console.log('Initializing WebRTC with session ID:', newSessionId);
        const webrtcInitialized = await interviewService.initializeWebRTC(newSessionId, true);
        if (webrtcInitialized) {
          console.log('WebRTC initialized successfully');
          // Create offer for video call
          const offer = await interviewService.createWebRTCOffer();
          if (offer) {
            console.log('WebRTC offer created and sent:', offer);
          } else {
            console.error('Failed to create WebRTC offer');
          }
        } else {
          console.warn('WebRTC initialization failed');
        }
      } catch (error) {
        console.error('Failed to initialize interview:', error);
        toast({
          title: 'Lỗi khởi tạo',
          description: 'Không thể kết nối với hệ thống',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    };

    initializeInterview();
  }, [scenarioId, toast, companyInfo, safeAgentNotes]);

  useEffect(() => {
    if (currentQuestion && isConnected) {
      const timer = setTimeout(() => {
        interviewService.requestSpeech(currentQuestion);
      }, QUESTION_SPEAK_DELAY);

      return () => clearTimeout(timer);
    }
  }, [currentQuestion, isConnected]);

  // Error handling
  useEffect(() => {
    if (lastError) {
      toast({
        title: '⚠️ Lỗi kết nối',
        description: lastError,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [lastError, toast]);

  // Meeting timer
  useEffect(() => {
    if (meetingState.isInterviewActive) {
      timerRef.current = setInterval(() => {
        setMeetingState((prev) => ({
          ...prev,
          meetingDuration: prev.meetingDuration + 1,
        }));
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [meetingState.isInterviewActive, setMeetingState]);

  // Video data handler
  const handleVideoData = useCallback(
    (type: string, data: any) => {
      if (isConnected) {
        interviewService.sendVideoData(type, {
          question_id: currentQuestion?.id,
          session_id: sessionId,
          ...data,
        });
      }
    },
    [isConnected, currentQuestion?.id, sessionId]
  );

  if (!isInitialized || !currentQuestion) {
    return <LoadingScreen companyInfo={companyInfo} isLoading={isLoading} />;
  }

  return (
    <Background>
      <Box position="relative" w="100vw" h="100vh" overflow="hidden">
        <TopHeaderBar
          companyInfo={companyInfo}
          meetingDuration={meetingState.meetingDuration}
          isRecording={controlsState.isRecording}
          onShowInfo={() => handlers.handleShowInfo(true)}
        />

        <SelfVideoView
          isVideoOff={controlsState.isVideoOff}
          isMuted={controlsState.isMuted}
          onVideoToggle={handlers.handleVideoToggle}
          currentQuestionId={currentQuestion?.id || ''}
          onVideoData={handleVideoData}
          isRecording={controlsState.isRecording}
          isProcessing={controlsState.isProcessingAnswer}
          sessionId={sessionId}
        />

        <QuestionDisplay
          currentQuestion={currentQuestion}
          currentQuestionIndex={meetingState.currentQuestionIndex}
          totalQuestions={meetingState.questions.length}
          isProcessingAnswer={controlsState.isProcessingAnswer}
          onAnswerQuestion={handlers.handleAnswerQuestion}
          onSpeechResponse={handlers.handleSpeechResponse}
        />

        <ControlBar
          isMuted={controlsState.isMuted}
          isVideoOff={controlsState.isVideoOff}
          showChat={controlsState.showChat}
          onMuteToggle={handlers.handleMuteToggle}
          onVideoToggle={handlers.handleVideoToggle}
          onChatToggle={handlers.handleChatToggle}
          onEndCall={handlers.handleEndCall}
        />

        {controlsState.showInfo && (
          <MeetingInfoModal
            companyInfo={companyInfo}
            agentNotes={safeAgentNotes}
            onClose={() => handlers.handleShowInfo(false)}
            isOpen={controlsState.showInfo}
            currentQuestionIndex={meetingState.currentQuestionIndex}
            totalQuestions={meetingState.questions.length}
          />
        )}
      </Box>
    </Background>
  );
};

export default GoogleMeetInterview;
