import React from 'react';
import { Box } from '@chakra-ui/react';
import { motion } from 'framer-motion';

interface BackgroundProps {
  children?: React.ReactNode;
}

export const Background: React.FC<BackgroundProps> = ({ children }) => {
  return (
    <Box 
      h="100vh" 
      w="100vw" 
      bg="linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)" 
      position="relative" 
      overflow="hidden"
    >
      {/* Enhanced Desktop Background Elements */}
      <Box position="absolute" top={0} left={0} right={0} bottom={0} zIndex={0}>
        {/* Primary gradient overlay */}
        <motion.div
          animate={{ 
            background: [
              "radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 60%)",
              "radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 60%)",
              "radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.15) 0%, transparent 60%)"
            ]
          }}
          transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
          style={{ width: "100%", height: "100%" }}
        />
        
        {/* Secondary floating elements */}
        <motion.div
          animate={{ 
            x: [0, 100, 0],
            y: [0, -50, 0],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "easeInOut" }}
          style={{
            position: "absolute",
            top: "10%",
            left: "10%",
            width: "300px",
            height: "300px",
            background: "radial-gradient(circle, rgba(34, 197, 94, 0.1) 0%, transparent 70%)",
            borderRadius: "50%"
          }}
        />
        
        <motion.div
          animate={{ 
            x: [0, -80, 0],
            y: [0, 60, 0],
            opacity: [0.1, 0.25, 0.1]
          }}
          transition={{ duration: 25, repeat: Infinity, ease: "easeInOut" }}
          style={{
            position: "absolute",
            bottom: "20%",
            right: "15%",
            width: "400px",
            height: "400px",
            background: "radial-gradient(circle, rgba(168, 85, 247, 0.1) 0%, transparent 70%)",
            borderRadius: "50%"
          }}
        />
      </Box>
      
      {/* Desktop Grid Pattern */}
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        opacity={0.03}
        backgroundImage="linear-gradient(rgba(255,255,255,.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,.1) 1px, transparent 1px)"
        backgroundSize="50px 50px"
      />
      
      {/* Content */}
      <Box position="relative" zIndex={1}>
        {children}
      </Box>
    </Box>
  );
}; 