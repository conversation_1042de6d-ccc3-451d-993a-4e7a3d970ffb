import React from 'react';
import {
  <PERSON><PERSON>,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Box,
  Text,
  Progress
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { HiS<PERSON><PERSON>, HiLightningBolt } from 'react-icons/hi';
import { CompanyInfo } from './types';

interface MeetingInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  companyInfo: CompanyInfo;
  currentQuestionIndex: number;
  totalQuestions: number;
  agentNotes: string[];
}

export const MeetingInfoModal: React.FC<MeetingInfoModalProps> = ({
  isOpen,
  onClose,
  companyInfo,
  currentQuestionIndex,
  totalQuestions,
  agentNotes
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay bg="blackAlpha.800" backdropFilter="blur(10px)" />
      <ModalContent 
        bg="rgba(0, 0, 0, 0.8)" 
        backdropFilter="blur(30px)"
        color="white" 
        borderRadius="3xl"
        border="1px solid rgba(255, 255, 255, 0.2)"
        boxShadow="0 25px 50px rgba(0, 0, 0, 0.5)"
      >
        <ModalHeader fontSize="2xl" fontWeight="bold">
          <HStack spacing={3}>
            <HiSparkles />
            <Text>Interview Analytics</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton size="lg" />
        <ModalBody pb={8}>
          <VStack spacing={8} align="stretch">
            <Box>
              <Text fontSize="xl" fontWeight="bold" mb={3}>
                {companyInfo.name}
              </Text>
              <Text fontSize="lg" color="blue.300" mb={2}>
                {companyInfo.position}
              </Text>
              <Text fontSize="md" color="whiteAlpha.700">
                {/* {companyInfo.industry} • {companyInfo.size} */}
              </Text>
            </Box>
            
            <Box>
              <Text fontSize="lg" fontWeight="semibold" mb={4}>
                Progress Analytics
              </Text>
              <Box bg="rgba(255, 255, 255, 0.1)" p={6} borderRadius="2xl">
                <Progress 
                  value={(currentQuestionIndex + 1) / totalQuestions * 100}
                  size="xl"
                  borderRadius="full"
                  bg="rgba(255, 255, 255, 0.1)"
                  sx={{
                    '& > div': {
                      background: 'linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899)',
                      borderRadius: 'full',
                      boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)'
                    }
                  }}
                />
                <HStack justify="space-between" mt={4}>
                  <Text fontSize="sm" color="whiteAlpha.700">
                    Question {currentQuestionIndex + 1} of {totalQuestions}
                  </Text>
                  <Text fontSize="sm" fontWeight="bold" color="blue.300">
                    {Math.round(((currentQuestionIndex + 1) / totalQuestions) * 100)}% Complete
                  </Text>
                </HStack>
              </Box>
            </Box>
            
            {agentNotes.length > 0 && (
              <Box>
                <Text fontSize="lg" fontWeight="semibold" mb={4}>
                  <HStack spacing={2}>
                    <HiLightningBolt />
                    <Text>Interview Tips</Text>
                  </HStack>
                </Text>
                <VStack spacing={3} align="stretch">
                  {agentNotes.map((note, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Box 
                        bg="rgba(59, 130, 246, 0.1)" 
                        p={4} 
                        borderRadius="xl"
                        border="1px solid rgba(59, 130, 246, 0.2)"
                      >
                        <Text fontSize="sm" color="blue.200">
                          💡 {note}
                        </Text>
                      </Box>
                    </motion.div>
                  ))}
                </VStack>
              </Box>
            )}
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}; 