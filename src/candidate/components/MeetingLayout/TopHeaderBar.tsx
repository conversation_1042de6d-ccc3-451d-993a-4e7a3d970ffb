import React from 'react';
import { 
  Box, 
  Flex, 
  HStack, 
  Text, 
  IconButton, 
  Badge,
  Tooltip,
  Avatar,
  AvatarGroup
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaInfoCircle, FaWifi, FaShieldAlt, FaUsers } from 'react-icons/fa';
import { HiLightningBolt } from 'react-icons/hi';
import { CompanyInfo } from './types';

interface TopHeaderBarProps {
  companyInfo: CompanyInfo;
  meetingDuration: number;
  isRecording: boolean;
  onShowInfo: () => void;
}

export const TopHeaderBar: React.FC<TopHeaderBarProps> = ({
  companyInfo,
  meetingDuration,
  isRecording,
  onShowInfo
}) => {
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <motion.div
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2, type: "spring", stiffness: 200 }}
    >
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        zIndex={30}
        bg="rgba(15, 23, 42, 0.8)"
        backdropFilter="blur(25px) saturate(180%)"
        borderBottom="1px solid rgba(255, 255, 255, 0.1)"
        px={8}
        py={4}
        boxShadow="0 8px 30px rgba(0, 0, 0, 0.12)"
      >
        <Flex justify="space-between" align="center">
          <HStack spacing={8}>
            {/* Window Controls */}
            <HStack spacing={3}>
              <motion.div whileHover={{ scale: 1.2 }}>
                <Box w={3} h={3} bg="red.400" borderRadius="full" cursor="pointer" />
              </motion.div>
              <motion.div whileHover={{ scale: 1.2 }}>
                <Box w={3} h={3} bg="yellow.400" borderRadius="full" cursor="pointer" />
              </motion.div>
              <motion.div whileHover={{ scale: 1.2 }}>
                <Box w={3} h={3} bg="green.400" borderRadius="full" cursor="pointer" />
              </motion.div>
            </HStack>
            
            {/* Meeting Title */}
            <HStack spacing={4}>
              <Text color="white" fontSize="xl" fontWeight="700" letterSpacing="wide">
                Mensetsu-kun
              </Text>
              <Text color="whiteAlpha.600">•</Text>
              <Text color="white" fontSize="lg" fontWeight="600" noOfLines={1}>
                {companyInfo.name}
              </Text>
              <Badge 
                bg="linear-gradient(45deg, #3b82f6, #8b5cf6)"
                color="white"
                px={3}
                py={1}
                borderRadius="full"
                fontSize="sm"
                fontWeight="bold"
              >
                {companyInfo.position}
              </Badge>
            </HStack>
            
            {/* Live Indicator */}
            <motion.div
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Badge 
                bg="linear-gradient(45deg, #ef4444, #dc2626)"
                color="white"
                size="lg"
                px={4}
                py={2}
                borderRadius="full"
                fontSize="sm"
                fontWeight="bold"
                boxShadow="0 6px 20px rgba(239, 68, 68, 0.4)"
              >
                {isRecording && (
                  <motion.span
                    animate={{ opacity: [1, 0.3, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    style={{ marginRight: '8px' }}
                  >
                    ●
                  </motion.span>
                )}
                LIVE INTERVIEW
              </Badge>
            </motion.div>
          </HStack>
          
          <HStack spacing={6}>
            {/* Network Quality */}
            <Tooltip label="Connection: Excellent" hasArrow placement="bottom">
              <HStack spacing={2} 
                bg="rgba(34, 197, 94, 0.2)"
                px={3} py={2} borderRadius="full"
                border="1px solid rgba(34, 197, 94, 0.3)"
              >
                <FaWifi color="#22c55e" size={16} />
                <Text color="green.300" fontSize="sm" fontWeight="medium">HD</Text>
              </HStack>
            </Tooltip>
            
            {/* Participants */}
            <Tooltip label="2 participants" hasArrow placement="bottom">
              <HStack spacing={2}>
                <AvatarGroup size="sm" max={3}>
                  <Avatar bg="blue.500" icon={<HiLightningBolt />} />
                  <Avatar name="You" bg="green.500" />
                </AvatarGroup>
                <FaUsers color="white" size={16} />
              </HStack>
            </Tooltip>
            
            {/* Security */}
            <Tooltip label="End-to-end encrypted" hasArrow placement="bottom">
              <Box color="green.400">
                <FaShieldAlt size={18} />
              </Box>
            </Tooltip>
            
            {/* Meeting Timer */}
            <motion.div
              animate={{ scale: [1, 1.02, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <Text 
                color="white" 
                fontSize="xl" 
                fontFamily="mono" 
                fontWeight="bold"
                bg="rgba(255, 255, 255, 0.1)"
                px={6}
                py={3}
                borderRadius="full"
                backdropFilter="blur(10px)"
                border="1px solid rgba(255, 255, 255, 0.2)"
                minW="100px"
                textAlign="center"
              >
                {formatTime(meetingDuration)}
              </Text>
            </motion.div>
            
            {/* Info Button */}
            <Tooltip label="Meeting details" hasArrow placement="bottom">
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <IconButton
                  aria-label="Meeting info"
                  icon={<FaInfoCircle />}
                  variant="ghost"
                  color="white"
                  size="lg"
                  borderRadius="full"
                  _hover={{ 
                    bg: "rgba(255, 255, 255, 0.15)",
                    transform: "scale(1.05)"
                  }}
                  transition="all 0.2s ease"
                  onClick={onShowInfo}
                />
              </motion.div>
            </Tooltip>
          </HStack>
        </Flex>
      </Box>
    </motion.div>
  );
}; 