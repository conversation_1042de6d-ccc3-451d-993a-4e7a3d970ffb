/**
 * 共通ページレイアウトコンポーネント
 * 全ページで使用される統一レイアウト構造を提供
 */
import React from 'react';
import { Box, Container, VStack, useColorModeValue } from '@chakra-ui/react';
import HeaderNavigation, { NavigationItem } from './HeaderNavigation';

interface User {
  profile: {
    name: string;
  };
}

interface PageLayoutProps {
  user?: User;
  breadcrumbs?: NavigationItem[];
  showBackButton?: boolean;
  backButtonLabel?: string;
  onBackClick?: () => void;
  maxWidth?: 'container.md' | 'container.xl' | 'container.lg';
  containerPy?: number | object;
  containerPx?: number | object;
  spacing?: number;
  children: React.ReactNode;
  showHeader?: boolean;
}

const PageLayout: React.FC<PageLayoutProps> = ({
  user,
  breadcrumbs = [],
  showBackButton = false,
  backButtonLabel = "戻る",
  onBackClick,
  maxWidth = 'container.xl',
  containerPy = { base: 4, md: 8 },
  containerPx = { base: 4, md: 6 },
  spacing = 8,
  children,
  showHeader = true,
}) => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');

  return (
    <Box minH="100vh" bg={bgColor}>
      {showHeader && (
        <HeaderNavigation
          user={user}
          breadcrumbs={breadcrumbs}
          showBackButton={showBackButton}
          backButtonLabel={backButtonLabel}
          onBackClick={onBackClick}
        />
      )}
      
      <Container maxW={maxWidth} py={containerPy} px={containerPx}>
        <VStack spacing={spacing} id="main-content" tabIndex={-1}>
          {children}
        </VStack>
      </Container>
    </Box>
  );
};

export default PageLayout;