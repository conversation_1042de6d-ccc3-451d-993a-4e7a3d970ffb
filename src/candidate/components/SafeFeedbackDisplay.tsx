/**
 * 心理的安全性を考慮したフィードバック表示コンポーネント
 * 段階的に情報を開示し、ユーザーの感情的負担を軽減
 */
import { useState } from "react";
import { Box, VStack, Text, Button, Progress, HStack, Icon } from "@chakra-ui/react";
import { motion, AnimatePresence } from "framer-motion";
import { fadeIn, staggerContainer, staggerItem } from "../motion";
import { BasicFeedback as Feedback } from "../data/mockApiData";
import { CheckCircleIcon, StarIcon } from "@chakra-ui/icons";
import { spacing, textStyles } from '@mensetsu-kun/shared/components/CommonStyles';

interface SafeFeedbackProps {
  feedback: Feedback | null;
  onComplete?: () => void;
}

type FeedbackStage = "positive" | "growth" | "full";

export const SafeFeedbackDisplay: React.FC<SafeFeedbackProps> = ({ 
  feedback, 
  onComplete 
}) => {
  const [stage, setStage] = useState<FeedbackStage>("positive");

  if (!feedback) {
    return null;
  }

  // フィードバックを段階的に表示するためのデータ整形
  const positivePoints = feedback.suggestions
    .filter((_, index) => index < 2)
    .map(suggestion => suggestion.replace("より", ""));

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return "support.500";
    if (rating >= 3) return "primary.500";
    return "caution.500";
  };

  const getRatingText = (rating: number) => {
    if (rating >= 4) return "素晴らしいパフォーマンス！";
    if (rating >= 3) return "良い回答でした";
    return "成長の余地があります";
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        style={{ width: "100%" }}
      >
        <VStack spacing={spacing.sectionSpacing} align="stretch">
          {/* Stage 1: ポジティブハイライト */}
          <motion.div variants={staggerItem}>
            <Box
              p={spacing.cardPadding}
              bg="support.50"
              borderRadius="lg"
              borderWidth={2}
              borderColor="support.200"
            >
              <HStack mb={spacing.cardPadding.base}>
                <Icon as={StarIcon} color="support.600" boxSize={spacing.iconBoxSize} />
                <Text 
                  {...textStyles.body} 
                  fontWeight="bold" 
                  color="support.700"
                >
                  素晴らしかった点
                </Text>
              </HStack>
              <Text 
                color="neutral.700" 
                lineHeight="tall"
                {...textStyles.body}
              >
                {feedback.aiAnalysis}
              </Text>
              
              {/* スコア表示（ポジティブに見せる） */}
              <VStack mt={4} spacing={3} align="stretch">
                <Box>
                  <Text {...textStyles.caption} color="neutral.600" mb={1}>
                    感情表現
                  </Text>
                  <Progress 
                    value={feedback.emotionScore ? feedback.emotionScore * 10 : 0} 
                    colorScheme="green" 
                    size={{ base: "sm", md: "md" }}
                    borderRadius="full"
                  />
                </Box>
                <Box>
                  <Text {...textStyles.caption} color="neutral.600" mb={1}>
                    自信度
                  </Text>
                  <Progress 
                    value={feedback.confidenceScore ? feedback.confidenceScore * 10 : 0} 
                    colorScheme="green" 
                    size={{ base: "sm", md: "md" }}
                    borderRadius="full"
                  />
                </Box>
              </VStack>
            </Box>
          </motion.div>

          {/* Stage 2: 成長の機会 */}
          {(stage === "growth" || stage === "full") && (
            <motion.div
              variants={fadeIn}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <Box
                p={spacing.cardPadding}
                bg="primary.50"
                borderRadius="lg"
                borderWidth={2}
                borderColor="primary.200"
              >
                <HStack mb={spacing.cardPadding.base}>
                  <Icon as={CheckCircleIcon} color="primary.600" boxSize={spacing.iconBoxSize} />
                  <Text 
                    {...textStyles.body} 
                    fontWeight="bold" 
                    color="primary.700"
                  >
                    さらに良くなるポイント
                  </Text>
                </HStack>
                <VStack align="start" spacing={2}>
                  {feedback.suggestions.map((suggestion, index) => (
                    <HStack key={index} align="start" spacing={2}>
                      <Text color="primary.600" mt={1} {...textStyles.body}>•</Text>
                      <Text 
                        color="neutral.700" 
                        {...textStyles.body}
                        flex={1}
                      >
                        {suggestion}
                      </Text>
                    </HStack>
                  ))}
                </VStack>
              </Box>
            </motion.div>
          )}

          {/* Stage 3: 詳細分析 */}
          {stage === "full" && (
            <motion.div
              variants={fadeIn}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              <Box
                p={6}
                bg="neutral.50"
                borderRadius="lg"
                borderWidth={1}
                borderColor="neutral.200"
              >
                <Text fontSize="lg" fontWeight="bold" color="neutral.700" mb={3}>
                  総合評価
                </Text>
                <HStack justify="space-between" align="center">
                  <HStack>
                    <Box
                      px={4}
                      py={2}
                      bg={getRatingColor(feedback.overallRating)}
                      color="white"
                      borderRadius="full"
                      fontWeight="bold"
                      fontSize="xl"
                    >
                      {feedback.overallRating}/5
                    </Box>
                    <Text color={getRatingColor(feedback.overallRating)} fontWeight="medium">
                      {getRatingText(feedback.overallRating)}
                    </Text>
                  </HStack>
                </HStack>
                
                {/* 詳細スコア */}
                <VStack align="stretch" mt={4} spacing={2}>
                  <HStack justify="space-between">
                    <Text fontSize="sm" color="neutral.600">関連性</Text>
                    <Text fontSize="sm" fontWeight="medium">
                      {feedback.relevanceScore ? `${Math.round(feedback.relevanceScore * 10)}/10` : "N/A"}
                    </Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text fontSize="sm" color="neutral.600">論理性</Text>
                    <Text fontSize="sm" fontWeight="medium">
                      {feedback.confidenceScore ? `${Math.round(feedback.confidenceScore * 10)}/10` : "N/A"}
                    </Text>
                  </HStack>
                </VStack>
              </Box>
            </motion.div>
          )}

          {/* ステージコントロールボタン */}
          <VStack spacing={3} w="full" maxW="300px" mx="auto">
            {stage === "positive" && (
              <motion.div 
                whileHover={{ scale: 1.02 }} 
                whileTap={{ scale: 0.98 }}
                style={{ width: "100%" }}
              >
                <Button
                  onClick={() => setStage("growth")}
                  colorScheme="primary"
                  size={textStyles.body.fontSize}
                  borderRadius="full"
                  px={spacing.buttonPadding.px}
                  py={spacing.cardPadding}
                  w="full"
                  {...textStyles.body}
                  minH="50px"
                >
                  成長のヒントを見る
                </Button>
              </motion.div>
            )}
            {stage === "growth" && (
              <VStack spacing={3} w="full">
                <Button
                  onClick={() => setStage("full")}
                  colorScheme="primary"
                  size={textStyles.body.fontSize}
                  w="full"
                  py={spacing.cardPadding}
                  {...textStyles.body}
                  minH="50px"
                >
                  詳細な分析を見る
                </Button>
                <Button
                  onClick={() => setStage("positive")}
                  variant="ghost"
                  size={{ base: "sm", md: "md" }}
                  {...textStyles.caption}
                >
                  戻る
                </Button>
              </VStack>
            )}
            {stage === "full" && onComplete && (
              <motion.div 
                whileHover={{ scale: 1.02 }} 
                whileTap={{ scale: 0.98 }}
                style={{ width: "100%" }}
              >
                <Button
                  onClick={onComplete}
                  colorScheme="support"
                  size={textStyles.body.fontSize}
                  borderRadius="full"
                  px={spacing.buttonPadding.px}
                  py={spacing.cardPadding}
                  w="full"
                  {...textStyles.body}
                  minH="50px"
                >
                  次へ進む
                </Button>
              </motion.div>
            )}
          </VStack>
        </VStack>
      </motion.div>
    </AnimatePresence>
  );
};