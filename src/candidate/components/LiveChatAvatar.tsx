import React, { useEffect, useRef } from 'react';
import { useToast } from '@chakra-ui/react';
import config from '../config';
import logger from '../utils/logger';
import type { AvatarEvent } from '../types/azure-speech';

const LiveChatAvatar: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const toast = useToast();

  useEffect(() => {
    const { SpeechSDK } = window;
    const key = config.azure.speechKey;
    const region = config.azure.speechRegion;

    if (!SpeechSDK || !SpeechSDK.ChatAvatarConnection) {
      toast({
        title: '拡張読み込みエラー',
        description: 'Avatar 拡張が読み込まれていません。<script>タグを確認してください。',
        status: 'error',
      });
      return;
    }

    // 1) SpeechConfig
    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
    speechConfig.speechSynthesisVoiceName = config.azure.voice.defaultVoice;

    // 2) AvatarConfig
    const videoFormat = new SpeechSDK.AvatarVideoFormat();
    const avatarConfig = new SpeechSDK.AvatarConfig(
      config.azure.avatar.defaultAvatarId,
      'casual',           // style
      videoFormat
    );
    avatarConfig.backgroundColor = config.azure.avatar.backgroundColor;

    // 3) ChatAvatarConnection の生成
    const avatar = new SpeechSDK.ChatAvatarConnection(speechConfig, avatarConfig);

    // 4) ビデオストリームを受け取る
    avatar.avatarEventReceived = (evt: AvatarEvent) => {
      logger.debug('Avatar event received', { type: evt.type, timestamp: evt.timestamp });
      
      if (evt.type === 'videoFrameReceived' && evt.data?.mediaStream && videoRef.current) {
        videoRef.current.srcObject = evt.data.mediaStream;
      }
    };

    // 5) 接続開始
    avatar.start()
      .then(() => logger.info('Avatar connection started'))
      .catch((err: Error) => {
        logger.error('Avatar connection failed', err);
        toast({
          title: 'Avatar 接続エラー',
          description: err.message || String(err),
          status: 'error',
        });
      });

    return () => {
      avatar.close();
    };
  }, [toast]);

  return (
    <video
      ref={videoRef}
      autoPlay
      playsInline
      style={{ width: '100%', height: '100%', background: '#000' }}
    />
  );
};

export default LiveChatAvatar; 