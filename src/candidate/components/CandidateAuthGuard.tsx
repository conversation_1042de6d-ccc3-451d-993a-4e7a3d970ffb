/**
 * 求職者認証ガードコンポーネント
 * 求職者のログイン状態を確認し、未認証の場合はリダイレクト
 */
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Box, Spinner, Text, VStack, useColorModeValue } from '@chakra-ui/react';

interface User {
  profile: {
    name: string;
  };
  role: string;
}

interface CandidateAuthGuardProps {
  children: (user: User) => React.ReactNode;
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
}

const CandidateAuthGuard: React.FC<CandidateAuthGuardProps> = ({
  children,
  redirectTo = '/demo-login',
  loadingComponent,
}) => {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const userData = localStorage.getItem('demo_user');
      
      if (userData) {
        const parsedUser = JSON.parse(userData);
        
        // 求職者ロールをチェック
        if (parsedUser.role !== 'candidate') {
          router.push(redirectTo);
          return;
        }
        
        setUser(parsedUser);
      } else {
        router.push(redirectTo);
        return;
      }
    } catch (error) {
      console.error('認証データの解析エラー:', error);
      router.push(redirectTo);
      return;
    }
    
    setIsLoading(false);
  }, [router, redirectTo]);

  // ローディング状態
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <Box 
        minH="100vh" 
        display="flex" 
        alignItems="center" 
        justifyContent="center"
        bg={useColorModeValue('gray.50', 'gray.900')}
      >
        <VStack spacing={4}>
          <Spinner size="lg" color="primary.500" />
          <Text color={useColorModeValue('gray.600', 'gray.400')}>
            読み込み中...
          </Text>
        </VStack>
      </Box>
    );
  }

  // 未認証の場合は何も表示しない（リダイレクト中）
  if (!user) {
    return null;
  }

  // 認証済みの場合は子コンポーネントにユーザー情報を渡す
  return <>{children(user)}</>;
};

export default CandidateAuthGuard;