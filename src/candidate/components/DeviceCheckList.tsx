/**
 * デバイス権限チェックリストコンポーネント
 * カメラ、マイクなどのデバイス権限確認とチェックリスト機能を提供
 */
import React, { useEffect } from 'react';
import {
  VStack,
  Box,
  Checkbox,
  Text,
  Badge,
  Alert,
  AlertIcon,
  AlertDescription,
  useColorModeValue,
} from '@chakra-ui/react';

export interface ChecklistItem {
  id: string;
  label: string;
  description: string;
  isChecked: boolean;
  isRequired: boolean;
  autoCheck?: boolean; // 自動チェック対象かどうか
}

interface DeviceCheckListProps {
  items: ChecklistItem[];
  onItemToggle: (id: string) => void;
  autoCheckPermissions?: boolean;
  showRequiredAlert?: boolean;
}

const DeviceCheckList: React.FC<DeviceCheckListProps> = ({
  items,
  onItemToggle,
  autoCheckPermissions = true,
  showRequiredAlert = true,
}) => {
  // デバイスのパーミッションチェック
  useEffect(() => {
    if (!autoCheckPermissions) return;

    const checkPermissions = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        
        // カメラとマイクの権限が取得できた場合、対応する項目を自動チェック
        items.forEach(item => {
          if (item.autoCheck && (item.id === 'camera' || item.id === 'microphone')) {
            if (!item.isChecked) {
              onItemToggle(item.id);
            }
          }
        });
        
        // ストリームを停止
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.log('デバイスアクセスエラー:', error);
      }
    };
    
    checkPermissions();
  }, [autoCheckPermissions, items, onItemToggle]);

  const requiredItems = items.filter(item => item.isRequired);
  const allRequiredChecked = requiredItems.every(item => item.isChecked);

  return (
    <VStack spacing={4} w="full" align="stretch">
      {items.map(item => (
        <Box
          key={item.id}
          p={4}
          borderWidth={1}
          borderRadius="md"
          borderColor={item.isChecked ? 'green.300' : useColorModeValue('gray.200', 'gray.600')}
          bg={item.isChecked ? 'green.50' : useColorModeValue('white', 'gray.800')}
          w="full"
          transition="all 0.3s"
        >
          <Checkbox
            isChecked={item.isChecked}
            onChange={() => onItemToggle(item.id)}
            colorScheme="green"
            size="lg"
          >
            <VStack align="start" spacing={1} ml={2}>
              <Box>
                <Text fontWeight="medium" display="inline">
                  {item.label}
                </Text>
                {item.isRequired && (
                  <Badge colorScheme="red" fontSize="xs" ml={2}>
                    必須
                  </Badge>
                )}
              </Box>
              <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                {item.description}
              </Text>
            </VStack>
          </Checkbox>
        </Box>
      ))}
      
      {showRequiredAlert && !allRequiredChecked && requiredItems.length > 0 && (
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            必須項目をすべてチェックすると次に進めます
          </AlertDescription>
        </Alert>
      )}
    </VStack>
  );
};

export default DeviceCheckList;