/**
 * 最終フィードバック表示画面
 * 全ての質問への回答をまとめて表示
 */
import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Progress,
  Card,
  CardBody,
  CardHeader,
  Badge,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  List,
  ListItem,
  ListIcon,
  Divider,
  Icon,
  Heading,
  useColorModeValue,
} from '@chakra-ui/react';
import { CheckCircleIcon, StarIcon, InfoIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';
import { spacing, textStyles, scoreColors, scoreSizes, commonStyles, ScoreBadge } from '@mensetsu-kun/shared/components/CommonStyles';

interface FeedbackData {
  questionId: string;
  questionText: string;
  candidateAnswer: string;
  aiAnalysis: string;
  confidenceScore?: number;
  clarityScore?: number;
  overall: number;
  suggestions: string[];
}

interface FinalFeedbackViewProps {
  feedbacks: FeedbackData[];
  onClose: () => void;
}

const MotionCard = motion.create(Card);

export const FinalFeedbackView: React.FC<FinalFeedbackViewProps> = ({ 
  feedbacks, 
  onClose 
}) => {
  const [activeTab, setActiveTab] = useState(0);

  // 総合スコアの計算
  const overallScore = feedbacks.length > 0 
    ? Math.round(feedbacks.reduce((sum, f) => sum + f.overall, 0) / feedbacks.length)
    : 0;

  const averageConfidence = feedbacks.length > 0
    ? Math.round(feedbacks.reduce((sum, f) => sum + (f.confidenceScore || 0), 0) / feedbacks.length)
    : 0;

  const averageClarity = feedbacks.length > 0
    ? Math.round(feedbacks.reduce((sum, f) => sum + (f.clarityScore || 0), 0) / feedbacks.length)
    : 0;

  // 成長提案をまとめる
  const allSuggestions = feedbacks.flatMap(f => f.suggestions);
  const uniqueSuggestions = [...new Set(allSuggestions)];

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'green';
    if (score >= 6) return 'yellow';
    return 'orange';
  };

  const getEncouragementMessage = (score: number) => {
    if (score >= 8) {
      return '素晴らしい面接でした！自信を持って本番に臨んでください。';
    } else if (score >= 6) {
      return '良い面接でした！いくつかのポイントを意識するとさらに良くなります。';
    } else {
      return '練習を重ねることで必ず上達します。一歩ずつ成長していきましょう！';
    }
  };

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')} py={spacing.sectionSpacing}>
      <VStack spacing={spacing.sectionSpacing} maxW={{ base: "full", md: "container.md", lg: "container.lg" }} mx="auto" px={spacing.cardPadding}>
        {/* ヘッダー */}
        <VStack spacing={spacing.cardPadding.base} textAlign="center">
          <Heading {...textStyles.heading} color={useColorModeValue('primary.700', 'primary.300')}>
            面接フィードバック
          </Heading>
          <Text color={useColorModeValue('gray.600', 'gray.300')} {...textStyles.body}>
            AIが分析したあなたの面接パフォーマンス
          </Text>
        </VStack>

        {/* 総合スコア */}
        <MotionCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          w="full"
        >
          <CardBody>
            <VStack spacing={spacing.sectionSpacing}>
              <HStack spacing={spacing.sectionSpacing}>
                <Icon as={StarIcon} color="yellow.400" boxSize={spacing.iconBoxSize} />
                <VStack align="start" spacing={0}>
                  <Text {...textStyles.heading} fontWeight="bold" color={useColorModeValue('primary.700', 'primary.300')}>
                    総合スコア
                  </Text>
                  <Text fontSize="xl" fontWeight="bold" color={`${getScoreColor(overallScore)}.500`}>
                    {overallScore}/10
                  </Text>
                </VStack>
              </HStack>
              
              <Box w="full" p={spacing.cardPadding} bg={useColorModeValue('primary.50', 'gray.800')} borderRadius="md">
                <Text {...textStyles.body} color={useColorModeValue('primary.700', 'primary.300')} textAlign="center">
                  {getEncouragementMessage(overallScore)}
                </Text>
              </Box>
            </VStack>
          </CardBody>
        </MotionCard>

        {/* 詳細スコア */}
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={spacing.sectionSpacing} w="full">
          <Stat bg={useColorModeValue('white', 'gray.800')} p={spacing.cardPadding} borderRadius="lg" boxShadow="sm">
            <StatLabel>総合評価</StatLabel>
            <StatNumber color={`${getScoreColor(overallScore)}.500`}>
              {overallScore}/10
            </StatNumber>
            <StatHelpText>
              全体的なパフォーマンス
            </StatHelpText>
          </Stat>

          <Stat bg={useColorModeValue('white', 'gray.800')} p={spacing.cardPadding} borderRadius="lg" boxShadow="sm">
            <StatLabel>自信度</StatLabel>
            <StatNumber color={`${getScoreColor(averageConfidence)}.500`}>
              {averageConfidence}/10
            </StatNumber>
            <StatHelpText>
              話し方の自信
            </StatHelpText>
          </Stat>

          <Stat bg={useColorModeValue('white', 'gray.800')} p={spacing.cardPadding} borderRadius="lg" boxShadow="sm">
            <StatLabel>明瞭性</StatLabel>
            <StatNumber color={`${getScoreColor(averageClarity)}.500`}>
              {averageClarity}/10
            </StatNumber>
            <StatHelpText>
              発話の明瞭さ
            </StatHelpText>
          </Stat>
        </SimpleGrid>

        {/* 詳細タブ */}
        <Card w="full">
          <CardBody>
            <Tabs index={activeTab} onChange={setActiveTab}>
              <TabList>
                <Tab>質問別分析</Tab>
                <Tab>成長提案</Tab>
                <Tab>次のステップ</Tab>
              </TabList>
              
              <TabPanels>
                {/* 質問別分析 */}
                <TabPanel px={0}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    {feedbacks.map((feedback, index) => (
                      <Card key={feedback.questionId} variant="outline">
                        <CardHeader>
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Text fontWeight="bold" fontSize="md">
                                質問 {index + 1}
                              </Text>
                              <Text fontSize="sm" color="gray.600" noOfLines={2}>
                                {feedback.questionText}
                              </Text>
                            </VStack>
                            <Badge 
                              colorScheme={getScoreColor(feedback.overall)}
                              fontSize="md"
                              px={3}
                              py={1}
                            >
                              {feedback.overall}/10
                            </Badge>
                          </HStack>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start" spacing={spacing.cardPadding.base}>
                            <Box>
                              <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
                                AIの分析
                              </Text>
                              <Text fontSize="sm" color="gray.600">
                                {feedback.aiAnalysis}
                              </Text>
                            </Box>
                            
                            <Divider />
                            
                            <HStack spacing={6} w="full">
                              <VStack spacing={1}>
                                <Text fontSize="xs" color="gray.500">自信度</Text>
                                <Text fontWeight="bold" color={`${getScoreColor(feedback.confidenceScore || 0)}.500`}>
                                  {feedback.confidenceScore}/10
                                </Text>
                              </VStack>
                              <VStack spacing={1}>
                                <Text fontSize="xs" color="gray.500">明瞭性</Text>
                                <Text fontWeight="bold" color={`${getScoreColor(feedback.clarityScore || 0)}.500`}>
                                  {feedback.clarityScore}/10
                                </Text>
                              </VStack>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    ))}
                  </VStack>
                </TabPanel>

                {/* 成長提案 */}
                <TabPanel px={0}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <Box p={4} bg="blue.50" borderRadius="md">
                      <HStack spacing={2} mb={3}>
                        <Icon as={InfoIcon} color="blue.500" />
                        <Text fontWeight="bold" color="blue.700">
                          成長のための提案
                        </Text>
                      </HStack>
                      <List spacing={spacing.cardPadding.base}>
                        {uniqueSuggestions.slice(0, 5).map((suggestion, index) => (
                          <ListItem key={index}>
                            <ListIcon as={CheckCircleIcon} color="blue.500" />
                            <Text fontSize="sm" color="blue.600">
                              {suggestion}
                            </Text>
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                    
                    <Box p={4} bg="green.50" borderRadius="md">
                      <HStack spacing={2} mb={3}>
                        <Icon as={InfoIcon} color="green.500" />
                        <Text fontWeight="bold" color="green.700">
                          あなたの強み
                        </Text>
                      </HStack>
                      <List spacing={2}>
                        <ListItem>
                          <ListIcon as={CheckCircleIcon} color="green.500" />
                          <Text fontSize="sm" color="green.600">
                            面接に真摯に取り組む姿勢が素晴らしいです
                          </Text>
                        </ListItem>
                        <ListItem>
                          <ListIcon as={CheckCircleIcon} color="green.500" />
                          <Text fontSize="sm" color="green.600">
                            練習を通じて着実に成長していることが分かります
                          </Text>
                        </ListItem>
                      </List>
                    </Box>
                  </VStack>
                </TabPanel>

                {/* 次のステップ */}
                <TabPanel px={0}>
                  <VStack spacing={spacing.sectionSpacing} align="stretch">
                    <Text fontWeight="bold" color="primary.700">
                      おすすめの次のアクション
                    </Text>
                    
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing.sectionSpacing}>
                      <Card>
                        <CardBody>
                          <VStack align="start" spacing={spacing.cardPadding.base}>
                            <Text fontWeight="bold" color="primary.600">
                              📚 追加練習
                            </Text>
                            <Text fontSize="sm" color="gray.600">
                              特に改善が必要な分野に焦点を当てて、もう一度練習してみましょう。
                            </Text>
                            <Button size="sm" colorScheme="primary" variant="outline">
                              練習を続ける
                            </Button>
                          </VStack>
                        </CardBody>
                      </Card>
                      
                      <Card>
                        <CardBody>
                          <VStack align="start" spacing={spacing.cardPadding.base}>
                            <Text fontWeight="bold" color="green.600">
                              ✨ 本番準備
                            </Text>
                            <Text fontSize="sm" color="gray.600">
                              このレベルなら本番でも十分通用します。自信を持って臨んでください。
                            </Text>
                            <Button size="sm" colorScheme="green" variant="outline">
                              面接対策Tips
                            </Button>
                          </VStack>
                        </CardBody>
                      </Card>
                    </SimpleGrid>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </CardBody>
        </Card>

        {/* アクションボタン */}
        <HStack spacing={spacing.sectionSpacing}>
          <Button
            colorScheme="primary"
            onClick={onClose}
            size="lg"
          >
            ダッシュボードに戻る
          </Button>
          <Button
            variant="outline"
            onClick={() => window.print()}
            size="lg"
          >
            レポートを印刷
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};