import React, { useState, useRef } from 'react';
import { <PERSON>, Button, Text, VStack, HStack, Badge } from '@chakra-ui/react';

interface WebSpeechTestProps {
  onResult?: (text: string) => void;
}

export const WebSpeechTest: React.FC<WebSpeechTestProps> = ({ onResult }) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);
  
  const recognitionRef = useRef<any>(null);

  const checkSupport = () => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    return !!SpeechRecognition;
  };

  const startListening = () => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      setError('Web Speech API not supported in this browser');
      setIsSupported(false);
      return;
    }

    const recognition = new SpeechRecognition();
    recognitionRef.current = recognition;

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'vi-VN';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      console.log('Web Speech recognition started');
      setIsListening(true);
      setError(null);
    };

    recognition.onend = () => {
      console.log('Web Speech recognition ended');
      setIsListening(false);
    };

    recognition.onresult = (event: any) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        console.log('Final transcript:', finalTranscript);
        setTranscript(prev => prev + finalTranscript);
        onResult?.(finalTranscript);
      }

      setInterimTranscript(interimTranscript);
    };

    recognition.onerror = (event: any) => {
      console.error('Web Speech recognition error:', event.error);
      setError(`Error: ${event.error} - ${event.message || 'Unknown error'}`);
      setIsListening(false);
    };

    try {
      recognition.start();
    } catch (err) {
      console.error('Failed to start recognition:', err);
      setError(`Failed to start: ${err}`);
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  };

  const clearTranscript = () => {
    setTranscript('');
    setInterimTranscript('');
    setError(null);
  };

  React.useEffect(() => {
    setIsSupported(checkSupport());
  }, []);

  if (!isSupported) {
    return (
      <Box p={4} bg="red.50" borderRadius="md" borderColor="red.200" borderWidth={1}>
        <Text color="red.700" fontWeight="bold">
          Web Speech API Not Supported
        </Text>
        <Text color="red.600" fontSize="sm" mt={2}>
          Your browser doesn't support the Web Speech API. 
          Please try Chrome, Edge, or Safari.
        </Text>
      </Box>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      <Box p={4} bg="gray.50" borderRadius="md">
        <HStack justify="space-between" mb={3}>
          <Text fontWeight="bold">Web Speech API Test</Text>
          <Badge colorScheme={isListening ? 'green' : 'gray'}>
            {isListening ? 'Listening' : 'Stopped'}
          </Badge>
        </HStack>

        <HStack spacing={3}>
          <Button
            colorScheme="green"
            onClick={startListening}
            disabled={isListening}
            size="sm"
          >
            Start Listening
          </Button>
          
          <Button
            colorScheme="red"
            onClick={stopListening}
            disabled={!isListening}
            size="sm"
          >
            Stop Listening
          </Button>
          
          <Button
            variant="outline"
            onClick={clearTranscript}
            size="sm"
          >
            Clear
          </Button>
        </HStack>
      </Box>

      {/* Interim Results */}
      {interimTranscript && (
        <Box p={3} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.400">
          <Text fontSize="sm" color="blue.600" fontWeight="bold" mb={1}>
            Recognizing (interim):
          </Text>
          <Text color="blue.800" fontStyle="italic">
            {interimTranscript}
          </Text>
        </Box>
      )}

      {/* Final Results */}
      {transcript && (
        <Box p={3} bg="green.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="green.400">
          <Text fontSize="sm" color="green.600" fontWeight="bold" mb={1}>
            Final transcript:
          </Text>
          <Text color="green.800">
            {transcript}
          </Text>
        </Box>
      )}

      {/* Error Display */}
      {error && (
        <Box p={3} bg="red.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="red.400">
          <Text fontSize="sm" color="red.600" fontWeight="bold" mb={1}>
            Error:
          </Text>
          <Text color="red.800" fontSize="sm">
            {error}
          </Text>
        </Box>
      )}

      {/* Instructions */}
      <Box p={3} bg="yellow.50" borderRadius="md">
        <Text fontSize="sm" color="yellow.700">
          <strong>Instructions:</strong>
          <br />
          1. Click "Start Listening" and allow microphone access
          <br />
          2. Speak clearly in Vietnamese
          <br />
          3. Interim results will show in blue, final results in green
          <br />
          4. Click "Stop Listening" when done
        </Text>
      </Box>
    </VStack>
  );
};
