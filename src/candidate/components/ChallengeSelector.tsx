/**
 * チャレンジ目標選択コンポーネント
 * フロー理論に基づくエンゲージメント設計
 */
import { useState } from "react";
import { Box, VStack, Text, HStack, Badge, SimpleGrid, Icon, useToast, VisuallyHidden, Button } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { cardHover } from "../motion";
import { StarIcon, CheckIcon, LockIcon } from "@chakra-ui/icons";

export interface Challenge {
  id: string;
  title: string;
  description: string;
  difficulty: "easy" | "medium" | "hard";
  icon: string;
  points: number;
  isUnlocked: boolean;
  requiredPoints?: number;
}

interface ChallengeSelectorProps {
  onSelect: (challenge: Challenge) => void;
  userPoints?: number;
  completedChallenges?: string[];
}

export const ChallengeSelector: React.FC<ChallengeSelectorProps> = ({ 
  onSelect, 
  userPoints = 0,
  completedChallenges = []
}) => {
  const [selectedChallenge, setSelectedChallenge] = useState<string | null>(null);
  const toast = useToast();

  const challenges: Challenge[] = [
    {
      id: "clear_speaker",
      title: "クリアスピーカー",
      description: "フィラーワード（えー、あのー）を5回未満に",
      difficulty: "easy",
      icon: "🎯",
      points: 10,
      isUnlocked: true,
    },
    {
      id: "time_keeper",
      title: "タイムキーパー",
      description: "回答を1分以内にまとめる",
      difficulty: "easy",
      icon: "⏱️",
      points: 10,
      isUnlocked: true,
    },
    {
      id: "star_method",
      title: "ストーリーテラー",
      description: "STARメソッドを使って構造的に回答",
      difficulty: "medium",
      icon: "⭐",
      points: 20,
      isUnlocked: userPoints >= 10,
      requiredPoints: 10,
    },
    {
      id: "emotion_master",
      title: "感情表現マスター",
      description: "適切な感情表現とトーンで話す",
      difficulty: "medium",
      icon: "😊",
      points: 20,
      isUnlocked: userPoints >= 10,
      requiredPoints: 10,
    },
    {
      id: "eye_contact",
      title: "アイコンタクトマスター",
      description: "カメラを見て話す時間を70%以上に",
      difficulty: "hard",
      icon: "👁️",
      points: 30,
      isUnlocked: userPoints >= 30,
      requiredPoints: 30,
    },
    {
      id: "complete_pro",
      title: "コンプリートプロ",
      description: "すべての評価項目で80点以上",
      difficulty: "hard",
      icon: "🏆",
      points: 50,
      isUnlocked: userPoints >= 50,
      requiredPoints: 50,
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy": return "green";
      case "medium": return "yellow";
      case "hard": return "red";
      default: return "gray";
    }
  };

  const handleChallengeSelect = (challenge: Challenge) => {
    if (!challenge.isUnlocked) {
      toast({
        title: "チャレンジがロックされています",
        description: `このチャレンジを解除するには${challenge.requiredPoints}ポイントが必要です`,
        status: "warning",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (completedChallenges.includes(challenge.id)) {
      toast({
        title: "既にクリア済みです",
        description: "他のチャレンジに挑戦してみましょう！",
        status: "info",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setSelectedChallenge(challenge.id);
    onSelect(challenge);
  };

  return (
    <VStack spacing={6} w="full" maxW="600px" mx="auto">
      {/* ヘッダー */}
      <VStack spacing={2} textAlign="center">
        <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color="primary.700">
          今回のチャレンジを選ぼう
        </Text>
        <HStack>
          <Text fontSize={{ base: "sm", md: "md" }} color="neutral.600">
            現在のポイント:
          </Text>
          <Badge colorScheme="primary" fontSize={{ base: "sm", md: "md" }} px={3} py={1}>
            {userPoints} pts
          </Badge>
        </HStack>
      </VStack>

      {/* チャレンジグリッド */}
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
        {challenges.map((challenge) => {
          const isCompleted = completedChallenges.includes(challenge.id);
          const isSelected = selectedChallenge === challenge.id;

          return (
            <motion.div
              key={challenge.id}
              whileHover={challenge.isUnlocked && !isCompleted ? { scale: 1.02 } : {}}
              whileTap={challenge.isUnlocked && !isCompleted ? { scale: 0.98 } : {}}
              style={{ width: "100%" }}
            >
              <Button
                as={Box}
                p={4}
                borderWidth={2}
                borderRadius="lg"
                cursor={challenge.isUnlocked && !isCompleted ? "pointer" : "not-allowed"}
                onClick={() => handleChallengeSelect(challenge)}
                borderColor={
                  isSelected ? "primary.500" : 
                  isCompleted ? "support.500" :
                  challenge.isUnlocked ? "neutral.200" : "neutral.100"
                }
                bg={
                  isCompleted ? "support.50" :
                  challenge.isUnlocked ? "white" : "neutral.50"
                }
                opacity={challenge.isUnlocked || isCompleted ? 1 : 0.6}
                position="relative"
                transition="all 0.3s"
                _hover={challenge.isUnlocked && !isCompleted ? {
                  borderColor: "primary.300",
                  boxShadow: "md"
                } : {}}
                _focus={{
                  borderColor: "primary.500",
                  boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.1)",
                  outline: "none"
                }}
                disabled={!challenge.isUnlocked || isCompleted}
                aria-label={`チャレンジ: ${challenge.title}. ${challenge.description}. ${challenge.isUnlocked ? isCompleted ? '完了済み' : '選択可能' : 'ロック中'}`}
                role="button"
                tabIndex={challenge.isUnlocked && !isCompleted ? 0 : -1}
                height="auto"
                variant="unstyled"
                display="block"
                w="full"
              >
                {/* 完了マーク */}
                {isCompleted && (
                  <Box
                    position="absolute"
                    top={2}
                    right={2}
                    bg="support.500"
                    borderRadius="full"
                    p={1}
                  >
                    <Icon as={CheckIcon} color="white" boxSize={3} />
                  </Box>
                )}

                {/* ロックアイコン */}
                {!challenge.isUnlocked && !isCompleted && (
                  <Box
                    position="absolute"
                    top={2}
                    right={2}
                  >
                    <Icon as={LockIcon} color="neutral.400" boxSize={4} />
                  </Box>
                )}

                <VStack align="start" spacing={3}>
                  <HStack justify="space-between" w="full">
                    <HStack>
                      <Text fontSize={{ base: "2xl", md: "3xl" }}>{challenge.icon}</Text>
                      <VStack align="start" spacing={0}>
                        <Text 
                          fontWeight="bold" 
                          fontSize={{ base: "sm", md: "md" }}
                          color={isCompleted ? "support.700" : "neutral.800"}
                        >
                          {challenge.title}
                        </Text>
                        <Badge 
                          colorScheme={getDifficultyColor(challenge.difficulty)}
                          fontSize="xs"
                        >
                          {challenge.difficulty}
                        </Badge>
                      </VStack>
                    </HStack>
                    <Badge colorScheme="primary" fontSize="sm">
                      {challenge.points}pt
                    </Badge>
                  </HStack>
                  
                  <Text 
                    fontSize={{ base: "xs", md: "sm" }} 
                    color="neutral.600"
                    noOfLines={2}
                  >
                    {challenge.description}
                  </Text>

                  {!challenge.isUnlocked && challenge.requiredPoints && (
                    <Text fontSize="xs" color="neutral.500">
                      🔒 {challenge.requiredPoints}ポイントで解除
                    </Text>
                  )}
                </VStack>
                
                {/* スクリーンリーダー用の詳細情報 */}
                <VisuallyHidden>
                  {challenge.isUnlocked ? 
                    (isCompleted ? 
                      `このチャレンジは既にクリア済みです。${challenge.points}ポイント獲得済み。` :
                      `このチャレンジを選択できます。クリアすると${challenge.points}ポイント獲得できます。`
                    ) : 
                    `このチャレンジはロックされています。${challenge.requiredPoints}ポイントで解除されます。`
                  }
                </VisuallyHidden>
              </Button>
            </motion.div>
          );
        })}
      </SimpleGrid>

      {/* ヒント */}
      <Box 
        w="full" 
        p={4} 
        bg="primary.50" 
        borderRadius="md"
        borderWidth={1}
        borderColor="primary.100"
      >
        <HStack spacing={2}>
          <Text fontSize="lg">💡</Text>
          <Text fontSize={{ base: "xs", md: "sm" }} color="primary.700">
            チャレンジをクリアしてポイントを獲得し、新しいチャレンジを解除しよう！
          </Text>
        </HStack>
      </Box>
    </VStack>
  );
};