/**
 * エラー境界コンポーネント
 * React コンポーネントツリー内のエラーをキャッチし、フォールバックUIを表示
 */

import React, { Component, ReactNode, ErrorInfo } from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  Button,
  Container,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
  Collapse,
  useDisclosure,
} from '@chakra-ui/react';
import logger from '../utils/logger';
import config from '../config';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// エラー詳細表示コンポーネント
const ErrorDetails: React.FC<{ error: Error; errorInfo: ErrorInfo }> = ({ error, errorInfo }) => {
  const { isOpen, onToggle } = useDisclosure();

  // 開発環境のみ詳細表示
  if (config.isProduction) {
    return null;
  }

  return (
    <Box mt={4}>
      <Button size="sm" onClick={onToggle} variant="ghost">
        {isOpen ? 'エラー詳細を隠す' : 'エラー詳細を表示'}
      </Button>
      <Collapse in={isOpen} animateOpacity>
        <Box mt={4} p={4} bg="gray.50" borderRadius="md">
          <VStack align="stretch" spacing={3}>
            <Box>
              <Text fontWeight="bold" fontSize="sm">エラーメッセージ:</Text>
              <Code colorScheme="red" p={2} display="block">
                {error.toString()}
              </Code>
            </Box>
            <Box>
              <Text fontWeight="bold" fontSize="sm">スタックトレース:</Text>
              <Code fontSize="xs" p={2} display="block" whiteSpace="pre-wrap">
                {error.stack}
              </Code>
            </Box>
            <Box>
              <Text fontWeight="bold" fontSize="sm">コンポーネントスタック:</Text>
              <Code fontSize="xs" p={2} display="block" whiteSpace="pre-wrap">
                {errorInfo.componentStack}
              </Code>
            </Box>
          </VStack>
        </Box>
      </Collapse>
    </Box>
  );
};

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // エラーが発生したら状態を更新
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // エラー情報をログに記録
    logger.error('Error caught by ErrorBoundary', {
      error: error.toString(),
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // 状態を更新
    this.setState({
      errorInfo,
    });

    // カスタムエラーハンドラーを呼び出し
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // カスタムフォールバックUIが提供されている場合
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      // デフォルトのエラーUI
      return (
        <Container maxW="container.md" py={10}>
          <Alert
            status="error"
            variant="subtle"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            textAlign="center"
            height="auto"
            borderRadius="lg"
            p={8}
          >
            <AlertIcon boxSize="40px" mr={0} />
            <AlertTitle mt={4} mb={1} fontSize="lg">
              申し訳ございません
            </AlertTitle>
            <AlertDescription maxWidth="sm">
              予期しないエラーが発生しました。
              お手数ですが、ページを再読み込みしてください。
            </AlertDescription>
            
            <VStack spacing={3} mt={6}>
              <Button
                colorScheme="blue"
                onClick={this.handleReload}
              >
                ページを再読み込み
              </Button>
              
              {/* リセットボタン（開発環境のみ） */}
              {!config.isProduction && (
                <Button
                  variant="ghost"
                  onClick={this.handleReset}
                  size="sm"
                >
                  エラーをリセット
                </Button>
              )}
            </VStack>

            {/* エラー詳細（開発環境のみ） */}
            {this.state.error && this.state.errorInfo && (
              <ErrorDetails
                error={this.state.error}
                errorInfo={this.state.errorInfo}
              />
            )}
          </Alert>

          {/* サポート情報 */}
          <Box mt={8} textAlign="center">
            <Text fontSize="sm" color="gray.600">
              問題が解決しない場合は、
              <Text as="span" color="blue.600" fontWeight="medium">
                サポートチーム
              </Text>
              までお問い合わせください。
            </Text>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

// 関数コンポーネント用のラッパー
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};