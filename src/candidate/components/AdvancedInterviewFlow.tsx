/**
 * 実践的面接フロー - 高度な面接練習体験
 * 業界別・シナリオ別・プレッシャー対応の面接練習
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Progress,
  useToast,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  Badge,
  Card,
  CardBody,
  CardHeader,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Divider,
  Heading,
  List,
  ListItem,
  ListIcon,
  Icon,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { AdaptiveAvatar, UserState } from './AdaptiveAvatar';
import AutoAnswerInput from './AutoAnswerInput';
import { CheckCircleIcon, WarningIcon, TimeIcon, StarIcon } from '@chakra-ui/icons';
import { fadeIn } from '../motion';
import DataService, { QuestionData, ScenarioConfig } from '../lib/unified-data';

interface AdvancedFeedback {
  id: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  communicationSkill: number;
  problemSolvingAbility: number;
  culturalFit: number;
  stressHandling: number;
  technicalAccuracy?: number;
  leadershipPotential?: number;
  strengths: string[];
  improvements: string[];
  nextSteps: string[];
  industryBenchmark?: number;
  levelComparison?: "junior" | "mid" | "senior" | "expert";
}

interface AdvancedInterviewFlowProps {
  scenarioId: string;
  onComplete: (results: AdvancedFeedback[]) => void;
  companyInfo?: {
    name: string;
    industry: string;
    position: string;
    description: string;
    interviewStyle: string;
    culture: string;
  };
  agentNotes?: string;
  onBack?: () => void;
}

const AdvancedInterviewFlow: React.FC<AdvancedInterviewFlowProps> = ({
  scenarioId,
  onComplete,
  companyInfo,
  agentNotes,
  onBack
}) => {
  const [scenario, setScenario] = useState<ScenarioConfig | null>(null);
  const [questions, setQuestions] = useState<QuestionData[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [allFeedbacks, setAllFeedbacks] = useState<AdvancedFeedback[]>([]);
  const [isInterviewComplete, setIsInterviewComplete] = useState(false);
  const [userState, setUserState] = useState<UserState>({
    confidence: 0.6,
    isPaused: false,
    speakingSpeed: "normal",
    emotionalState: "calm",
  });
  
  // 実践練習特有の状態
  const [pressureLevel, setPressureLevel] = useState(1);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [interruptionMode, setInterruptionMode] = useState(false);
  const [currentHints, setCurrentHints] = useState<string[]>([]);
  
  const toast = useToast();
  const { isOpen: isHintOpen, onOpen: onHintOpen, onClose: onHintClose } = useDisclosure();
  const { isOpen: isInfoOpen, onOpen: onInfoOpen, onClose: onInfoClose } = useDisclosure();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // カラーモード値を事前に取得
  const primaryColor700 = useColorModeValue('primary.700', 'primary.300');
  const grayColor600 = useColorModeValue('gray.600', 'gray.300');
  const whiteColor = useColorModeValue('white', 'gray.800');
  const grayBg50 = useColorModeValue('gray.50', 'gray.700');
  const bgDark = useColorModeValue('gray.900', 'black');
  const modalDescColor = useColorModeValue('gray.600', 'gray.400');

  // シナリオ初期化
  useEffect(() => {
    // 既に初期化済みの場合はスキップ
    if (scenario) return;
    
    const loadedScenario = DataService.getScenarioById(scenarioId);
    const scenarioQuestions = DataService.getQuestionsByScenario(scenarioId);
    
    if (loadedScenario && scenarioQuestions.length > 0) {
      setScenario(loadedScenario);
      setQuestions(scenarioQuestions);
      setCurrentQuestion(scenarioQuestions[0]);
      setPressureLevel(loadedScenario.scenarioSettings.pressureLevel);
      
      if (loadedScenario.scenarioSettings.timeConstraints) {
        setTimeRemaining(scenarioQuestions[0]?.estimatedTime || 180);
      }
      
      // トーストが既に表示されているか確認
      if (!toast.isActive('interview-start')) {
        toast({
          id: 'interview-start',
          title: `実践練習開始: ${loadedScenario.name}`,
          description: "より高度な面接体験をお楽しみください",
          status: 'info',
          duration: 4000,
          isClosable: true,
        });
      }
    }
  }, [scenarioId]);

  // タイマー機能
  useEffect(() => {
    if (timeRemaining !== null && timeRemaining > 0 && !isLoading) {
      timerRef.current = setTimeout(() => {
        setTimeRemaining(prev => prev !== null ? prev - 1 : null);
      }, 1000);
    } else if (timeRemaining === 0) {
      handleTimeUp();
    }
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeRemaining, isLoading]);

  const handleTimeUp = () => {
    if (!toast.isActive('time-up')) {
      toast({
        id: 'time-up',
        title: "時間切れ",
        description: "回答時間が終了しました。次の質問に進みます。",
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    }
    goToNextQuestion();
  };

  const goToNextQuestion = () => {
    if (!scenario || questions.length === 0) return;
    
    if (currentQuestionIndex < questions.length - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);
      setCurrentQuestion(questions[nextIndex]);
      
      // タイマーリセット
      if (scenario.scenarioSettings.timeConstraints) {
        setTimeRemaining(questions[nextIndex].estimatedTime);
      }
      
      // プレッシャーレベル調整
      adjustPressureLevel(nextIndex);
      
      console.log(`次の質問に進みました (${nextIndex + 1}/${questions.length})`);
    } else {
      setIsInterviewComplete(true);
      if (!toast.isActive('interview-complete')) {
        toast({
          id: 'interview-complete',
          title: '実践練習完了',
          description: '高度な分析結果をご確認ください',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  const adjustPressureLevel = (questionIndex: number) => {
    if (!scenario || questions.length === 0) return;
    
    const basePressure = scenario.scenarioSettings.pressureLevel;
    const progressRatio = questionIndex / questions.length;
    
    // 面接の進行とともにプレッシャーを調整
    const newPressureLevel = Math.min(5, basePressure + Math.floor(progressRatio * 2));
    setPressureLevel(newPressureLevel);
    
    // ユーザー状態の更新
    setUserState(prev => ({
      ...prev,
      emotionalState: newPressureLevel > 3 ? "nervous" : "calm",
      confidence: Math.max(0.3, prev.confidence - (newPressureLevel * 0.1))
    }));
  };

  const handleSkipQuestion = () => {
    if (!scenario) return;
    
    // 直接次の質問に進む（重複toast防止）
    goToNextQuestion();
  };

  const handlePauseInterview = () => {
    setUserState(prev => ({ ...prev, isPaused: true }));
    
    if (!toast.isActive('interview-pause')) {
      toast({
        id: 'interview-pause',
        title: '面接を一時停止しました',
        description: '再開ボタンで面接を続けられます',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleResumeInterview = () => {
    setUserState(prev => ({ ...prev, isPaused: false }));
    
    if (!toast.isActive('interview-resume')) {
      toast({
        id: 'interview-resume',
        title: '面接を再開しました',
        description: '面接を続けてください',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleAnswerSubmit = async (audioBlob: Blob) => {
    if (!scenario || !currentQuestion) return;
    
    try {
      setIsLoading(true);
      
      // タイマー停止
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      
      console.log('🎯 高度な音声解析を開始します');
      console.log('📊 現在のallFeedbacks数:', allFeedbacks.length);
      console.log('📋 現在の質問:', currentQuestion);
      
      // 高度な分析を実行
      const mockAnswer = "実践的な回答のシミュレーション。より詳細で構造化された分析を行います。";
      const advancedFeedback = await generateAdvancedFeedback(
        currentQuestion,
        mockAnswer,
        scenario
      );
      
      console.log('📝 生成されたフィードバック:', advancedFeedback);
      
      setAllFeedbacks(prev => {
        const newFeedbacks = [...prev, advancedFeedback];
        console.log('💾 フィードバック更新後:', newFeedbacks.length, '件');
        return newFeedbacks;
      });
      
      // 成果に基づくユーザー状態更新
      const overallScore = advancedFeedback.communicationSkill;
      setUserState(prev => ({
        ...prev,
        confidence: Math.min(1, prev.confidence + (overallScore > 7 ? 0.1 : -0.05)),
        emotionalState: overallScore > 8 ? "confident" : overallScore > 6 ? "calm" : "nervous"
      }));
      
      // 遅延後に次の質問へ
      setTimeout(() => {
        goToNextQuestion();
      }, 1500);
      
    } catch (error) {
      console.error('高度な分析エラー:', error);
      if (!toast.isActive('analysis-error')) {
        toast({
          id: 'analysis-error',
          title: 'エラー',
          description: '分析に失敗しました。再試行してください。',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const showHints = () => {
    if (currentQuestion?.hints) {
      setCurrentHints(currentQuestion.hints);
      onHintOpen();
    }
  };


  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPressureIndicatorColor = () => {
    switch (pressureLevel) {
      case 1: case 2: return 'green';
      case 3: case 4: return 'yellow';
      case 5: return 'red';
      default: return 'gray';
    }
  };

  if (!scenario) {
    return <Box>シナリオを読み込んでいます...</Box>;
  }

  if (isInterviewComplete) {
    return (
      <Box minH="100vh" w="full" px={{ base: 4, md: 6, lg: 8 }} py={{ base: 4, md: 8 }}>
        <VStack 
          spacing={{ base: 4, md: 8 }} 
          maxW={{ base: "full", md: "container.md", lg: "container.lg" }}
          mx="auto"
          id="main-content"
          tabIndex={-1}
        >
          <motion.div
            variants={fadeIn}
            initial="initial"
            animate="animate"
          >
            <VStack spacing={6} textAlign="center">
              <Heading size={{ base: "lg", md: "xl" }} color={primaryColor700}>
                実践練習完了！
              </Heading>
              <Text color={grayColor600} fontSize={{ base: "md", md: "lg" }}>
                {scenario.name}の練習が完了しました。詳細な分析結果をご確認ください。
              </Text>
              
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} w="full">
                <Stat bg={whiteColor} p={4} borderRadius="lg">
                  <StatLabel>回答数</StatLabel>
                  <StatNumber color="primary.600">{allFeedbacks.length}</StatNumber>
                  <StatHelpText>質問への回答</StatHelpText>
                </Stat>
                
                <Stat bg={whiteColor} p={4} borderRadius="lg">
                  <StatLabel>平均スコア</StatLabel>
                  <StatNumber color="green.600">
                    {allFeedbacks.length > 0 
                      ? Math.round(allFeedbacks.reduce((sum, f) => sum + f.communicationSkill, 0) / allFeedbacks.length)
                      : 0}/10
                  </StatNumber>
                  <StatHelpText>コミュニケーション</StatHelpText>
                </Stat>
                
                <Stat bg={whiteColor} p={4} borderRadius="lg">
                  <StatLabel>難易度</StatLabel>
                  <StatNumber color="orange.600">{scenario.difficulty.toUpperCase()}</StatNumber>
                  <StatHelpText>挑戦レベル</StatHelpText>
                </Stat>
              </SimpleGrid>
              
              <Button
                colorScheme="primary"
                size="lg"
                onClick={() => {
                  console.log('🔍 面接完了 - フィードバック数:', allFeedbacks.length);
                  console.log('🔍 全フィードバック:', allFeedbacks);
                  onComplete(allFeedbacks);
                }}
                px={8}
              >
                詳細な分析レポートを見る
              </Button>
            </VStack>
          </motion.div>
        </VStack>
      </Box>
    );
  }

  return (
    <Box 
      minH="100vh" 
      w="full" 
      position="relative"
      overflow="hidden"
      bg={bgDark}
      id="main-content"
      tabIndex={-1}
    >
      {/* 面接情報モーダル */}
      <Modal isOpen={isInfoOpen} onClose={onInfoClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>面接情報</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontSize="lg" fontWeight="bold" mb={2}>
                  {scenario.name}
                </Text>
                <Text fontSize="sm" color={modalDescColor}>
                  {scenario.description}
                </Text>
              </Box>
              
              <HStack justify="space-between">
                <VStack align="start" spacing={2}>
                  <Badge colorScheme={getPressureIndicatorColor()} fontSize="sm">
                    {DataService.getPressureLevelDisplay(pressureLevel)}
                  </Badge>
                  <Badge colorScheme="blue" fontSize="sm">
                    {DataService.getDifficultyLabel(scenario.difficulty)}
                  </Badge>
                </VStack>
                
                <VStack align="end" spacing={2}>
                  <Text fontSize="sm">
                    質問 {currentQuestionIndex + 1} / {questions.length}
                  </Text>
                  {timeRemaining !== null && (
                    <HStack spacing={1}>
                      <Icon as={TimeIcon} color={timeRemaining < 30 ? 'red.500' : 'gray.500'} />
                      <Text color={timeRemaining < 30 ? 'red.500' : 'gray.600'}>
                        残り時間: {formatTime(timeRemaining)}
                      </Text>
                    </HStack>
                  )}
                </VStack>
              </HStack>
              
              <Progress 
                value={(currentQuestionIndex + 1) / questions.length * 100}
                colorScheme="primary"
                size="md"
                borderRadius="full"
                hasStripe
                isAnimated
              />
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 上部固定コントロール */}
      <Box
        position="absolute"
        top={4}
        left={4}
        right={4}
        zIndex={1000}
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Button
          size="sm"
          variant="solid"
          bg="whiteAlpha.200"
          color="white"
          _hover={{ bg: "whiteAlpha.300" }}
          backdropFilter="blur(10px)"
          onClick={onInfoOpen}
        >
          📊 面接情報
        </Button>
        
        {onBack && (
          <Button
            size="sm"
            variant="solid" 
            bg="whiteAlpha.200"
            color="white"
            _hover={{ bg: "whiteAlpha.300" }}
            backdropFilter="blur(10px)"
            onClick={onBack}
          >
            ← 戻る
          </Button>
        )}
      </Box>


      {/* フルスクリーンアバター */}
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            variants={fadeIn}
            initial="initial"
            animate="animate"
            exit="exit"
            style={{ 
              width: "100%", 
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }}
          >
            {/* フルスクリーンアダプティブアバター */}
            <Box 
              w="100vw" 
              h="100vh"
              position="relative"
            >
              <AdaptiveAvatar 
                userState={userState}
                isSpeaking={false}
              />
            </Box>
          </motion.div>
        </AnimatePresence>
      </Box>
      
      {/* 下部固定質問・コントロール */}
      <Box
        position="absolute"
        bottom={0}
        left={0}
        right={0}
        bg="blackAlpha.700"
        backdropFilter="blur(10px)"
        color="white"
        p={6}
        zIndex={1000}
      >
        <VStack spacing={4}>
          {/* 質問表示 */}
          {currentQuestion && (
            <Box w="full" maxW="800px" mx="auto">
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between" w="full">
                  <Badge colorScheme="primary" fontSize="sm" bg="primary.500">
                    {currentQuestion.category}
                  </Badge>
                  <Badge 
                    colorScheme={currentQuestion.difficulty === 'expert' ? 'red' : 
                               currentQuestion.difficulty === 'hard' ? 'orange' : 'yellow'}
                    fontSize="sm"
                  >
                    {DataService.getDifficultyLabel(currentQuestion.difficulty)}
                  </Badge>
                </HStack>
                
                <Text fontSize="lg" fontWeight="medium" lineHeight="tall" color="white">
                  {currentQuestion.text}
                </Text>
                
                {currentQuestion.context && (
                  <Box p={3} bg="whiteAlpha.200" borderRadius="md">
                    <Text fontSize="sm" color="whiteAlpha.900">
                      💡 {currentQuestion.context}
                    </Text>
                  </Box>
                )}
                
                {currentQuestion.hints && (
                  <HStack>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      colorScheme="whiteAlpha"
                      color="white"
                      borderColor="whiteAlpha.400"
                      _hover={{ bg: "whiteAlpha.200" }}
                      onClick={showHints}
                    >
                      💡 ヒントを見る
                    </Button>
                  </HStack>
                )}
              </VStack>
            </Box>
          )}
          
          {/* 答え入力とコントロール */}
          <Box w="full" maxW="800px" mx="auto">
            <VStack spacing={3}>
              <AutoAnswerInput 
                onSubmit={(audioBlob: Blob) => handleAnswerSubmit(audioBlob)}
                isLoading={isLoading} 
              />
              
              <HStack spacing={2} justify="center">
                <Button
                  size="sm"
                  variant="solid"
                  bg="whiteAlpha.200"
                  color="white"
                  _hover={{ bg: "whiteAlpha.300" }}
                  onClick={handlePauseInterview}
                  isDisabled={userState.isPaused}
                >
                  ⏸️ 一時停止
                </Button>
                
                {userState.isPaused && (
                  <Button
                    size="sm"
                    variant="solid"
                    bg="whiteAlpha.200"
                    color="white"
                    _hover={{ bg: "whiteAlpha.300" }}
                    onClick={handleResumeInterview}
                  >
                    ▶️ 再開
                  </Button>
                )}
                
                <Button
                  size="sm"
                  variant="solid"
                  bg="whiteAlpha.200"
                  color="white"
                  _hover={{ bg: "whiteAlpha.300" }}
                  onClick={handleSkipQuestion}
                >
                  ⏭️ スキップ
                </Button>
              </HStack>
            </VStack>
          </Box>
        </VStack>
      </Box>

      {/* ヒント表示モーダル */}
      <Modal isOpen={isHintOpen} onClose={onHintClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>💡 ヒント</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={2} align="start">
              {currentHints.map((hint, index) => (
                <Text key={index} fontSize="sm">
                  • {hint}
                </Text>
              ))}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

// 高度な分析機能
const generateAdvancedFeedback = async (
  question: QuestionData,
  answer: string,
  scenario: ScenarioConfig
): Promise<AdvancedFeedback> => {
  // 実際の実装では、より高度なAI分析を使用
  const analysisResult = await analyzeAdvancedAnswer(question, answer, scenario);
  
  return {
    id: `advanced_feedback_${Date.now()}`,
    questionId: question.id,
    candidateAnswer: answer,
    aiAnalysis: analysisResult.analysis,
    
    communicationSkill: analysisResult.scores.communication,
    problemSolvingAbility: analysisResult.scores.problemSolving,
    culturalFit: analysisResult.scores.culturalFit,
    stressHandling: analysisResult.scores.stressHandling,
    
    technicalAccuracy: question.industry === 'tech' ? analysisResult.scores.technical : undefined,
    leadershipPotential: question.category.includes('リーダー') ? analysisResult.scores.leadership : undefined,
    
    strengths: analysisResult.strengths,
    improvements: analysisResult.improvements,
    nextSteps: analysisResult.nextSteps,
    
    industryBenchmark: analysisResult.benchmark,
    levelComparison: analysisResult.level
  };
};

// モック分析関数（実際の実装では外部APIを使用）
const analyzeAdvancedAnswer = async (
  question: QuestionData,
  answer: string,
  scenario: ScenarioConfig
): Promise<any> => {
  // シミュレーション用の遅延
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  return {
    analysis: "高度な分析による詳細フィードバック。回答の構造化、論理性、業界知識の深さを評価しました。",
    scores: {
      communication: Math.floor(Math.random() * 3) + 7, // 7-10
      problemSolving: Math.floor(Math.random() * 3) + 6, // 6-9
      culturalFit: Math.floor(Math.random() * 2) + 8, // 8-10
      stressHandling: Math.floor(Math.random() * 4) + 6, // 6-10
      technical: question.industry === 'tech' ? Math.floor(Math.random() * 3) + 7 : undefined,
      leadership: Math.floor(Math.random() * 3) + 6
    },
    strengths: [
      "論理的な構造化が優秀",
      "具体例の使用が効果的", 
      "専門知識の深さが印象的"
    ],
    improvements: [
      "より簡潔な表現を心がける",
      "数値的根拠の提示を強化",
      "ステークホルダー視点の追加"
    ],
    nextSteps: [
      "ケーススタディ練習を継続",
      "業界トレンドの調査強化",
      "プレゼンテーション스킬の向上"
    ],
    benchmark: Math.floor(Math.random() * 20) + 75, // 75-95
    level: ["junior", "mid", "senior"][Math.floor(Math.random() * 3)] as "junior" | "mid" | "senior"
  };
};

export default AdvancedInterviewFlow;