/**
 * アクセシビリティ対応のヘルパーコンポーネント
 * WCAG 2.1 AA準拠を支援する機能
 */
import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  VStack,
  Text,
  HStack,
  Icon,
  useColorMode,
  useColorModeValue,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Switch,
  FormControl,
  FormLabel,
  VisuallyHidden,
} from '@chakra-ui/react';
import { SettingsIcon, SunIcon, MoonIcon } from '@chakra-ui/icons';

interface AccessibilitySettings {
  highContrast: boolean;
  fontSize: number;
  reducedMotion: boolean;
  announcements: boolean;
}

interface AccessibilityHelperProps {
  onSettingsChange?: (settings: AccessibilitySettings) => void;
}

export const AccessibilityHelper: React.FC<AccessibilityHelperProps> = ({
  onSettingsChange,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { colorMode, toggleColorMode } = useColorMode();
  
  const [settings, setSettings] = useState<AccessibilitySettings>({
    highContrast: false,
    fontSize: 16,
    reducedMotion: false,
    announcements: true,
  });

  const [announcement, setAnnouncement] = useState<string>('');

  // 設定変更時の処理
  const handleSettingChange = (key: keyof AccessibilitySettings, value: boolean | number) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);

    // 変更を音声で通知
    if (settings.announcements) {
      const messages = {
        highContrast: value ? 'ハイコントラストモードを有効にしました' : 'ハイコントラストモードを無効にしました',
        fontSize: `文字サイズを${value}に変更しました`,
        reducedMotion: value ? 'アニメーション軽減モードを有効にしました' : 'アニメーション軽減モードを無効にしました',
        announcements: value ? '音声案内を有効にしました' : '音声案内を無効にしました',
      };
      setAnnouncement(messages[key]);
    }
  };

  // CSS変数でフォントサイズを動的に変更
  useEffect(() => {
    document.documentElement.style.setProperty('--font-size-base', `${settings.fontSize}px`);
  }, [settings.fontSize]);

  // reduced-motionの設定
  useEffect(() => {
    if (settings.reducedMotion) {
      document.documentElement.style.setProperty('--motion-reduce', 'reduce');
    } else {
      document.documentElement.style.setProperty('--motion-reduce', 'no-preference');
    }
  }, [settings.reducedMotion]);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <>

      {/* アクセシビリティコントロール */}
      <Box
        position="fixed"
        top={4}
        right={4}
        zIndex={900}
      >
        <VStack spacing={2}>
          {/* ダークモード切り替えボタン */}
          <Button
            onClick={toggleColorMode}
            size="md"
            variant="outline"
            bg={useColorModeValue('white', 'gray.800')}
            borderColor={useColorModeValue('gray.300', 'gray.600')}
            color={useColorModeValue('gray.700', 'gray.200')}
            _hover={{
              bg: useColorModeValue('gray.50', 'gray.700'),
              transform: 'scale(1.05)',
            }}
            _focus={{
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
            aria-label={colorMode === 'light' ? 'ダークモードに切り替え' : 'ライトモードに切り替え'}
            boxShadow="md"
            transition="all 0.2s"
          >
            <Icon 
              as={colorMode === 'light' ? MoonIcon : SunIcon} 
              boxSize={5}
            />
          </Button>
          
          {/* アクセシビリティ設定ボタン */}
          <Button
            onClick={onOpen}
            size="sm"
            colorScheme="primary"
            leftIcon={<Icon as={SettingsIcon} />}
            aria-label="アクセシビリティ設定を開く"
            _focus={{
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
            boxShadow="md"
            bg={useColorModeValue('primary.500', 'primary.600')}
            _hover={{
              bg: useColorModeValue('primary.600', 'primary.700'),
              transform: 'scale(1.05)',
            }}
            transition="all 0.2s"
          >
            A11y
            <VisuallyHidden>
              アクセシビリティ設定メニューを開きます。文字サイズ、コントラスト、アニメーションなどの設定を変更できます。
            </VisuallyHidden>
          </Button>
        </VStack>
      </Box>

      {/* 設定モーダル */}
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>アクセシビリティ設定</ModalHeader>
          <ModalCloseButton 
            aria-label="設定を閉じる"
            _focus={{
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
          />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              
              {/* ダークモード切り替え - 強調表示 */}
              <Box 
                p={4} 
                bg={colorMode === 'dark' ? 'primary.900' : 'primary.50'} 
                borderRadius="md" 
                borderWidth={2}
                borderColor={colorMode === 'dark' ? 'primary.600' : 'primary.200'}
              >
                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="dark-mode" mb="0" flex="1">
                    <HStack>
                      <Icon 
                        as={colorMode === 'light' ? MoonIcon : SunIcon} 
                        color={colorMode === 'dark' ? 'primary.300' : 'primary.600'}
                        boxSize={5}
                      />
                      <VStack align="start" spacing={0}>
                        <Text fontWeight="bold" color={colorMode === 'dark' ? 'primary.200' : 'primary.700'}>
                          ダークモード
                        </Text>
                        <Text fontSize="xs" color={colorMode === 'dark' ? 'primary.300' : 'primary.600'}>
                          目の疲労軽減に効果的
                        </Text>
                      </VStack>
                    </HStack>
                  </FormLabel>
                  <Switch
                    id="dark-mode"
                    isChecked={colorMode === 'dark'}
                    onChange={toggleColorMode}
                    colorScheme="primary"
                    aria-describedby="dark-mode-desc"
                    size="lg"
                  />
                  <VisuallyHidden id="dark-mode-desc">
                    ダークモードを切り替えます。目の疲労軽減に効果的です。
                  </VisuallyHidden>
                </FormControl>
              </Box>

              {/* ハイコントラスト */}
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="high-contrast" mb="0" flex="1">
                  <Text>ハイコントラスト</Text>
                </FormLabel>
                <Switch
                  id="high-contrast"
                  isChecked={settings.highContrast}
                  onChange={(e) => handleSettingChange('highContrast', e.target.checked)}
                  colorScheme="primary"
                  aria-describedby="high-contrast-desc"
                />
                <VisuallyHidden id="high-contrast-desc">
                  テキストと背景のコントラストを強くして、読みやすさを向上させます。
                </VisuallyHidden>
              </FormControl>

              {/* フォントサイズ */}
              <FormControl>
                <FormLabel htmlFor="font-size">
                  文字サイズ: {settings.fontSize}px
                </FormLabel>
                <Slider
                  id="font-size"
                  min={12}
                  max={24}
                  step={1}
                  value={settings.fontSize}
                  onChange={(value) => handleSettingChange('fontSize', value)}
                  colorScheme="primary"
                  aria-label="文字サイズを調整"
                  aria-describedby="font-size-desc"
                >
                  <SliderTrack>
                    <SliderFilledTrack />
                  </SliderTrack>
                  <SliderThumb boxSize={6}>
                    <Box color="primary.500" as={Text} fontSize="xs">A</Box>
                  </SliderThumb>
                </Slider>
                <HStack justify="space-between" mt={2}>
                  <Text fontSize="xs" color="gray.500">小</Text>
                  <Text fontSize="xs" color="gray.500">大</Text>
                </HStack>
                <VisuallyHidden id="font-size-desc">
                  インターフェース全体の文字サイズを12から24ピクセルの間で調整できます。
                </VisuallyHidden>
              </FormControl>

              {/* アニメーション軽減 */}
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="reduced-motion" mb="0" flex="1">
                  <Text>アニメーション軽減</Text>
                </FormLabel>
                <Switch
                  id="reduced-motion"
                  isChecked={settings.reducedMotion}
                  onChange={(e) => handleSettingChange('reducedMotion', e.target.checked)}
                  colorScheme="primary"
                  aria-describedby="reduced-motion-desc"
                />
                <VisuallyHidden id="reduced-motion-desc">
                  動きのあるアニメーションを軽減します。動きに敏感な方におすすめです。
                </VisuallyHidden>
              </FormControl>

              {/* 音声案内 */}
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="announcements" mb="0" flex="1">
                  <Text>音声案内</Text>
                </FormLabel>
                <Switch
                  id="announcements"
                  isChecked={settings.announcements}
                  onChange={(e) => handleSettingChange('announcements', e.target.checked)}
                  colorScheme="primary"
                  aria-describedby="announcements-desc"
                />
                <VisuallyHidden id="announcements-desc">
                  操作時の音声による案内を有効または無効にします。
                </VisuallyHidden>
              </FormControl>

              {/* キーボードショートカット説明 */}
              <Box 
                p={4} 
                bg={bgColor} 
                borderWidth={1} 
                borderColor={borderColor} 
                borderRadius="md"
              >
                <Text fontWeight="bold" mb={2}>キーボードショートカット</Text>
                <VStack align="start" spacing={1} fontSize="sm">
                  <Text><kbd>Tab</kbd> / <kbd>Shift+Tab</kbd>: 要素間の移動</Text>
                  <Text><kbd>Enter</kbd> / <kbd>Space</kbd>: ボタンの実行</Text>
                  <Text><kbd>Esc</kbd>: モーダルを閉じる</Text>
                  <Text><kbd>A</kbd>: アクセシビリティ設定を開く</Text>
                </VStack>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* ライブアナウンス領域 */}
      <Box 
        aria-live="polite" 
        aria-atomic="true" 
        position="absolute" 
        left="-10000px" 
        width="1px" 
        height="1px" 
        overflow="hidden"
      >
        {announcement}
      </Box>
    </>
  );
};

// キーボードショートカットのフック
export const useKeyboardShortcuts = (onAccessibilityOpen: () => void) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + A でアクセシビリティ設定を開く
      if (event.altKey && event.key.toLowerCase() === 'a') {
        event.preventDefault();
        onAccessibilityOpen();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onAccessibilityOpen]);
};