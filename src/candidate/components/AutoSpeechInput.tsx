import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Progress,
  Button,
  useToast,
  UseToastOptions,
} from '@chakra-ui/react';
import {
  FaMicrophone,
  FaMicrophoneSlash,
  FaStop,
  FaPlay,
} from 'react-icons/fa';
import { useSpeechToText } from '../hooks/useSpeechToText';

interface AutoSpeechInputProps {
  questionId: string;
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
  autoStart?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

interface SpeechStatus {
  text: string;
  color: string;
}

const AUTO_RECOVERABLE_ERRORS = ['aborted', 'network', 'no-speech'] as const;

const isRecoverableError = (error: string | null): boolean => {
  if (!error) return false;
  return AUTO_RECOVERABLE_ERRORS.some((code) => error.includes(code));
};

const isValidFinalText = (text: string): boolean => {
  return !!text?.trim();
};

const getSpeechStatus = (
  isInitializing: boolean,
  error: string | null,
  isListening: boolean,
  isSpeaking: boolean,
  hasStarted: boolean,
  finalText: string
): SpeechStatus => {
  if (isInitializing) {
    return { text: '初期化中...', color: 'blue.500' };
  }

  if (error) {
    if (isRecoverableError(error)) {
      return { text: '再接続中...', color: 'orange.500' };
    }
    return { text: 'エラーが発生しました', color: 'red.500' };
  }

  if (!isListening && !hasStarted) {
    return { text: '録音を開始するにはタップしてください', color: 'gray.500' };
  }

  if (isListening && !isSpeaking) {
    return { text: '聞き取り中...', color: 'green.500' };
  }

  if (isListening && isSpeaking) {
    return { text: '録音中...', color: 'red.500' };
  }

  if (!isListening && finalText) {
    return { text: '録音完了', color: 'blue.500' };
  }

  return { text: '準備完了', color: 'gray.500' };
};

const getVoiceLevel = (isSpeaking: boolean): number => {
  return isSpeaking ? Math.random() * 100 : 0;
};

const createToast = (
  type: 'error' | 'success' | 'warning',
  message: string
): UseToastOptions => ({
  title: type === 'error' ? 'エラー' : type === 'success' ? '成功' : '警告',
  description: message,
  status: type,
  duration: type === 'error' ? 5000 : 3000,
  isClosable: true,
});

export const AutoSpeechInput: React.FC<AutoSpeechInputProps> = ({
  questionId,
  onTranscriptionComplete,
  onError,
  autoStart = true,
  disabled = false,
  placeholder = '質問に答えるために話し始めてください...',
}) => {
  const toast = useToast();
  const [hasStarted, setHasStarted] = useState(false);
  const [previousQuestionId, setPreviousQuestionId] = useState<string | null>(
    null
  );

  const {
    isListening,
    isInitializing,
    currentText,
    finalText,
    confidence,
    error,
    isSpeaking,
    startListening,
    stopListening,
    resetTranscription,
    sendResponse,
  } = useSpeechToText({
    questionId,
    autoStart: false,
    language: 'vi-VN',
    enableVoiceActivityDetection: true,
    silenceTimeoutMs: 4000,
    onTranscriptionComplete: (text: string) => {
      onTranscriptionComplete?.(text);
      toast(createToast('success', 'あなたの回答が正常に送信されました。'));
    },
    onError: (errorMessage: string) => {
      handleError(errorMessage);
    },
  });

  // Validate props
  useEffect(() => {
    if (
      onTranscriptionComplete &&
      typeof onTranscriptionComplete !== 'function'
    ) {
      console.warn(
        'AutoSpeechInput: onTranscriptionComplete should be a function'
      );
    }
    if (onError && typeof onError !== 'function') {
      console.warn('AutoSpeechInput: onError should be a function');
    }
  }, [onTranscriptionComplete, onError]);

  // Centralized error handler
  const handleError = useCallback(
    (message: string) => {
      console.error('AutoSpeechInput error:', message);
      toast(createToast('error', message));
      onError?.(message);
    },
    [toast, onError]
  );

  // Check if ready to auto-start
  const isReadyToAutoStart = useCallback(() => {
    return autoStart && !disabled && !hasStarted && !isInitializing;
  }, [autoStart, disabled, hasStarted, isInitializing]);

  // Auto-start when component mounts
  useEffect(() => {
    if (isReadyToAutoStart()) {
      handleStartListening();
    }
  }, [isReadyToAutoStart]);

  // Handle recoverable errors
  useEffect(() => {
    if (error && isRecoverableError(error)) {
      const timer = setTimeout(() => {
        resetTranscription();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [error, resetTranscription]);

  // Reset when questionId changes (only if different)
  useEffect(() => {
    if (questionId && questionId !== previousQuestionId) {
      setPreviousQuestionId(questionId);
      handleReset();
    }
  }, [questionId, previousQuestionId]);

  // Auto-restart when stopped unexpectedly
  useEffect(() => {
    if (
      !isListening &&
      hasStarted &&
      !isInitializing &&
      !disabled &&
      !finalText
    ) {
      const timer = setTimeout(() => {
        handleStartListening();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isListening, hasStarted, isInitializing, disabled, finalText]);

  // Memoized values
  const speechStatus = useMemo(
    () =>
      getSpeechStatus(
        isInitializing,
        error,
        isListening,
        isSpeaking,
        hasStarted,
        finalText
      ),
    [isInitializing, error, isListening, isSpeaking, hasStarted, finalText]
  );

  const voiceLevel = useMemo(() => getVoiceLevel(isSpeaking), [isSpeaking]);

  // Event handlers
  const handleStartListening = useCallback(async () => {
    if (disabled || isListening) return;

    const success = await startListening();
    if (success) {
      setHasStarted(true);
    } else {
      handleError('マイクを確認して再試行してください。');
    }
  }, [disabled, isListening, startListening, handleError]);

  const handleStopListening = useCallback(async () => {
    await stopListening();
  }, [stopListening]);

  const handleSendResponse = useCallback(() => {
    if (isValidFinalText(finalText)) {
      sendResponse();
    } else {
      toast(createToast('warning', '回答を送信する前に何か話してください。'));
    }
  }, [finalText, sendResponse, toast]);

  const handleReset = useCallback(() => {
    resetTranscription();
    setHasStarted(false);
  }, [resetTranscription]);

  return (
    <Box
      p={6}
      borderWidth={2}
      borderRadius="lg"
      borderColor={speechStatus.color}
      bg="white"
      shadow="md"
      transition="all 0.3s"
    >
      <VStack spacing={4} align="stretch">
        {/* Status Header */}
        <HStack justify="space-between" align="center">
          <HStack>
            {isListening ? (
              <FaMicrophone
                color={isSpeaking ? '#E53E3E' : '#38A169'}
                size={20}
              />
            ) : (
              <FaMicrophoneSlash color="#A0AEC0" size={20} />
            )}
            <Text fontWeight="bold" color={speechStatus.color}>
              {speechStatus.text}
            </Text>
          </HStack>

          {confidence > 0 && (
            <Text fontSize="sm" color="gray.600">
              信頼度: {Math.round(confidence * 100)}%
            </Text>
          )}
        </HStack>

        {/* Voice Level Indicator */}
        {isListening && (
          <Box>
            <Text fontSize="sm" color="gray.600" mb={2}>
              音量レベル:
            </Text>
            <Progress
              value={voiceLevel}
              colorScheme={isSpeaking ? 'red' : 'green'}
              size="sm"
              borderRadius="md"
            />
          </Box>
        )}

        {/* Current Recognition Text */}
        {currentText && (
          <Box
            p={3}
            bg="blue.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="blue.400"
          >
            <Text fontSize="sm" color="blue.600" fontStyle="italic">
              "{currentText}"
            </Text>
          </Box>
        )}

        {/* Final Transcription */}
        {finalText && (
          <Box
            p={4}
            bg="green.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="green.400"
          >
            <Text fontSize="sm" color="gray.600" mb={1}>
              記録された内容:
            </Text>
            <Text color="green.800" fontWeight="medium">
              {finalText}
            </Text>
          </Box>
        )}

        {/* Placeholder when no content */}
        {!currentText && !finalText && !isListening && (
          <Box p={4} bg="gray.50" borderRadius="md" textAlign="center">
            <Text color="gray.500" fontStyle="italic">
              {placeholder}
            </Text>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Box
            p={3}
            bg="red.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="red.400"
          >
            <Text fontSize="sm" color="red.600">
              エラー: {error}
            </Text>
          </Box>
        )}

        {/* Control Buttons */}
        <HStack spacing={3} justify="center">
          {!isListening ? (
            <Button
              leftIcon={<FaPlay />}
              colorScheme="green"
              onClick={handleStartListening}
              disabled={disabled || isInitializing}
              isLoading={isInitializing}
              loadingText="初期化中..."
            >
              録音開始
            </Button>
          ) : (
            <Button
              leftIcon={<FaStop />}
              colorScheme="red"
              onClick={handleStopListening}
              disabled={disabled}
            >
              録音停止
            </Button>
          )}

          {finalText && (
            <>
              <Button
                colorScheme="blue"
                onClick={handleSendResponse}
                disabled={disabled || !isValidFinalText(finalText)}
              >
                回答を送信
              </Button>

              <Button
                variant="outline"
                onClick={handleReset}
                disabled={disabled || isListening}
              >
                やり直し
              </Button>
            </>
          )}
        </HStack>

        {/* Instructions */}
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500">
            {isListening
              ? '4秒間無音の場合、システムは自動的に停止します'
              : '音声で回答するには「録音開始」をタップしてください'}
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};
