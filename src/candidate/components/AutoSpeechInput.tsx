import React, { useEffect, useState, useCallback } from 'react';
import { Box, Text, VStack, HStack, Progress, Button, useToast } from '@chakra-ui/react';
import { FaMicrophone, FaMicrophoneSlash, FaStop, FaPlay } from 'react-icons/fa';
import { useSpeechToText } from '../hooks/useSpeechToText';

interface AutoSpeechInputProps {
  questionId: string;
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
  autoStart?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const AutoSpeechInput: React.FC<AutoSpeechInputProps> = ({
  questionId,
  onTranscriptionComplete,
  onError,
  autoStart = true,
  disabled = false,
  placeholder = "Bắt đầu nói để trả lời câu hỏi..."
}) => {
  const toast = useToast();
  const [hasStarted, setHasStarted] = useState(false);

  const {
    isListening,
    isInitializing,
    currentText,
    finalText,
    confidence,
    error,
    isSpeaking,
    startListening,
    stopListening,
    resetTranscription,
    sendResponse
  } = useSpeechToText({
    questionId,
    autoStart: false,
    language: 'vi-VN',
    enableVoiceActivityDetection: true,
    silenceTimeoutMs: 4000,
    onTranscriptionComplete: (text: string) => {
      onTranscriptionComplete?.(text);
      toast({
        title: 'Đã gửi câu trả lời',
        description: 'Câu trả lời của bạn đã được gửi thành công.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    },
    onError: (errorMessage: string) => {
      onError?.(errorMessage);
      toast({
        title: 'Lỗi nhận diện giọng nói',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  });

  // Auto start when component mounts
  useEffect(() => {
    if (autoStart && !disabled && !hasStarted && !isInitializing) {
      console.log('Auto-starting speech recognition for question:', questionId);
      handleStartListening();
    }
  }, [autoStart, disabled, hasStarted, isInitializing, questionId]);

  // Handle errors
  useEffect(() => {
    if (error) {
      console.error('Speech recognition error:', error);
    }
  }, [error]);

  // Reset when question changes
  useEffect(() => {
    if (questionId) {
      console.log('Question changed, resetting speech input:', questionId);
      handleReset();
    }
  }, [questionId]);

  const handleStartListening = useCallback(async () => {
    if (disabled || isListening) {
      console.log('Cannot start listening:', { disabled, isListening });
      return;
    }

    console.log('Starting speech recognition...');
    const success = await startListening();
    if (success) {
      setHasStarted(true);
      console.log('Speech recognition started successfully');
    } else {
      console.error('Failed to start speech recognition');
      toast({
        title: 'Không thể bắt đầu ghi âm',
        description: 'Vui lòng kiểm tra microphone và thử lại.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [disabled, isListening, startListening, toast]);

  const handleStopListening = useCallback(async () => {
    console.log('Stopping speech recognition...');
    await stopListening();
  }, [stopListening]);

  const handleSendResponse = useCallback(() => {
    if (finalText.trim()) {
      console.log('Manually sending response:', finalText);
      sendResponse();
    } else {
      toast({
        title: 'Không có nội dung',
        description: 'Vui lòng nói gì đó trước khi gửi câu trả lời.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    }
  }, [finalText, sendResponse, toast]);

  const handleReset = useCallback(() => {
    console.log('Resetting speech input');
    resetTranscription();
    setHasStarted(false);
  }, [resetTranscription]);

  const getStatusText = () => {
    if (isInitializing) return 'Đang khởi tạo...';
    if (!isListening && !hasStarted) return 'Nhấn để bắt đầu ghi âm';
    if (isListening && !isSpeaking) return 'Đang lắng nghe...';
    if (isListening && isSpeaking) return 'Đang ghi âm...';
    if (!isListening && finalText) return 'Đã hoàn thành ghi âm';
    return 'Sẵn sàng';
  };

  const getStatusColor = () => {
    if (error) return 'red.500';
    if (isInitializing) return 'blue.500';
    if (isListening && isSpeaking) return 'red.500';
    if (isListening) return 'green.500';
    if (finalText) return 'blue.500';
    return 'gray.500';
  };

  const getVoiceLevel = () => {
    if (isSpeaking) return Math.random() * 100; // Simulated voice level
    return 0;
  };

  return (
    <Box
      p={6}
      borderWidth={2}
      borderRadius="lg"
      borderColor={getStatusColor()}
      bg="white"
      shadow="md"
      transition="all 0.3s"
    >
      <VStack spacing={4} align="stretch">
        {/* Status Header */}
        <HStack justify="space-between" align="center">
          <HStack>
            {isListening ? (
              <FaMicrophone color={isSpeaking ? '#E53E3E' : '#38A169'} size={20} />
            ) : (
              <FaMicrophoneSlash color="#A0AEC0" size={20} />
            )}
            <Text fontWeight="bold" color={getStatusColor()}>
              {getStatusText()}
            </Text>
          </HStack>
          
          {confidence > 0 && (
            <Text fontSize="sm" color="gray.600">
              Độ tin cậy: {Math.round(confidence * 100)}%
            </Text>
          )}
        </HStack>

        {/* Voice Level Indicator */}
        {isListening && (
          <Box>
            <Text fontSize="sm" color="gray.600" mb={2}>
              Mức âm thanh:
            </Text>
            <Progress
              value={getVoiceLevel()}
              colorScheme={isSpeaking ? 'red' : 'green'}
              size="sm"
              borderRadius="md"
            />
          </Box>
        )}

        {/* Current Recognition Text */}
        {currentText && (
          <Box
            p={3}
            bg="blue.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="blue.400"
          >
            <Text fontSize="sm" color="blue.600" fontStyle="italic">
              Đang nhận diện: "{currentText}"
            </Text>
          </Box>
        )}

        {/* Final Transcription */}
        {finalText && (
          <Box
            p={4}
            bg="green.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="green.400"
          >
            <Text fontSize="sm" color="gray.600" mb={1}>
              Nội dung đã ghi:
            </Text>
            <Text color="green.800" fontWeight="medium">
              {finalText}
            </Text>
          </Box>
        )}

        {/* Placeholder when no content */}
        {!currentText && !finalText && !isListening && (
          <Box
            p={4}
            bg="gray.50"
            borderRadius="md"
            textAlign="center"
          >
            <Text color="gray.500" fontStyle="italic">
              {placeholder}
            </Text>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Box
            p={3}
            bg="red.50"
            borderRadius="md"
            borderLeft="4px solid"
            borderLeftColor="red.400"
          >
            <Text fontSize="sm" color="red.600">
              Lỗi: {error}
            </Text>
          </Box>
        )}

        {/* Control Buttons */}
        <HStack spacing={3} justify="center">
          {!isListening ? (
            <Button
              leftIcon={<FaPlay />}
              colorScheme="green"
              onClick={handleStartListening}
              disabled={disabled || isInitializing}
              isLoading={isInitializing}
              loadingText="Đang khởi tạo..."
            >
              Bắt đầu ghi âm
            </Button>
          ) : (
            <Button
              leftIcon={<FaStop />}
              colorScheme="red"
              onClick={handleStopListening}
              disabled={disabled}
            >
              Dừng ghi âm
            </Button>
          )}

          {finalText && (
            <>
              <Button
                colorScheme="blue"
                onClick={handleSendResponse}
                disabled={disabled || !finalText.trim()}
              >
                Gửi câu trả lời
              </Button>
              
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={disabled || isListening}
              >
                Làm lại
              </Button>
            </>
          )}
        </HStack>

        {/* Speech API Info */}
        <Box p={3} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.400">
          <Text fontSize="sm" color="blue.700">
            <strong>🎤 Speech Recognition:</strong> Using browser's Web Speech API
            <br />
            <Text as="span" fontSize="xs" color="blue.600">
              (Azure Speech Service temporarily disabled due to CORS issues)
            </Text>
          </Text>
        </Box>

        {/* Debug Info (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <Box p={2} bg="gray.100" borderRadius="md" fontSize="xs">
            <Text><strong>Debug Info:</strong></Text>
            <Text>Question ID: {questionId}</Text>
            <Text>Is Listening: {isListening ? 'Yes' : 'No'}</Text>
            <Text>Is Speaking: {isSpeaking ? 'Yes' : 'No'}</Text>
            <Text>Has Started: {hasStarted ? 'Yes' : 'No'}</Text>
            <Text>Is Initializing: {isInitializing ? 'Yes' : 'No'}</Text>
            <Text>Current Text Length: {currentText.length}</Text>
            <Text>Final Text Length: {finalText.length}</Text>
            <Text>Confidence: {Math.round(confidence * 100)}%</Text>
            {error && <Text color="red.500">Error: {error}</Text>}
          </Box>
        )}

        {/* Instructions */}
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500">
            {isListening
              ? "Hệ thống sẽ tự động dừng khi bạn im lặng trong 4 giây"
              : "Nhấn 'Bắt đầu ghi âm' để trả lời bằng giọng nói"
            }
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};
