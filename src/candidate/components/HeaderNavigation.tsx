/**
 * ヘッダーナビゲーションコンポーネント
 * 全画面共通のナビゲーション機能を提供
 */
import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Button,
  Text,
  Avatar,
  useColorModeValue,
  Icon,
  Container,
  Flex,
  useColorMode,
  useDisclosure,
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { ArrowBackIcon, SunIcon, MoonIcon } from '@chakra-ui/icons';
import { SettingsModal } from './SettingsModal';

export interface NavigationItem {
  label: string;
  href: string;
  isCurrentPage?: boolean;
}

interface HeaderNavigationProps {
  user?: {
    profile: {
      name: string;
    };
  };
  breadcrumbs?: NavigationItem[];
  showBackButton?: boolean;
  backButtonLabel?: string;
  onBackClick?: () => void;
}

const HeaderNavigation: React.FC<HeaderNavigationProps> = ({
  user,
  breadcrumbs = [],
  showBackButton = false,
  backButtonLabel = "戻る",
  onBackClick,
}) => {
  const router = useRouter();
  const { colorMode, toggleColorMode } = useColorMode();
  const { isOpen: isSettingsOpen, onOpen: onSettingsOpen, onClose: onSettingsClose } = useDisclosure();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'gray.100');

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      router.back();
    }
  };

  const handleHomeClick = () => {
    // 全ての場合で test-data（エージェントリンク一覧）へ遷移
    router.push('/test-data');
  };

  return (
    <Box
      bg={bgColor}
      borderBottom="1px solid"
      borderColor={borderColor}
      position="sticky"
      top={0}
      zIndex={500}
      boxShadow="sm"
    >
      <Container maxW="container.xl" py={3}>
        <VStack spacing={2} align="stretch">
          {/* メインナビゲーション行 */}
          <Flex justify="space-between" align="center" w="full">
            {/* 左側: ロゴ・戻るボタン */}
            <HStack spacing={4}>
              {showBackButton ? (
                <Button
                  variant="ghost"
                  leftIcon={<ArrowBackIcon />}
                  onClick={handleBackClick}
                  size="sm"
                  color={textColor}
                >
                  {backButtonLabel}
                </Button>
              ) : (
                <Button
                  variant="ghost"
                  onClick={handleHomeClick}
                  size="sm"
                  color={textColor}
                >
                  面接くん
                </Button>
              )}
            </HStack>

            {/* 右側: アクセシビリティ控制 */}
            <HStack spacing={2}>
              {/* ダークモード切り替えボタン */}
              <Button
                onClick={toggleColorMode}
                size="sm"
                variant="outline"
                bg={useColorModeValue('white', 'gray.800')}
                borderColor={useColorModeValue('gray.300', 'gray.600')}
                color={useColorModeValue('gray.700', 'gray.200')}
                _hover={{
                  bg: useColorModeValue('gray.50', 'gray.700'),
                }}
                aria-label={colorMode === 'light' ? 'ダークモードに切り替え' : 'ライトモードに切り替え'}
              >
                <Icon 
                  as={colorMode === 'light' ? MoonIcon : SunIcon} 
                  boxSize={4}
                />
              </Button>
              
              {user && (
                <HStack spacing={2}>
                  <Text fontSize="sm" fontWeight="medium" color={textColor}>
                    {user.profile.name}さん
                  </Text>
                  <Avatar
                    name={user.profile.name}
                    size="sm"
                    bg="primary.500"
                    cursor="pointer"
                    onClick={onSettingsOpen}
                    _hover={{
                      transform: 'scale(1.05)',
                      boxShadow: 'md',
                    }}
                    transition="all 0.2s"
                    aria-label="設定を開く"
                  />
                </HStack>
              )}
            </HStack>
          </Flex>

        </VStack>
      </Container>
      
      {/* 設定モーダル */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={onSettingsClose}
        onSettingsChange={(settings) => {
          // 設定変更時の処理をここに追加可能
          console.log('Settings changed:', settings);
        }}
      />
    </Box>
  );
};

export default HeaderNavigation;