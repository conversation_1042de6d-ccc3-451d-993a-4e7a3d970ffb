import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  VStack,
  Container,
  useToast,
  Text,
  Progress,
  HStack,
  Button,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { AdaptiveAvatar, UserState } from './AdaptiveAvatar';
import QuestionDisplay from './QuestionDisplay';
import AnswerInput from './AnswerInput';
import { AutoSpeechInput } from './AutoSpeechInput';
import { SafeFeedbackDisplay } from './SafeFeedbackDisplay';
import { BreathingPrompt } from './BreathingPrompt';
import { ChallengeSelector, Challenge } from './ChallengeSelector';
import { SkillTree, UserSkills } from './SkillTree';
import {
  AccessibilityHelper,
  useKeyboardShortcuts,
} from './AccessibilityHelper';
import {
  UnifiedMockDataService,
  type BasicQuestion,
  type BasicFeedback,
} from '../data/mockApiData';
import { InterviewDataApi } from '../services/api';
import { fadeIn } from '../motion';
import type { InterviewSessionResponse } from '../../shared/types';

// 統合APIの型を使用
type Question = BasicQuestion;
type Feedback = BasicFeedback;

interface AnalyzeResponse {
  text?: string;
  error?: string;
  feedback?: Feedback;
}

const InterviewRoom: React.FC = () => {
  const router = useRouter();
  const { token, mode } = router.query;

  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [interviewSession, setInterviewSession] =
    useState<InterviewSessionResponse | null>(null);
  const [isTokenBased] = useState(mode === 'token-based');
  const [showBreathing, setShowBreathing] = useState(true);
  const [selectedChallenge, setSelectedChallenge] = useState<Challenge | null>(
    null
  );
  const [showChallengeSelector, setShowChallengeSelector] = useState(false);
  const [showSkillTree, setShowSkillTree] = useState(false);
  const [userPoints, setUserPoints] = useState(0);
  const [completedChallenges, setCompletedChallenges] = useState<string[]>([]);
  const [userSkills, setUserSkills] = useState<UserSkills>({
    communication: 25,
    logic: 20,
    confidence: 30,
    creativity: 15,
    timeManagement: 35,
    emotionalControl: 40,
  });
  const [totalInterviews, setTotalInterviews] = useState(3);
  const [averageScore, setAverageScore] = useState(7.5);
  const [accessibilityOpen, setAccessibilityOpen] = useState(false);
  const [userState, setUserState] = useState<UserState>({
    confidence: 0.5,
    isPaused: false,
    speakingSpeed: 'normal',
    emotionalState: 'nervous',
  });
  const toast = useToast();

  // キーボードショートカットの設定
  useKeyboardShortcuts(() => setAccessibilityOpen(true));

  /**
   * モックデータから質問を取得する
   */
  const fetchQuestion = async () => {
    try {
      setIsLoading(true);
      console.log('📋 質問を取得中...');

      // 統合APIから質問を取得
      const mockQuestions = await UnifiedMockDataService.getBasicQuestions();
      setQuestions(mockQuestions);

      // 最初の質問を設定
      if (mockQuestions.length > 0) {
        setCurrentQuestion(mockQuestions[0]);
        console.log('✅ 質問を取得しました:', mockQuestions[0].text);
      }
    } catch (error: unknown) {
      console.error('質問取得エラー:', error);
      const errorMessage =
        error instanceof Error ? error.message : '不明なエラーが発生しました';
      toast({
        title: 'エラー',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 次の質問に進む
   */
  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);
      setCurrentQuestion(questions[nextIndex]);
      setFeedback(null);
    } else {
      console.log('🎉 すべての質問が完了しました');
      toast({
        title: '面接完了',
        description: 'すべての質問にお答えいただき、ありがとうございました。',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  /**
   * 回答テキストの処理
   */
  const handleTextAnswerSubmit = async (text: string) => {
    try {
      console.log('📝 テキスト回答を処理します:', text);
      setIsLoading(true);

      // モックフィードバックを生成
      const mockFeedbacks = await UnifiedMockDataService.getBasicFeedbacks();
      const mockFeedback = mockFeedbacks[0] || {
        id: 'mock-feedback',
        content: `「${text}」という回答について、とても良い内容でした。`,
        score: 85,
        timestamp: new Date().toISOString(),
      };

      setFeedback(mockFeedback);

      // ユーザーのスキルを更新
      setUserSkills((prev) => ({
        ...prev,
        communication: Math.min(100, prev.communication + 2),
        logic: Math.min(100, prev.logic + 1),
        confidence: Math.min(100, prev.confidence + 3),
        creativity: Math.min(100, prev.creativity + 1),
      }));

      setUserPoints((prev) => prev + 10);

      toast({
        title: '回答を送信しました',
        description: 'フィードバックを生成中です...',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('テキスト回答処理エラー:', error);
      toast({
        title: 'エラーが発生しました',
        description: 'もう一度お試しください',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 回答音声の解析処理（モック版）
   */
  const handleAnswerSubmit = async (audioBlob: Blob) => {
    try {
      console.log('🎤 音声解析を開始します:', {
        size: audioBlob.size,
        type: audioBlob.type,
      });

      // 統合API音声解析を実行
      const mockFile = new File([audioBlob], 'recording.wav', {
        type: 'audio/wav',
      });
      const result = await UnifiedMockDataService.analyzeAudio(mockFile);

      console.log('✅ 音声解析完了:', result);

      // フィードバックを設定
      setFeedback(result.feedback);

      // 成功トーストを表示
      toast({
        title: '回答を受け付けました',
        description:
          'AIによる分析が完了しました。フィードバックをご確認ください。',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // 3秒後に次の質問に自動進行
      setTimeout(() => {
        goToNextQuestion();
      }, 3000);
    } catch (error: unknown) {
      console.error('音声解析エラー:', error);
      const errorMessage =
        error instanceof Error ? error.message : '音声の解析に失敗しました';
      toast({
        title: 'エラー',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    if (!showBreathing) {
      fetchQuestion();
    }
  }, [showBreathing]);

  // 深呼吸完了後の処理
  const handleBreathingComplete = () => {
    setShowBreathing(false);
    setShowChallengeSelector(true);
    setUserState((prev) => ({ ...prev, emotionalState: 'calm' }));
  };

  // チャレンジ選択後の処理
  const handleChallengeSelect = (challenge: Challenge) => {
    setSelectedChallenge(challenge);
    setShowChallengeSelector(false);
    console.log('🎯 選択されたチャレンジ:', challenge.title);
  };

  // 面接開始ボタンの処理
  const handleStartInterview = () => {
    setShowChallengeSelector(false);
  };

  // スキルツリー表示の処理
  const handleShowSkillTree = () => {
    setShowSkillTree(true);
  };

  // スキルツリーを閉じる処理
  const handleCloseSkillTree = () => {
    setShowSkillTree(false);
  };

  // フィードバック完了後の処理
  const handleFeedbackComplete = () => {
    // チャレンジ達成チェック
    if (selectedChallenge && feedback) {
      const challengeCompleted = checkChallengeCompletion(
        selectedChallenge,
        feedback
      );
      if (challengeCompleted) {
        setUserPoints((prev) => prev + selectedChallenge.points);
        setCompletedChallenges((prev) => [...prev, selectedChallenge.id]);
        updateUserSkills(selectedChallenge, feedback);

        toast({
          title: 'チャレンジクリア！',
          description: `「${selectedChallenge.title}」を達成しました！+${selectedChallenge.points}ポイント`,
          status: 'success',
          duration: 4000,
          isClosable: true,
        });
      }
    }
    goToNextQuestion();
  };

  // チャレンジ達成判定
  const checkChallengeCompletion = (
    challenge: Challenge,
    feedback: Feedback
  ): boolean => {
    // 簡単な達成判定ロジック（実際はより詳細な判定を行う）
    switch (challenge.id) {
      case 'clear_speaker':
        return (feedback.clarityScore ?? 0) >= 8;
      case 'time_keeper':
        return (feedback.confidenceScore ?? 0) >= 7;
      case 'star_method':
        return (
          (feedback.clarityScore ?? 0) >= 8 &&
          (feedback.confidenceScore ?? 0) >= 7
        );
      default:
        return feedback.overall >= 7;
    }
  };

  // スキル更新
  const updateUserSkills = (challenge: Challenge, feedback: Feedback) => {
    setUserSkills((prev) => {
      const newSkills = { ...prev };

      // チャレンジタイプに基づいてスキルを更新
      switch (challenge.id) {
        case 'clear_speaker':
          newSkills.communication = Math.min(100, newSkills.communication + 5);
          break;
        case 'time_keeper':
          newSkills.timeManagement = Math.min(
            100,
            newSkills.timeManagement + 5
          );
          break;
        case 'star_method':
          newSkills.logic = Math.min(100, newSkills.logic + 5);
          break;
        case 'emotion_master':
          newSkills.emotionalControl = Math.min(
            100,
            newSkills.emotionalControl + 5
          );
          break;
        case 'eye_contact':
          newSkills.confidence = Math.min(100, newSkills.confidence + 5);
          break;
        case 'complete_pro':
          Object.keys(newSkills).forEach((key) => {
            newSkills[key as keyof UserSkills] = Math.min(
              100,
              newSkills[key as keyof UserSkills] + 3
            );
          });
          break;
      }

      return newSkills;
    });
  };

  // 自動進行モード（モックデータ用）
  const enableAutoProgress = () => {
    // フィードバック表示を短縮して自動進行
    setTimeout(() => {
      if (feedback) {
        goToNextQuestion();
      }
    }, 2000); // 2秒後に自動で次の質問へ
  };

  useEffect(() => {
    if (feedback && process.env.NODE_ENV === 'development') {
      enableAutoProgress();
    }
  }, [feedback]);

  // 回答中の状態を更新
  useEffect(() => {
    if (feedback) {
      // フィードバックに基づいてユーザー状態を更新
      const confidence = feedback.confidenceScore
        ? feedback.confidenceScore / 10
        : 0.5;
      setUserState((prev) => ({
        ...prev,
        confidence,
        emotionalState: confidence > 0.7 ? 'confident' : 'calm',
      }));
    }
  }, [feedback]);

  return (
    <Box
      minH="100vh"
      w="full"
      px={{ base: 2, md: 4 }}
      py={{ base: 2, md: 8 }}
      role="main"
      aria-label="面接練習アプリケーション"
    >
      {/* アクセシビリティヘルパー */}
      <AccessibilityHelper />
      <AnimatePresence mode="wait">
        {showBreathing ? (
          <motion.div
            key="breathing"
            variants={fadeIn}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <BreathingPrompt onComplete={handleBreathingComplete} />
          </motion.div>
        ) : showChallengeSelector ? (
          <motion.div
            key="challenge-selector"
            variants={fadeIn}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <VStack spacing={6} maxW="container.sm" mx="auto">
              <ChallengeSelector
                onSelect={handleChallengeSelect}
                userPoints={userPoints}
                completedChallenges={completedChallenges}
              />
              <VStack spacing={3}>
                <Button
                  colorScheme="primary"
                  onClick={handleStartInterview}
                  size={{ base: 'md', md: 'lg' }}
                  w="full"
                  maxW="300px"
                >
                  {selectedChallenge
                    ? `「${selectedChallenge.title}」で開始`
                    : '面接を開始'}
                </Button>
                <Button
                  variant="ghost"
                  onClick={handleShowSkillTree}
                  size={{ base: 'sm', md: 'md' }}
                  color="primary.600"
                >
                  🌟 スキルツリーを見る
                </Button>
              </VStack>
            </VStack>
          </motion.div>
        ) : showSkillTree ? (
          <motion.div
            key="skill-tree"
            variants={fadeIn}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <VStack spacing={6} maxW="container.lg" mx="auto">
              <SkillTree
                userSkills={userSkills}
                totalInterviews={totalInterviews}
                averageScore={averageScore}
              />
              <Button
                colorScheme="primary"
                onClick={handleCloseSkillTree}
                size={{ base: 'md', md: 'lg' }}
              >
                戻る
              </Button>
            </VStack>
          </motion.div>
        ) : (
          <motion.div
            key="interview"
            variants={fadeIn}
            initial="initial"
            animate="animate"
          >
            <VStack
              spacing={{ base: 4, md: 8 }}
              maxW="container.sm"
              mx="auto"
              id="main-content"
              role="main"
              aria-label="面接練習メインエリア"
            >
              {/* 進捗表示 */}
              {questions.length > 0 && (
                <Box
                  w="full"
                  p={{ base: 3, md: 4 }}
                  bg="primary.50"
                  borderRadius="lg"
                  borderWidth={1}
                  borderColor="primary.100"
                >
                  <HStack justify="space-between" mb={2}>
                    <Text
                      fontSize={{ base: 'xs', md: 'sm' }}
                      fontWeight="bold"
                      color="primary.700"
                    >
                      面接進捗
                    </Text>
                    <Text
                      fontSize={{ base: 'xs', md: 'sm' }}
                      color="primary.600"
                    >
                      {currentQuestionIndex + 1} / {questions.length}
                    </Text>
                  </HStack>
                  <Progress
                    value={
                      ((currentQuestionIndex + 1) / questions.length) * 100
                    }
                    colorScheme="primary"
                    size={{ base: 'sm', md: 'md' }}
                    borderRadius="full"
                    hasStripe
                    isAnimated
                  />
                </Box>
              )}

              {/* アダプティブアバター */}
              <Box
                w="full"
                h={{ base: '200px', md: '300px', lg: '400px' }}
                maxW="400px"
                mx="auto"
              >
                <AdaptiveAvatar
                  userState={userState}
                  feedback={feedback?.aiAnalysis}
                  isSpeaking={!!feedback?.aiAnalysis}
                />
              </Box>

              {/* 質問表示 */}
              <Box w="full">
                <QuestionDisplay
                  question={currentQuestion}
                  isLoading={isLoading}
                />
              </Box>

              {/* 回答入力またはフィードバック表示 */}
              <Box w="full">
                {!feedback ? (
                  <AutoSpeechInput
                    questionId={currentQuestion?.id || 'unknown'}
                    onTranscriptionComplete={handleTextAnswerSubmit}
                    disabled={isLoading}
                    autoStart={true}
                  />
                ) : (
                  <SafeFeedbackDisplay
                    feedback={feedback}
                    onComplete={handleFeedbackComplete}
                  />
                )}
              </Box>
            </VStack>
          </motion.div>
        )}
      </AnimatePresence>

      {/* スキップリンク */}
      <Box
        position="absolute"
        top="-40px"
        left="6px"
        bg="primary.600"
        color="white"
        px={4}
        py={2}
        borderRadius="md"
        zIndex={1000}
        _focus={{
          top: '6px',
          outline: 'none',
          boxShadow: '0 0 0 3px rgba(37, 99, 235, 0.3)',
        }}
      >
        <Button
          as="a"
          href="#main-content"
          variant="unstyled"
          color="white"
          fontSize="sm"
          _focus={{ outline: 'none' }}
        >
          メインコンテンツにスキップ
        </Button>
      </Box>
    </Box>
  );
};

export default InterviewRoom;
