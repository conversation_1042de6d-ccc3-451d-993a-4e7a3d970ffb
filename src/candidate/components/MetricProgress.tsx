
import React from "react";
import { VStack, HStack, Text, Progress } from "@chakra-ui/react";
import { textStyles } from "./CommonStyles";

export interface MetricProgressProps {
  label: string;
  value: number;
  color?: string;
  description?: string;
}

const MetricProgress: React.FC<MetricProgressProps> = ({ label, value, color = "blue", description }) => (
  <VStack spacing={1} align="stretch">
    <HStack justify="space-between">
      <Text {...textStyles.body} color="gray.600">{label}</Text>
      <Text {...textStyles.body} fontWeight="bold">{value}%</Text>
    </HStack>
    <Progress value={value} colorScheme={color} size="sm" borderRadius="md" />
    {description && (
      <Text fontSize="xs" color="gray.500">{description}</Text>
    )}
  </VStack>
);

export default MetricProgress;
