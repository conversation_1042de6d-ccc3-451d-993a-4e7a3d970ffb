import { test } from 'node:test';
import assert from 'node:assert/strict';
import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import MetricProgress from '../MetricProgress';

test('renders label and value', () => {
  const html = renderToStaticMarkup(<MetricProgress label="Speed" value={75} />);
  assert.ok(html.includes('Speed'));
  assert.ok(html.includes('75%'));
});
