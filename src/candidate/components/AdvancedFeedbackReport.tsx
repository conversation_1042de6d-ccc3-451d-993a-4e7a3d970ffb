/**
 * 高度な分析レポート表示コンポーネント
 * 実践練習の詳細な分析結果とベンチマーク比較
 */
import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  useColorModeValue,
  Heading,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Progress,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  List,
  ListItem,
  ListIcon,
  Divider,
  Icon,
  CircularProgress,
  CircularProgressLabel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import DataService, { ScenarioConfig } from '../lib/unified-data';

interface AdvancedFeedback {
  id: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  communicationSkill: number;
  problemSolvingAbility: number;
  culturalFit: number;
  stressHandling: number;
  technicalAccuracy?: number;
  leadershipPotential?: number;
  strengths: string[];
  improvements: string[];
  nextSteps: string[];
  industryBenchmark?: number;
  levelComparison?: "junior" | "mid" | "senior" | "expert";
}
import {
  CheckCircleIcon,
  WarningIcon,
  StarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  InfoIcon
} from '@chakra-ui/icons';
import { spacing, textStyles, scoreColors, scoreSizes, commonStyles, ScoreBadge } from '@mensetsu-kun/shared/components/CommonStyles';

interface AdvancedFeedbackReportProps {
  feedbacks: AdvancedFeedback[];
  scenarioId: string;
  onClose: () => void;
}

// motion.create()を使用してMotionCardを作成
const MotionCard = motion.create(Card);

const AdvancedFeedbackReport: React.FC<AdvancedFeedbackReportProps> = ({
  feedbacks,
  scenarioId,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const scenario = DataService.getScenarioById(scenarioId);

  // 分析データの計算
  const averageScores = {
    communication: Math.round(
      feedbacks.reduce((sum, f) => sum + f.communicationSkill, 0) / feedbacks.length
    ),
    problemSolving: Math.round(
      feedbacks.reduce((sum, f) => sum + f.problemSolvingAbility, 0) / feedbacks.length
    ),
    culturalFit: Math.round(
      feedbacks.reduce((sum, f) => sum + f.culturalFit, 0) / feedbacks.length
    ),
    stressHandling: Math.round(
      feedbacks.reduce((sum, f) => sum + f.stressHandling, 0) / feedbacks.length
    ),
    technical: feedbacks.some(f => f.technicalAccuracy) 
      ? Math.round(
          feedbacks
            .filter(f => f.technicalAccuracy)
            .reduce((sum, f) => sum + (f.technicalAccuracy || 0), 0) / 
          feedbacks.filter(f => f.technicalAccuracy).length
        )
      : null,
    leadership: feedbacks.some(f => f.leadershipPotential)
      ? Math.round(
          feedbacks
            .filter(f => f.leadershipPotential)
            .reduce((sum, f) => sum + (f.leadershipPotential || 0), 0) / 
          feedbacks.filter(f => f.leadershipPotential).length
        )
      : null
  };

  const overallScore = Math.round(
    (averageScores.communication + averageScores.problemSolving + 
     averageScores.culturalFit + averageScores.stressHandling) / 4
  );

  const industryBenchmark = feedbacks.length > 0 
    ? feedbacks[0].industryBenchmark || 75
    : 75;

  const allStrengths = [...new Set(feedbacks.flatMap(f => f.strengths))];
  const allImprovements = [...new Set(feedbacks.flatMap(f => f.improvements))];
  const allNextSteps = [...new Set(feedbacks.flatMap(f => f.nextSteps))];

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'green';
    if (score >= 6) return 'yellow';
    if (score >= 4) return 'orange';
    return 'red';
  };

  const getPerformanceLevel = (score: number) => {
    if (score >= 9) return { level: "優秀", color: "green" };
    if (score >= 7) return { level: "良好", color: "blue" };
    if (score >= 5) return { level: "標準", color: "yellow" };
    return { level: "要改善", color: "red" };
  };

  const getBenchmarkComparison = (userScore: number, benchmark: number) => {
    const diff = userScore - benchmark;
    if (diff > 5) return { text: "業界平均を大きく上回る", color: "green", icon: ArrowUpIcon };
    if (diff > 0) return { text: "業界平均を上回る", color: "blue", icon: ArrowUpIcon };
    if (diff > -5) return { text: "業界平均レベル", color: "yellow", icon: null };
    return { text: "業界平均を下回る", color: "red", icon: ArrowDownIcon };
  };

  if (!scenario) {
    return <Box>レポートを読み込んでいます...</Box>;
  }

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')} py={spacing.sectionSpacing}>
      <VStack spacing={spacing.sectionSpacing} maxW="container.xl" mx="auto" px={spacing.cardPadding}>
        {/* ヘッダー */}
        <VStack spacing={3} textAlign="center">
          <Heading size={{ base: "lg", md: "xl" }} color={useColorModeValue('primary.700', 'primary.300')}>
            実践練習分析レポート
          </Heading>
          <Text color={useColorModeValue('gray.600', 'gray.300')} fontSize={{ base: "md", md: "lg" }}>
            {scenario.name} - 詳細パフォーマンス分析
          </Text>
          <Badge colorScheme="primary" fontSize="md" px={3} py={1}>
            {feedbacks.length}問回答完了
          </Badge>
        </VStack>

        {/* 総合スコア */}
        <MotionCard
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          w="full"
          bg={useColorModeValue('white', 'gray.800')}
        >
          <CardHeader textAlign="center">
            <VStack spacing={4}>
              <CircularProgress 
                value={overallScore * 10} 
                size="120px" 
                color={`${getScoreColor(overallScore)}.400`}
                thickness="8px"
              >
                <CircularProgressLabel>
                  <VStack spacing={0}>
                    <Text fontSize="2xl" fontWeight="bold" color={`${getScoreColor(overallScore)}.500`}>
                      {overallScore}
                    </Text>
                    <Text fontSize="sm" color="gray.500">/ 10</Text>
                  </VStack>
                </CircularProgressLabel>
              </CircularProgress>
              
              <VStack spacing={1}>
                <Heading size="md">総合評価</Heading>
                <Badge 
                  colorScheme={getPerformanceLevel(overallScore).color} 
                  fontSize="lg" 
                  px={4} 
                  py={2}
                >
                  {getPerformanceLevel(overallScore).level}
                </Badge>
              </VStack>
            </VStack>
          </CardHeader>
        </MotionCard>

        {/* 詳細分析タブ */}
        <Card w="full" bg={useColorModeValue('white', 'gray.800')}>
          <CardBody>
            <Tabs index={activeTab} onChange={setActiveTab}>
              <TabList>
                <Tab>スキル別分析</Tab>
                <Tab>ベンチマーク比較</Tab>
                <Tab>強み・改善点</Tab>
                <Tab>問題別詳細</Tab>
                <Tab>次のステップ</Tab>
              </TabList>
              
              <TabPanels>
                {/* スキル別分析 */}
                <TabPanel px={0}>
                  <VStack spacing={6}>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} w="full">
                      <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                        <StatLabel>コミュニケーション</StatLabel>
                        <StatNumber color={`${getScoreColor(averageScores.communication)}.500`}>
                          {averageScores.communication}/10
                        </StatNumber>
                        <StatHelpText>
                          <Progress 
                            value={averageScores.communication * 10} 
                            colorScheme={getScoreColor(averageScores.communication)}
                            size="sm"
                          />
                        </StatHelpText>
                      </Stat>

                      <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                        <StatLabel>問題解決能力</StatLabel>
                        <StatNumber color={`${getScoreColor(averageScores.problemSolving)}.500`}>
                          {averageScores.problemSolving}/10
                        </StatNumber>
                        <StatHelpText>
                          <Progress 
                            value={averageScores.problemSolving * 10} 
                            colorScheme={getScoreColor(averageScores.problemSolving)}
                            size="sm"
                          />
                        </StatHelpText>
                      </Stat>

                      <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                        <StatLabel>企業適合性</StatLabel>
                        <StatNumber color={`${getScoreColor(averageScores.culturalFit)}.500`}>
                          {averageScores.culturalFit}/10
                        </StatNumber>
                        <StatHelpText>
                          <Progress 
                            value={averageScores.culturalFit * 10} 
                            colorScheme={getScoreColor(averageScores.culturalFit)}
                            size="sm"
                          />
                        </StatHelpText>
                      </Stat>

                      <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                        <StatLabel>ストレス耐性</StatLabel>
                        <StatNumber color={`${getScoreColor(averageScores.stressHandling)}.500`}>
                          {averageScores.stressHandling}/10
                        </StatNumber>
                        <StatHelpText>
                          <Progress 
                            value={averageScores.stressHandling * 10} 
                            colorScheme={getScoreColor(averageScores.stressHandling)}
                            size="sm"
                          />
                        </StatHelpText>
                      </Stat>

                      {averageScores.technical && (
                        <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                          <StatLabel>技術力</StatLabel>
                          <StatNumber color={`${getScoreColor(averageScores.technical)}.500`}>
                            {averageScores.technical}/10
                          </StatNumber>
                          <StatHelpText>
                            <Progress 
                              value={averageScores.technical * 10} 
                              colorScheme={getScoreColor(averageScores.technical)}
                              size="sm"
                            />
                          </StatHelpText>
                        </Stat>
                      )}

                      {averageScores.leadership && (
                        <Stat bg={useColorModeValue('gray.50', 'gray.700')} p={4} borderRadius="lg">
                          <StatLabel>リーダーシップ</StatLabel>
                          <StatNumber color={`${getScoreColor(averageScores.leadership)}.500`}>
                            {averageScores.leadership}/10
                          </StatNumber>
                          <StatHelpText>
                            <Progress 
                              value={averageScores.leadership * 10} 
                              colorScheme={getScoreColor(averageScores.leadership)}
                              size="sm"
                            />
                          </StatHelpText>
                        </Stat>
                      )}
                    </SimpleGrid>
                  </VStack>
                </TabPanel>

                {/* ベンチマーク比較 */}
                <TabPanel px={0}>
                  <VStack spacing={6}>
                    <Alert status="info" borderRadius="md">
                      <AlertIcon />
                      <AlertDescription>
                        業界標準と比較したあなたのパフォーマンスです。
                      </AlertDescription>
                    </Alert>
                    
                    {(() => {
                      const comparison = getBenchmarkComparison(overallScore * 10, industryBenchmark);
                      return (
                        <Card w="full" bg={useColorModeValue(`${comparison.color}.50`, `${comparison.color}.900`)}>
                          <CardBody>
                            <HStack justify="center" spacing={3}>
                              {comparison.icon && <Icon as={comparison.icon} color={`${comparison.color}.500`} />}
                              <VStack spacing={1}>
                                <Text fontSize="lg" fontWeight="bold" color={`${comparison.color}.700`}>
                                  {comparison.text}
                                </Text>
                                <Text fontSize="sm" color={`${comparison.color}.600`}>
                                  あなた: {overallScore * 10}% | 業界平均: {industryBenchmark}%
                                </Text>
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      );
                    })()}

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                      <Card>
                        <CardHeader>
                          <Heading size="sm">同レベル比較</Heading>
                        </CardHeader>
                        <CardBody>
                          <Text fontSize="sm">
                            同じ経験レベルの候補者と比較して、あなたのスコアは
                            <Badge colorScheme="blue" ml={2}>上位30%</Badge>
                            に位置しています。
                          </Text>
                        </CardBody>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Heading size="sm">改善可能性</Heading>
                        </CardHeader>
                        <CardBody>
                          <Text fontSize="sm">
                            特定の分野を重点的に改善することで、
                            <Badge colorScheme="green" ml={2}>+15ポイント</Badge>
                            の向上が期待できます。
                          </Text>
                        </CardBody>
                      </Card>
                    </SimpleGrid>
                  </VStack>
                </TabPanel>

                {/* 強み・改善点 */}
                <TabPanel px={0}>
                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    <Card bg={useColorModeValue('green.50', 'green.900')}>
                      <CardHeader>
                        <HStack>
                          <Icon as={CheckCircleIcon} color="green.500" />
                          <Heading size="md" color="green.700">あなたの強み</Heading>
                        </HStack>
                      </CardHeader>
                      <CardBody>
                        <List spacing={3}>
                          {allStrengths.slice(0, 5).map((strength, index) => (
                            <ListItem key={index}>
                              <ListIcon as={CheckCircleIcon} color="green.500" />
                              <Text fontSize="sm">{strength}</Text>
                            </ListItem>
                          ))}
                        </List>
                      </CardBody>
                    </Card>

                    <Card bg={useColorModeValue('blue.50', 'blue.900')}>
                      <CardHeader>
                        <HStack>
                          <Icon as={InfoIcon} color="blue.500" />
                          <Heading size="md" color="blue.700">改善のポイント</Heading>
                        </HStack>
                      </CardHeader>
                      <CardBody>
                        <List spacing={3}>
                          {allImprovements.slice(0, 5).map((improvement, index) => (
                            <ListItem key={index}>
                              <ListIcon as={WarningIcon} color="blue.500" />
                              <Text fontSize="sm">{improvement}</Text>
                            </ListItem>
                          ))}
                        </List>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </TabPanel>

                {/* 問題別詳細 */}
                <TabPanel px={0}>
                  <TableContainer>
                    <Table variant="simple">
                      <Thead>
                        <Tr>
                          <Th>質問</Th>
                          <Th>コミュニケーション</Th>
                          <Th>問題解決</Th>
                          <Th>総合評価</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {feedbacks.map((feedback, index) => (
                          <Tr key={feedback.id}>
                            <Td>
                              <Text fontSize="sm" fontWeight="medium">
                                質問 {index + 1}
                              </Text>
                            </Td>
                            <Td>
                              <Badge colorScheme={getScoreColor(feedback.communicationSkill)}>
                                {feedback.communicationSkill}/10
                              </Badge>
                            </Td>
                            <Td>
                              <Badge colorScheme={getScoreColor(feedback.problemSolvingAbility)}>
                                {feedback.problemSolvingAbility}/10
                              </Badge>
                            </Td>
                            <Td>
                              <Badge colorScheme={getScoreColor(
                                Math.round((feedback.communicationSkill + feedback.problemSolvingAbility) / 2)
                              )}>
                                {Math.round((feedback.communicationSkill + feedback.problemSolvingAbility) / 2)}/10
                              </Badge>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </TableContainer>
                </TabPanel>

                {/* 次のステップ */}
                <TabPanel px={0}>
                  <VStack spacing={6}>
                    <Card w="full" bg={useColorModeValue('primary.50', 'primary.900')}>
                      <CardHeader>
                        <HStack>
                          <Icon as={StarIcon} color="primary.500" />
                          <Heading size="md" color={useColorModeValue('primary.700', 'primary.300')}>
                            推奨される次のアクション
                          </Heading>
                        </HStack>
                      </CardHeader>
                      <CardBody>
                        <List spacing={3}>
                          {allNextSteps.slice(0, 5).map((step, index) => (
                            <ListItem key={index}>
                              <ListIcon as={CheckCircleIcon} color="primary.500" />
                              <Text fontSize="sm">{step}</Text>
                            </ListItem>
                          ))}
                        </List>
                      </CardBody>
                    </Card>

                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                      <Button colorScheme="primary" size="lg">
                        追加練習を行う
                      </Button>
                      <Button variant="outline" size="lg">
                        レポートを保存
                      </Button>
                    </SimpleGrid>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </CardBody>
        </Card>

        {/* アクションボタン */}
        <HStack spacing={4}>
          <Button colorScheme="primary" size="lg" onClick={onClose}>
            練習を終了
          </Button>
          <Button variant="outline" size="lg" onClick={() => window.print()}>
            レポートを印刷
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};

export default AdvancedFeedbackReport;