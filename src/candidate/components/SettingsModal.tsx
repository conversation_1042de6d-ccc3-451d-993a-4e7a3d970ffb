/**
 * 統合設定モーダルコンポーネント
 * アクセシビリティ設定、面接設定、音声設定などを統合管理
 */
import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  VStack,
  Text,
  HStack,
  Icon,
  useColorMode,
  useColorModeValue,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Switch,
  FormControl,
  FormLabel,
  VisuallyHidden,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Divider,
  Badge,
} from '@chakra-ui/react';
import { 
  SettingsIcon, 
  SunIcon, 
  MoonIcon, 
  ViewIcon,
  TimeIcon,
  InfoIcon,
} from '@chakra-ui/icons';

// アクセシビリティ設定
interface AccessibilitySettings {
  highContrast: boolean;
  fontSize: number;
  reducedMotion: boolean;
  announcements: boolean;
}

// 面接設定
interface InterviewSettings {
  defaultDuration: number;
  autoProgress: boolean;
  feedbackLevel: 'basic' | 'detailed' | 'expert';
  showTimer: boolean;
  pressureLevel: number;
}

// 音声設定
interface AudioSettings {
  micSensitivity: number;
  speechRate: number;
  speechVolume: number;
  voiceLanguage: 'ja-JP' | 'en-US';
  enableSystemNotifications: boolean;
}

// 統合設定
interface UnifiedSettings {
  accessibility: AccessibilitySettings;
  interview: InterviewSettings;
  audio: AudioSettings;
}

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSettingsChange?: (settings: UnifiedSettings) => void;
}

export const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  onSettingsChange,
}) => {
  const { colorMode, toggleColorMode } = useColorMode();
  
  const [settings, setSettings] = useState<UnifiedSettings>({
    accessibility: {
      highContrast: false,
      fontSize: 16,
      reducedMotion: false,
      announcements: true,
    },
    interview: {
      defaultDuration: 30,
      autoProgress: false,
      feedbackLevel: 'detailed',
      showTimer: true,
      pressureLevel: 3,
    },
    audio: {
      micSensitivity: 50,
      speechRate: 50,
      speechVolume: 80,
      voiceLanguage: 'ja-JP',
      enableSystemNotifications: true,
    },
  });

  const [announcement, setAnnouncement] = useState<string>('');

  // 設定変更時の処理
  const handleSettingChange = (
    category: keyof UnifiedSettings,
    key: string,
    value: boolean | number | string
  ) => {
    const newSettings = {
      ...settings,
      [category]: {
        ...settings[category],
        [key]: value,
      },
    };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);

    // 変更を音声で通知
    if (settings.accessibility.announcements) {
      setAnnouncement(`設定が変更されました`);
    }
  };

  // CSS変数でフォントサイズを動的に変更
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--font-size-base', 
      `${settings.accessibility.fontSize}px`
    );
  }, [settings.accessibility.fontSize]);

  // reduced-motionの設定
  useEffect(() => {
    if (settings.accessibility.reducedMotion) {
      document.documentElement.style.setProperty('--motion-reduce', 'reduce');
    } else {
      document.documentElement.style.setProperty('--motion-reduce', 'no-preference');
    }
  }, [settings.accessibility.reducedMotion]);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent maxH="90vh" overflowY="auto">
          <ModalHeader>
            <HStack>
              <Icon as={SettingsIcon} boxSize={5} />
              <Text>設定</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton 
            aria-label="設定を閉じる"
            _focus={{
              boxShadow: "0 0 0 3px rgba(37, 99, 235, 0.3)",
              outline: "none"
            }}
          />
          <ModalBody pb={6}>
            <Tabs variant="enclosed" colorScheme="primary">
              <TabList>
                <Tab>
                  <HStack spacing={2}>
                    <Icon as={ViewIcon} boxSize={4} />
                    <Text>表示</Text>
                  </HStack>
                </Tab>
                <Tab>
                  <HStack spacing={2}>
                    <Icon as={TimeIcon} boxSize={4} />
                    <Text>面接</Text>
                  </HStack>
                </Tab>
                <Tab>
                  <HStack spacing={2}>
                    <Text>🔊</Text>
                    <Text>音声</Text>
                  </HStack>
                </Tab>
              </TabList>

              <TabPanels>
                {/* アクセシビリティ設定タブ */}
                <TabPanel>
                  <VStack spacing={6} align="stretch">
                    
                    {/* ダークモード切り替え - 強調表示 */}
                    <Box 
                      p={4} 
                      bg={colorMode === 'dark' ? 'primary.900' : 'primary.50'} 
                      borderRadius="md" 
                      borderWidth={2}
                      borderColor={colorMode === 'dark' ? 'primary.600' : 'primary.200'}
                    >
                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="dark-mode" mb="0" flex="1">
                          <HStack>
                            <Icon 
                              as={colorMode === 'light' ? MoonIcon : SunIcon} 
                              color={colorMode === 'dark' ? 'primary.300' : 'primary.600'}
                              boxSize={5}
                            />
                            <VStack align="start" spacing={0}>
                              <Text fontWeight="bold" color={colorMode === 'dark' ? 'primary.200' : 'primary.700'}>
                                ダークモード
                              </Text>
                              <Text fontSize="xs" color={colorMode === 'dark' ? 'primary.300' : 'primary.600'}>
                                目の疲労軽減に効果的
                              </Text>
                            </VStack>
                          </HStack>
                        </FormLabel>
                        <Switch
                          id="dark-mode"
                          isChecked={colorMode === 'dark'}
                          onChange={toggleColorMode}
                          colorScheme="primary"
                          size="lg"
                        />
                      </FormControl>
                    </Box>

                    {/* ハイコントラスト */}
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor="high-contrast" mb="0" flex="1">
                        <VStack align="start" spacing={0}>
                          <Text>ハイコントラスト</Text>
                          <Text fontSize="xs" color="gray.500">
                            テキストの見やすさを向上
                          </Text>
                        </VStack>
                      </FormLabel>
                      <Switch
                        id="high-contrast"
                        isChecked={settings.accessibility.highContrast}
                        onChange={(e) => handleSettingChange('accessibility', 'highContrast', e.target.checked)}
                        colorScheme="primary"
                      />
                    </FormControl>

                    <Divider />

                    {/* フォントサイズ */}
                    <FormControl>
                      <FormLabel htmlFor="font-size">
                        <HStack justify="space-between">
                          <Text>文字サイズ</Text>
                          <Badge colorScheme="primary">{settings.accessibility.fontSize}px</Badge>
                        </HStack>
                      </FormLabel>
                      <Slider
                        id="font-size"
                        min={12}
                        max={24}
                        step={1}
                        value={settings.accessibility.fontSize}
                        onChange={(value) => handleSettingChange('accessibility', 'fontSize', value)}
                        colorScheme="primary"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb boxSize={6}>
                          <Box color="primary.500" as={Text} fontSize="xs">A</Box>
                        </SliderThumb>
                      </Slider>
                      <HStack justify="space-between" mt={2}>
                        <Text fontSize="xs" color="gray.500">小</Text>
                        <Text fontSize="xs" color="gray.500">大</Text>
                      </HStack>
                    </FormControl>

                    <Divider />

                    {/* アニメーション軽減 */}
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor="reduced-motion" mb="0" flex="1">
                        <VStack align="start" spacing={0}>
                          <Text>アニメーション軽減</Text>
                          <Text fontSize="xs" color="gray.500">
                            動きを抑えて快適に
                          </Text>
                        </VStack>
                      </FormLabel>
                      <Switch
                        id="reduced-motion"
                        isChecked={settings.accessibility.reducedMotion}
                        onChange={(e) => handleSettingChange('accessibility', 'reducedMotion', e.target.checked)}
                        colorScheme="primary"
                      />
                    </FormControl>

                    {/* 音声案内 */}
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor="announcements" mb="0" flex="1">
                        <VStack align="start" spacing={0}>
                          <Text>音声案内</Text>
                          <Text fontSize="xs" color="gray.500">
                            操作時の音声ガイド
                          </Text>
                        </VStack>
                      </FormLabel>
                      <Switch
                        id="announcements"
                        isChecked={settings.accessibility.announcements}
                        onChange={(e) => handleSettingChange('accessibility', 'announcements', e.target.checked)}
                        colorScheme="primary"
                      />
                    </FormControl>
                  </VStack>
                </TabPanel>

                {/* 面接設定タブ */}
                <TabPanel>
                  <VStack spacing={6} align="stretch">
                    
                    {/* デフォルト面接時間 */}
                    <FormControl>
                      <FormLabel htmlFor="default-duration">
                        <HStack justify="space-between">
                          <Text>デフォルト面接時間</Text>
                          <Badge colorScheme="blue">{settings.interview.defaultDuration}分</Badge>
                        </HStack>
                      </FormLabel>
                      <NumberInput
                        id="default-duration"
                        min={5}
                        max={120}
                        step={5}
                        value={settings.interview.defaultDuration}
                        onChange={(_, value) => handleSettingChange('interview', 'defaultDuration', value)}
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </FormControl>

                    <Divider />

                    {/* フィードバックレベル */}
                    <FormControl>
                      <FormLabel htmlFor="feedback-level">フィードバックレベル</FormLabel>
                      <Select
                        id="feedback-level"
                        value={settings.interview.feedbackLevel}
                        onChange={(e) => handleSettingChange('interview', 'feedbackLevel', e.target.value)}
                      >
                        <option value="basic">基本（簡潔なフィードバック）</option>
                        <option value="detailed">詳細（推奨）</option>
                        <option value="expert">専門的（詳細な分析）</option>
                      </Select>
                    </FormControl>

                    <Divider />

                    {/* プレッシャーレベル */}
                    <FormControl>
                      <FormLabel htmlFor="pressure-level">
                        <HStack justify="space-between">
                          <Text>プレッシャーレベル</Text>
                          <Badge colorScheme="orange">レベル {settings.interview.pressureLevel}</Badge>
                        </HStack>
                      </FormLabel>
                      <Slider
                        id="pressure-level"
                        min={1}
                        max={5}
                        step={1}
                        value={settings.interview.pressureLevel}
                        onChange={(value) => handleSettingChange('interview', 'pressureLevel', value)}
                        colorScheme="orange"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb boxSize={6} />
                      </Slider>
                      <HStack justify="space-between" mt={2}>
                        <Text fontSize="xs" color="gray.500">リラックス</Text>
                        <Text fontSize="xs" color="gray.500">本格的</Text>
                      </HStack>
                    </FormControl>

                    <Divider />

                    {/* その他のオプション */}
                    <VStack spacing={4} align="stretch">
                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="auto-progress" mb="0" flex="1">
                          <VStack align="start" spacing={0}>
                            <Text>自動進行</Text>
                            <Text fontSize="xs" color="gray.500">
                              回答後自動で次の質問へ
                            </Text>
                          </VStack>
                        </FormLabel>
                        <Switch
                          id="auto-progress"
                          isChecked={settings.interview.autoProgress}
                          onChange={(e) => handleSettingChange('interview', 'autoProgress', e.target.checked)}
                          colorScheme="primary"
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="show-timer" mb="0" flex="1">
                          <VStack align="start" spacing={0}>
                            <Text>タイマー表示</Text>
                            <Text fontSize="xs" color="gray.500">
                              残り時間を表示
                            </Text>
                          </VStack>
                        </FormLabel>
                        <Switch
                          id="show-timer"
                          isChecked={settings.interview.showTimer}
                          onChange={(e) => handleSettingChange('interview', 'showTimer', e.target.checked)}
                          colorScheme="primary"
                        />
                      </FormControl>
                    </VStack>
                  </VStack>
                </TabPanel>

                {/* 音声設定タブ */}
                <TabPanel>
                  <VStack spacing={6} align="stretch">
                    
                    {/* マイク感度 */}
                    <FormControl>
                      <FormLabel htmlFor="mic-sensitivity">
                        <HStack justify="space-between">
                          <Text>マイク感度</Text>
                          <Badge colorScheme="green">{settings.audio.micSensitivity}%</Badge>
                        </HStack>
                      </FormLabel>
                      <Slider
                        id="mic-sensitivity"
                        min={0}
                        max={100}
                        step={5}
                        value={settings.audio.micSensitivity}
                        onChange={(value) => handleSettingChange('audio', 'micSensitivity', value)}
                        colorScheme="green"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb boxSize={6} />
                      </Slider>
                    </FormControl>

                    <Divider />

                    {/* 音声合成速度 */}
                    <FormControl>
                      <FormLabel htmlFor="speech-rate">
                        <HStack justify="space-between">
                          <Text>音声速度</Text>
                          <Badge colorScheme="blue">{settings.audio.speechRate}%</Badge>
                        </HStack>
                      </FormLabel>
                      <Slider
                        id="speech-rate"
                        min={25}
                        max={200}
                        step={5}
                        value={settings.audio.speechRate}
                        onChange={(value) => handleSettingChange('audio', 'speechRate', value)}
                        colorScheme="blue"
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <SliderThumb boxSize={6} />
                      </Slider>
                      <HStack justify="space-between" mt={2}>
                        <Text fontSize="xs" color="gray.500">ゆっくり</Text>
                        <Text fontSize="xs" color="gray.500">早口</Text>
                      </HStack>
                    </FormControl>

                    <Divider />

                    {/* 音声言語 */}
                    <FormControl>
                      <FormLabel htmlFor="voice-language">音声言語</FormLabel>
                      <Select
                        id="voice-language"
                        value={settings.audio.voiceLanguage}
                        onChange={(e) => handleSettingChange('audio', 'voiceLanguage', e.target.value)}
                      >
                        <option value="ja-JP">日本語</option>
                        <option value="en-US">英語（米国）</option>
                      </Select>
                    </FormControl>

                    <Divider />

                    {/* システム通知 */}
                    <FormControl display="flex" alignItems="center">
                      <FormLabel htmlFor="system-notifications" mb="0" flex="1">
                        <VStack align="start" spacing={0}>
                          <Text>システム通知</Text>
                          <Text fontSize="xs" color="gray.500">
                            デスクトップ通知を有効化
                          </Text>
                        </VStack>
                      </FormLabel>
                      <Switch
                        id="system-notifications"
                        isChecked={settings.audio.enableSystemNotifications}
                        onChange={(e) => handleSettingChange('audio', 'enableSystemNotifications', e.target.checked)}
                        colorScheme="primary"
                      />
                    </FormControl>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>

            {/* 設定リセットボタン */}
            <Box mt={6} pt={4} borderTopWidth={1} borderColor={borderColor}>
              <HStack justify="space-between">
                <Text fontSize="sm" color="gray.500">
                  設定は自動的に保存されます
                </Text>
                <Button 
                  size="sm" 
                  variant="outline" 
                  colorScheme="red"
                  onClick={() => {
                    // 設定リセット処理
                    if (confirm('設定をリセットしますか？')) {
                      setAnnouncement('設定がリセットされました');
                    }
                  }}
                >
                  リセット
                </Button>
              </HStack>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* ライブアナウンス領域 */}
      <Box 
        aria-live="polite" 
        aria-atomic="true" 
        position="absolute" 
        left="-10000px" 
        width="1px" 
        height="1px" 
        overflow="hidden"
      >
        {announcement}
      </Box>
    </>
  );
};

// 設定フック
export const useSettings = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  return {
    isOpen,
    onOpen,
    onClose,
  };
};