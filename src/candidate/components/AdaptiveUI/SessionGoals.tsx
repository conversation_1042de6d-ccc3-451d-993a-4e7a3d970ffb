/**
 * セッション目標設定コンポーネント
 * フロー理論に基づく目標設定システム
 * 適切な挑戦レベルとスキルバランスを提供
 */
import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Checkbox,
  Progress,
  useColorModeValue,
  Icon,
  Tooltip,
  Heading,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { CheckCircleIcon, TimeIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useUserAdaptive, PersonalGoal } from '../../shared/contexts/UserAdaptiveContext';

const MotionCard = motion.create(Card);

// セッション目標の型定義
interface SessionGoal {
  id: string;
  title: string;
  description: string;
  category: 'communication' | 'technical' | 'behavioral' | 'confidence';
  difficulty: 'easy' | 'moderate' | 'challenging';
  estimatedTime: number; // 分
  achievabilityScore: number; // 0-1
  relevanceScore: number; // 0-1
  icon: React.ComponentType;
}

// カテゴリ別の色設定
const CATEGORY_COLORS = {
  communication: 'blue',
  technical: 'green',
  behavioral: 'purple',
  confidence: 'orange',
} as const;

// 難易度別の色設定
const DIFFICULTY_COLORS = {
  easy: 'green',
  moderate: 'yellow',
  challenging: 'red',
} as const;

interface SessionGoalsProps {
  scenarioId: string;
  onGoalsSelected: (goals: SessionGoal[]) => void;
  maxGoals?: number;
}

export const SessionGoals: React.FC<SessionGoalsProps> = ({
  scenarioId,
  onGoalsSelected,
  maxGoals = 3,
}) => {
  const { state, actions } = useUserAdaptive();
  const [selectedGoals, setSelectedGoals] = useState<SessionGoal[]>([]);
  
  // カラーモード対応
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const selectedBorderColor = useColorModeValue('primary.300', 'primary.500');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const mutedTextColor = useColorModeValue('gray.500', 'gray.400');

  // ユーザーレベルに基づく推奨目標生成
  const suggestedGoals = useMemo(() => {
    return generateOptimalGoals(state.experienceLevel, state.personalGoals, scenarioId);
  }, [state.experienceLevel, state.personalGoals, scenarioId]);

  const handleGoalToggle = (goal: SessionGoal) => {
    setSelectedGoals(prev => {
      const isSelected = prev.some(g => g.id === goal.id);
      
      if (isSelected) {
        return prev.filter(g => g.id !== goal.id);
      } else if (prev.length < maxGoals) {
        return [...prev, goal];
      }
      
      return prev;
    });
  };

  const handleConfirmGoals = () => {
    onGoalsSelected(selectedGoals);
    
    // 選択した目標を個人目標に追加（まだ存在しない場合）
    selectedGoals.forEach(goal => {
      const existingGoal = state.personalGoals.find((pg: PersonalGoal) => 
        pg.title === goal.title && pg.category === goal.category
      );
      
      if (!existingGoal) {
        actions.addPersonalGoal({
          title: goal.title,
          description: goal.description,
          category: goal.category,
          isCompleted: false,
          progress: 0,
        });
      }
    });
  };

  const getCategoryIcon = (category: SessionGoal['category']) => {
    switch (category) {
      case 'communication':
        return '💬';
      case 'technical':
        return '⚙️';
      case 'behavioral':
        return '🎭';
      case 'confidence':
        return '🌟';
      default:
        return '📌';
    }
  };

  const estimatedTotalTime = selectedGoals.reduce((sum, goal) => sum + goal.estimatedTime, 0);
  const averageDifficulty = selectedGoals.length > 0 
    ? selectedGoals.reduce((sum, goal) => {
        const difficultyMap = { easy: 1, moderate: 2, challenging: 3 };
        return sum + difficultyMap[goal.difficulty];
      }, 0) / selectedGoals.length
    : 0;

  return (
    <VStack spacing={6} align="stretch">
      {/* ヘッダー */}
      <VStack spacing={2} textAlign="center">
        <Heading size="lg" color="primary.600">
          今回の練習目標を選んでください
        </Heading>
        <Text color={mutedTextColor}>
          あなたのレベルに合わせて、適切な挑戦目標を提案しています
        </Text>
        <Text fontSize="sm" color={mutedTextColor}>
          最大{maxGoals}つまで選択できます（{selectedGoals.length}/{maxGoals}）
        </Text>
      </VStack>

      {/* 目標一覧 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
        <AnimatePresence>
          {suggestedGoals.map((goal) => {
            const isSelected = selectedGoals.some(g => g.id === goal.id);
            const isDisabled = !isSelected && selectedGoals.length >= maxGoals;
            
            return (
              <MotionCard
                key={goal.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                bg={cardBg}
                borderWidth={2}
                borderColor={isSelected ? selectedBorderColor : borderColor}
                cursor={isDisabled ? 'not-allowed' : 'pointer'}
                opacity={isDisabled ? 0.6 : 1}
                _hover={!isDisabled ? {
                  borderColor: selectedBorderColor,
                  transform: 'translateY(-2px)',
                  boxShadow: 'lg',
                } : {}}
                onClick={() => !isDisabled && handleGoalToggle(goal)}
              >
                <CardBody>
                  <VStack spacing={3} align="start">
                    {/* ヘッダー */}
                    <HStack justify="space-between" w="full">
                      <HStack spacing={2}>
                        <Text fontSize="lg">{getCategoryIcon(goal.category)}</Text>
                        <Checkbox
                          isChecked={isSelected}
                          isDisabled={isDisabled}
                          colorScheme="primary"
                          onChange={() => !isDisabled && handleGoalToggle(goal)}
                        />
                      </HStack>
                      <Badge
                        colorScheme={DIFFICULTY_COLORS[goal.difficulty]}
                        variant="subtle"
                        fontSize="xs"
                      >
                        {goal.difficulty === 'easy' ? '易' : 
                         goal.difficulty === 'moderate' ? '中' : '高'}
                      </Badge>
                    </HStack>

                    {/* タイトルと説明 */}
                    <VStack spacing={1} align="start">
                      <Text
                        fontWeight="bold"
                        fontSize="md"
                        color={textColor}
                        lineHeight="1.3"
                      >
                        {goal.title}
                      </Text>
                      <Text
                        fontSize="sm"
                        color={mutedTextColor}
                        lineHeight="1.4"
                      >
                        {goal.description}
                      </Text>
                    </VStack>

                    {/* メタ情報 */}
                    <HStack justify="space-between" w="full">
                      <Badge
                        colorScheme={CATEGORY_COLORS[goal.category]}
                        variant="outline"
                        fontSize="xs"
                      >
                        {goal.category === 'communication' ? 'コミュニケーション' :
                         goal.category === 'technical' ? '技術' :
                         goal.category === 'behavioral' ? '行動' : '自信'}
                      </Badge>
                      
                      <HStack spacing={2} fontSize="xs" color={mutedTextColor}>
                        <Icon as={TimeIcon} />
                        <Text>{goal.estimatedTime}分</Text>
                      </HStack>
                    </HStack>

                    {/* 達成可能性インジケーター */}
                    <Box w="full">
                      <HStack justify="space-between" mb={1}>
                        <Text fontSize="xs" color={mutedTextColor}>
                          達成可能性
                        </Text>
                        <Text fontSize="xs" color={mutedTextColor}>
                          {Math.round(goal.achievabilityScore * 100)}%
                        </Text>
                      </HStack>
                      <Progress
                        value={goal.achievabilityScore * 100}
                        size="sm"
                        colorScheme="green"
                        borderRadius="full"
                      />
                    </Box>
                  </VStack>
                </CardBody>
              </MotionCard>
            );
          })}
        </AnimatePresence>
      </SimpleGrid>

      {/* 選択状況の表示 */}
      {selectedGoals.length > 0 && (
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            選択した目標：{selectedGoals.length}個 | 
            予想時間：{estimatedTotalTime}分 | 
            難易度：{averageDifficulty < 1.5 ? '易しい' : 
                    averageDifficulty < 2.5 ? '適度' : 'チャレンジング'}
          </AlertDescription>
        </Alert>
      )}

      {/* 確定ボタン */}
      <HStack justify="center">
        <Button
          colorScheme="primary"
          size="lg"
          isDisabled={selectedGoals.length === 0}
          onClick={handleConfirmGoals}
          leftIcon={<CheckCircleIcon />}
          px={8}
        >
          この目標で練習を開始する
        </Button>
      </HStack>
    </VStack>
  );
};

/**
 * ユーザーレベルに基づいて最適な目標を生成
 */
function generateOptimalGoals(
  experienceLevel: 'beginner' | 'intermediate' | 'advanced',
  personalGoals: PersonalGoal[],
  scenarioId: string
): SessionGoal[] {
  const baseGoals: Omit<SessionGoal, 'achievabilityScore' | 'relevanceScore'>[] = [
    // コミュニケーション系
    {
      id: 'comm_clear_expression',
      title: '明確な自己表現',
      description: '考えを整理して、相手に分かりやすく伝える',
      category: 'communication',
      difficulty: 'easy',
      estimatedTime: 15,
      icon: () => null,
    },
    {
      id: 'comm_active_listening',
      title: '積極的な傾聴',
      description: '質問を正確に理解し、適切に反応する',
      category: 'communication',
      difficulty: 'moderate',
      estimatedTime: 20,
      icon: () => null,
    },
    {
      id: 'comm_storytelling',
      title: 'ストーリーテリング',
      description: 'エピソードを使って印象的に話す',
      category: 'communication',
      difficulty: 'challenging',
      estimatedTime: 25,
      icon: () => null,
    },
    
    // 技術系
    {
      id: 'tech_problem_solving',
      title: '問題解決の説明',
      description: '技術的な課題をどう解決するか論理的に説明',
      category: 'technical',
      difficulty: 'moderate',
      estimatedTime: 20,
      icon: () => null,
    },
    {
      id: 'tech_experience_sharing',
      title: '技術経験の共有',
      description: '過去の技術的な取り組みを具体的に説明',
      category: 'technical',
      difficulty: 'easy',
      estimatedTime: 15,
      icon: () => null,
    },
    
    // 行動系
    {
      id: 'behav_teamwork',
      title: 'チームワークの実証',
      description: 'チームでの協力経験を具体的に説明',
      category: 'behavioral',
      difficulty: 'moderate',
      estimatedTime: 18,
      icon: () => null,
    },
    {
      id: 'behav_leadership',
      title: 'リーダーシップの発揮',
      description: '主体的に行動した経験を印象的に伝える',
      category: 'behavioral',
      difficulty: 'challenging',
      estimatedTime: 22,
      icon: () => null,
    },
    
    // 自信系
    {
      id: 'conf_nervous_control',
      title: '緊張のコントロール',
      description: '落ち着いて、自信を持って話す',
      category: 'confidence',
      difficulty: 'easy',
      estimatedTime: 12,
      icon: () => null,
    },
    {
      id: 'conf_positive_attitude',
      title: 'ポジティブな姿勢',
      description: '前向きで建設的な態度を保つ',
      category: 'confidence',
      difficulty: 'easy',
      estimatedTime: 10,
      icon: () => null,
    },
  ];

  // 経験レベルに基づいて目標をフィルタリングと調整
  const filteredGoals = baseGoals.map(goal => ({
    ...goal,
    achievabilityScore: calculateAchievabilityScore(goal, experienceLevel, personalGoals),
    relevanceScore: calculateRelevanceScore(goal, scenarioId, personalGoals),
  }))
  .filter(goal => {
    // 経験レベルに応じた難易度フィルタ
    if (experienceLevel === 'beginner' && goal.difficulty === 'challenging') {
      return false;
    }
    if (experienceLevel === 'advanced' && goal.difficulty === 'easy') {
      return goal.category === 'confidence'; // 上級者でも自信系は有効
    }
    return true;
  })
  .sort((a, b) => {
    // 達成可能性と関連性のスコアでソート
    const scoreA = (a.achievabilityScore + a.relevanceScore) / 2;
    const scoreB = (b.achievabilityScore + b.relevanceScore) / 2;
    return scoreB - scoreA;
  })
  .slice(0, 6); // 最大6個の目標を提案

  return filteredGoals;
}

/**
 * 達成可能性スコアを計算
 */
function calculateAchievabilityScore(
  goal: Omit<SessionGoal, 'achievabilityScore' | 'relevanceScore'>,
  experienceLevel: 'beginner' | 'intermediate' | 'advanced',
  personalGoals: PersonalGoal[]
): number {
  let score = 0.5; // ベーススコア
  
  // 経験レベルに基づく調整
  const levelMultiplier = {
    beginner: { easy: 0.8, moderate: 0.6, challenging: 0.4 },
    intermediate: { easy: 0.9, moderate: 0.8, challenging: 0.6 },
    advanced: { easy: 0.95, moderate: 0.9, challenging: 0.8 },
  };
  
  score *= levelMultiplier[experienceLevel][goal.difficulty];
  
  // 個人目標の進捗に基づく調整
  const relatedGoal = personalGoals.find(pg => pg.category === goal.category);
  if (relatedGoal) {
    score += (relatedGoal.progress / 100) * 0.2; // 最大0.2のボーナス
  }
  
  return Math.min(1, Math.max(0.1, score));
}

/**
 * 関連性スコアを計算
 */
function calculateRelevanceScore(
  goal: Omit<SessionGoal, 'achievabilityScore' | 'relevanceScore'>,
  scenarioId: string,
  personalGoals: PersonalGoal[]
): number {
  let score = 0.5; // ベーススコア
  
  // シナリオに基づく調整
  if (scenarioId.includes('technical') && goal.category === 'technical') {
    score += 0.3;
  }
  if (scenarioId.includes('behavioral') && goal.category === 'behavioral') {
    score += 0.3;
  }
  if (scenarioId.includes('basic') && goal.category === 'communication') {
    score += 0.2;
  }
  
  // 個人目標との関連性
  const hasRelatedGoal = personalGoals.some(pg => 
    pg.category === goal.category && !pg.isCompleted
  );
  if (hasRelatedGoal) {
    score += 0.2;
  }
  
  return Math.min(1, Math.max(0.1, score));
}