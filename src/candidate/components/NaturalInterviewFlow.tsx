/**
 * 自然な面接フロー - 最後にまとめてフィードバック
 * 中断・再開機能とヒント機能付き
 */
import React, { useState, useEffect } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Button, 
  Progress, 
  useToast,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { AdaptiveAvatar, UserState } from './AdaptiveAvatar';
import QuestionDisplay from './QuestionDisplay';
import AutoAnswerInput from './AutoAnswerInput';
import { AccessibilityHelper } from './AccessibilityHelper';
import { UnifiedMockDataService, type BasicQuestion, type BasicFeedback } from '../data/mockApiData';
import { FinalFeedbackView } from './FinalFeedbackView';
import { fadeIn } from '../motion';

// 統合APIの型を使用
type Question = BasicQuestion;
type Feedback = BasicFeedback & {
  questionId: string;
  questionText: string;
};

const NaturalInterviewFlow: React.FC = () => {
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [allFeedbacks, setAllFeedbacks] = useState<Feedback[]>([]);
  const [isInterviewPaused, setIsInterviewPaused] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [isInterviewComplete, setIsInterviewComplete] = useState(false);
  const [showFinalFeedback, setShowFinalFeedback] = useState(false);
  const [userState, setUserState] = useState<UserState>({
    confidence: 0.5,
    isPaused: false,
    speakingSpeed: "normal",
    emotionalState: "calm",
  });
  const toast = useToast();

  /**
   * モックデータから質問を取得する
   */
  const fetchQuestions = async () => {
    try {
      setIsLoading(true);
      console.log('📋 質問を取得中...');
      
      const mockQuestions = await UnifiedMockDataService.getBasicQuestions();
      setQuestions(mockQuestions);
      
      if (mockQuestions.length > 0) {
        setCurrentQuestion(mockQuestions[0]);
        console.log('✅ 質問を取得しました:', mockQuestions[0].text);
      }
    } catch (error: unknown) {
      console.error('質問取得エラー:', error);
      const errorMessage = error instanceof Error ? error.message : '不明なエラーが発生しました';
      toast({
        title: 'エラー',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 次の質問に進む
   */
  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);
      setCurrentQuestion(questions[nextIndex]);
      console.log(`📋 次の質問に進みました (${nextIndex + 1}/${questions.length})`);
    } else {
      console.log('🎉 すべての質問が完了しました');
      setIsInterviewComplete(true);
      toast({
        title: '面接完了',
        description: 'お疲れ様でした！最後にまとめてフィードバックをご確認ください。',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  /**
   * 回答音声の解析処理（バックグラウンド処理）
   */
  const handleAnswerSubmit = async (audioBlob: Blob) => {
    try {
      console.log('🎤 音声解析を開始します:', {
        size: audioBlob.size,
        type: audioBlob.type
      });

      // UIでは何も表示せず、バックグラウンドで解析
      const mockFile = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
      const result = await UnifiedMockDataService.analyzeAudio(mockFile);

      console.log('✅ 音声解析完了:', result);
      
      // フィードバックをバックグラウンドで蓄積
      const enhancedFeedback: Feedback = {
        ...result.feedback,
        questionId: currentQuestion?.id || '',
        questionText: currentQuestion?.text || ''
      };
      
      setAllFeedbacks(prev => [...prev, enhancedFeedback]);
      
      // サンプル回答の場合は即座に、実際の回答の場合は少し間を開けて次の質問へ
      const isSampleAnswer = audioBlob.size <= 1024; // サンプル回答の判定
      const delay = isSampleAnswer ? 500 : 2000; // サンプルは0.5秒、実際は2秒
      
      setTimeout(() => {
        goToNextQuestion();
      }, delay);

    } catch (error: unknown) {
      console.error('音声解析エラー:', error);
      const errorMessage = error instanceof Error ? error.message : '音声の解析に失敗しました';
      toast({
        title: 'エラー',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 面接一時停止
  const handlePauseInterview = () => {
    setIsInterviewPaused(true);
    setUserState(prev => ({ ...prev, isPaused: true }));
  };

  // 面接再開
  const handleResumeInterview = () => {
    setIsInterviewPaused(false);
    setShowHints(false);
    setUserState(prev => ({ ...prev, isPaused: false }));
  };

  // 最終フィードバック画面への遷移
  const handleViewFinalFeedback = () => {
    console.log('最終フィードバックを表示:', allFeedbacks);
    setShowFinalFeedback(true);
  };

  // フィードバック画面を閉じる
  const handleCloseFeedback = () => {
    // ダッシュボードに戻る
    window.location.href = '/candidate-dashboard';
  };

  // 質問別のヒントを生成
  const generateHint = (question: Question | null): string => {
    if (!question) return '';
    
    const hints = {
      '自己紹介': '具体的な経験やエピソードを交えて話しましょう。「いつ・どこで・何を・どのように」を意識してください。',
      '志望動機': 'なぜその会社なのか、なぜその職種なのかを明確に。あなたの価値観と会社の価値観の一致点を伝えましょう。',
      '強み': '具体的な成果や数字を交えて説明しましょう。その強みをどう活かせるかも併せて伝えてください。',
    };
    
    // 質問文からキーワードを抽出してヒントを選択
    for (const [key, hint] of Object.entries(hints)) {
      if (question.text.includes(key)) {
        return hint;
      }
    }
    
    return 'STAR法（状況・課題・行動・結果）を使って構造的に回答してみましょう。具体的なエピソードがあると説得力が増します。';
  };

  useEffect(() => {
    fetchQuestions();
  }, []);

  // フィードバック表示中の場合
  if (showFinalFeedback) {
    return (
      <FinalFeedbackView 
        feedbacks={allFeedbacks}
        onClose={handleCloseFeedback}
      />
    );
  }

  return (
    <Box minH="100vh" w="full" px={{ base: 4, md: 6, lg: 8 }} py={{ base: 4, md: 8 }}>
      {/* アクセシビリティヘルパー */}
      <AccessibilityHelper />
      
      <VStack 
        spacing={{ base: 4, md: 8 }} 
        maxW={{ base: "full", md: "container.md", lg: "container.lg" }}
        mx="auto"
        id="main-content"
        tabIndex={-1}
        outline="none"
      >
        {/* 進捗表示 */}
        {questions.length > 0 && !isInterviewComplete && (
          <Box 
            w="full" 
            p={{ base: 4, md: 6 }} 
            bg={useColorModeValue('primary.50', 'gray.800')}
            borderRadius="lg" 
            borderWidth={1} 
            borderColor={useColorModeValue('primary.100', 'gray.700')}
          >
            <HStack justify="space-between" mb={2}>
              <Text 
                fontSize={{ base: "sm", md: "md" }} 
                fontWeight="bold" 
                color={useColorModeValue('primary.700', 'primary.300')}
              >
                面接進捗
              </Text>
              <Text 
                fontSize={{ base: "sm", md: "md" }} 
                color={useColorModeValue('primary.600', 'primary.400')}
              >
                {currentQuestionIndex + 1} / {questions.length}
              </Text>
            </HStack>
            <Progress 
              value={(currentQuestionIndex + 1) / questions.length * 100} 
              colorScheme="primary" 
              size={{ base: "sm", md: "md" }}
              borderRadius="full"
              hasStripe
              isAnimated
            />
          </Box>
        )}

        {/* メインコンテンツ */}
        <AnimatePresence mode="wait">
          {isInterviewComplete ? (
            <motion.div
              key="complete"
              variants={fadeIn}
              initial="initial"
              animate="animate"
            >
              <VStack spacing={6} textAlign="center">
                <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color={useColorModeValue('primary.700', 'primary.300')}>
                  面接お疲れ様でした！
                </Text>
                <Text color={useColorModeValue('gray.600', 'gray.300')} fontSize={{ base: "md", md: "lg" }}>
                  全ての質問にお答えいただき、ありがとうございました。
                  AIが分析した詳細なフィードバックをご確認ください。
                </Text>
                <Box
                  p={{ base: 4, md: 6 }}
                  bg={useColorModeValue('green.50', 'green.900')}
                  borderRadius="md"
                  borderWidth={1}
                  borderColor={useColorModeValue('green.200', 'green.700')}
                >
                  <Text color={useColorModeValue('green.700', 'green.300')} fontSize={{ base: "sm", md: "md" }}>
                    ✨ {allFeedbacks.length}つの質問への回答を分析しました
                  </Text>
                </Box>
                <Button
                  colorScheme="primary"
                  size="lg"
                  onClick={handleViewFinalFeedback}
                  px={8}
                >
                  詳細なフィードバックを見る
                </Button>
              </VStack>
            </motion.div>
          ) : isInterviewPaused ? (
            <motion.div
              key="paused"
              variants={fadeIn}
              initial="initial"
              animate="animate"
            >
              <VStack spacing={6}>
                <Box 
                  w="full" 
                  h={{ base: "200px", md: "300px" }}
                  maxW="400px"
                  mx="auto"
                >
                  <AdaptiveAvatar 
                    userState={userState}
                    isSpeaking={false}
                  />
                </Box>
                
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <AlertDescription>
                    面接を一時停止中です。お時間のあるときに再開してください。
                  </AlertDescription>
                </Alert>
                
                {showHints && currentQuestion && (
                  <Box p={4} bg="blue.50" borderRadius="md" w="full">
                    <VStack spacing={3}>
                      <Text fontWeight="bold" color="blue.700">
                        💡 回答のヒント
                      </Text>
                      <Text fontSize="sm" color="blue.600" textAlign="center">
                        {generateHint(currentQuestion)}
                      </Text>
                    </VStack>
                  </Box>
                )}
                
                <VStack spacing={3}>
                  <Button
                    colorScheme="primary"
                    onClick={handleResumeInterview}
                    size="lg"
                  >
                    面接を再開
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowHints(!showHints)}
                    size="sm"
                  >
                    {showHints ? 'ヒントを隠す' : 'ヒントを見る'}
                  </Button>
                </VStack>
              </VStack>
            </motion.div>
          ) : (
            <motion.div
              key="interview"
              variants={fadeIn}
              initial="initial"
              animate="animate"
            >
              <VStack spacing={{ base: 4, md: 6 }}>
                {/* アダプティブアバター */}
                <Box 
                  w="full" 
                  h={{ base: "200px", md: "300px", lg: "400px" }}
                  maxW="400px"
                  mx="auto"
                >
                  <AdaptiveAvatar 
                    userState={userState}
                    isSpeaking={false}
                  />
                </Box>
                
                {/* 質問表示 */}
                <Box w="full">
                  <QuestionDisplay question={currentQuestion} isLoading={isLoading} />
                </Box>
                
                {/* 回答入力エリア */}
                <Box w="full">
                  <VStack spacing={4}>
                    <AutoAnswerInput 
                      onSubmit={handleAnswerSubmit} 
                      isLoading={isLoading} 
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePauseInterview}
                      color="gray.500"
                      _hover={{ color: "gray.700" }}
                    >
                      少し考える時間が欲しい
                    </Button>
                  </VStack>
                </Box>
              </VStack>
            </motion.div>
          )}
        </AnimatePresence>
      </VStack>
    </Box>
  );
};

export default NaturalInterviewFlow;