/**
 * ステップ式フォームコンポーネント
 * 複数ステップからなるフォームの進行管理とナビゲーションを提供
 */
import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Button,
  Text,
  Progress,
  useColorModeValue,
} from '@chakra-ui/react';
import { ArrowForwardIcon, ArrowBackIcon } from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';

const MotionBox = motion(Box);

export interface Step {
  title: string;
  description: string;
  component: React.ReactNode;
}

interface StepperFormProps {
  steps: Step[];
  currentStep: number;
  onNext: () => void;
  onBack: () => void;
  canProceed: boolean;
  nextButtonText?: string;
  backButtonText?: string;
  finalButtonText?: string;
  showProgress?: boolean;
  onComplete?: () => void;
}

const StepperForm: React.FC<StepperFormProps> = ({
  steps,
  currentStep,
  onNext,
  onBack,
  canProceed,
  nextButtonText = "次へ",
  backButtonText = "前へ",
  finalButtonText = "完了",
  showProgress = true,
  onComplete,
}) => {
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      onNext();
    } else if (onComplete) {
      onComplete();
    }
  };

  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <VStack spacing={6} w="full">
      {/* 進捗バー */}
      {showProgress && (
        <Box w="full">
          <HStack justify="space-between" mb={2}>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              ステップ {currentStep + 1} / {steps.length}
            </Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              {steps[currentStep]?.title}
            </Text>
          </HStack>
          <Progress
            value={((currentStep + 1) / steps.length) * 100}
            colorScheme="primary"
            borderRadius="full"
            hasStripe
            isAnimated
          />
        </Box>
      )}

      {/* ステップコンテンツ */}
      <AnimatePresence mode="wait">
        <MotionBox
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          w="full"
        >
          {steps[currentStep]?.component}
        </MotionBox>
      </AnimatePresence>

      {/* ナビゲーションボタン */}
      <HStack justify="space-between" w="full">
        <Button
          variant="ghost"
          leftIcon={<ArrowBackIcon />}
          onClick={onBack}
          isDisabled={isFirstStep}
        >
          {backButtonText}
        </Button>
        
        <Button
          colorScheme="primary"
          size="lg"
          rightIcon={<ArrowForwardIcon />}
          onClick={handleNext}
          isDisabled={!canProceed}
          px={8}
        >
          {isLastStep ? finalButtonText : nextButtonText}
        </Button>
      </HStack>
    </VStack>
  );
};

export default StepperForm;