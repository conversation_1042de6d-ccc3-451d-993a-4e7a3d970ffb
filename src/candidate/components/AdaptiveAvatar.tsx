/**
 * アダプティブアバターコンポーネント
 * ユーザーの状態に応じて表情や振る舞いを動的に変化
 */
import { useState, useEffect } from "react";
import { Box, VStack, Text, Badge, Flex, useColorModeValue } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { avatarExpression, pulse } from "../motion";

export interface UserState {
  confidence: number; // 0-1
  isPaused: boolean;
  speakingSpeed: "slow" | "normal" | "fast";
  emotionalState: "nervous" | "calm" | "confident";
}

interface AdaptiveAvatarProps {
  userState?: UserState;
  isSpeaking?: boolean;
  feedback?: string;
}

type AvatarExpression = "neutral" | "encouraging" | "patient" | "celebrating";

export const AdaptiveAvatar: React.FC<AdaptiveAvatarProps> = ({
  userState = {
    confidence: 0.5,
    isPaused: false,
    speakingSpeed: "normal",
    emotionalState: "calm",
  },
  isSpeaking = false,
  feedback,
}) => {
  const [expression, setExpression] = useState<AvatarExpression>("neutral");
  const [encouragementMessage, setEncouragementMessage] = useState<string>("");

  // ユーザーの状態に応じて表情とメッセージを更新
  useEffect(() => {
    if (userState.emotionalState === "nervous" || userState.confidence < 0.3) {
      setExpression("encouraging");
      setEncouragementMessage("大丈夫です、あなたのペースで話してください");
    } else if (userState.isPaused && !isSpeaking) {
      setExpression("patient");
      setEncouragementMessage("ゆっくり考えて構いませんよ");
    } else if (userState.confidence > 0.7) {
      setExpression("celebrating");
      setEncouragementMessage("素晴らしい調子です！");
    } else {
      setExpression("neutral");
      setEncouragementMessage("");
    }
  }, [userState, isSpeaking]);

  // 表情に応じた絵文字とスタイルを取得
  const getAvatarStyle = () => {
    switch (expression) {
      case "encouraging":
        return {
          emoji: "😊",
          bgColor: "support.100",
          borderColor: "support.300",
          badgeColor: "support",
        };
      case "patient":
        return {
          emoji: "😌",
          bgColor: "primary.50",
          borderColor: "primary.200",
          badgeColor: "primary",
        };
      case "celebrating":
        return {
          emoji: "🎉",
          bgColor: "caution.50",
          borderColor: "caution.300",
          badgeColor: "yellow",
        };
      default:
        return {
          emoji: "👨‍💼",
          bgColor: "neutral.100",
          borderColor: "neutral.300",
          badgeColor: "gray",
        };
    }
  };

  const avatarStyle = getAvatarStyle();

  return (
    <VStack spacing={{ base: 2, md: 4 }} w="100%" h="100%">
      {/* メインアバターエリア */}
      <Box
        w="100%"
        h="100%"
        position="relative"
        bg={useColorModeValue('gradient(to-br, primary.50, primary.100)', 'linear-gradient(135deg, var(--chakra-colors-gray-800), var(--chakra-colors-gray-700))')}
        borderRadius={{ base: "md", md: "lg" }}
        overflow="hidden"
        display="flex"
        alignItems="center"
        justifyContent="center"
        flexDirection="column"
      >
        {/* アバター本体 */}
        <motion.div
          animate={expression}
          variants={avatarExpression}
          style={{ position: "relative" }}
        >
          <Box
            w={{ base: "120px", md: "150px", lg: "180px" }}
            h={{ base: "120px", md: "150px", lg: "180px" }}
            borderRadius="full"
            bg={avatarStyle.bgColor}
            border={{ base: "3px solid", md: "4px solid" }}
            borderColor={avatarStyle.borderColor}
            display="flex"
            alignItems="center"
            justifyContent="center"
            boxShadow={{ base: "lg", md: "xl" }}
            position="relative"
          >
            {/* 表情 */}
            <Text 
              fontSize={{ base: "4xl", md: "5xl", lg: "6xl" }} 
              role="img" 
              aria-label="avatar"
            >
              {avatarStyle.emoji}
            </Text>

            {/* 話している時のインジケーター */}
            {isSpeaking && (
              <motion.div
                style={{
                  position: "absolute",
                  bottom: -8,
                  right: -8,
                }}
                variants={pulse}
                animate="animate"
              >
                <Box
                  w={{ base: "30px", md: "35px", lg: "40px" }}
                  h={{ base: "30px", md: "35px", lg: "40px" }}
                  borderRadius="full"
                  bg="support.500"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text fontSize={{ base: "md", md: "lg", lg: "xl" }}>🎤</Text>
                </Box>
              </motion.div>
            )}
          </Box>
        </motion.div>

        {/* 名前とステータス */}
        <VStack spacing={{ base: 1, md: 2 }} mt={{ base: 3, md: 6 }}>
          <Text 
            fontSize={{ base: "md", md: "lg", lg: "xl" }} 
            fontWeight="bold" 
            color={useColorModeValue('neutral.800', 'neutral.100')}
          >
            AIメンター
          </Text>
          <Badge
            colorScheme={avatarStyle.badgeColor}
            fontSize={{ base: "xs", md: "sm" }}
            px={{ base: 2, md: 3 }}
            py={1}
            borderRadius="full"
          >
            {isSpeaking ? "話しています" : "聞いています"}
          </Badge>
        </VStack>

        {/* 励ましメッセージ */}
        {encouragementMessage && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            style={{
              position: "absolute",
              bottom: 10,
              left: 10,
              right: 10,
            }}
          >
            <Box
              bg={useColorModeValue('white', 'gray.800')}
              p={{ base: 2, md: 3 }}
              borderRadius="md"
              boxShadow="md"
              borderLeft="4px solid"
              borderLeftColor={avatarStyle.borderColor}
            >
              <Text 
                fontSize={{ base: "xs", md: "sm" }} 
                color={useColorModeValue('neutral.700', 'neutral.300')}
                textAlign="center"
              >
                {encouragementMessage}
              </Text>
            </Box>
          </motion.div>
        )}

        {/* フィードバックメッセージ */}
        {feedback && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            style={{
              position: "absolute",
              top: 10,
              left: 10,
              right: 10,
            }}
          >
            <Box
              bg={useColorModeValue('rgba(255, 255, 255, 0.95)', 'rgba(45, 55, 72, 0.95)')}
              p={{ base: 2, md: 3 }}
              borderRadius="md"
              boxShadow="lg"
              maxH={{ base: "60px", md: "80px" }}
              overflow="auto"
            >
              <Text 
                fontSize={{ base: "xs", md: "sm" }} 
                color={useColorModeValue('neutral.800', 'neutral.100')}
                textAlign="center"
              >
                {feedback.length > 50 ? `${feedback.substring(0, 50)}...` : feedback}
              </Text>
            </Box>
          </motion.div>
        )}

        {/* ユーザー状態インジケーター（デバッグ用、本番では非表示） */}
        {process.env.NODE_ENV === "development" && (
          <Box
            position="absolute"
            top={1}
            right={1}
            p={1}
            bg="rgba(0,0,0,0.7)"
            color="white"
            borderRadius="sm"
            fontSize="2xs"
          >
            <Text>信頼度: {Math.round(userState.confidence * 100)}%</Text>
            <Text>速度: {userState.speakingSpeed}</Text>
            <Text>状態: {userState.emotionalState}</Text>
          </Box>
        )}
      </Box>
    </VStack>
  );
};