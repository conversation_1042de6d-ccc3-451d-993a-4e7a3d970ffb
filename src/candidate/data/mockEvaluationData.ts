/**
 * モックデータパターン集 - 5つのユーザータイプ別評価データ
 * 実際の面接シナリオに基づいた詳細なフィードバックデータ
 */

import {
  ComprehensiveInterviewReport,
  CandidateProfile,
  STARCExample,
  AudioAnalysisResult,
  PersonalizedActionPlan,
  BenchmarkComparison,
  ActionItem,
  Resource
} from '../types/evaluation';

// ========================================
// パターン1: 新卒・IT企業エンジニア志望（優秀層）
// ========================================
export const mockDataPattern1_NewGradEngineer: ComprehensiveInterviewReport = {
  metadata: {
    candidateName: "田中大輝",
    position: "ソフトウェアエンジニア",
    interviewDate: "2024年3月20日",
    duration: "50分",
    interviewType: "technical"
  },

  overallAssessment: {
    score: 85,
    level: "junior",
    recommendation: "hire"
  },

  detailedEvaluation: {
    communication: {
      verbalFluency: {
        score: 72,
        metrics: {
          fillerWords: 2.1, // per minute
          speechRate: 165,
          pauseFrequency: 0.8,
          sentenceCoherence: 78
        }
      },
      structuring: {
        score: 80,
        metrics: {
          introductionClarity: 85,
          logicalFlow: 82,
          conclusion: 75,
          timeManagement: 78
        }
      },
      nonVerbal: {
        score: 75,
        metrics: {
          eyeContact: 70,
          facialExpression: 78,
          voiceTone: 80,
          energy: 85
        }
      }
    },

    logicalThinking: {
      problemAnalysis: {
        score: 92,
        metrics: {
          issueIdentification: 95,
          rootCauseAnalysis: 88,
          frameworkUsage: 90,
          dataEvidence: 95
        }
      },
      solutionDesign: {
        score: 88,
        metrics: {
          creativity: 85,
          feasibility: 92,
          comprehensiveness: 85,
          prioritization: 90
        }
      }
    },

    experienceQuality: {
      leadership: {
        score: 70,
        examples: [{
          question: "チームプロジェクトでリーダーシップを発揮した経験は？",
          response: {
            situation: "研究室で5人のチームメンバーと機械学習プロジェクトに取り組みました",
            task: "3ヶ月で論文投稿レベルの成果を出す必要がありました",
            action: "週次ミーティングを設定し、各メンバーの進捗管理とコード レビューを主導しました",
            result: "プロジェクトは期限内に完成し、学会で発表することができました",
            competency: "技術的なリーダーシップと計画管理能力を身につけました"
          },
          evaluation: {
            situation: { clarity: 85, relevance: 90, specificity: 80 },
            task: { roleClarity: 88, challengeLevel: 85, scope: 75 },
            action: { initiative: 92, methodology: 85, collaboration: 88 },
            result: { quantification: 75, impact: 80, learning: 85 },
            competency: { transferability: 85, growthMindset: 90 }
          },
          improvements: [{
            suggestion: "チーム規模や具体的な技術的課題をもう少し詳しく説明する",
            examplePhrase: "5人のメンバーそれぞれの専門分野が異なる中で...",
            practicePrompt: "チームの多様性をどう活かしたか具体的に説明してください"
          }]
        }],
        metrics: {
          teamSize: 5,
          complexity: 75,
          outcomes: 85,
          influence: 70
        }
      },
      problemSolving: {
        score: 95,
        examples: [{
          question: "最も困難だった技術的問題の解決経験は？",
          response: {
            situation: "卒業研究でディープラーニングモデルの精度が期待値を大幅に下回っていました",
            task: "モデルの精度を70%から85%以上に改善する必要がありました",
            action: "データ前処理の見直し、複数のアーキテクチャの比較検討、ハイパーパラメータの最適化を体系的に実施しました",
            result: "最終的に88%の精度を達成し、処理速度も30%向上させることができました",
            competency: "体系的な問題解決手法と、失敗を学習に変える能力を習得しました"
          },
          evaluation: {
            situation: { clarity: 92, relevance: 95, specificity: 90 },
            task: { roleClarity: 95, challengeLevel: 90, scope: 85 },
            action: { initiative: 95, methodology: 92, collaboration: 80 },
            result: { quantification: 95, impact: 88, learning: 92 },
            competency: { transferability: 90, growthMindset: 95 }
          },
          improvements: [{
            suggestion: "ビジネスインパクトの観点を追加する",
            examplePhrase: "この精度向上により、実際のプロダクトでは...",
            practicePrompt: "技術的改善が事業にどう貢献するか説明してください"
          }]
        }],
        metrics: {
          challengeLevel: 90,
          innovation: 85,
          stakeholders: 70,
          results: 95
        }
      }
    },

    culturalFit: {
      values: {
        score: 78,
        alignment: {
          companyMission: 85,
          teamDynamics: 80,
          workStyle: 75,
          growthMindset: 95
        }
      },
      motivation: {
        score: 88,
        factors: {
          intrinsic: 92,
          careerGoals: 85,
          companyKnowledge: 80,
          enthusiasm: 95
        }
      }
    },

    technicalCompetence: {
      domainKnowledge: {
        score: 90,
        areas: {
          industryTrends: 85,
          technicalDepth: 95,
          practicalApplication: 88,
          continuousLearning: 95
        }
      },
      roleSpecific: {
        score: 88,
        competencies: [{
          name: "プログラミング",
          score: 92,
          evidence: ["AtCoderでの青レート達成", "GitHub上の複数のOSSプロジェクトへの貢献"],
          gaps: ["大規模システムでの実務経験"],
          developmentPlan: "インターンシップや個人プロジェクトでの経験積み上げ"
        }]
      }
    },

    adaptability: {
      changeManagement: {
        score: 82,
        examples: [{
          context: "研究テーマの大幅変更",
          challenge: "指導教授の異動により、研究の方向性を変える必要があった",
          approach: "新しい分野を短期間で学習し、既存の知識を応用",
          outcome: "3ヶ月で新テーマでの研究を軌道に乗せた",
          learningPoints: ["柔軟性の重要性", "基礎知識の応用力"]
        }],
        metrics: {
          flexibility: 85,
          resilience: 88,
          learningSpeed: 90,
          ambiguityTolerance: 75
        }
      }
    },

    professionalism: {
      presentation: {
        score: 80,
        metrics: {
          appearance: 85,
          punctuality: 90,
          preparation: 85,
          etiquette: 80
        }
      }
    }
  },

  audioVideoAnalysis: {
    audio: {
      segments: [
        {
          timestamp: "00:05:23",
          type: "excellent",
          transcript: "この問題を解決するために、まず問題を3つの要素に分解しました。データの質、アルゴリズムの選択、そして評価指標の設定です。",
          feedback: "構造化された思考プロセスが明確に表現されています",
          confidence: 0.92
        },
        {
          timestamp: "00:15:45",
          type: "improvement",
          transcript: "えーっと、その時は、まあ、かなり困ったんですけど...",
          feedback: "フィラーワードを減らし、より直接的な表現を心がけましょう",
          confidence: 0.78
        }
      ],
      prosodyAnalysis: {
        averagePitch: 165,
        pitchVariation: 45,
        speakingRate: 165,
        pauseAnalysis: {
          totalPauses: 25,
          averagePauseLength: 0.8,
          inappropriatePauses: 3
        },
        fillerWords: {
          count: 18,
          frequency: 2.1,
          types: { "えー": 8, "あのー": 5, "まあ": 3, "その": 2 }
        }
      },
      emotionAnalysis: {
        overallSentiment: "positive",
        confidence: 0.85,
        emotions: {
          enthusiasm: 88,
          nervousness: 35,
          confidence: 78,
          frustration: 12
        }
      }
    }
  },

  actionPlan: {
    immediate: [
      {
        action: "フィラーワード削減練習",
        method: "1日5分、スマートフォンで自己紹介を録音し、フィラーワードをカウント",
        metric: "1分あたり1回以下を目標",
        resources: [{
          type: "exercise",
          title: "音読練習アプリ",
          description: "滑舌と流暢性を向上させる練習",
          estimatedTime: "10分/日"
        }]
      }
    ],
    shortTerm: [
      {
        action: "ビジネス視点の技術説明練習",
        method: "技術的な成果をビジネス価値に結び付けて説明する練習",
        metric: "非技術者にも理解できる説明ができる",
        deadline: "1週間後",
        resources: [{
          type: "book",
          title: "エンジニアのためのビジネス思考入門",
          description: "技術者向けのビジネス基礎知識",
          estimatedTime: "2時間"
        }]
      }
    ],
    longTerm: [
      {
        action: "実務経験の積み上げ",
        method: "インターンシップや個人プロジェクトで大規模システム開発を経験",
        metric: "実際のプロダクト開発経験を1つ以上積む",
        deadline: "1ヶ月後"
      }
    ]
  },

  benchmarkComparison: {
    industryAverage: 75,
    roleSpecificAverage: 72,
    experienceLevelAverage: 70,
    userPercentile: 78,
    improvementPotential: 12
  },

  summary: {
    keyStrengths: [
      "アルゴリズムの理解が深く、実装力が高い",
      "オープンソースへの貢献実績が豊富",
      "学習意欲が非常に高く、新しい技術への適応力がある",
      "問題解決における体系的なアプローチが優秀"
    ],
    primaryImprovements: [
      "ビジネス視点での技術選定経験の不足",
      "大規模チームでの協働経験が限定的",
      "コミュニケーションがやや技術寄りになりがち",
      "フィラーワードの使用頻度を減らす必要"
    ],
    nextSteps: [
      "非技術者向けの説明練習を実施",
      "ビジネス書を月2冊読む習慣をつける",
      "フィラーワード削減のための音読練習",
      "実務でのチーム開発経験を積む"
    ]
  }
};

// ========================================
// パターン2: 中途・営業職（課題あり層）
// ========================================
export const mockDataPattern2_MidCareerSales: ComprehensiveInterviewReport = {
  metadata: {
    candidateName: "佐藤健一",
    position: "法人営業",
    interviewDate: "2024年3月18日",
    duration: "45分",
    interviewType: "behavioral"
  },

  overallAssessment: {
    score: 58,
    level: "mid",
    recommendation: "maybe"
  },

  detailedEvaluation: {
    communication: {
      verbalFluency: {
        score: 45,
        metrics: {
          fillerWords: 5.8,
          speechRate: 280, // 速すぎる
          pauseFrequency: 2.1,
          sentenceCoherence: 52
        }
      },
      structuring: {
        score: 38,
        metrics: {
          introductionClarity: 40,
          logicalFlow: 35,
          conclusion: 30, // 結論が不明確
          timeManagement: 45
        }
      },
      nonVerbal: {
        score: 65,
        metrics: {
          eyeContact: 60,
          facialExpression: 70,
          voiceTone: 65,
          energy: 75
        }
      }
    },

    logicalThinking: {
      problemAnalysis: {
        score: 55,
        metrics: {
          issueIdentification: 60,
          rootCauseAnalysis: 45,
          frameworkUsage: 30, // フレームワーク使用なし
          dataEvidence: 40 // 定量的根拠が薄い
        }
      },
      solutionDesign: {
        score: 62,
        metrics: {
          creativity: 70,
          feasibility: 65,
          comprehensiveness: 50,
          prioritization: 55
        }
      }
    },

    experienceQuality: {
      leadership: {
        score: 68,
        examples: [{
          question: "チームを牽引した経験について教えてください",
          response: {
            situation: "前職で営業チームのリーダーをしていました",
            task: "チーム全体の売上目標を達成する必要がありました",
            action: "メンバーと話し合って、いろいろな方法を試しました",
            result: "結果的に目標は達成できました",
            competency: "チームワークの大切さを学びました"
          },
          evaluation: {
            situation: { clarity: 40, relevance: 70, specificity: 30 },
            task: { roleClarity: 60, challengeLevel: 45, scope: 35 },
            action: { initiative: 55, methodology: 25, collaboration: 70 },
            result: { quantification: 20, impact: 40, learning: 45 },
            competency: { transferability: 40, growthMindset: 60 }
          },
          improvements: [{
            suggestion: "具体的な数値と期間を含めて状況を説明する",
            examplePhrase: "5名のチームで、四半期売上目標1億円の達成に向けて...",
            practicePrompt: "STAR法を使って、もう一度この経験を整理してみましょう"
          }]
        }],
        metrics: {
          teamSize: 0, // 不明
          complexity: 45,
          outcomes: 50,
          influence: 65
        }
      },
      problemSolving: {
        score: 52,
        examples: [{
          question: "最も困難だった営業経験は？",
          response: {
            situation: "お客様がなかなか契約してくれませんでした",
            task: "何とか契約を取る必要がありました",
            action: "何度も訪問して話をしました",
            result: "最終的に契約してもらえました",
            competency: "諦めないことが大事だと思いました"
          },
          evaluation: {
            situation: { clarity: 35, relevance: 60, specificity: 25 },
            task: { roleClarity: 50, challengeLevel: 40, scope: 30 },
            action: { initiative: 60, methodology: 30, collaboration: 40 },
            result: { quantification: 15, impact: 35, learning: 40 },
            competency: { transferability: 35, growthMindset: 50 }
          },
          improvements: [{
            suggestion: "顧客の具体的な課題と解決プロセスを詳細に説明する",
            examplePhrase: "顧客は導入コストを懸念しており、ROIの証明が必要でした...",
            practicePrompt: "顧客のニーズをどう把握し、どう対応したか具体的に説明してください"
          }]
        }],
        metrics: {
          challengeLevel: 55,
          innovation: 40,
          stakeholders: 60,
          results: 55
        }
      }
    },

    culturalFit: {
      values: {
        score: 68,
        alignment: {
          companyMission: 65,
          teamDynamics: 75,
          workStyle: 70,
          growthMindset: 60
        }
      },
      motivation: {
        score: 72,
        factors: {
          intrinsic: 70,
          careerGoals: 65,
          companyKnowledge: 55, // 企業研究不足
          enthusiasm: 85
        }
      }
    },

    technicalCompetence: {
      domainKnowledge: {
        score: 65,
        areas: {
          industryTrends: 60,
          technicalDepth: 70,
          practicalApplication: 75,
          continuousLearning: 55
        }
      },
      roleSpecific: {
        score: 70,
        competencies: [{
          name: "営業プロセス管理",
          score: 75,
          evidence: ["5年間の営業経験", "既存顧客との良好な関係"],
          gaps: ["新規開拓のスキル", "データ分析力"],
          developmentPlan: "営業研修と分析ツールの習得"
        }]
      }
    },

    adaptability: {
      changeManagement: {
        score: 70,
        examples: [{
          context: "営業手法の変更",
          challenge: "デジタル化に対応する必要があった",
          approach: "先輩に教えてもらいながら新しいツールを覚えた",
          outcome: "何とか使えるようになった",
          learningPoints: ["新しいことを覚えるのは大変"]
        }],
        metrics: {
          flexibility: 65,
          resilience: 75,
          learningSpeed: 60,
          ambiguityTolerance: 70
        }
      }
    },

    professionalism: {
      presentation: {
        score: 75,
        metrics: {
          appearance: 80,
          punctuality: 85,
          preparation: 60, // 準備不足
          etiquette: 80
        }
      }
    }
  },

  audioVideoAnalysis: {
    audio: {
      segments: [
        {
          timestamp: "00:08:15",
          type: "improvement",
          transcript: "えーっと、まあ、その時は、あのー、お客様がですね、なんか、あまり乗り気じゃなくて...",
          feedback: "フィラーワードが多すぎます。「お客様は導入に消極的でした」のように簡潔に表現しましょう",
          confidence: 0.65
        },
        {
          timestamp: "00:23:42",
          type: "neutral",
          transcript: "営業は人と人との関係だと思います。信頼関係が一番大切です。",
          feedback: "良い考え方ですが、具体的なエピソードで補強すると説得力が増します",
          confidence: 0.72
        }
      ],
      prosodyAnalysis: {
        averagePitch: 180,
        pitchVariation: 65,
        speakingRate: 280, // 速すぎる
        pauseAnalysis: {
          totalPauses: 45,
          averagePauseLength: 0.6,
          inappropriatePauses: 12
        },
        fillerWords: {
          count: 78,
          frequency: 5.8,
          types: { "えーっと": 22, "まあ": 18, "なんか": 15, "あのー": 12, "その": 11 }
        }
      },
      emotionAnalysis: {
        overallSentiment: "neutral",
        confidence: 0.68,
        emotions: {
          enthusiasm: 65,
          nervousness: 75,
          confidence: 45,
          frustration: 25
        }
      }
    }
  },

  actionPlan: {
    immediate: [
      {
        action: "STAR法の基本習得",
        method: "過去の経験3つをSTAR法で書き直す練習",
        metric: "構造化された回答ができる",
        resources: [{
          type: "article",
          title: "STAR法面接テクニック",
          description: "行動面接での回答構造化手法",
          estimatedTime: "30分"
        }]
      }
    ],
    shortTerm: [
      {
        action: "話速コントロール練習",
        method: "メトロノームを使って適切な話速（150-180WPM）で話す練習",
        metric: "聞き取りやすい速度で話せる",
        deadline: "1週間後"
      },
      {
        action: "数値による成果の言語化",
        method: "過去の営業実績を具体的な数値で整理",
        metric: "定量的な成果を3つ以上説明できる",
        deadline: "1週間後"
      }
    ],
    longTerm: [
      {
        action: "業界知識の強化",
        method: "業界ニュース3本/日を読み、要約する",
        metric: "主要トレンド5つを説明できる",
        deadline: "1ヶ月後"
      }
    ]
  },

  benchmarkComparison: {
    industryAverage: 72,
    roleSpecificAverage: 70,
    experienceLevelAverage: 75,
    userPercentile: 35,
    improvementPotential: 25
  },

  summary: {
    keyStrengths: [
      "人当たりの良さと親しみやすさ",
      "既存顧客との関係維持力",
      "粘り強く取り組む姿勢",
      "チームワークを重視する考え方"
    ],
    primaryImprovements: [
      "STAR法を使った構造的な回答",
      "フィラーワードの削減（現在5.8回/分）",
      "話速のコントロール（現在280WPM→180WPM目標）",
      "定量的な成果の説明力",
      "企業研究の深化"
    ],
    nextSteps: [
      "STAR法トレーニングの実施",
      "週2回の模擬面接練習（4週間）",
      "営業実績の数値化作業",
      "業界トレンド学習プログラム",
      "話速改善のための音読練習"
    ]
  }
};

// ========================================
// パターン3: 第二新卒・キャリアチェンジ（ポテンシャル重視）
// ========================================
export const mockDataPattern3_CareerChange: ComprehensiveInterviewReport = {
  metadata: {
    candidateName: "山田美咲",
    position: "マーケティング",
    interviewDate: "2024年3月22日",
    duration: "40分",
    interviewType: "behavioral"
  },

  overallAssessment: {
    score: 71,
    level: "junior",
    recommendation: "hire"
  },

  detailedEvaluation: {
    communication: {
      verbalFluency: {
        score: 78,
        metrics: {
          fillerWords: 1.8,
          speechRate: 155,
          pauseFrequency: 0.9,
          sentenceCoherence: 82
        }
      },
      structuring: {
        score: 75,
        metrics: {
          introductionClarity: 80,
          logicalFlow: 78,
          conclusion: 70,
          timeManagement: 72
        }
      },
      nonVerbal: {
        score: 80,
        metrics: {
          eyeContact: 85,
          facialExpression: 82,
          voiceTone: 78,
          energy: 85
        }
      }
    },

    logicalThinking: {
      problemAnalysis: {
        score: 68,
        metrics: {
          issueIdentification: 75,
          rootCauseAnalysis: 65,
          frameworkUsage: 55,
          dataEvidence: 70
        }
      },
      solutionDesign: {
        score: 72,
        metrics: {
          creativity: 80,
          feasibility: 70,
          comprehensiveness: 65,
          prioritization: 75
        }
      }
    },

    experienceQuality: {
      leadership: {
        score: 65,
        examples: [{
          question: "リーダーシップを発揮した経験は？",
          response: {
            situation: "アパレル店舗で新人スタッフ3名の指導を担当しました",
            task: "3ヶ月で独立して接客ができるレベルまで育成する必要がありました",
            action: "個々の特性を把握し、段階的な研修プログラムを作成。毎週フィードバック面談を実施しました",
            result: "全員が予定より2週間早く独立し、顧客満足度も向上しました",
            competency: "人材育成において、個別対応の重要性を学びました"
          },
          evaluation: {
            situation: { clarity: 85, relevance: 80, specificity: 78 },
            task: { roleClarity: 88, challengeLevel: 75, scope: 80 },
            action: { initiative: 85, methodology: 80, collaboration: 82 },
            result: { quantification: 82, impact: 78, learning: 85 },
            competency: { transferability: 88, growthMindset: 85 }
          },
          improvements: [{
            suggestion: "具体的な研修内容や改善された満足度の数値を追加",
            examplePhrase: "顧客満足度が4.2から4.6に向上し...",
            practicePrompt: "研修プログラムの具体的な工夫点を説明してください"
          }]
        }],
        metrics: {
          teamSize: 3,
          complexity: 70,
          outcomes: 82,
          influence: 75
        }
      },
      problemSolving: {
        score: 75,
        examples: [{
          question: "困難な状況をどう乗り越えましたか？",
          response: {
            situation: "コロナ禍で店舗売上が前年比40%減少しました",
            task: "限られたリソースで売上回復策を立案・実行する必要がありました",
            action: "オンライン接客システムを提案し、SNSを活用した顧客エンゲージメント施策を実施しました",
            result: "3ヶ月で売上を前年比80%まで回復させることができました",
            competency: "危機をイノベーションの機会として捉える視点を得ました"
          },
          evaluation: {
            situation: { clarity: 90, relevance: 88, specificity: 85 },
            task: { roleClarity: 85, challengeLevel: 90, scope: 88 },
            action: { initiative: 88, methodology: 85, collaboration: 80 },
            result: { quantification: 92, impact: 90, learning: 88 },
            competency: { transferability: 90, growthMindset: 92 }
          },
          improvements: [{
            suggestion: "実施した施策の詳細とROIを追加",
            examplePhrase: "SNS投稿により平均エンゲージメント率が15%向上し...",
            practicePrompt: "各施策の効果測定方法と結果を説明してください"
          }]
        }],
        metrics: {
          challengeLevel: 90,
          innovation: 85,
          stakeholders: 75,
          results: 88
        }
      }
    },

    culturalFit: {
      values: {
        score: 85,
        alignment: {
          companyMission: 90,
          teamDynamics: 85,
          workStyle: 80,
          growthMindset: 95
        }
      },
      motivation: {
        score: 88,
        factors: {
          intrinsic: 92,
          careerGoals: 90,
          companyKnowledge: 80,
          enthusiasm: 95
        }
      }
    },

    technicalCompetence: {
      domainKnowledge: {
        score: 55, // マーケティング実務経験不足
        areas: {
          industryTrends: 70,
          technicalDepth: 45,
          practicalApplication: 50,
          continuousLearning: 85
        }
      },
      roleSpecific: {
        score: 60,
        competencies: [{
          name: "デジタルマーケティング",
          score: 50,
          evidence: ["SNSマーケティングの独学", "Google Analytics基礎資格"],
          gaps: ["実務でのキャンペーン運用経験", "データ分析スキル"],
          developmentPlan: "実践的なデジタルマーケティング研修"
        }]
      }
    },

    adaptability: {
      changeManagement: {
        score: 92,
        examples: [{
          context: "業界変化への対応",
          challenge: "小売業からIT業界への転身",
          approach: "独学でマーケティング知識を習得し、実際に個人でECサイトを運営",
          outcome: "3ヶ月で基礎知識を身につけ、実践経験も積むことができた",
          learningPoints: ["学習能力の高さ", "実践志向の重要性"]
        }],
        metrics: {
          flexibility: 90,
          resilience: 95,
          learningSpeed: 92,
          ambiguityTolerance: 88
        }
      }
    },

    professionalism: {
      presentation: {
        score: 85,
        metrics: {
          appearance: 90,
          punctuality: 95,
          preparation: 85,
          etiquette: 85
        }
      }
    }
  },

  audioVideoAnalysis: {
    audio: {
      segments: [
        {
          timestamp: "00:12:30",
          type: "excellent",
          transcript: "小売での顧客対応経験は、ユーザー視点でのマーケティング施策立案に直接活かせると考えています。実際の顧客の声を聞いてきたからこそ、データの背景にある感情を理解できます。",
          feedback: "転用可能スキルの説明が非常に優秀です。具体性と論理性を兼ね備えています",
          confidence: 0.95
        },
        {
          timestamp: "00:25:15",
          type: "improvement",
          transcript: "マーケティングの専門知識はまだ不足していますが、学習意欲は高いです。",
          feedback: "ポジティブな表現に変換しましょう。「基礎知識を習得中で、実践経験を積みたいと考えています」など",
          confidence: 0.82
        }
      ],
      prosodyAnalysis: {
        averagePitch: 175,
        pitchVariation: 40,
        speakingRate: 155,
        pauseAnalysis: {
          totalPauses: 18,
          averagePauseLength: 0.9,
          inappropriatePauses: 2
        },
        fillerWords: {
          count: 12,
          frequency: 1.8,
          types: { "えーっと": 4, "そうですね": 3, "まあ": 3, "あの": 2 }
        }
      },
      emotionAnalysis: {
        overallSentiment: "positive",
        confidence: 0.88,
        emotions: {
          enthusiasm: 92,
          nervousness: 25,
          confidence: 82,
          frustration: 8
        }
      }
    }
  },

  actionPlan: {
    immediate: [
      {
        action: "転用可能スキルの言語化強化",
        method: "小売業務経験をマーケティング業務にどう活かせるか、具体例3つを整理",
        metric: "説得力のある転用スキル説明ができる"
      }
    ],
    shortTerm: [
      {
        action: "デジタルマーケティング基礎学習",
        method: "Google Digital Marketing Coursera受講",
        metric: "基本的なマーケティング用語と手法を説明できる",
        deadline: "2週間後"
      }
    ],
    longTerm: [
      {
        action: "実践プロジェクト経験",
        method: "個人でECサイト運営プロジェクトを実施",
        metric: "マーケティング施策の企画・実行・分析ができる",
        deadline: "1ヶ月後"
      }
    ]
  },

  benchmarkComparison: {
    industryAverage: 68,
    roleSpecificAverage: 65,
    experienceLevelAverage: 62,
    userPercentile: 72,
    improvementPotential: 18
  },

  summary: {
    keyStrengths: [
      "キャリアチェンジへの明確で具体的な動機",
      "危機的状況での創造的な問題解決能力",
      "顧客視点での発想力（接客経験の活用）",
      "非常に高い学習意欲と適応力",
      "転用可能スキルの認識と言語化能力"
    ],
    primaryImprovements: [
      "マーケティング実務経験の不足（現在スコア55/100）",
      "デジタルマーケティングツールの実践経験",
      "業界専門知識の深化",
      "弱みの表現をよりポジティブに"
    ],
    nextSteps: [
      "デジタルマーケティング基礎コース受講",
      "個人プロジェクトでの実践経験積み上げ",
      "業界研究とトレンド把握",
      "転用スキルのさらなる言語化練習",
      "ポジティブな自己表現の練習"
    ]
  }
};

// ========================================
// パターン4: ハイポテンシャル・コンサル志望
// ========================================
export const mockDataPattern4_HighPotentialConsulting: ComprehensiveInterviewReport = {
  metadata: {
    candidateName: "鈴木理恵",
    position: "経営コンサルタント",
    interviewDate: "2024年3月25日",
    duration: "60分",
    interviewType: "case"
  },

  overallAssessment: {
    score: 89,
    level: "senior",
    recommendation: "strong_hire"
  },

  detailedEvaluation: {
    communication: {
      verbalFluency: {
        score: 92,
        metrics: {
          fillerWords: 0.5,
          speechRate: 168,
          pauseFrequency: 0.3,
          sentenceCoherence: 95
        }
      },
      structuring: {
        score: 95,
        metrics: {
          introductionClarity: 98,
          logicalFlow: 95,
          conclusion: 92,
          timeManagement: 95
        }
      },
      nonVerbal: {
        score: 88,
        metrics: {
          eyeContact: 90,
          facialExpression: 85,
          voiceTone: 88,
          energy: 90
        }
      }
    },

    logicalThinking: {
      problemAnalysis: {
        score: 95,
        metrics: {
          issueIdentification: 98,
          rootCauseAnalysis: 95,
          frameworkUsage: 92,
          dataEvidence: 95
        }
      },
      solutionDesign: {
        score: 90,
        metrics: {
          creativity: 78, // 改善の余地あり
          feasibility: 95,
          comprehensiveness: 92,
          prioritization: 95
        }
      }
    },

    experienceQuality: {
      leadership: {
        score: 88,
        examples: [{
          question: "困難なプロジェクトをどうリードしましたか？",
          response: {
            situation: "投資銀行で50億円のM&A案件において、デューデリジェンスチームのリーダーを務めました",
            task: "3週間という限られた期間で、対象企業の財務・事業分析を完了させる必要がありました",
            action: "チームを機能別に分割し、並行作業を可能にする分析フレームワークを設計。毎日の進捗会議と週次でのクライアント報告体制を構築しました",
            result: "期限内に分析を完了し、発見された課題により買収価格を15%引き下げることに成功しました",
            competency: "複雑なプロジェクトにおける構造化思考とチーム管理の重要性を深く理解しました"
          },
          evaluation: {
            situation: { clarity: 95, relevance: 98, specificity: 92 },
            task: { roleClarity: 95, challengeLevel: 92, scope: 95 },
            action: { initiative: 95, methodology: 92, collaboration: 90 },
            result: { quantification: 95, impact: 98, learning: 90 },
            competency: { transferability: 95, growthMindset: 88 }
          },
          improvements: [{
            suggestion: "チームメンバーの動機付けやコンフリクト解決の視点を追加",
            examplePhrase: "異なる専門性を持つメンバー間の調整では...",
            practicePrompt: "ソフトスキルの側面でのリーダーシップをより詳しく"
          }]
        }],
        metrics: {
          teamSize: 8,
          complexity: 95,
          outcomes: 95,
          influence: 88
        }
      },
      problemSolving: {
        score: 92,
        examples: [{
          question: "最も困難な分析課題について",
          response: {
            situation: "クライアント企業の収益性が3年連続で悪化しており、原因が不明な状態でした",
            task: "根本原因を特定し、具体的な改善策を提案する必要がありました",
            action: "MECE分析により問題を顧客・商品・オペレーション・組織に分解。各領域で定量分析を実施し、ベンチマーク比較も行いました",
            result: "オペレーション効率の低下が主因と特定。業務改善により営業利益率を3%改善する提案を行い、実際に2.8%の改善を実現しました",
            competency: "データドリブンな問題解決と仮説検証の重要性を実感しました"
          },
          evaluation: {
            situation: { clarity: 92, relevance: 95, specificity: 88 },
            task: { roleClarity: 95, challengeLevel: 95, scope: 92 },
            action: { initiative: 92, methodology: 98, collaboration: 85 },
            result: { quantification: 98, impact: 95, learning: 90 },
            competency: { transferability: 95, growthMindset: 92 }
          },
          improvements: [{
            suggestion: "クライアントとのコミュニケーション面での工夫も説明",
            examplePhrase: "経営陣への報告では、複雑な分析結果を...",
            practicePrompt: "ステークホルダー管理の観点からも説明してください"
          }]
        }],
        metrics: {
          challengeLevel: 95,
          innovation: 85,
          stakeholders: 88,
          results: 95
        }
      }
    },

    culturalFit: {
      values: {
        score: 82,
        alignment: {
          companyMission: 88,
          teamDynamics: 80,
          workStyle: 85,
          growthMindset: 95
        }
      },
      motivation: {
        score: 90,
        factors: {
          intrinsic: 95,
          careerGoals: 92,
          companyKnowledge: 85,
          enthusiasm: 90
        }
      }
    },

    technicalCompetence: {
      domainKnowledge: {
        score: 92,
        areas: {
          industryTrends: 90,
          technicalDepth: 95,
          practicalApplication: 92,
          continuousLearning: 90
        }
      },
      roleSpecific: {
        score: 90,
        competencies: [{
          name: "戦略分析",
          score: 95,
          evidence: ["M&Aデューデリジェンス経験", "財務モデリング", "市場分析"],
          gaps: ["イノベーション戦略", "組織変革"],
          developmentPlan: "デザインシンキングと組織開発の学習"
        }]
      }
    },

    adaptability: {
      changeManagement: {
        score: 87,
        examples: [{
          context: "業界変化への対応",
          challenge: "金融業界のデジタル化対応",
          approach: "新技術トレンドを学習し、従来分析手法にデジタル要素を統合",
          outcome: "デジタル変革プロジェクトのリーダーに抜擢された",
          learningPoints: ["継続学習の重要性", "変化を機会として捉える姿勢"]
        }],
        metrics: {
          flexibility: 85,
          resilience: 90,
          learningSpeed: 92,
          ambiguityTolerance: 90
        }
      }
    },

    professionalism: {
      presentation: {
        score: 92,
        metrics: {
          appearance: 95,
          punctuality: 98,
          preparation: 90,
          etiquette: 92
        }
      }
    }
  },

  audioVideoAnalysis: {
    audio: {
      segments: [
        {
          timestamp: "00:15:30",
          type: "excellent",
          transcript: "この問題を3つの軸で整理します。第一に市場環境、第二に競合状況、第三に内部能力です。まず市場環境から見ていくと...",
          feedback: "MECE な構造化が瞬時にできており、極めて優秀です",
          confidence: 0.98
        },
        {
          timestamp: "00:35:45",
          type: "improvement",
          transcript: "データを見ると明らかに効率が悪いので、改善が必要です。",
          feedback: "より創造的な解決策の提示ができるとさらに良いでしょう",
          confidence: 0.85
        }
      ],
      prosodyAnalysis: {
        averagePitch: 160,
        pitchVariation: 30,
        speakingRate: 168,
        pauseAnalysis: {
          totalPauses: 8,
          averagePauseLength: 0.5,
          inappropriatePauses: 0
        },
        fillerWords: {
          count: 3,
          frequency: 0.5,
          types: { "えーっと": 2, "そうですね": 1 }
        }
      },
      emotionAnalysis: {
        overallSentiment: "positive",
        confidence: 0.92,
        emotions: {
          enthusiasm: 88,
          nervousness: 10,
          confidence: 95,
          frustration: 5
        }
      }
    }
  },

  actionPlan: {
    immediate: [
      {
        action: "創造的思考力の強化",
        method: "デザインシンキングワークショップへの参加",
        metric: "従来手法にない革新的アプローチを1つ以上提示できる"
      }
    ],
    shortTerm: [
      {
        action: "ソフトスキル強化",
        method: "組織心理学やリーダーシップ理論の学習",
        metric: "人間関係の複雑さを考慮した解決策を提示できる",
        deadline: "2週間後"
      }
    ],
    longTerm: [
      {
        action: "グローバル視点の拡充",
        method: "海外事例研究と国際的なビジネス環境の理解を深める",
        metric: "グローバル市場を考慮した戦略を立案できる",
        deadline: "1ヶ月後"
      }
    ]
  },

  benchmarkComparison: {
    industryAverage: 78,
    roleSpecificAverage: 75,
    experienceLevelAverage: 82,
    userPercentile: 92,
    improvementPotential: 8
  },

  summary: {
    keyStrengths: [
      "極めて高い構造化思考力（MECE分析が優秀）",
      "定量分析能力と仮説検証スキル",
      "複雑な問題の本質を見抜く洞察力",
      "優れたプレゼンテーション能力",
      "高いプロフェッショナリズム"
    ],
    primaryImprovements: [
      "創造的・イノベーティブな発想の強化",
      "ソフトスキル（人間関係・組織）の視点",
      "グローバル市場への理解",
      "チームメンバーへの共感力向上"
    ],
    nextSteps: [
      "デザインシンキング手法の習得",
      "組織開発・変革管理の学習",
      "国際的なケーススタディの研究",
      "多様なバックグラウンドを持つチームとの協働経験",
      "クリエイティブ業界の事例研究"
    ]
  }
};

// ========================================
// パターン5: エグゼクティブ・経営層
// ========================================
export const mockDataPattern5_Executive: ComprehensiveInterviewReport = {
  metadata: {
    candidateName: "松本克也",
    position: "事業部長",
    interviewDate: "2024年3月28日",
    duration: "75分",
    interviewType: "behavioral"
  },

  overallAssessment: {
    score: 82,
    level: "expert",
    recommendation: "hire"
  },

  detailedEvaluation: {
    communication: {
      verbalFluency: {
        score: 90,
        metrics: {
          fillerWords: 0.3,
          speechRate: 150,
          pauseFrequency: 0.2,
          sentenceCoherence: 92
        }
      },
      structuring: {
        score: 88,
        metrics: {
          introductionClarity: 90,
          logicalFlow: 88,
          conclusion: 85,
          timeManagement: 90
        }
      },
      nonVerbal: {
        score: 90,
        metrics: {
          eyeContact: 95,
          facialExpression: 85,
          voiceTone: 90,
          energy: 88
        }
      }
    },

    logicalThinking: {
      problemAnalysis: {
        score: 85,
        metrics: {
          issueIdentification: 90,
          rootCauseAnalysis: 85,
          frameworkUsage: 80,
          dataEvidence: 85
        }
      },
      solutionDesign: {
        score: 88,
        metrics: {
          creativity: 85,
          feasibility: 92,
          comprehensiveness: 88,
          prioritization: 90
        }
      }
    },

    experienceQuality: {
      leadership: {
        score: 88,
        examples: [{
          question: "最も困難な組織変革をどう導きましたか？",
          response: {
            situation: "製造業で200名規模の事業部において、デジタル変革とコスト削減を同時に実現する必要がありました",
            task: "3年間で売上を維持しながら、生産性を30%向上させる組織変革を実行する責任がありました",
            action: "変革ビジョンを策定し、段階的な実行計画を作成。全社員向けの説明会を実施し、部門横断的なタスクフォースを設置。外部コンサルタントとも連携しました",
            result: "2年8ヶ月で生産性35%向上を達成。同時に従業員満足度も15%向上させることができました",
            competency: "変革において、技術面だけでなく人的側面への配慮が成功の鍵であることを学びました"
          },
          evaluation: {
            situation: { clarity: 88, relevance: 92, specificity: 85 },
            task: { roleClarity: 95, challengeLevel: 92, scope: 90 },
            action: { initiative: 88, methodology: 85, collaboration: 92 },
            result: { quantification: 95, impact: 92, learning: 88 },
            competency: { transferability: 90, growthMindset: 85 }
          },
          improvements: [{
            suggestion: "具体的な抵抗勢力への対処方法を詳述",
            examplePhrase: "変革に反対する部門長2名に対しては...",
            practicePrompt: "ステークホルダー間の利害調整をどう行ったか説明してください"
          }]
        }],
        metrics: {
          teamSize: 200,
          complexity: 92,
          outcomes: 90,
          influence: 88
        }
      },
      problemSolving: {
        score: 85,
        examples: [{
          question: "事業の危機的状況をどう乗り越えましたか？",
          response: {
            situation: "主力製品の市場シェアが3年で25%から12%に急落しました",
            task: "事業の立て直しと新しい成長軌道の構築が急務でした",
            action: "市場分析を徹底的に行い、顧客ニーズの変化を特定。新製品開発と既存製品のリニューアルを並行して進めました",
            result: "2年でシェアを18%まで回復させ、新製品が全売上の40%を占めるようになりました",
            competency: "危機的状況でこそ、長期視点での戦略思考が重要であることを実感しました"
          },
          evaluation: {
            situation: { clarity: 90, relevance: 95, specificity: 88 },
            task: { roleClarity: 88, challengeLevel: 95, scope: 90 },
            action: { initiative: 85, methodology: 80, collaboration: 82 },
            result: { quantification: 92, impact: 88, learning: 85 },
            competency: { transferability: 88, growthMindset: 82 }
          },
          improvements: [{
            suggestion: "DXなど現代的な解決手法の活用を説明",
            examplePhrase: "デジタル技術を活用した顧客分析により...",
            practicePrompt: "デジタル変革の観点からの取り組みも説明してください"
          }]
        }],
        metrics: {
          challengeLevel: 95,
          innovation: 80,
          stakeholders: 85,
          results: 88
        }
      }
    },

    culturalFit: {
      values: {
        score: 84,
        alignment: {
          companyMission: 85,
          teamDynamics: 88,
          workStyle: 80,
          growthMindset: 82
        }
      },
      motivation: {
        score: 87,
        factors: {
          intrinsic: 90,
          careerGoals: 85,
          companyKnowledge: 82,
          enthusiasm: 88
        }
      }
    },

    technicalCompetence: {
      domainKnowledge: {
        score: 88,
        areas: {
          industryTrends: 85,
          technicalDepth: 90,
          practicalApplication: 90,
          continuousLearning: 78 // 改善の余地
        }
      },
      roleSpecific: {
        score: 84,
        competencies: [{
          name: "事業戦略",
          score: 90,
          evidence: ["20年の事業管理経験", "複数の事業立て直し実績"],
          gaps: ["デジタル変革リーダーシップ", "ESG経営"],
          developmentPlan: "DXリーダーシップ研修とESG戦略学習"
        }]
      }
    },

    adaptability: {
      changeManagement: {
        score: 79,
        examples: [{
          context: "業界構造の変化",
          challenge: "伝統的製造業からサービス業への転換",
          approach: "段階的なビジネスモデル変革と人材育成",
          outcome: "5年でサービス売上比率を30%まで向上",
          learningPoints: ["変革の継続性", "人材の重要性"]
        }],
        metrics: {
          flexibility: 82,
          resilience: 85,
          learningSpeed: 75, // 改善余地
          ambiguityTolerance: 88
        }
      }
    },

    professionalism: {
      presentation: {
        score: 92,
        metrics: {
          appearance: 95,
          punctuality: 98,
          preparation: 88,
          etiquette: 95
        }
      }
    }
  },

  audioVideoAnalysis: {
    audio: {
      segments: [
        {
          timestamp: "00:25:15",
          type: "excellent",
          transcript: "事業の本質は顧客価値の創造です。我々は技術力に自信がありましたが、顧客の真のニーズを見失っていました。そこで全社を挙げて顧客起点の事業再構築に取り組みました。",
          feedback: "リーダーとしての洞察力と本質的な問題把握が優秀です",
          confidence: 0.95
        },
        {
          timestamp: "00:45:30",
          type: "improvement",
          transcript: "デジタル化については、正直まだ勉強が必要な部分もあります。",
          feedback: "エグゼクティブレベルでは学習姿勢よりも戦略的視点を強調した方が良いでしょう",
          confidence: 0.78
        }
      ],
      prosodyAnalysis: {
        averagePitch: 140,
        pitchVariation: 25,
        speakingRate: 150,
        pauseAnalysis: {
          totalPauses: 12,
          averagePauseLength: 1.2,
          inappropriatePauses: 1
        },
        fillerWords: {
          count: 4,
          frequency: 0.3,
          types: { "そうですね": 2, "まあ": 1, "えー": 1 }
        }
      },
      emotionAnalysis: {
        overallSentiment: "positive",
        confidence: 0.88,
        emotions: {
          enthusiasm: 85,
          nervousness: 15,
          confidence: 90,
          frustration: 10
        }
      }
    }
  },

  actionPlan: {
    immediate: [
      {
        action: "デジタル戦略の理解強化",
        method: "DX成功事例の研究とエグゼクティブ向けセミナー参加",
        metric: "デジタル変革戦略を語れるレベルになる"
      }
    ],
    shortTerm: [
      {
        action: "ESG経営の知識習得",
        method: "サステナビリティレポートの分析と専門書籍の読破",
        metric: "ESG観点からの事業戦略を説明できる",
        deadline: "2週間後"
      }
    ],
    longTerm: [
      {
        action: "グローバルガバナンスの理解",
        method: "国際的な経営環境とコンプライアンス要件の学習",
        metric: "グローバル展開時のリスク管理を説明できる",
        deadline: "1ヶ月後"
      }
    ]
  },

  benchmarkComparison: {
    industryAverage: 75,
    roleSpecificAverage: 78,
    experienceLevelAverage: 80,
    userPercentile: 85,
    improvementPotential: 12
  },

  summary: {
    keyStrengths: [
      "豊富な事業管理経験と実績",
      "危機的状況での確実な成果創出",
      "優れたコミュニケーション能力",
      "チーム・組織の結束力向上スキル",
      "長期的視点での戦略思考"
    ],
    primaryImprovements: [
      "デジタル変革リーダーシップの強化",
      "ESG/サステナビリティ経営の理解",
      "継続学習への積極性",
      "グローバル市場への理解深化",
      "最新技術トレンドへの感度向上"
    ],
    nextSteps: [
      "DXリーダーシップ研修への参加",
      "ESG戦略セミナーの受講",
      "グローバル事業展開の事例研究",
      "テクノロジー業界との交流機会創出",
      "継続学習習慣の確立"
    ]
  }
};

// ========================================
// データ生成ヘルパー関数
// ========================================
export function getEvaluationDataByProfile(profile: CandidateProfile): ComprehensiveInterviewReport {
  // プロファイルに基づいて適切なモックデータを返す
  if (profile.type === '新卒' && profile.industry === 'IT') {
    return mockDataPattern1_NewGradEngineer;
  }
  if (profile.type === '中途' && profile.position.includes('営業')) {
    return mockDataPattern2_MidCareerSales;
  }
  if (profile.type === '第二新卒') {
    return mockDataPattern3_CareerChange;
  }
  if (profile.position.includes('コンサル')) {
    return mockDataPattern4_HighPotentialConsulting;
  }
  if (profile.experienceLevel === 'expert') {
    return mockDataPattern5_Executive;
  }
  
  // デフォルトは新卒エンジニアパターン
  return mockDataPattern1_NewGradEngineer;
}

export const ALL_MOCK_PATTERNS = [
  mockDataPattern1_NewGradEngineer,
  mockDataPattern2_MidCareerSales,
  mockDataPattern3_CareerChange,
  mockDataPattern4_HighPotentialConsulting,
  mockDataPattern5_Executive
];