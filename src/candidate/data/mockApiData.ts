/**
 * 統一モックデータ生成
 * APIの代替として一貫性のあるモックデータを提供
 */

import {
  ComprehensiveInterviewReport,
  STARCExample,
  CandidateProfile,
  EvaluationConfig,
  InterviewEvaluationFramework,
  AudioAnalysisResult,
  PersonalizedActionPlan,
  BenchmarkComparison,
} from '../types/evaluation';

/**
 * 候補者プロファイルのモックデータ生成
 */
export function generateMockCandidateProfile(candidateId: string): CandidateProfile {
  const profiles: Record<string, CandidateProfile> = {
    'agent-generated': {
      type: '中途',
      position: 'ソフトウェアエンジニア',
      industry: 'IT',
      experienceLevel: 'mid',
      yearsOfExperience: 5,
      previousIndustry: 'IT',
      targetIndustry: 'IT',
      specializations: ['React', 'TypeScript', 'Node.js'],
    },
    'candidate-001': {
      type: '中途',
      position: 'ソフトウェアエンジニア',
      industry: 'IT',
      experienceLevel: 'mid',
      yearsOfExperience: 5,
      previousIndustry: 'IT',
      targetIndustry: 'IT',
      specializations: ['React', 'TypeScript', 'Node.js'],
    },
    'candidate-002': {
      type: '新卒',
      position: 'マーケティング',
      industry: 'EC',
      experienceLevel: 'entry',
      yearsOfExperience: 0,
      targetIndustry: 'EC',
      specializations: ['デジタルマーケティング', 'データ分析'],
    },
  };

  return profiles[candidateId] || profiles['candidate-001'];
}

/**
 * 評価設定のモックデータ生成
 */
export function generateMockEvaluationConfig(
  position: string,
  industry: string
): EvaluationConfig {
  const baseWeights = {
    communication: 0.2,
    logicalThinking: 0.2,
    experienceQuality: 0.15,
    culturalFit: 0.15,
    technicalCompetence: 0.15,
    adaptability: 0.1,
    professionalism: 0.05,
  };

  // 業界・職種別の重み調整
  if (industry === 'IT') {
    baseWeights.technicalCompetence = 0.25;
    baseWeights.logicalThinking = 0.25;
    baseWeights.communication = 0.15;
  }

  return {
    weights: baseWeights,
    industrySpecific: true,
    roleSpecific: true,
    includeAudioAnalysis: true,
    includeVideoAnalysis: false,
    detailLevel: 'comprehensive',
  };
}

/**
 * 面接質問のモックデータ生成
 */
export function generateMockQuestions(
  scenarioId: string,
  candidateProfile: CandidateProfile
): string[] {
  const baseQuestions = [
    'これまでの経験で最も困難だった課題とその解決方法を教えてください',
    'チームプロジェクトでリーダーシップを発揮した経験はありますか？',
    '技術的な問題をどのように解決しますか？',
    '当社で実現したいことは何ですか？',
    '5年後のキャリアビジョンを教えてください',
  ];

  // 業界別の追加質問
  const industryQuestions: Record<string, string[]> = {
    IT: [
      '最新の技術トレンドについてどう考えていますか？',
      'コードレビューでの経験を教えてください',
    ],
    finance: [
      'リスク管理についての考え方は？',
      '規制変更への対応経験は？',
    ],
  };

  const additionalQuestions = industryQuestions[candidateProfile.industry] || [];
  return [...baseQuestions, ...additionalQuestions];
}

/**
 * STAR+C分析例のモックデータ生成
 */
export function generateMockSTARExamples(questionIds: string[]): STARCExample[] {
  const mockExamples: STARCExample[] = [
    {
      question: 'チームプロジェクトでリーダーシップを発揮した経験は？',
      response: {
        situation: '研究室で5人のチームメンバーと機械学習プロジェクトに取り組みました',
        task: '3ヶ月で論文投稿レベルの成果を出す必要がありました',
        action: '週次ミーティングを設定し、各メンバーの進捗管理とコードレビューを主導しました',
        result: 'プロジェクトは期限内に完成し、学会で発表することができました',
        competency: '技術的なリーダーシップと計画管理能力を身につけました',
      },
      evaluation: {
        situation: {
          clarity: 85,
          relevance: 90,
          specificity: 80,
        },
        task: {
          roleClarity: 88,
          challengeLevel: 85,
          scope: 80,
        },
        action: {
          initiative: 92,
          methodology: 85,
          collaboration: 88,
        },
        result: {
          quantification: 75,
          impact: 85,
          learning: 90,
        },
        competency: {
          transferability: 85,
          growthMindset: 88,
        },
      },
      improvements: [
        {
          suggestion: 'チーム規模や具体的な技術的課題をもう少し詳しく説明する',
          examplePhrase: '5人のメンバーそれぞれの専門分野が異なる中で...',
          practicePrompt: '具体的な数値や詳細を含めて状況を説明してください',
        },
      ],
    },
  ];

  return mockExamples.slice(0, questionIds.length);
}

/**
 * 音声分析結果のモックデータ
 */
function generateMockAudioAnalysis(): AudioAnalysisResult {
  return {
    segments: [
      {
        timestamp: '0:15-0:45',
        type: 'excellent',
        transcript: '私がリーダーとして取り組んだプロジェクトについてお話しします。',
        feedback: '明確な導入と構造化された回答の開始が素晴らしいです。',
        confidence: 0.92,
      },
      {
        timestamp: '1:20-1:35',
        type: 'improvement',
        transcript: 'えー、その時は、あの、チームの調整が...えー、難しくて。',
        feedback: 'フィラーワードが多く、もう少し簡潔に話すことができれば良いでしょう。',
        confidence: 0.78,
      },
    ],
    prosodyAnalysis: {
      averagePitch: 180,
      pitchVariation: 45,
      speakingRate: 165,
      pauseAnalysis: {
        totalPauses: 12,
        averagePauseLength: 1.2,
        inappropriatePauses: 3,
      },
      fillerWords: {
        count: 8,
        frequency: 2.1,
        types: { 'えー': 4, 'あの': 3, 'その': 1 },
      },
    },
    emotionAnalysis: {
      overallSentiment: 'positive',
      confidence: 0.85,
      emotions: {
        enthusiasm: 75,
        nervousness: 25,
        confidence: 80,
        frustration: 10,
      },
    },
  };
}

/**
 * 詳細評価フレームワークのモックデータ
 */
function generateMockDetailedEvaluation(): InterviewEvaluationFramework {
  return {
    communication: {
      verbalFluency: {
        score: 78,
        metrics: {
          fillerWords: 2.1,
          speechRate: 165,
          pauseFrequency: 1.2,
          sentenceCoherence: 85,
        },
      },
      structuring: {
        score: 85,
        metrics: {
          introductionClarity: 90,
          logicalFlow: 82,
          conclusion: 88,
          timeManagement: 80,
        },
      },
      nonVerbal: {
        score: 75,
        metrics: {
          eyeContact: 80,
          facialExpression: 75,
          voiceTone: 78,
          energy: 72,
        },
      },
    },
    logicalThinking: {
      problemAnalysis: {
        score: 88,
        metrics: {
          issueIdentification: 90,
          rootCauseAnalysis: 85,
          frameworkUsage: 88,
          dataEvidence: 90,
        },
      },
      solutionDesign: {
        score: 82,
        metrics: {
          creativity: 80,
          feasibility: 85,
          comprehensiveness: 82,
          prioritization: 80,
        },
      },
    },
    experienceQuality: {
      leadership: {
        score: 85,
        examples: generateMockSTARExamples(['q1']),
        metrics: {
          teamSize: 5,
          complexity: 85,
          outcomes: 88,
          influence: 82,
        },
      },
      problemSolving: {
        score: 88,
        examples: generateMockSTARExamples(['q2']),
        metrics: {
          challengeLevel: 85,
          innovation: 80,
          stakeholders: 90,
          results: 88,
        },
      },
    },
    culturalFit: {
      values: {
        score: 82,
        alignment: {
          companyMission: 85,
          teamDynamics: 80,
          workStyle: 82,
          growthMindset: 85,
        },
      },
      motivation: {
        score: 88,
        factors: {
          intrinsic: 85,
          careerGoals: 90,
          companyKnowledge: 88,
          enthusiasm: 90,
        },
      },
    },
    technicalCompetence: {
      domainKnowledge: {
        score: 85,
        areas: {
          industryTrends: 88,
          technicalDepth: 82,
          practicalApplication: 85,
          continuousLearning: 88,
        },
      },
      roleSpecific: {
        score: 82,
        competencies: [
          {
            name: 'React Development',
            score: 85,
            evidence: ['実務経験3年', 'TypeScript活用'],
            gaps: ['最新のHooks API'],
            developmentPlan: 'React 18の新機能を学習',
          },
        ],
      },
    },
    adaptability: {
      changeManagement: {
        score: 80,
        examples: [
          {
            context: 'リモートワークへの移行',
            challenge: 'チームコミュニケーションの課題',
            approach: '定期的なオンラインミーティング設定',
            outcome: '生産性20%向上',
            learningPoints: ['デジタルツール活用', 'プロセス改善'],
          },
        ],
        metrics: {
          flexibility: 85,
          resilience: 80,
          learningSpeed: 88,
          ambiguityTolerance: 75,
        },
      },
    },
    professionalism: {
      presentation: {
        score: 85,
        metrics: {
          appearance: 90,
          punctuality: 95,
          preparation: 85,
          etiquette: 88,
        },
      },
    },
  };
}

/**
 * アクションプランのモックデータ
 */
function generateMockActionPlan(): PersonalizedActionPlan {
  return {
    immediate: [
      {
        action: 'フィラーワードの削減',
        method: '録音練習とフィードバック',
        metric: '2回/分以下に削減',
        resources: [
          {
            type: 'exercise',
            title: '1分間スピーチ練習',
            description: '毎日1分間のスピーチを録音し振り返る',
            estimatedTime: '10分/日',
          },
        ],
      },
    ],
    shortTerm: [
      {
        action: 'STAR法の習得',
        method: '過去の経験を整理し練習',
        metric: '5つの経験をSTAR形式で準備',
        resources: [
          {
            type: 'article',
            title: 'STAR法完全ガイド',
            description: '行動面接での効果的な回答方法',
            url: 'https://example.com/star-method',
            estimatedTime: '30分',
          },
        ],
      },
    ],
    longTerm: [
      {
        action: '技術力の向上',
        method: '新しいフレームワークの学習',
        metric: 'React 18の新機能を習得',
        resources: [
          {
            type: 'course',
            title: 'React 18マスターコース',
            description: '最新のReact機能を実践的に学習',
            estimatedTime: '20時間',
          },
        ],
      },
    ],
  };
}

/**
 * ベンチマーク比較のモックデータ
 */
function generateMockBenchmarkComparison(): BenchmarkComparison {
  return {
    industryAverage: 75,
    roleSpecificAverage: 78,
    experienceLevelAverage: 80,
    userPercentile: 70,
    improvementPotential: 15,
  };
}

/**
 * 包括的面接レポートのモックデータ生成
 */
export function generateMockComprehensiveReport(
  candidateId: string,
  interviewId: string
): ComprehensiveInterviewReport {
  const candidateProfile = generateMockCandidateProfile(candidateId);

  return {
    metadata: {
      candidateName: candidateProfile.type === '新卒' ? '田中太郎' : '佐藤花子',
      position: candidateProfile.position,
      interviewDate: new Date().toLocaleDateString('ja-JP'),
      duration: '45分',
      interviewType: 'behavioral',
    },
    overallAssessment: {
      score: 82,
      level: candidateProfile.experienceLevel,
      recommendation: 'hire',
    },
    detailedEvaluation: generateMockDetailedEvaluation(),
    audioVideoAnalysis: {
      audio: generateMockAudioAnalysis(),
    },
    actionPlan: generateMockActionPlan(),
    benchmarkComparison: generateMockBenchmarkComparison(),
    summary: {
      keyStrengths: [
        '論理的思考力が優れている',
        '技術的な知識が豊富',
        '学習意欲が高い',
        'チームワークを重視している',
      ],
      primaryImprovements: [
        'フィラーワードの使用を減らす',
        '具体的な数値を含めた説明',
        '結論を明確にする',
        '時間管理の改善',
      ],
      nextSteps: [
        'STAR法での回答練習',
        '録音による発話改善',
        '業界知識の拡充',
        'プレゼンテーション練習',
      ],
    },
  };
}

/**
 * 基本的な面接質問データ（mockData.tsから統合）
 */
export interface BasicQuestion {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number;
}

export interface BasicFeedback {
  id: string;
  interviewId: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  emotionScore?: number;
  confidenceScore?: number;
  relevanceScore?: number;
  clarityScore?: number;
  overall: number;
  overallRating: number;
  suggestions: string[];
  timestamp: string;
}

/**
 * 基本的な面接質問の生成
 */
export function generateMockBasicQuestions(): BasicQuestion[] {
  return [
    {
      id: "q1",
      text: "自己紹介をお願いします。経歴と志望動機を含めて2分程度でお話しください。",
      category: "基本情報",
      difficulty: "easy",
      estimatedTime: 120
    },
    {
      id: "q2", 
      text: "これまでの経験で最も困難だった課題と、それをどのように解決したか教えてください。",
      category: "経験・スキル",
      difficulty: "medium",
      estimatedTime: 180
    },
    {
      id: "q3",
      text: "チームで働く際に重視することは何ですか？具体的なエピソードがあれば教えてください。",
      category: "コミュニケーション",
      difficulty: "medium", 
      estimatedTime: 150
    },
    {
      id: "q4",
      text: "5年後のキャリアビジョンを教えてください。この会社でどのような貢献をしたいですか？",
      category: "将来性",
      difficulty: "medium",
      estimatedTime: 180
    },
    {
      id: "q5",
      text: "技術的な課題に直面した時、どのようなアプローチで解決に取り組みますか？",
      category: "技術力",
      difficulty: "hard",
      estimatedTime: 200
    }
  ];
}

/**
 * 基本的なフィードバックの生成
 */
export function generateMockBasicFeedbacks(): BasicFeedback[] {
  return [
    {
      id: "feedback_001",
      interviewId: "interview_001",
      questionId: "q1",
      candidateAnswer: "私は3年間フロントエンド開発に従事し、ReactとTypeScriptを中心に業務を行ってきました。特にユーザビリティ向上に興味があり、御社のプロダクトの品質向上に貢献したいと考えています。",
      aiAnalysis: "自己紹介は簡潔で要点が整理されています。技術スタックと志望動機が明確に述べられており、好印象です。",
      emotionScore: 8.2,
      confidenceScore: 7.5,
      relevanceScore: 9.0,
      overall: 8,
      overallRating: 4,
      suggestions: [
        "より具体的な成果や数値を含めると説得力が増します",
        "企業研究の深さをアピールできるとさらに良いでしょう"
      ],
      timestamp: "2025-06-09T14:05:00Z"
    },
    {
      id: "feedback_002",
      interviewId: "interview_001", 
      questionId: "q2",
      candidateAnswer: "レガシーシステムのリニューアルプロジェクトで、要件定義が曖昧な状況がありました。ステークホルダーとの定期的な会議を設定し、プロトタイプを作成して認識を合わせることで解決しました。",
      aiAnalysis: "具体的な課題と解決策が述べられており、問題解決能力の高さが伺えます。コミュニケーション能力も評価できます。",
      emotionScore: 7.8,
      confidenceScore: 8.2,
      relevanceScore: 8.7,
      overall: 8,
      overallRating: 4,
      suggestions: [
        "結果や成果をより詳しく説明すると良いでしょう",
        "チームメンバーとの協力についても触れると完璧です"
      ],
      timestamp: "2025-06-09T14:08:00Z"
    }
  ];
}

/**
 * 業界別質問データ（advancedPracticeData.tsから統合）
 */
export interface AdvancedQuestion {
  id: string;
  text: string;
  category: string;
  difficulty: "medium" | "hard" | "expert";
  industry?: string;
  jobRole?: string;
  estimatedTime: number;
  followUpQuestions?: string[];
  evaluationCriteria: string[];
  scenarioType?: "normal" | "pressure" | "case_study" | "behavioral";
  context?: string;
  hints?: string[];
}

export function generateMockAdvancedQuestions(): { [industry: string]: AdvancedQuestion[] } {
  return {
    "tech": [
      {
        id: "tech_001",
        text: "大規模なレガシーシステムのモダナイゼーションプロジェクトを任されました。技術的負債が蓄積した状況で、どのようなアプローチで進めますか？",
        category: "技術戦略",
        difficulty: "hard",
        industry: "tech",
        jobRole: "software_engineer",
        estimatedTime: 300,
        followUpQuestions: [
          "ステークホルダーとの合意形成はどう図りますか？",
          "リスク評価とミティゲーション戦略は？"
        ],
        evaluationCriteria: [
          "技術的知見の深さ",
          "プロジェクト管理能力",
          "リスク管理意識",
          "ステークホルダー調整力"
        ],
        scenarioType: "case_study",
        context: "あなたは主任エンジニアとして、創業10年のスタートアップで働いています。急速な成長により、システムの複雑性が増している状況です。",
        hints: [
          "段階的な移行戦略を考える",
          "ビジネス影響を最小化する方法",
          "チーム編成と責任分担"
        ]
      }
    ],
    "consulting": [
      {
        id: "consulting_001",
        text: "クライアントは小売業界の老舗企業です。デジタル化が遅れており、売上が3年連続で減少しています。どのような戦略提案をしますか？",
        category: "戦略立案",
        difficulty: "hard",
        industry: "consulting", 
        jobRole: "strategy_consultant",
        estimatedTime: 360,
        followUpQuestions: [
          "実装優先順位はどう決めますか？",
          "ROIの測定方法は？",
          "組織変革の課題は？"
        ],
        evaluationCriteria: [
          "論理的思考力",
          "構造化能力",
          "ビジネス理解",
          "実行可能性の検討"
        ],
        scenarioType: "case_study",
        context: "創業80年の老舗デパートチェーン。従業員数3000人、年商500億円。",
        hints: [
          "MECE思考の活用",
          "Quick Winの特定",
          "Change Managementの重要性"
        ]
      }
    ]
  };
}

/**
 * 音声解析シミュレーション（mockData.tsから統合）
 */
export async function analyzeAudio(audioFile: File): Promise<{ text: string; feedback: BasicFeedback }> {
  // サンプル回答の場合は解析時間を短縮
  const isSmallFile = audioFile.size <= 1024;
  const delay = isSmallFile ? 100 : 1000;
  await new Promise(resolve => setTimeout(resolve, delay));
  
  // モック解析結果
  const transcription = "ありがとうございます。私は○○大学を卒業後、△△会社で3年間フロントエンド開発に携わってきました。特にReactとTypeScriptを使用したSPA開発に従事し、ユーザビリティの向上に取り組んでまいりました。";
  
  const feedback: BasicFeedback = {
    id: `feedback_${Date.now()}`,
    interviewId: "current_interview",
    questionId: "current_question", 
    candidateAnswer: transcription,
    aiAnalysis: "回答は明確で構成されており、技術的な経験が適切に説明されています。具体的な技術スタックと業務内容が述べられており、信頼性が高い回答です。",
    emotionScore: 7.8,
    confidenceScore: 8.1,
    relevanceScore: 8.5,
    clarityScore: 8.3,
    overall: 8.2,
    overallRating: 4,
    suggestions: [
      "より具体的なプロジェクトの成果や数値があると説得力が増します",
      "技術選択の理由や学習過程についても言及すると良いでしょう"
    ],
    timestamp: new Date().toISOString()
  };
  
  return { text: transcription, feedback };
}

/**
 * データアクセス関数（統合版）
 */
export const UnifiedMockDataService = {
  // 基本的な質問とフィードバック
  getBasicQuestions: async (): Promise<BasicQuestion[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return generateMockBasicQuestions();
  },

  getBasicQuestionById: async (id: string): Promise<BasicQuestion | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const questions = generateMockBasicQuestions();
    return questions.find(q => q.id === id) || null;
  },

  getBasicFeedbacks: async (interviewId?: string): Promise<BasicFeedback[]> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const feedbacks = generateMockBasicFeedbacks();
    if (interviewId) {
      return feedbacks.filter(f => f.interviewId === interviewId);
    }
    return feedbacks;
  },

  // 業界別質問
  getQuestionsByIndustry: (industry: string): AdvancedQuestion[] => {
    const advancedQuestions = generateMockAdvancedQuestions();
    return advancedQuestions[industry] || [];
  },

  // 音声解析
  analyzeAudio: analyzeAudio,

  // 音声合成シミュレーション
  synthesizeText: async (text: string): Promise<Blob> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const dummyAudio = new Blob(["dummy audio data"], { type: "audio/wav" });
    return dummyAudio;
  }
};

/**
 * データ検証用ユーティリティ
 */
export class MockDataValidator {
  /**
   * 生成されたデータの妥当性をチェック
   */
  static validateComprehensiveReport(report: ComprehensiveInterviewReport): boolean {
    try {
      // 必須フィールドの存在確認
      const requiredFields = [
        'metadata',
        'overallAssessment',
        'detailedEvaluation',
        'summary',
      ];

      for (const field of requiredFields) {
        if (!(field in report)) {
          console.error(`Missing required field: ${field}`);
          return false;
        }
      }

      // スコアの範囲確認
      if (report.overallAssessment.score < 0 || report.overallAssessment.score > 100) {
        console.error('Overall score out of range');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Data validation failed:', error);
      return false;
    }
  }

  /**
   * 利用可能な候補者IDを返す
   */
  static getAvailableCandidateIds(): string[] {
    return ['candidate-001', 'candidate-002'];
  }

  /**
   * デバッグ用データサマリー
   */
  static getDataSummary() {
    return {
      candidateProfiles: MockDataValidator.getAvailableCandidateIds().length,
      basicQuestions: generateMockBasicQuestions().length,
      advancedQuestions: Object.values(generateMockAdvancedQuestions()).flat().length,
      lastGenerated: new Date().toISOString(),
      version: '2.0.0',
    };
  }
}