/** @type {import('next').NextConfig} */
const path = require('path');
const dotenv = require('dotenv');

// ルートディレクトリの.envファイルを読み込む（ローカル開発環境用）
if (process.env.NODE_ENV !== 'production') {
  dotenv.config({ path: path.resolve(__dirname, '../../.env') });
}

// 環境変数の存在確認（警告のみ、ビルドは停止しない）
if (!process.env.NEXT_PUBLIC_AZURE_SPEECH_KEY || !process.env.NEXT_PUBLIC_AZURE_SPEECH_REGION) {
  console.warn('⚠️  Azure Speech Service環境変数が設定されていません。');
  console.warn('NEXT_PUBLIC_AZURE_SPEECH_KEY と NEXT_PUBLIC_AZURE_SPEECH_REGION を設定してください。');
  // process.exit(1); // 本番環境では環境変数はVercelダッシュボードで設定されるため、ここでexitしない
}

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  trailingSlash: false,
  env: {
    NEXT_PUBLIC_AZURE_SPEECH_KEY: process.env.NEXT_PUBLIC_AZURE_SPEECH_KEY,
    NEXT_PUBLIC_AZURE_SPEECH_REGION: process.env.NEXT_PUBLIC_AZURE_SPEECH_REGION,
  },
  transpilePackages: [
    '@mensetsu-kun/shared', // pnpm workspaceパッケージをトランスパイル対象に追加
  ],
  async rewrites() {
    // 環境に応じてAPIベースURLを切り替え
    const apiBaseUrl = process.env.NODE_ENV === 'production'
      ? process.env.NEXT_PUBLIC_API_BASE_URL // プロダクション環境用Vercel環境変数
      : 'http://localhost:8080'; // ローカル開発環境用

    // プロダクション環境でAPIベースURLが設定されていない場合は空の配列を返す
    if (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_API_BASE_URL) {
      console.warn('⚠️  プロダクション環境でNEXT_PUBLIC_API_BASE_URLが設定されていません。');
      return [];
    }

    return [
      {
        source: '/api/:path*',
        destination: `${apiBaseUrl}/api/:path*`,
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version' },
        ],
      },
    ];
  },
};

module.exports = nextConfig
