/* アクセシビリティ対応とダークモード強制適用CSS */

/* フォントサイズ変数 */
:root {
  --font-size-base: 16px;
  --motion-reduce: no-preference;
}

/* ダークモード時の背景色強制適用 */
[data-theme="dark"] #__next,
[data-theme="dark"] #__next > div,
[data-theme="dark"] .css-cx4sf0,
[data-theme="dark"] .chakra-ui-dark {
  background-color: #171923 !important;
  color: #F7FAFC !important;
}

/* ライトモード時の背景色強制適用 */
[data-theme="light"] #__next,
[data-theme="light"] #__next > div,
[data-theme="light"] .css-cx4sf0,
[data-theme="light"] .chakra-ui-light {
  background-color: #F9FAFB !important;
  color: #1A202C !important;
}

/* Chakra UIのテーマ切り替えアニメーション */
#__next,
#__next > div,
body {
  transition: background-color 0.3s ease, color 0.3s ease !important;
}

/* レイアウトコンテナの幅制限をリセット */
#__next,
#__next > div,
.chakra-container,
[data-theme] #__next,
[data-theme] #__next > div {
  max-width: none !important;
  width: 100% !important;
}

/* ダークモード時のテキストカラー改善 */
[data-theme="dark"] [class*="css-"] {
  --chakra-colors-gray-600: #A0AEC0 !important;
  --chakra-colors-gray-500: #CBD5E0 !important;
  --chakra-colors-gray-400: #E2E8F0 !important;
  --chakra-colors-neutral-500: #A0AEC0 !important;
  --chakra-colors-neutral-600: #A0AEC0 !important;
  --chakra-colors-neutral-800: #E2E8F0 !important;
}

[data-theme="dark"] .chakra-text[color="gray.600"],
[data-theme="dark"] [class*="css-"][style*="gray.600"],
[data-theme="dark"] [style*="color: var(--chakra-colors-gray-600)"] {
  color: var(--chakra-colors-gray-400) !important;
}

/* ダークモード時の特定要素のカラー調整 */
[data-theme="dark"] .chakra-stat__label,
[data-theme="dark"] .chakra-stat__help-text,
[data-theme="dark"] .chakra-form__label,
[data-theme="dark"] .chakra-badge,
[data-theme="dark"] .chakra-text {
  color: var(--chakra-colors-gray-300) !important;
}

/* ダークモード時の動的生成クラスのカラー調整（高優先度） */
html[data-theme="dark"] [class*="css-"][style*="color"],
body[data-theme="dark"] [class*="css-"][style*="color"] {
  color: var(--chakra-colors-gray-300) !important;
}

/* gray.600を使用している要素の強制上書き */
[data-theme="dark"] [style*="gray-600"],
[data-theme="dark"] [style*="gray.600"],
[data-theme="dark"] .css-1dux963,
[data-theme="dark"] .css-1bm1he0,
[data-theme="dark"] .css-kzjxsw,
[data-theme="dark"] .css-12npqni,
[data-theme="dark"] .css-1029vkl,
[data-theme="dark"] .css-1kb1z75,
[data-theme="dark"] .css-8ry2zc,
[data-theme="dark"] .css-10w1fjl,
[data-theme="dark"] .css-8w8vsv,
[data-theme="dark"] .css-ei60ws {
  color: #CBD5E0 !important;
}

/* 特定のregion要素のダークモード対応 */
[data-theme="dark"] [role="region"] {
  background-color: var(--chakra-colors-gray-800) !important;
  border-color: var(--chakra-colors-gray-700) !important;
}

/* ダークモード時のCard要素 */
[data-theme="dark"] .chakra-card {
  background-color: var(--chakra-colors-gray-800) !important;
  border-color: var(--chakra-colors-gray-700) !important;
}

/* ダークモード時のHeading要素 */
[data-theme="dark"] .chakra-heading {
  color: var(--chakra-colors-gray-100) !important;
}

/* ダークモード時のList要素 */
[data-theme="dark"] .chakra-list__item {
  color: var(--chakra-colors-gray-300) !important;
}

/* ダークモード時の一般的なテキスト要素 */
[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div:not([class*="chakra-"]):not([id="__next"]) {
  color: var(--chakra-colors-gray-300);
}

/* ダークモード時のアラート系要素 */
[data-theme="dark"] .chakra-alert {
  background-color: var(--chakra-colors-gray-800) !important;
  border-color: var(--chakra-colors-gray-700) !important;
  color: var(--chakra-colors-gray-100) !important;
}

/* ダークモード時のチェックボックス関連 */
[data-theme="dark"] .chakra-checkbox__label {
  color: var(--chakra-colors-gray-300) !important;
}

/* ダークモード時のStat要素の徹底的な修正 */
[data-theme="dark"] .chakra-stat,
[data-theme="dark"] .chakra-stat__label,
[data-theme="dark"] .chakra-stat__number,
[data-theme="dark"] .chakra-stat__help-text {
  color: var(--chakra-colors-gray-300) !important;
}

/* ダークモード時のCard内のテキスト */
[data-theme="dark"] .chakra-card .chakra-text,
[data-theme="dark"] .chakra-card p,
[data-theme="dark"] .chakra-card span {
  color: var(--chakra-colors-gray-300) !important;
}

/* ダークモード時のVStack内のテキスト */
[data-theme="dark"] .chakra-stack > *,
[data-theme="dark"] .chakra-stack p,
[data-theme="dark"] .chakra-stack span {
  color: var(--chakra-colors-gray-300) !important;
}

/* より強力な一般的なルール */
[data-theme="dark"] [class*="css-"] p,
[data-theme="dark"] [class*="css-"] span,
[data-theme="dark"] [class*="css-"] div:not([class*="chakra-stack"]):not([class*="chakra-container"]) {
  color: var(--chakra-colors-gray-300) !important;
}

/* フォントサイズの動的調整 */
html {
  font-size: var(--font-size-base);
}

/* 高コントラストモード */
.high-contrast {
  filter: contrast(150%);
}

.high-contrast button,
.high-contrast input,
.high-contrast textarea {
  border: 2px solid #000 !important;
}

.high-contrast text {
  color: #000 !important;
}

/* アニメーション軽減 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* カスタムのモーション軽減 */
.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* フォーカス表示の強化 */
*:focus {
  outline: 2px solid #2563EB !important;
  outline-offset: 2px !important;
}

/* スクリーンリーダー用テキスト */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* スキップリンク */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #2563EB;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
  outline: 3px solid rgba(37, 99, 235, 0.3);
}

/* キーボードナビゲーション支援 */
[tabindex="-1"]:focus {
  outline: none;
}

/* タッチターゲットのサイズ確保 */
button,
input,
select,
textarea,
a {
  min-height: 44px;
  min-width: 44px;
}

/* 色に依存しない情報伝達 */
.error::before {
  content: "⚠ ";
  font-weight: bold;
}

.success::before {
  content: "✓ ";
  font-weight: bold;
}

.warning::before {
  content: "⚠ ";
  font-weight: bold;
}

/* 読みやすさの向上 - レイアウトコンテナは除外 */
p, li {
  line-height: 1.5;
  max-width: 70ch; /* 読みやすい行長 */
}

/* テキストコンテンツのみに適用（レイアウトコンテナは除外） */
.readable-text,
article p,
section p,
.content-text {
  line-height: 1.5;
  max-width: 70ch;
}

/* コントラスト比の確保 */
.low-contrast-fix {
  color: #1a1a1a !important;
  background: #ffffff !important;
}

/* 音声読み上げ用の一時停止 */
.pause-speech {
  speak: never;
}

/* ランドマーク要素の明確化 */
main[role="main"],
nav[role="navigation"],
aside[role="complementary"],
header[role="banner"],
footer[role="contentinfo"] {
  outline: 1px solid transparent; /* フォーカス時のみ表示 */
}

main[role="main"]:focus,
nav[role="navigation"]:focus,
aside[role="complementary"]:focus,
header[role="banner"]:focus,
footer[role="contentinfo"]:focus {
  outline-color: #2563EB;
}