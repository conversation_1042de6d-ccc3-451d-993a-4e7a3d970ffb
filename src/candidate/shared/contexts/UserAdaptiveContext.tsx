/**
 * ユーザーアダプティブコンテキスト
 * プロダクト憲法第二条「個人の尊重」に基づく
 * 個人の特性や状態に応じたパーソナライゼーション機能を提供
 */
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// ユーザーの経験レベル
export type ExperienceLevel = 'beginner' | 'intermediate' | 'advanced';

// 感情状態
export type EmotionalState = 'anxious' | 'nervous' | 'calm' | 'confident' | 'excited';

// 学習スタイル
export type LearningStyle = 'visual' | 'auditory' | 'kinesthetic' | 'reading';

// プログレスデータの型定義
export interface ProgressData {
  sessionDate: string;
  scenarioId: string;
  overallScore: number;
  improvementAreas: string[];
  achievements: string[];
  timeSpent: number; // 分
}

// 個人目標の型定義
export interface PersonalGoal {
  id: string;
  title: string;
  description: string;
  category: 'communication' | 'technical' | 'behavioral' | 'confidence';
  targetDate?: string;
  isCompleted: boolean;
  progress: number; // 0-100
}

// ユーザーアダプティブ状態の型定義
export interface UserAdaptiveState {
  // 基本属性
  experienceLevel: ExperienceLevel;
  emotionalState: EmotionalState;
  learningStyle: LearningStyle;
  
  // パーソナライゼーション情報
  personalGoals: PersonalGoal[];
  progressHistory: ProgressData[];
  preferences: {
    feedbackPacing: 'immediate' | 'gradual' | 'delayed';
    supportLevel: 'minimal' | 'moderate' | 'high';
    challengePreference: 'easy' | 'moderate' | 'challenging';
    reminderFrequency: 'none' | 'low' | 'medium' | 'high';
  };
  
  // 現在のセッション状態
  currentSession: {
    startTime: string;
    scenarioId: string;
    currentStress: number; // 1-10
    engagementLevel: number; // 1-10
    confidenceLevel: number; // 1-10
  } | null;
  
  // 適応的設定
  adaptiveSettings: {
    autoAdjustDifficulty: boolean;
    enableEncouragement: boolean;
    showProgressIndicators: boolean;
    personalizedFeedback: boolean;
  };
}

// コンテキストアクション
export interface UserAdaptiveActions {
  updateExperienceLevel: (level: ExperienceLevel) => void;
  updateEmotionalState: (state: EmotionalState) => void;
  updateLearningStyle: (style: LearningStyle) => void;
  addPersonalGoal: (goal: Omit<PersonalGoal, 'id'>) => void;
  updatePersonalGoal: (id: string, updates: Partial<PersonalGoal>) => void;
  deletePersonalGoal: (id: string) => void;
  addProgressData: (progress: ProgressData) => void;
  startSession: (scenarioId: string) => void;
  endSession: () => void;
  updateSessionMetrics: (metrics: {
    stress?: number;
    engagement?: number;
    confidence?: number;
  }) => void;
  updatePreferences: (preferences: Partial<UserAdaptiveState['preferences']>) => void;
  updateAdaptiveSettings: (settings: Partial<UserAdaptiveState['adaptiveSettings']>) => void;
  resetUserData: () => void;
}

// コンテキストの型定義
export interface UserAdaptiveContextType {
  state: UserAdaptiveState;
  actions: UserAdaptiveActions;
}

// デフォルト状態
const defaultState: UserAdaptiveState = {
  experienceLevel: 'beginner',
  emotionalState: 'nervous',
  learningStyle: 'visual',
  personalGoals: [],
  progressHistory: [],
  preferences: {
    feedbackPacing: 'gradual',
    supportLevel: 'moderate',
    challengePreference: 'moderate',
    reminderFrequency: 'medium',
  },
  currentSession: null,
  adaptiveSettings: {
    autoAdjustDifficulty: true,
    enableEncouragement: true,
    showProgressIndicators: true,
    personalizedFeedback: true,
  },
};

// コンテキスト作成
const UserAdaptiveContext = createContext<UserAdaptiveContextType | null>(null);

// プロバイダーコンポーネント
export const UserAdaptiveProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useState<UserAdaptiveState>(() => {
    // ローカルストレージから初期状態を復元
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('userAdaptiveState');
      if (saved) {
        try {
          return { ...defaultState, ...JSON.parse(saved) };
        } catch (error) {
          console.warn('Failed to parse saved user adaptive state:', error);
        }
      }
    }
    return defaultState;
  });

  // 状態をローカルストレージに保存
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('userAdaptiveState', JSON.stringify(state));
    }
  }, [state]);

  // アクション定義
  const actions: UserAdaptiveActions = {
    updateExperienceLevel: (level) => {
      setState(prev => ({ ...prev, experienceLevel: level }));
    },

    updateEmotionalState: (emotionalState) => {
      setState(prev => ({ ...prev, emotionalState }));
    },

    updateLearningStyle: (style) => {
      setState(prev => ({ ...prev, learningStyle: style }));
    },

    addPersonalGoal: (goalData) => {
      const newGoal: PersonalGoal = {
        ...goalData,
        id: `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };
      setState(prev => ({
        ...prev,
        personalGoals: [...prev.personalGoals, newGoal],
      }));
    },

    updatePersonalGoal: (id, updates) => {
      setState(prev => ({
        ...prev,
        personalGoals: prev.personalGoals.map(goal =>
          goal.id === id ? { ...goal, ...updates } : goal
        ),
      }));
    },

    deletePersonalGoal: (id) => {
      setState(prev => ({
        ...prev,
        personalGoals: prev.personalGoals.filter(goal => goal.id !== id),
      }));
    },

    addProgressData: (progress) => {
      setState(prev => ({
        ...prev,
        progressHistory: [progress, ...prev.progressHistory].slice(0, 50), // 最新50件を保持
      }));
    },

    startSession: (scenarioId) => {
      setState(prev => ({
        ...prev,
        currentSession: {
          startTime: new Date().toISOString(),
          scenarioId,
          currentStress: 5,
          engagementLevel: 5,
          confidenceLevel: 5,
        },
      }));
    },

    endSession: () => {
      setState(prev => ({ ...prev, currentSession: null }));
    },

    updateSessionMetrics: (metrics) => {
      setState(prev => ({
        ...prev,
        currentSession: prev.currentSession ? {
          ...prev.currentSession,
          ...metrics,
        } : null,
      }));
    },

    updatePreferences: (preferences) => {
      setState(prev => ({
        ...prev,
        preferences: { ...prev.preferences, ...preferences },
      }));
    },

    updateAdaptiveSettings: (settings) => {
      setState(prev => ({
        ...prev,
        adaptiveSettings: { ...prev.adaptiveSettings, ...settings },
      }));
    },

    resetUserData: () => {
      setState(defaultState);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('userAdaptiveState');
      }
    },
  };

  return (
    <UserAdaptiveContext.Provider value={{ state, actions }}>
      {children}
    </UserAdaptiveContext.Provider>
  );
};

// カスタムフック
export const useUserAdaptive = () => {
  const context = useContext(UserAdaptiveContext);
  if (!context) {
    throw new Error('useUserAdaptive must be used within a UserAdaptiveProvider');
  }
  return context;
};

// 便利なカスタムフック群
export const useExperienceLevel = () => {
  const { state } = useUserAdaptive();
  return state.experienceLevel;
};

export const useEmotionalState = () => {
  const { state, actions } = useUserAdaptive();
  return {
    emotionalState: state.emotionalState,
    updateEmotionalState: actions.updateEmotionalState,
  };
};

export const usePersonalGoals = () => {
  const { state, actions } = useUserAdaptive();
  return {
    goals: state.personalGoals,
    addGoal: actions.addPersonalGoal,
    updateGoal: actions.updatePersonalGoal,
    deleteGoal: actions.deletePersonalGoal,
  };
};

export const useSessionMetrics = () => {
  const { state, actions } = useUserAdaptive();
  return {
    currentSession: state.currentSession,
    startSession: actions.startSession,
    endSession: actions.endSession,
    updateMetrics: actions.updateSessionMetrics,
  };
};

export const useProgressHistory = () => {
  const { state, actions } = useUserAdaptive();
  return {
    history: state.progressHistory,
    addProgress: actions.addProgressData,
  };
};