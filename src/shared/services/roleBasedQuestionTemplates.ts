/**
 * 役割別質問テンプレートシステム
 * InterviewerRoleに基づく質問生成とカスタマイズ
 * プロダクト憲法の3原則に基づく心理的安全性の確保
 */

import {
  InterviewerRoleType,
  InterviewerRole,
  InterviewerPersonality,
  QuestionStyle,
} from '../types/interviewer-roles';
import {
  QuestionTemplate,
  QuestionCategory,
  TemplateVariable,
} from '../types/intelligent-questions';

// ===== 役割別質問テンプレート =====

/**
 * 役割別質問テンプレートの定義
 */
export interface RoleBasedQuestionTemplate extends QuestionTemplate {
  // 役割固有の情報
  roleType: InterviewerRoleType;
  roleSpecific: {
    // 役割に特化した質問内容
    focusAreas: string[];
    evaluationPerspective: string;
    keywordTriggers: string[];
    
    // パーソナリティ調整
    personalityAdjustments: {
      formality: Record<InterviewerPersonality['formality'], string>;
      supportiveness: Record<InterviewerPersonality['supportiveness'], string>;
      questioningStyle: Record<InterviewerPersonality['questioningStyle'], string>;
    };
    
    // 質問スタイル調整
    styleAdjustments: {
      behavioralVariant?: string;
      technicalVariant?: string;
      visionaryVariant?: string;
      leadershipVariant?: string;
    };
  };
}

/**
 * 役割別デフォルト質問テンプレート
 * 各役割タイプに対応するテンプレートのマップ
 */
export class RoleBasedQuestionTemplates {
  private static instance: RoleBasedQuestionTemplates;
  private roleTemplates: Map<InterviewerRoleType, RoleBasedQuestionTemplate[]> = new Map();

  constructor() {
    this.initializeRoleTemplates();
  }

  public static getInstance(): RoleBasedQuestionTemplates {
    if (!RoleBasedQuestionTemplates.instance) {
      RoleBasedQuestionTemplates.instance = new RoleBasedQuestionTemplates();
    }
    return RoleBasedQuestionTemplates.instance;
  }

  /**
   * 役割に基づく質問テンプレートを取得
   */
  public getTemplatesForRole(roleType: InterviewerRoleType): RoleBasedQuestionTemplate[] {
    return this.roleTemplates.get(roleType) || [];
  }

  /**
   * 役割とカテゴリに基づくテンプレートフィルタリング
   */
  public getTemplatesForRoleAndCategory(
    roleType: InterviewerRoleType,
    category: QuestionCategory
  ): RoleBasedQuestionTemplate[] {
    const roleTemplates = this.getTemplatesForRole(roleType);
    return roleTemplates.filter(template => template.category === category);
  }

  /**
   * 役割、カテゴリ、質問スタイルに基づくテンプレート選択
   */
  public selectTemplatesForRoleStyle(
    roleType: InterviewerRoleType,
    category: QuestionCategory,
    questionStyle: QuestionStyle,
    count: number = 3
  ): RoleBasedQuestionTemplate[] {
    let templates = this.getTemplatesForRoleAndCategory(roleType, category);
    
    // 質問スタイルに基づくフィルタリング
    templates = this.filterByQuestionStyle(templates, category, questionStyle);
    
    // スコアリングとソート
    const scoredTemplates = templates.map(template => ({
      template,
      score: this.calculateTemplateScore(template, questionStyle),
    }));
    
    scoredTemplates.sort((a, b) => b.score - a.score);
    
    return scoredTemplates
      .slice(0, Math.min(count, templates.length))
      .map(item => item.template);
  }

  /**
   * パーソナリティに基づく質問テンプレートの調整
   */
  public adjustTemplateForPersonality(
    template: RoleBasedQuestionTemplate,
    personality: InterviewerPersonality
  ): RoleBasedQuestionTemplate {
    const adjustedTemplate = { ...template };
    let adjustedText = template.template;

    // フォーマリティの調整
    const formalityAdjustment = template.roleSpecific.personalityAdjustments.formality[personality.formality];
    if (formalityAdjustment) {
      adjustedText = this.applyTextAdjustment(adjustedText, formalityAdjustment);
    }

    // サポート度の調整
    const supportivenessAdjustment = template.roleSpecific.personalityAdjustments.supportiveness[personality.supportiveness];
    if (supportivenessAdjustment) {
      adjustedText = this.applyTextAdjustment(adjustedText, supportivenessAdjustment);
    }

    // 質問スタイルの調整
    const questioningStyleAdjustment = template.roleSpecific.personalityAdjustments.questioningStyle[personality.questioningStyle];
    if (questioningStyleAdjustment) {
      adjustedText = this.applyTextAdjustment(adjustedText, questioningStyleAdjustment);
    }

    // 心理的安全性の調整
    if (personality.empathyLevel === 'high') {
      adjustedTemplate.safetyConfiguration = {
        ...adjustedTemplate.safetyConfiguration,
        includeReassurance: true,
        gentleVersion: adjustedTemplate.safetyConfiguration.gentleVersion || 
          this.generateGentleVersion(adjustedText),
      };
    }

    adjustedTemplate.template = adjustedText;
    return adjustedTemplate;
  }

  // ===== プライベートメソッド =====

  /**
   * 質問スタイルに基づくテンプレートフィルタリング
   */
  private filterByQuestionStyle(
    templates: RoleBasedQuestionTemplate[],
    category: QuestionCategory,
    questionStyle: QuestionStyle
  ): RoleBasedQuestionTemplate[] {
    return templates.filter(template => {
      // カテゴリ別フォーカス重視度チェック
      const focusScore = this.getCategoryFocusScore(category, questionStyle);
      
      // フォーカススコアが低い場合はフィルタリング
      if (focusScore < 0.3) return false;
      
      // 深掘りトピックとの一致チェック
      if (questionStyle.deepDiveTopics.length > 0) {
        const hasMatchingTopic = template.roleSpecific.focusAreas.some(area =>
          questionStyle.deepDiveTopics.some(topic => 
            area.toLowerCase().includes(topic.toLowerCase())
          )
        );
        if (!hasMatchingTopic) return false;
      }
      
      return true;
    });
  }

  /**
   * カテゴリ別フォーカススコアの取得
   */
  private getCategoryFocusScore(category: QuestionCategory, questionStyle: QuestionStyle): number {
    const focusMap: Record<QuestionCategory, keyof QuestionStyle> = {
      'behavioral': 'behavioralFocus',
      'technical': 'technicalFocus',
      'leadership': 'leadershipFocus',
      'teamwork': 'teamworkFocus',
      'problem-solving': 'problemSolvingFocus',
      'company-culture-fit': 'culturalFitFocus',
      'motivation': 'visionaryFocus',
      'self-introduction': 'culturalFitFocus',
      'experience-skills': 'behavioralFocus',
      'career-goals': 'visionaryFocus',
      'situational': 'problemSolvingFocus',
    };

    const focusKey = focusMap[category];
    return (questionStyle[focusKey] as number) || 0.5;
  }

  /**
   * テンプレートのスコアリング
   */
  private calculateTemplateScore(
    template: RoleBasedQuestionTemplate,
    questionStyle: QuestionStyle
  ): number {
    let score = 0.5; // ベーススコア

    // 優先カテゴリとの一致
    if (questionStyle.preferredCategories.includes(template.category)) {
      score += 0.3;
    }

    // 深掘りトピックとの一致
    const matchingTopics = template.roleSpecific.focusAreas.filter(area =>
      questionStyle.deepDiveTopics.some(topic => 
        area.toLowerCase().includes(topic.toLowerCase())
      )
    );
    score += matchingTopics.length * 0.1;

    // 心理的安全性配慮
    if (template.safetyConfiguration.includeReassurance) {
      score += 0.1;
    }

    return Math.min(1.0, score);
  }

  /**
   * テキスト調整の適用
   */
  private applyTextAdjustment(text: string, adjustment: string): string {
    // 調整タイプに基づいてテキストを変更
    switch (adjustment) {
      case 'add_prefix':
        return `ご質問させていただきます。${text}`;
      case 'make_casual':
        return text.replace(/です。/g, 'ですね。').replace(/ください/g, 'くださいね');
      case 'make_formal':
        return text.replace(/ですね。/g, 'です。').replace(/くださいね/g, 'ください');
      case 'add_encouragement':
        return `${text} お気軽にお答えください。`;
      case 'make_directive':
        return text.replace(/お聞かせください/g, '教えてください').replace(/いかがですか/g, 'どうですか');
      case 'make_conversational':
        return `${text} どのような経験をお持ちでしょうか。`;
      case 'add_socratic':
        return `${text} その理由も含めて考えをお聞かせください。`;
      default:
        return text;
    }
  }

  /**
   * 優しい版の生成
   */
  private generateGentleVersion(text: string): string {
    const gentlePrefixes = [
      'お時間のある時で構いませんので、',
      'ご自身のペースで、',
      'どのような内容でも結構ですので、',
    ];
    
    const randomPrefix = gentlePrefixes[Math.floor(Math.random() * gentlePrefixes.length)];
    return `${randomPrefix}${text}`;
  }

  /**
   * 役割別テンプレートの初期化
   */
  private initializeRoleTemplates(): void {
    // HR (人事) 役割のテンプレート
    this.roleTemplates.set('hr', [
      // 自己紹介カテゴリ
      {
        id: 'hr_self_intro_basic',
        name: 'HR基本自己紹介',
        category: 'self-introduction',
        template: 'まずは自己紹介をお願いします。お名前、これまでのご経験、そして当社への応募理由を簡潔に教えてください。',
        variables: [],
        adaptationRules: [],
        roleType: 'hr',
        roleSpecific: {
          focusAreas: ['人物像把握', '企業適合性', 'コミュニケーション能力'],
          evaluationPerspective: '企業文化との適合性と基本的なコミュニケーション能力',
          keywordTriggers: ['経験', '志望理由', '強み'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            behavioralVariant: 'これまでの行動パターンや価値観も含めて教えてください。',
            visionaryVariant: 'キャリアビジョンと併せてお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'リラックスして、ご自身のペースで自己紹介をお聞かせください。',
          encouragingVersion: 'あなたのことを詳しく知りたいと思います。どのようなお話でも歓迎です。',
        },
      },
      
      // 企業文化適合性カテゴリ
      {
        id: 'hr_culture_fit_values',
        name: 'HR価値観適合性',
        category: 'company-culture-fit',
        template: '当社では「{{companyValues}}」を大切にしています。あなたが仕事をする上で大切にしている価値観と、どのような点で共通していると思いますか。',
        variables: [
          {
            name: 'companyValues',
            type: 'company',
            required: true,
          },
        ],
        adaptationRules: [],
        roleType: 'hr',
        roleSpecific: {
          focusAreas: ['価値観', '企業文化理解', '適合性'],
          evaluationPerspective: '企業価値観との一致と文化適応力',
          keywordTriggers: ['価値観', '文化', '適合'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            visionaryVariant: '将来的な価値観の発展も含めて教えてください。',
            behavioralVariant: '具体的な行動例も併せてお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['不適合', '合わない'],
          includeReassurance: true,
          gentleVersion: 'ご自身の価値観について、思うところをお聞かせください。',
          encouragingVersion: 'あなたらしい価値観をぜひ教えてください。',
        },
      },

      // 志望動機カテゴリ
      {
        id: 'hr_motivation_company',
        name: 'HR企業志望動機',
        category: 'motivation',
        template: '数ある企業の中から当社を選んでいただいた理由を教えてください。特に魅力に感じた点があれば詳しくお聞かせください。',
        variables: [],
        adaptationRules: [],
        roleType: 'hr',
        roleSpecific: {
          focusAreas: ['志望動機', '企業理解', '熱意'],
          evaluationPerspective: '企業への理解度と入社意欲の強さ',
          keywordTriggers: ['志望理由', '魅力', '選択理由'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            visionaryVariant: '長期的なキャリアビジョンとの関連も含めて教えてください。',
            behavioralVariant: '選択に至った具体的なプロセスもお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '当社に興味を持っていただいたきっかけを教えてください。',
          encouragingVersion: '当社のどのような点に魅力を感じていただけましたか。',
        },
      },
    ]);

    // CEO 役割のテンプレート
    this.roleTemplates.set('ceo', [
      // ビジョン・将来目標カテゴリ
      {
        id: 'ceo_vision_leadership',
        name: 'CEOビジョン・リーダーシップ',
        category: 'leadership',
        template: '将来、あなたがリーダーとして組織を率いるとしたら、どのようなビジョンを描き、どのように実現していきたいと考えますか。',
        variables: [],
        adaptationRules: [],
        roleType: 'ceo',
        roleSpecific: {
          focusAreas: ['ビジョナリー思考', 'リーダーシップポテンシャル', '戦略的思考'],
          evaluationPerspective: '将来の経営幹部としてのポテンシャルと戦略的思考力',
          keywordTriggers: ['ビジョン', 'リーダーシップ', '戦略'],
          personalityAdjustments: {
            formality: {
              casual: '',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            visionaryVariant: '業界全体への影響も含めて考えをお聞かせください。',
            behavioralVariant: '過去のリーダーシップ経験も踏まえて教えてください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '問題'],
          includeReassurance: true,
          gentleVersion: 'あなたなりのリーダーシップ観について教えてください。',
          encouragingVersion: 'あなたの理想とするリーダー像をぜひお聞かせください。',
        },
      },

      // 事業理解・戦略カテゴリ
      {
        id: 'ceo_business_strategy',
        name: 'CEO事業戦略理解',
        category: 'problem-solving',
        template: '当社の事業について調べていただいたと思いますが、今後5年間で業界や当社が直面する最大の課題は何だと思いますか。そしてその解決に向けてどのようなアプローチが必要だと考えますか。',
        variables: [],
        adaptationRules: [],
        roleType: 'ceo',
        roleSpecific: {
          focusAreas: ['事業理解', '戦略的思考', '課題解決能力'],
          evaluationPerspective: '事業への理解度と戦略的な課題解決能力',
          keywordTriggers: ['事業戦略', '課題', '解決策'],
          personalityAdjustments: {
            formality: {
              casual: '',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            visionaryVariant: '長期的な業界の変化も考慮してお答えください。',
            technicalVariant: '技術的な側面からのアプローチも含めて教えてください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['間違い', '不正解'],
          includeReassurance: true,
          gentleVersion: '業界や当社についてのご理解を教えてください。',
          encouragingVersion: 'あなたの視点からの分析をぜひお聞かせください。',
        },
      },
    ]);

    // Technical Lead 役割のテンプレート
    this.roleTemplates.set('technical_lead', [
      // 技術的経験カテゴリ
      {
        id: 'tech_lead_technical_experience',
        name: '技術リード技術経験',
        category: 'technical',
        template: '{{position}}のポジションで最も重要だと考える技術スキルは何ですか。そのスキルを身につけるために、これまでどのような学習や実践を行ってきましたか。',
        variables: [
          {
            name: 'position',
            type: 'position',
            required: true,
          },
        ],
        adaptationRules: [],
        roleType: 'technical_lead',
        roleSpecific: {
          focusAreas: ['技術スキル', '学習能力', '実践経験'],
          evaluationPerspective: '技術的な深さと継続的な学習姿勢',
          keywordTriggers: ['技術', 'スキル', '学習'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: '',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            technicalVariant: '具体的な実装経験も含めて詳しく教えてください。',
            behavioralVariant: '学習プロセスでの工夫や課題克服方法もお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['不足', '劣る'],
          includeReassurance: true,
          gentleVersion: 'これまでの技術的な経験について教えてください。',
          encouragingVersion: 'あなたの技術的な取り組みについてぜひお聞かせください。',
        },
      },

      // 問題解決カテゴリ
      {
        id: 'tech_lead_problem_solving',
        name: '技術リード問題解決',
        category: 'problem-solving',
        template: '技術的な課題に直面した際の、あなたなりの分析・解決アプローチを教えてください。最近解決した具体的な技術課題があれば、そのプロセスも含めてお聞かせください。',
        variables: [],
        adaptationRules: [],
        roleType: 'technical_lead',
        roleSpecific: {
          focusAreas: ['問題分析', '技術的解決策', 'システム思考'],
          evaluationPerspective: '技術的な問題解決能力と論理的思考力',
          keywordTriggers: ['問題解決', '技術課題', 'アプローチ'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: '',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            technicalVariant: '技術的な詳細と実装方法も含めて教えてください。',
            behavioralVariant: 'チームでの協力や意思決定プロセスもお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '間違い'],
          includeReassurance: true,
          gentleVersion: '技術的な課題への取り組み方について教えてください。',
          encouragingVersion: 'あなたの問題解決のアプローチをぜひ詳しく教えてください。',
        },
      },
    ]);

    // Team Leader 役割のテンプレート
    this.roleTemplates.set('team_leader', [
      // チームワークカテゴリ
      {
        id: 'team_leader_teamwork',
        name: 'チームリーダーチームワーク',
        category: 'teamwork',
        template: 'チームメンバーとして、またはリーダーとして、チームの成果を最大化するために大切にしていることは何ですか。具体的な経験があれば教えてください。',
        variables: [],
        adaptationRules: [],
        roleType: 'team_leader',
        roleSpecific: {
          focusAreas: ['チーム運営', '協働', 'メンバーサポート'],
          evaluationPerspective: 'チーム環境での働き方とリーダーシップポテンシャル',
          keywordTriggers: ['チーム', '協力', 'サポート'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: '',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            behavioralVariant: '具体的な行動と結果も含めて教えてください。',
            leadershipVariant: 'リーダーシップの発揮方法も併せてお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['対立', '問題'],
          includeReassurance: true,
          gentleVersion: 'チームでの働き方について教えてください。',
          encouragingVersion: 'あなたのチームへの貢献方法をぜひお聞かせください。',
        },
      },
    ]);

    // Peer (同僚) 役割のテンプレート
    this.roleTemplates.set('peer', [
      // 経験・スキルカテゴリ
      {
        id: 'peer_experience_skills',
        name: '同僚レベル経験スキル',
        category: 'experience-skills',
        template: '私たちと一緒に働く仲間として、あなたの得意分野や強みを教えてください。どのような場面でその強みを活かせると思いますか。',
        variables: [],
        adaptationRules: [],
        roleType: 'peer',
        roleSpecific: {
          focusAreas: ['実務スキル', '協働能力', '貢献方法'],
          evaluationPerspective: '同僚としての働きやすさと実務能力',
          keywordTriggers: ['強み', '得意', '協力'],
          personalityAdjustments: {
            formality: {
              casual: 'make_casual',
              formal: '',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: '',
            },
            questioningStyle: {
              directive: '',
              conversational: 'make_conversational',
              socratic: '',
            },
          },
          styleAdjustments: {
            behavioralVariant: '具体的な成果や経験も含めて教えてください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['不足', '劣る'],
          includeReassurance: true,
          gentleVersion: 'あなたの強みについて気軽に教えてください。',
          encouragingVersion: 'どのような特技や経験をお持ちですか。ぜひ教えてください。',
        },
      },
    ]);

    // Senior Manager 役割のテンプレート
    this.roleTemplates.set('senior_manager', [
      // リーダーシップカテゴリ
      {
        id: 'senior_mgr_leadership',
        name: '上級管理職リーダーシップ',
        category: 'leadership',
        template: 'マネジメントやリーダーシップについて、あなたの考えや経験を教えてください。困難な状況でのチーム運営で心がけていることはありますか。',
        variables: [],
        adaptationRules: [],
        roleType: 'senior_manager',
        roleSpecific: {
          focusAreas: ['マネジメント', 'リーダーシップ', '組織運営'],
          evaluationPerspective: '管理職としてのポテンシャルと組織運営能力',
          keywordTriggers: ['マネジメント', 'リーダーシップ', '組織'],
          personalityAdjustments: {
            formality: {
              casual: '',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: 'make_conversational',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            behavioralVariant: '具体的なマネジメント経験も含めて教えてください。',
            visionaryVariant: '組織の将来像も含めて考えをお聞かせください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '問題'],
          includeReassurance: true,
          gentleVersion: 'マネジメントに対するお考えを教えてください。',
          encouragingVersion: 'あなたのリーダーシップ観をぜひお聞かせください。',
        },
      },
    ]);

    // External (外部面接官) 役割のテンプレート
    this.roleTemplates.set('external', [
      // 行動面接カテゴリ
      {
        id: 'external_behavioral',
        name: '外部面接官行動面接',
        category: 'behavioral',
        template: 'これまでの経験の中で、期待以上の成果を出した事例を教えてください。その時の状況、取った行動、そして結果を具体的にお聞かせください。',
        variables: [],
        adaptationRules: [],
        roleType: 'external',
        roleSpecific: {
          focusAreas: ['行動パターン', '成果創出', '客観的評価'],
          evaluationPerspective: '客観的で構造化された行動評価',
          keywordTriggers: ['行動', '成果', '結果'],
          personalityAdjustments: {
            formality: {
              casual: '',
              formal: 'make_formal',
              balanced: '',
            },
            supportiveness: {
              encouraging: 'add_encouragement',
              neutral: '',
              challenging: 'make_directive',
            },
            questioningStyle: {
              directive: 'make_directive',
              conversational: '',
              socratic: 'add_socratic',
            },
          },
          styleAdjustments: {
            behavioralVariant: 'STAR法（Situation, Task, Action, Result）に沿って教えてください。',
          },
        },
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '問題'],
          includeReassurance: true,
          gentleVersion: '成功した経験について教えてください。',
          encouragingVersion: 'あなたが誇りに思う成果について、詳しくお聞かせください。',
        },
      },
    ]);
  }
}

/**
 * 役割別質問テンプレート取得のヘルパー関数
 */
export function getRoleBasedQuestionTemplates(): RoleBasedQuestionTemplates {
  return RoleBasedQuestionTemplates.getInstance();
}

/**
 * 役割タイプから質問テンプレートを取得
 */
export function getTemplatesForRole(roleType: InterviewerRoleType): RoleBasedQuestionTemplate[] {
  return getRoleBasedQuestionTemplates().getTemplatesForRole(roleType);
}

/**
 * 役割とカテゴリに基づくテンプレート取得
 */
export function getTemplatesForRoleAndCategory(
  roleType: InterviewerRoleType,
  category: QuestionCategory
): RoleBasedQuestionTemplate[] {
  return getRoleBasedQuestionTemplates().getTemplatesForRoleAndCategory(roleType, category);
}