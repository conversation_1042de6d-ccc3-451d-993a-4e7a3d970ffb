/**
 * 企業マスター管理サービス
 * 永続データとしての企業・職種・質問テンプレートの管理
 */
import {
  CompanyMaster,
  PositionMaster,
  QuestionTemplate,
  InterviewLink,
  CompanySuggestion,
  PositionSuggestion,
  SuggestionResponse,
  DataRetentionPolicy,
  DeletionCandidate,
} from '../types/company-master';

export class CompanyMasterService {
  private readonly storageKey = 'mensetsu_company_masters';
  private readonly retentionPolicy: DataRetentionPolicy = {
    interviewResultsRetentionDays: 90,
    linkRetentionDaysAfterCompletion: 30,
    candidateDataRetentionDays: 365,
    masterDataCleanupPolicy: {
      unusedCompanyDays: 730, // 2年
      unusedPositionDays: 365, // 1年
      lowRatedTemplateThreshold: 2.0,
    },
  };

  constructor() {
    // 初期モックデータを設定
    if (typeof window !== 'undefined') {
      this.initializeMockData();
    }
  }

  /**
   * 開発用モックデータの初期化
   */
  private initializeMockData(): void {
    const companies = this.getAllCompanies();
    
    if (companies.length === 0) {
      const mockCompanies: CompanyMaster[] = [
        {
          id: 'company_1',
          name: '株式会社テックイノベーション',
          industry: 'IT・ソフトウェア開発',
          description: '最先端のAI技術を活用したSaaSプラットフォームを開発するテクノロジー企業',
          culture: 'フラットな組織文化で、チャレンジを推奨。失敗を学びと捉える成長志向の環境',
          website: 'https://tech-innovation.example.com',
          businessDescription: 'AI/ML、クラウドプラットフォーム、SaaS開発',
          size: 'sme',
          location: ['東京', '大阪', 'リモート'],
          benefits: ['フレックス制度', '在宅勤務', '技術書購入支援', 'カンファレンス参加支援'],
          workEnvironment: {
            remoteWork: true,
            flexTime: true,
            averageOvertimeHours: 20,
          },
          interviewStyle: 'casual',
          positions: [
            {
              id: 'position_1',
              companyId: 'company_1',
              title: 'フロントエンドエンジニア',
              department: '開発部',
              level: 'mid',
              requirements: ['React/Vue.js経験3年以上', 'TypeScript実務経験', 'UI/UX設計経験'],
              preferredSkills: ['Next.js', 'GraphQL', 'デザインシステム構築経験'],
              expectedDuration: 60,
              difficulty: 'medium',
              focusAreas: ['技術力', 'UI/UX理解', 'チーム協調性'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              usageCount: 10,
              isActive: true,
            },
            {
              id: 'position_2',
              companyId: 'company_1',
              title: 'バックエンドエンジニア',
              department: '開発部',
              level: 'senior',
              requirements: ['Node.js/Python経験5年以上', 'マイクロサービス設計経験', 'AWS実務経験'],
              preferredSkills: ['Kubernetes', 'gRPC', 'システムアーキテクチャ設計'],
              expectedDuration: 90,
              difficulty: 'hard',
              focusAreas: ['アーキテクチャ設計', 'パフォーマンス最適化', 'リーダーシップ'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              usageCount: 8,
              isActive: true,
            }
          ],
          questionTemplates: [],
          createdAt: new Date('2024-01-01').toISOString(),
          updatedAt: new Date().toISOString(),
          lastUsed: new Date().toISOString(),
          usageCount: 15,
          createdBy: 'system',
        },
        {
          id: 'company_2',
          name: 'グローバルコンサルティング株式会社',
          industry: 'コンサルティング',
          description: '戦略立案から実行支援まで、企業の経営課題を解決するプロフェッショナルファーム',
          culture: '成果主義、プロフェッショナリズム、グローバルマインド',
          website: 'https://global-consulting.example.com',
          businessDescription: '経営戦略、デジタルトランスフォーメーション、組織変革',
          size: 'large',
          location: ['東京', 'ニューヨーク', 'ロンドン', 'シンガポール'],
          benefits: ['MBA取得支援', '海外研修', '高額インセンティブ', 'プレミアム健康保険'],
          workEnvironment: {
            remoteWork: true,
            flexTime: false,
            averageOvertimeHours: 40,
          },
          interviewStyle: 'formal',
          positions: [
            {
              id: 'position_3',
              companyId: 'company_2',
              title: 'コンサルタント',
              department: 'コンサルティング部',
              level: 'entry',
              requirements: ['論理的思考力', 'プレゼンテーション能力', '英語ビジネスレベル'],
              preferredSkills: ['MBA', '業界知識', 'データ分析スキル'],
              expectedDuration: 45,
              difficulty: 'medium',
              focusAreas: ['問題解決能力', 'コミュニケーション', '学習意欲'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              usageCount: 20,
              isActive: true,
            }
          ],
          questionTemplates: [],
          createdAt: new Date('2024-02-01').toISOString(),
          updatedAt: new Date().toISOString(),
          lastUsed: new Date().toISOString(),
          usageCount: 25,
          createdBy: 'system',
        },
        {
          id: 'company_3',
          name: 'スタートアップベンチャー株式会社',
          industry: 'フィンテック',
          description: '革新的な決済ソリューションで金融業界に変革をもたらすスタートアップ',
          culture: 'スピード重視、フラット、全員がオーナーシップを持つ',
          website: 'https://startup-venture.example.com',
          businessDescription: 'モバイル決済、ブロックチェーン、金融API',
          size: 'startup',
          location: ['東京', 'リモート'],
          benefits: ['ストックオプション', 'フルリモート可', '副業OK', 'ペット同伴可'],
          workEnvironment: {
            remoteWork: true,
            flexTime: true,
            averageOvertimeHours: 30,
          },
          interviewStyle: 'casual',
          positions: [],
          questionTemplates: [],
          createdAt: new Date('2024-03-01').toISOString(),
          updatedAt: new Date().toISOString(),
          lastUsed: new Date().toISOString(),
          usageCount: 8,
          createdBy: 'system',
        }
      ];

      // モックデータを保存
      localStorage.setItem(this.storageKey, JSON.stringify(mockCompanies));
    }
  }

  /**
   * 企業名でのサジェスト検索
   */
  async suggestCompanies(query: string, limit: number = 5): Promise<CompanySuggestion[]> {
    const companies = this.getAllCompanies();
    
    return companies
      .map(company => ({
        id: company.id,
        name: company.name,
        industry: company.industry,
        matchScore: this.calculateMatchScore(query, company.name),
        lastUsed: company.lastUsed,
        usageCount: company.usageCount,
      }))
      .filter(suggestion => suggestion.matchScore > 0.3)
      .sort((a, b) => {
        // マッチスコア × 使用頻度で優先順位決定
        const scoreA = a.matchScore * (1 + Math.log(a.usageCount + 1) * 0.1);
        const scoreB = b.matchScore * (1 + Math.log(b.usageCount + 1) * 0.1);
        return scoreB - scoreA;
      })
      .slice(0, limit);
  }

  /**
   * 職種名でのサジェスト検索
   */
  async suggestPositions(
    query: string, 
    companyId?: string, 
    limit: number = 5
  ): Promise<PositionSuggestion[]> {
    const companies = this.getAllCompanies();
    let positions: PositionMaster[] = [];

    if (companyId) {
      const company = companies.find(c => c.id === companyId);
      positions = company?.positions || [];
    } else {
      positions = companies.flatMap(c => c.positions);
    }

    return positions
      .map(position => ({
        id: position.id,
        companyId: position.companyId,
        title: position.title,
        department: position.department,
        level: position.level,
        matchScore: this.calculateMatchScore(query, position.title),
        lastUsed: position.updatedAt,
        usageCount: position.usageCount,
      }))
      .filter(suggestion => suggestion.matchScore > 0.3)
      .sort((a, b) => {
        const scoreA = a.matchScore * (1 + Math.log(a.usageCount + 1) * 0.1);
        const scoreB = b.matchScore * (1 + Math.log(b.usageCount + 1) * 0.1);
        return scoreB - scoreA;
      })
      .slice(0, limit);
  }

  /**
   * 企業の作成または更新
   */
  async createOrUpdateCompany(
    companyData: Partial<CompanyMaster>,
    agentId: string
  ): Promise<CompanyMaster> {
    const companies = this.getAllCompanies();
    const now = new Date().toISOString();

    // 既存企業の検索（名前の類似度で判定）
    const existingCompany = companies.find(c => 
      this.calculateMatchScore(companyData.name || '', c.name) > 0.8
    );

    if (existingCompany && companyData.id !== existingCompany.id) {
      // 既存企業の更新
      const updatedCompany: CompanyMaster = {
        ...existingCompany,
        ...companyData,
        id: existingCompany.id,
        updatedAt: now,
        usageCount: existingCompany.usageCount + 1,
        lastUsed: now,
      };

      this.saveCompany(updatedCompany);
      return updatedCompany;
    } else {
      // 新規企業の作成
      const newCompany: CompanyMaster = {
        id: companyData.id || `company_${Date.now()}`,
        name: companyData.name || '',
        industry: companyData.industry || '',
        description: companyData.description || '',
        culture: companyData.culture || '',
        interviewStyle: companyData.interviewStyle || 'formal',
        size: companyData.size || 'sme',
        website: companyData.website,
        logoUrl: companyData.logoUrl,
        createdAt: companyData.createdAt || now,
        updatedAt: now,
        createdBy: agentId,
        lastUsed: now,
        usageCount: (companyData.usageCount || 0) + 1,
        positions: companyData.positions || [],
        questionTemplates: companyData.questionTemplates || [],
      };

      this.saveCompany(newCompany);
      return newCompany;
    }
  }

  /**
   * 職種の作成または更新
   */
  async createOrUpdatePosition(
    positionData: Partial<PositionMaster>,
    companyId: string
  ): Promise<PositionMaster> {
    const company = this.getCompanyById(companyId);
    if (!company) {
      throw new Error(`Company not found: ${companyId}`);
    }

    const now = new Date().toISOString();

    // 既存職種の検索
    const existingPosition = company.positions.find(p => 
      this.calculateMatchScore(positionData.title || '', p.title) > 0.8 &&
      p.department === positionData.department
    );

    if (existingPosition && positionData.id !== existingPosition.id) {
      // 既存職種の更新
      const updatedPosition: PositionMaster = {
        ...existingPosition,
        ...positionData,
        id: existingPosition.id,
        companyId,
        updatedAt: now,
        usageCount: existingPosition.usageCount + 1,
      };

      // 企業データ内の職種を更新
      company.positions = company.positions.map(p => 
        p.id === updatedPosition.id ? updatedPosition : p
      );
      this.saveCompany(company);
      return updatedPosition;
    } else {
      // 新規職種の作成
      const newPosition: PositionMaster = {
        id: positionData.id || `position_${Date.now()}`,
        companyId,
        title: positionData.title || '',
        department: positionData.department || '',
        level: positionData.level || 'mid',
        requirements: positionData.requirements || [],
        preferredSkills: positionData.preferredSkills || [],
        salaryRange: positionData.salaryRange,
        expectedDuration: positionData.expectedDuration || 60,
        difficulty: positionData.difficulty || 'medium',
        focusAreas: positionData.focusAreas || [],
        createdAt: positionData.createdAt || now,
        updatedAt: now,
        isActive: positionData.isActive !== false,
        usageCount: (positionData.usageCount || 0) + 1,
      };

      company.positions.push(newPosition);
      this.saveCompany(company);
      return newPosition;
    }
  }

  /**
   * 質問テンプレートの保存
   */
  async saveQuestionTemplate(
    template: Partial<QuestionTemplate>,
    companyId: string,
    agentId: string
  ): Promise<QuestionTemplate> {
    const company = this.getCompanyById(companyId);
    if (!company) {
      throw new Error(`Company not found: ${companyId}`);
    }

    const now = new Date().toISOString();
    const newTemplate: QuestionTemplate = {
      id: template.id || `template_${Date.now()}`,
      companyId,
      positionId: template.positionId,
      name: template.name || `テンプレート_${new Date().toLocaleDateString()}`,
      description: template.description || '',
      questions: template.questions || [],
      totalEstimatedTime: template.totalEstimatedTime || 0,
      difficulty: template.difficulty || 'medium',
      psychologicalSafety: template.psychologicalSafety || {
        enabled: true,
        anxietyReduction: true,
        encouragementLevel: 'medium',
        personalizedMessaging: true,
      },
      createdAt: template.createdAt || now,
      updatedAt: now,
      createdBy: agentId,
      usageCount: template.usageCount || 0,
      lastUsed: now,
      tags: template.tags || [],
    };

    // 既存テンプレートの更新または新規追加
    const existingIndex = company.questionTemplates.findIndex(t => t.id === newTemplate.id);
    if (existingIndex >= 0) {
      company.questionTemplates[existingIndex] = newTemplate;
    } else {
      company.questionTemplates.push(newTemplate);
    }

    this.saveCompany(company);
    return newTemplate;
  }

  /**
   * 企業別の質問テンプレート取得
   */
  getQuestionTemplates(companyId: string, positionId?: string): QuestionTemplate[] {
    const company = this.getCompanyById(companyId);
    if (!company) return [];

    return company.questionTemplates.filter(template => {
      if (positionId) {
        return template.positionId === positionId || template.positionId == null;
      }
      return true;
    });
  }

  /**
   * データクリーンアップ対象の特定
   */
  async identifyDeletionCandidates(): Promise<DeletionCandidate[]> {
    const now = new Date();
    const candidates: DeletionCandidate[] = [];

    // 期限切れデータの特定（実装時はデータベースから取得）
    // ここではモック実装として概念を示す

    return candidates;
  }

  /**
   * 全企業一覧の取得
   */
  getAllCompanies(): CompanyMaster[] {
    if (typeof window === 'undefined') return []; // SSR対応
    
    const stored = localStorage.getItem(this.storageKey);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * IDによる企業の取得
   */
  getCompanyById(id: string): CompanyMaster | undefined {
    return this.getAllCompanies().find(c => c.id === id);
  }

  // ===== プライベートメソッド =====

  private saveCompany(company: CompanyMaster): void {
    if (typeof window === 'undefined') return; // SSR対応
    
    const companies = this.getAllCompanies();
    const existingIndex = companies.findIndex(c => c.id === company.id);
    
    if (existingIndex >= 0) {
      companies[existingIndex] = company;
    } else {
      companies.push(company);
    }
    
    localStorage.setItem(this.storageKey, JSON.stringify(companies));
  }

  private calculateMatchScore(query: string, target: string): number {
    if (!query || !target) return 0;
    
    const normalizedQuery = query.toLowerCase().trim();
    const normalizedTarget = target.toLowerCase().trim();
    
    // 完全一致
    if (normalizedQuery === normalizedTarget) return 1.0;
    
    // 前方一致
    if (normalizedTarget.startsWith(normalizedQuery)) return 0.9;
    
    // 部分一致
    if (normalizedTarget.includes(normalizedQuery)) return 0.7;
    
    // 文字レベルの類似度（簡易実装）
    const commonChars = this.countCommonChars(normalizedQuery, normalizedTarget);
    const similarity = commonChars / Math.max(normalizedQuery.length, normalizedTarget.length);
    
    return similarity > 0.5 ? similarity * 0.6 : 0;
  }

  private countCommonChars(str1: string, str2: string): number {
    const chars1 = str1.split('');
    const chars2 = str2.split('');
    let commonCount = 0;
    
    chars1.forEach(char => {
      const index = chars2.indexOf(char);
      if (index >= 0) {
        commonCount++;
        chars2.splice(index, 1);
      }
    });
    
    return commonCount;
  }
}

export default CompanyMasterService;