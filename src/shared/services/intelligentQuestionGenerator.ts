/**
 * インテリジェント質問生成サービス
 * プロダクト憲法の3原則に基づく動的質問生成システム
 * 
 * 第一条：心理的安全性の絶対的保障
 * 第二条：個人の尊重と個別化  
 * 第三条：透明性と信頼
 */
import {
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  GeneratedQuestion,
  QuestionCategory,
  QuestionTemplate,
  CandidateState,
  AdaptiveAdjustment,
  EncouragementTrigger,
} from '../types/intelligent-questions';
import {
  InterviewerRole,
  InterviewerRoleType,
  InterviewerPersonality,
  QuestionStyle,
} from '../types/interviewer-roles';
import { 
  formatSafeFeedback, 
  getRandomEncouragement,
  convertToPositiveLanguage,
  ENCOURAGEMENT_PHRASES 
} from '../constants/language';
import {
  RoleBasedQuestionTemplates,
  RoleBasedQuestionTemplate,
  getRoleBasedQuestionTemplates,
} from './roleBasedQuestionTemplates';

/**
 * インテリジェント質問生成エンジン
 */
export class IntelligentQuestionGenerator {
  private questionTemplates: Map<QuestionCategory, QuestionTemplate[]> = new Map();
  private roleBasedTemplates: RoleBasedQuestionTemplates;
  private adaptiveHistory: AdaptiveAdjustment[] = [];
  
  constructor() {
    this.initializeQuestionTemplates();
    this.roleBasedTemplates = getRoleBasedQuestionTemplates();
  }

  /**
   * メイン質問生成メソッド
   */
  async generateQuestions(
    request: QuestionGenerationRequest,
    interviewerRole?: InterviewerRole
  ): Promise<QuestionGenerationResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // 1. 候補者プロファイル分析
    const candidateAnalysis = this.analyzeCandidateProfile(request.candidateProfile);
    
    // 2. 心理的安全性設定の適用
    const safetyConfig = this.configurePsychologicalSafety(request.adaptiveSettings);
    
    // 3. 質問選択と生成
    const questions = await this.selectAndGenerateQuestions(
      request,
      candidateAnalysis,
      safetyConfig,
      interviewerRole
    );
    
    // 4. セッション設定の最適化
    const sessionConfig = this.optimizeSessionConfiguration(questions, request);
    
    // 5. 支援メッセージの生成
    const supportiveMessaging = this.generateSupportiveMessaging(
      request,
      candidateAnalysis,
      safetyConfig,
      interviewerRole
    );
    
    const processingTime = Date.now() - startTime;
    
    return {
      requestId,
      questions,
      sessionConfig,
      supportiveMessaging,
      metadata: {
        generatedAt: new Date().toISOString(),
        processingTime,
        adaptationApplied: this.getAppliedAdaptations(),
        qualityScore: this.calculateQualityScore(questions),
      },
    };
  }

  /**
   * 候補者プロファイル分析
   */
  private analyzeCandidateProfile(profile?: QuestionGenerationRequest['candidateProfile']) {
    if (!profile) {
      return {
        experienceLevel: 'mid' as const,
        anxietyLevel: 'medium' as const,
        supportNeeds: 'standard' as const,
        adaptationStrategy: 'balanced' as const,
        hasResumeData: false,
        keyExperiences: [],
        topSkills: [],
        focusAreas: [],
      };
    }

    const anxietyLevel = this.assessAnxietyLevel(profile);
    const supportNeeds = this.determineSupportNeeds(profile, anxietyLevel);
    const adaptationStrategy = this.selectAdaptationStrategy(profile, anxietyLevel);

    // 履歴書データから重要情報を抽出
    const resumeAnalysis = this.analyzeResumeData(profile.resumeData);

    return {
      experienceLevel: profile.experienceLevel,
      anxietyLevel,
      supportNeeds,
      adaptationStrategy,
      hasResumeData: !!profile.resumeData,
      ...resumeAnalysis,
    };
  }

  /**
   * 履歴書データ分析
   */
  private analyzeResumeData(resumeData?: NonNullable<QuestionGenerationRequest['candidateProfile']>['resumeData']) {
    if (!resumeData) {
      return {
        keyExperiences: [],
        topSkills: [],
        focusAreas: [],
      };
    }

    const keyExperiences: string[] = [];
    const topSkills: string[] = [];
    const focusAreas: string[] = [];

    // 最新の職歴から重要な経験を抽出
    if (resumeData.workExperience && resumeData.workExperience.length > 0) {
      const recentJob = resumeData.workExperience[0];
      keyExperiences.push(`${recentJob.company}での${recentJob.position}経験`);
      
      // 実績があれば追加
      if (recentJob.achievements && recentJob.achievements.length > 0) {
        focusAreas.push(...recentJob.achievements.slice(0, 2));
      }
    }

    // プロジェクト経験から技術スキルを抽出
    if (resumeData.projects && resumeData.projects.length > 0) {
      resumeData.projects.forEach(project => {
        if (project.technologies) {
          topSkills.push(...project.technologies);
        }
      });
    }

    // 資格情報を追加
    if (resumeData.certifications && resumeData.certifications.length > 0) {
      topSkills.push(...resumeData.certifications.slice(0, 3));
    }

    return {
      keyExperiences: [...new Set(keyExperiences)], // 重複除去
      topSkills: [...new Set(topSkills)].slice(0, 5), // 上位5つ
      focusAreas: [...new Set(focusAreas)].slice(0, 3), // 上位3つ
    };
  }

  /**
   * 心理的安全性設定
   */
  private configurePsychologicalSafety(settings?: QuestionGenerationRequest['adaptiveSettings']) {
    const defaultConfig = {
      personalizeForCandidate: true,
      adjustForAnxiety: true,
      emphasizeGrowth: true,
      avoidNegativeLanguage: true,
    };

    return {
      ...defaultConfig,
      ...settings,
      // 心理的安全性は常に最高レベルで適用
      enableEncouragement: true,
      usePositiveLanguage: true,
      provideClarification: true,
      allowThinkingTime: true,
    };
  }

  /**
   * 質問選択と生成
   */
  private async selectAndGenerateQuestions(
    request: QuestionGenerationRequest,
    candidateAnalysis: any,
    safetyConfig: any,
    interviewerRole?: InterviewerRole
  ): Promise<GeneratedQuestion[]> {
    const { questionSettings, companyInfo } = request;
    const questions: GeneratedQuestion[] = [];
    
    // カテゴリ別質問数の配分
    const categoryDistribution = this.distributeCategoriesByCount(
      questionSettings.categories,
      questionSettings.totalQuestions
    );

    for (const [category, count] of Array.from(categoryDistribution.entries())) {
      const categoryQuestions = await this.generateCategoryQuestions(
        category,
        count,
        companyInfo,
        candidateAnalysis,
        safetyConfig,
        questionSettings.difficulty,
        interviewerRole
      );
      
      questions.push(...categoryQuestions);
    }

    // 難易度と順序の最適化
    return this.optimizeQuestionOrder(questions, candidateAnalysis, safetyConfig);
  }

  /**
   * カテゴリ別質問生成
   */
  private async generateCategoryQuestions(
    category: QuestionCategory,
    count: number,
    companyInfo: any,
    candidateAnalysis: any,
    safetyConfig: any,
    baseDifficulty: string,
    interviewerRole?: InterviewerRole
  ): Promise<GeneratedQuestion[]> {
    // 役割ベースのテンプレートを優先して使用
    let templates: (QuestionTemplate | RoleBasedQuestionTemplate)[];
    if (interviewerRole) {
      const roleTemplates = this.roleBasedTemplates.selectTemplatesForRoleStyle(
        interviewerRole.type,
        category,
        interviewerRole.questionStyle,
        count
      );
      
      // 役割テンプレートを個性に合わせて調整
      const adjustedRoleTemplates = roleTemplates.map(template =>
        this.roleBasedTemplates.adjustTemplateForPersonality(template, interviewerRole.personality)
      );
      
      templates = adjustedRoleTemplates.length > 0 ? adjustedRoleTemplates : this.questionTemplates.get(category) || [];
    } else {
      templates = this.questionTemplates.get(category) || [];
    }
    
    const selectedTemplates = this.selectBestTemplates(templates, count, candidateAnalysis);
    
    const questions: GeneratedQuestion[] = [];
    
    for (let i = 0; i < count; i++) {
      const template = selectedTemplates[i % selectedTemplates.length];
      const question = await this.generateQuestionFromTemplate(
        template,
        companyInfo,
        candidateAnalysis,
        safetyConfig,
        baseDifficulty,
        i,
        interviewerRole
      );
      questions.push(question);
    }
    
    return questions;
  }

  /**
   * テンプレートから質問生成
   */
  private async generateQuestionFromTemplate(
    template: QuestionTemplate | RoleBasedQuestionTemplate,
    companyInfo: any,
    candidateAnalysis: any,
    safetyConfig: any,
    baseDifficulty: string,
    index: number,
    interviewerRole?: InterviewerRole
  ): Promise<GeneratedQuestion> {
    // 個人情報に基づく変数を生成（既存の会社情報変数と統合）
    let templateVariables = {
      companyName: companyInfo.name,
      position: companyInfo.position,
      industry: companyInfo.industry,
      ...this.generatePersonalizedVariables(candidateAnalysis, companyInfo),
    };
    
    // 役割ベースのテンプレートの場合、追加変数を生成
    if (this.isRoleBasedTemplate(template) && interviewerRole) {
      templateVariables = {
        ...templateVariables,
        ...this.generateRoleSpecificVariables(template, interviewerRole, companyInfo),
      };
    }

    // テンプレート変数の置換
    let questionText = this.replaceTemplateVariables(template.template, templateVariables);

    // 心理的安全性の適用
    questionText = this.applySafetyModifications(questionText, safetyConfig, candidateAnalysis);
    
    // 難易度調整
    const adjustedDifficulty = this.adjustDifficulty(baseDifficulty, candidateAnalysis, index);
    
    // 支援要素の生成
    const supportiveElements = this.generateSupportiveElements(
      template.category,
      candidateAnalysis,
      safetyConfig
    );

    // フォローアップ質問の生成
    const followUpQuestions = this.generateFollowUpQuestions(
      questionText,
      template.category,
      adjustedDifficulty
    );

    return {
      id: this.generateQuestionId(template.id, index),
      text: questionText,
      category: template.category,
      intent: this.generateQuestionIntent(template, companyInfo),
      difficulty: adjustedDifficulty as 'easy' | 'medium' | 'hard',
      estimatedAnswerTime: this.calculateEstimatedTime(adjustedDifficulty, template.category),
      followUpQuestions,
      evaluationCriteria: this.generateEvaluationCriteria(template.category, companyInfo),
      supportiveElements,
      metadata: {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'ai',
        adaptationReason: this.getAdaptationReason(candidateAnalysis),
      },
    };
  }

  /**
   * 心理的安全性の適用
   */
  private applySafetyModifications(
    questionText: string,
    safetyConfig: any,
    candidateAnalysis: any
  ): string {
    let modifiedText = questionText;

    // ネガティブ言語の変換
    if (safetyConfig.avoidNegativeLanguage) {
      modifiedText = convertToPositiveLanguage(modifiedText);
    }

    // 不安レベルに応じた調整
    if (candidateAnalysis.anxietyLevel === 'high') {
      modifiedText = this.adjustForHighAnxiety(modifiedText);
    }

    // 成長志向の言語追加
    if (safetyConfig.emphasizeGrowth) {
      modifiedText = this.addGrowthOrientedLanguage(modifiedText);
    }

    return modifiedText;
  }

  /**
   * 高不安レベル対応の調整
   */
  private adjustForHighAnxiety(questionText: string): string {
    const gentlePrefixes = [
      "お時間のあるときで構いませんので、",
      "ご自身のペースで、",
      "リラックスして、",
      "どんな内容でも大丈夫ですので、",
    ];
    
    const randomPrefix = gentlePrefixes[Math.floor(Math.random() * gentlePrefixes.length)];
    return `${randomPrefix}${questionText}`;
  }

  /**
   * 成長志向言語の追加
   */
  private addGrowthOrientedLanguage(questionText: string): string {
    const growthSuffixes = [
      "どんな経験からも学びがあると思いますので、お聞かせください。",
      "あなたの成長の過程を教えていただけますか。",
      "どのような気づきや学びがありましたか。",
    ];
    
    // 簡単な質問の場合のみ成長志向の言語を追加
    if (questionText.length < 100) {
      const randomSuffix = growthSuffixes[Math.floor(Math.random() * growthSuffixes.length)];
      return `${questionText} ${randomSuffix}`;
    }
    
    return questionText;
  }

  /**
   * 支援メッセージの生成
   */
  private generateSupportiveMessaging(
    request: QuestionGenerationRequest,
    candidateAnalysis: any,
    safetyConfig: any,
    interviewerRole?: InterviewerRole
  ) {
    const openingMessage = this.generateOpeningMessage(candidateAnalysis, request.companyInfo, interviewerRole);
    const transitionMessages = this.generateTransitionMessages(candidateAnalysis, interviewerRole);
    const closingMessage = this.generateClosingMessage(interviewerRole);
    const encouragementTriggers = this.generateEncouragementTriggers(candidateAnalysis, interviewerRole);

    return {
      openingMessage,
      transitionMessages,
      closingMessage,
      encouragementTriggers,
    };
  }





  // ===== ユーティリティメソッド =====

  private generateRequestId(): string {
    return `qg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateQuestionId(templateId: string, index: number): string {
    return `q_${templateId}_${index}_${Date.now()}`;
  }

  private assessAnxietyLevel(profile: any): 'low' | 'medium' | 'high' {
    // 初回利用者は不安レベルを高めに設定
    if (profile.previousInterviews === 0) return 'high';
    if (profile.previousInterviews < 3) return 'medium';
    return 'low';
  }

  private determineSupportNeeds(profile: any, anxietyLevel: string): 'minimal' | 'standard' | 'high' {
    if (anxietyLevel === 'high') return 'high';
    if (profile.experienceLevel === 'entry') return 'high';
    return 'standard';
  }

  private selectAdaptationStrategy(profile: any, anxietyLevel: string): string {
    if (anxietyLevel === 'high') return 'supportive';
    if (profile.experienceLevel === 'executive') return 'challenging';
    return 'balanced';
  }

  private distributeCategoriesByCount(
    categories: QuestionCategory[], 
    totalQuestions: number
  ): Map<QuestionCategory, number> {
    const distribution = new Map<QuestionCategory, number>();
    const baseCount = Math.floor(totalQuestions / categories.length);
    const remainder = totalQuestions % categories.length;

    categories.forEach((category, index) => {
      const count = baseCount + (index < remainder ? 1 : 0);
      distribution.set(category, count);
    });

    return distribution;
  }

  private selectBestTemplates(
    templates: QuestionTemplate[], 
    count: number, 
    candidateAnalysis: any
  ): QuestionTemplate[] {
    // 候補者分析に基づいてテンプレートをスコアリング
    const scoredTemplates = templates.map(template => ({
      template,
      score: this.scoreTemplate(template, candidateAnalysis),
    }));

    // スコア順にソートして上位を選択
    scoredTemplates.sort((a, b) => b.score - a.score);
    
    return scoredTemplates
      .slice(0, Math.max(count, templates.length))
      .map(item => item.template);
  }

  private scoreTemplate(template: QuestionTemplate, candidateAnalysis: any): number {
    let score = 0.5; // ベーススコア

    // 心理的安全性設定があるテンプレートを優先
    if (template.safetyConfiguration.includeReassurance) score += 0.2;
    if (template.safetyConfiguration.gentleVersion) score += 0.1;

    // 不安レベルに応じた調整
    if (candidateAnalysis.anxietyLevel === 'high' && template.safetyConfiguration.gentleVersion) {
      score += 0.3;
    }

    return Math.min(1, score);
  }

  private replaceTemplateVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value);
    });
    return result;
  }

  /**
   * 候補者の履歴書情報を基にした変数生成
   */
  private generatePersonalizedVariables(candidateAnalysis: any, companyInfo: any): Record<string, string> {
    const variables: Record<string, string> = {};

    // 会社情報の変数
    variables.company = companyInfo.name;
    variables.position = companyInfo.position;
    variables.industry = companyInfo.industry;

    // 候補者の経験に基づく変数
    if (candidateAnalysis.keyExperiences.length > 0) {
      variables.recentExperience = candidateAnalysis.keyExperiences[0];
      variables.experiences = candidateAnalysis.keyExperiences.join('、');
    }

    // スキルに基づく変数
    if (candidateAnalysis.topSkills.length > 0) {
      variables.mainSkill = candidateAnalysis.topSkills[0];
      variables.skills = candidateAnalysis.topSkills.join('、');
    }

    // フォーカスエリアに基づく変数
    if (candidateAnalysis.focusAreas.length > 0) {
      variables.achievement = candidateAnalysis.focusAreas[0];
      variables.focusArea = candidateAnalysis.focusAreas.join('、');
    }

    return variables;
  }

  private adjustDifficulty(
    baseDifficulty: string, 
    candidateAnalysis: any, 
    questionIndex: number
  ): string {
    if (candidateAnalysis.anxietyLevel === 'high') {
      return questionIndex < 2 ? 'easy' : 'medium';
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      return baseDifficulty === 'hard' ? 'medium' : baseDifficulty;
    }
    
    return baseDifficulty;
  }

  private generateSupportiveElements(
    category: QuestionCategory,
    candidateAnalysis: any,
    safetyConfig: any
  ) {
    const elements: any = {};

    if (candidateAnalysis.anxietyLevel === 'high') {
      elements.encouragementPrefix = "ご自身の経験を思い返しながら、";
      elements.reassuranceMessage = "どのような内容でも、あなたの経験として価値があります。";
    }

    if (category === 'technical' || category === 'problem-solving') {
      elements.clarificationNote = "分からない部分があれば、遠慮なくお尋ねください。";
    }

    return elements;
  }

  private generateFollowUpQuestions(
    questionText: string,
    category: QuestionCategory,
    difficulty: string
  ): string[] {
    const followUps: string[] = [];

    if (category === 'experience-skills') {
      followUps.push("その経験から何を学ばれましたか？");
      followUps.push("同じような状況になったら、どう対応されますか？");
    }

    if (category === 'problem-solving') {
      followUps.push("他に考えられる解決方法はありますか？");
      followUps.push("その判断をした理由を教えてください。");
    }

    return followUps;
  }

  private generateQuestionIntent(template: QuestionTemplate, companyInfo: any) {
    return {
      primary: `${template.category}の評価`,
      secondary: ['コミュニケーション能力', '論理的思考'],
      skillAssessment: this.getSkillAssessmentForCategory(template.category),
      personalityTraits: this.getPersonalityTraitsForCategory(template.category),
    };
  }

  private generateEvaluationCriteria(category: QuestionCategory, companyInfo: any) {
    // カテゴリ別の評価基準を返す
    const baseCriteria = [
      {
        name: '明確性',
        description: '回答が明確で理解しやすいか',
        weight: 0.3,
        scoringGuideline: {
          excellent: '非常に明確で具体的',
          good: '概ね明確',
          acceptable: '理解可能',
          needsImprovement: 'より具体的な説明があると良い',
        },
      },
    ];

    return baseCriteria;
  }

  private getSkillAssessmentForCategory(category: QuestionCategory): string[] {
    const skillMap: Record<QuestionCategory, string[]> = {
      'self-introduction': ['コミュニケーション', 'プレゼンテーション'],
      'experience-skills': ['専門スキル', '問題解決', '学習能力'],
      'motivation': ['意欲', '価値観', '目標設定'],
      'problem-solving': ['論理的思考', '創造性', '分析力'],
      'teamwork': ['協調性', 'リーダーシップ', 'コミュニケーション'],
      'leadership': ['リーダーシップ', '意思決定', '人材育成'],
      'career-goals': ['計画性', '自己認識', '成長意欲'],
      'company-culture-fit': ['価値観', '適応性', '企業理解'],
      'technical': ['技術スキル', '専門知識', '実装能力'],
      'behavioral': ['行動特性', '価値観', '判断力'],
      'situational': ['応用力', '適応性', '意思決定'],
    };

    return skillMap[category] || ['総合評価'];
  }

  private getPersonalityTraitsForCategory(category: QuestionCategory): string[] {
    const traitMap: Record<QuestionCategory, string[]> = {
      'self-introduction': ['自信', '社交性'],
      'experience-skills': ['責任感', '継続力'],
      'motivation': ['情熱', '目的意識'],
      'problem-solving': ['論理性', '創造性'],
      'teamwork': ['協調性', '共感性'],
      'leadership': ['主体性', '影響力'],
      'career-goals': ['計画性', '向上心'],
      'company-culture-fit': ['適応性', '価値観'],
      'technical': ['論理性', '探究心'],
      'behavioral': ['誠実性', '一貫性'],
      'situational': ['柔軟性', '判断力'],
    };

    return traitMap[category] || ['総合的な人柄'];
  }

  private optimizeQuestionOrder(
    questions: GeneratedQuestion[],
    candidateAnalysis: any,
    safetyConfig: any
  ): GeneratedQuestion[] {
    // 心理的安全性を考慮した質問順序の最適化
    const ordered = [...questions];

    // 1. 自己紹介を最初に
    ordered.sort((a, b) => {
      if (a.category === 'self-introduction') return -1;
      if (b.category === 'self-introduction') return 1;
      return 0;
    });

    // 2. 難易度による調整
    if (candidateAnalysis.anxietyLevel === 'high') {
      // 不安レベルが高い場合は易しい質問から始める
      const selfIntro = ordered.filter(q => q.category === 'self-introduction');
      const easy = ordered.filter(q => q.difficulty === 'easy' && q.category !== 'self-introduction');
      const medium = ordered.filter(q => q.difficulty === 'medium');
      const hard = ordered.filter(q => q.difficulty === 'hard');
      
      return [...selfIntro, ...easy, ...medium, ...hard];
    }

    return ordered;
  }

  private optimizeSessionConfiguration(
    questions: GeneratedQuestion[],
    request: QuestionGenerationRequest
  ) {
    const totalDuration = questions.reduce((sum, q) => sum + q.estimatedAnswerTime, 0) / 60; // 分に変換
    
    // 休憩ポイントの計算（20分ごと）
    const breakPoints: number[] = [];
    let cumulativeTime = 0;
    
    questions.forEach((question, index) => {
      cumulativeTime += question.estimatedAnswerTime / 60;
      if (cumulativeTime >= 20 && breakPoints.length === 0) {
        breakPoints.push(index + 1);
      } else if (cumulativeTime >= 40 && breakPoints.length === 1) {
        breakPoints.push(index + 1);
      }
    });

    return {
      totalDuration: Math.ceil(totalDuration),
      breakPoints,
      adaptiveFlow: true,
      difficultyProgression: 'adaptive' as const,
    };
  }

  private calculateEstimatedTime(difficulty: string, category: QuestionCategory): number {
    const baseTime: Record<string, number> = {
      'easy': 120,    // 2分
      'medium': 180,  // 3分
      'hard': 240,    // 4分
    };

    const categoryMultiplier: Record<QuestionCategory, number> = {
      'self-introduction': 0.8,
      'experience-skills': 1.2,
      'motivation': 1.0,
      'problem-solving': 1.5,
      'teamwork': 1.1,
      'leadership': 1.3,
      'career-goals': 1.0,
      'company-culture-fit': 1.0,
      'technical': 1.8,
      'behavioral': 1.2,
      'situational': 1.4,
    };

    return Math.round((baseTime[difficulty] || 180) * (categoryMultiplier[category] || 1.0));
  }

  private getAppliedAdaptations(): string[] {
    return this.adaptiveHistory.map(adj => adj.type);
  }

  private calculateQualityScore(questions: GeneratedQuestion[]): number {
    // 質問の品質スコアを計算
    let totalScore = 0;
    
    questions.forEach(question => {
      let questionScore = 0.5; // ベーススコア
      
      // 支援要素があれば加点
      if (question.supportiveElements.encouragementPrefix) questionScore += 0.1;
      if (question.supportiveElements.reassuranceMessage) questionScore += 0.1;
      if (question.supportiveElements.clarificationNote) questionScore += 0.1;
      
      // フォローアップ質問があれば加点
      if (question.followUpQuestions && question.followUpQuestions.length > 0) {
        questionScore += 0.2;
      }
      
      totalScore += questionScore;
    });
    
    return Math.min(1, totalScore / questions.length);
  }

  private getAdaptationReason(candidateAnalysis: any): string {
    const reasons: string[] = [];
    
    if (candidateAnalysis.anxietyLevel === 'high') {
      reasons.push('高不安レベルに対応');
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      reasons.push('初心者レベルに適応');
    }
    
    if (candidateAnalysis.supportNeeds === 'high') {
      reasons.push('高サポートニーズに対応');
    }
    
    return reasons.join(', ') || '標準設定';
  }

  // ===== 役割ベース機能のヘルパーメソッド =====

  /**
   * 役割ベーステンプレートかどうかを判定
   */
  private isRoleBasedTemplate(template: QuestionTemplate | RoleBasedQuestionTemplate): template is RoleBasedQuestionTemplate {
    return 'roleType' in template;
  }

  /**
   * 役割固有の変数を生成
   */
  private generateRoleSpecificVariables(
    template: RoleBasedQuestionTemplate,
    interviewerRole: InterviewerRole,
    companyInfo: any
  ): Record<string, string> {
    const variables: Record<string, string> = {};

    // 企業の価値観情報があれば追加
    if (companyInfo.culture) {
      variables.companyValues = companyInfo.culture;
    }

    // 役割に応じた固有変数
    switch (interviewerRole.type) {
      case 'hr':
        variables.roleContext = '人事担当者として';
        variables.evaluationFocus = '企業文化適合性と人物像';
        break;
      case 'ceo':
        variables.roleContext = '経営陣として';
        variables.evaluationFocus = 'ビジョンと戦略的思考';
        break;
      case 'technical_lead':
        variables.roleContext = '技術責任者として';
        variables.evaluationFocus = '技術力と問題解決能力';
        break;
      case 'team_leader':
        variables.roleContext = 'チームリーダーとして';
        variables.evaluationFocus = 'チームワークとリーダーシップ';
        break;
      case 'peer':
        variables.roleContext = '将来の同僚として';
        variables.evaluationFocus = '協働しやすさと実務能力';
        break;
      case 'senior_manager':
        variables.roleContext = '上級管理職として';
        variables.evaluationFocus = 'マネジメント能力と組織貢献';
        break;
      case 'external':
        variables.roleContext = '客観的な視点から';
        variables.evaluationFocus = '総合的な能力と適性';
        break;
    }

    return variables;
  }

  /**
   * 役割に基づくメッセージ調整
   */
  private adjustMessageForRole(message: string, interviewerRole: InterviewerRole): string {
    // フォーマリティレベルの調整
    if (interviewerRole.personality.formality === 'formal') {
      message = message.replace(/ですね/g, 'です').replace(/ましょう/g, 'ます');
    } else if (interviewerRole.personality.formality === 'casual') {
      message = message.replace(/です/g, 'ですね').replace(/ます/g, 'ましょう');
    }

    // サポート度の調整
    if (interviewerRole.personality.supportiveness === 'encouraging') {
      message = `${message} リラックスして、あなたらしさを存分に発揮してください。`;
    } else if (interviewerRole.personality.supportiveness === 'challenging') {
      message = `${message} 率直で具体的な回答を期待しています。`;
    }

    // 役割固有のコンテキスト追加
    const roleContext = this.getRoleContextMessage(interviewerRole.type);
    if (roleContext) {
      message = `${message} ${roleContext}`;
    }

    return message;
  }

  /**
   * 役割別コンテキストメッセージの取得
   */
  private getRoleContextMessage(roleType: InterviewerRoleType): string {
    const contextMap: Record<InterviewerRoleType, string> = {
      'hr': '私は人事担当として、あなたの人物像と企業適合性を確認させていただきます。',
      'ceo': '経営陣の視点から、あなたのビジョンや戦略的思考についてお聞きします。',
      'technical_lead': '技術責任者として、あなたの技術力や問題解決アプローチを評価いたします。',
      'team_leader': 'チームリーダーの立場から、協働やリーダーシップについて伺います。',
      'peer': '将来の同僚として、一緒に働く上での相性を確認させてください。',
      'senior_manager': '管理職の視点から、組織運営やマネジメントについてお聞きします。',
      'external': '客観的な立場から、総合的な能力について評価させていただきます。',
    };

    return contextMap[roleType] || '';
  }

  /**
   * オープニングメッセージ生成（役割対応版）
   */
  private generateOpeningMessage(
    candidateAnalysis: any, 
    companyInfo: any, 
    interviewerRole?: InterviewerRole
  ): string {
    let baseMessage = `${companyInfo.name}での面接練習を始めましょう。`;
    
    // 役割に基づくメッセージ調整
    if (interviewerRole) {
      baseMessage = this.adjustMessageForRole(baseMessage, interviewerRole);
    }
    
    if (candidateAnalysis.anxietyLevel === 'high') {
      return `${baseMessage} リラックスして、ご自身のペースで進めてください。間違いなどを心配する必要はありません。この時間は、あなたの成長のためのものです。準備ができましたら、最初の質問にお答えください。`;
    }
    
    if (candidateAnalysis.experienceLevel === 'entry') {
      return `${baseMessage} 初めての面接練習かもしれませんが、どんな回答でも大丈夫です。この経験を通じて、きっと新しい発見があります。楽しみながら進めましょう。`;
    }
    
    return `${baseMessage} あなたの経験や考えを自由にお聞かせください。この練習が、より良い面接のきっかけになることを願っています。`;
  }

  /**
   * 遷移メッセージ生成（役割対応版）
   */
  private generateTransitionMessages(
    candidateAnalysis: any, 
    interviewerRole?: InterviewerRole
  ): string[] {
    let messages = [
      "素晴らしい回答でした。次の質問に移りましょう。",
      "とても興味深いお話をありがとうございます。続いて、",
      "あなたの経験がよく伝わってきました。では、次に",
      "そのような視点をお持ちなのですね。それでは、",
    ];

    // 役割に基づく調整
    if (interviewerRole) {
      messages = messages.map(msg => this.adjustMessageForRole(msg, interviewerRole));
      
      // 役割固有のメッセージを追加
      const roleSpecificMessages = this.getRoleSpecificTransitionMessages(interviewerRole.type);
      messages = [...messages, ...roleSpecificMessages];
    }

    if (candidateAnalysis.anxietyLevel === 'high') {
      messages.push(
        "ここまでとてもよくできています。少し深呼吸をして、次に進みましょう。",
        "順調に進んでいますね。次の質問もあなたのペースで大丈夫です。"
      );
    }

    return messages;
  }

  /**
   * 役割固有の遷移メッセージ
   */
  private getRoleSpecificTransitionMessages(roleType: InterviewerRoleType): string[] {
    const messageMap: Record<InterviewerRoleType, string[]> = {
      'hr': [
        "人事の観点から、もう少し詳しく伺いたいことがあります。",
        "企業文化の観点から、次の質問をさせてください。",
      ],
      'ceo': [
        "経営の視点から、さらに深くお聞きしたいと思います。",
        "戦略的な観点で、次の質問に移らせていただきます。",
      ],
      'technical_lead': [
        "技術的な側面から、もう少し詳しく伺います。",
        "実装の観点で、次の質問をお聞きします。",
      ],
      'team_leader': [
        "チーム運営の観点から、次の質問です。",
        "協働の視点で、さらにお聞きしたいことがあります。",
      ],
      'peer': [
        "同僚として一緒に働く観点から、お聞きします。",
        "日常的な協力の視点で、次の質問です。",
      ],
      'senior_manager': [
        "組織運営の観点から、次の質問に移ります。",
        "マネジメントの視点で、さらに伺います。",
      ],
      'external': [
        "客観的な評価の観点から、次の質問です。",
        "総合的な視点で、さらにお聞きします。",
      ],
    };

    return messageMap[roleType] || [];
  }

  /**
   * クロージングメッセージ生成（役割対応版）
   */
  private generateClosingMessage(interviewerRole?: InterviewerRole): string {
    let baseMessage = "面接練習お疲れさまでした。今日の経験は必ずあなたの成長につながります。すべての回答に、あなたらしさと成長への意欲が感じられました。この調子で、自信を持って本番に臨んでください。";
    
    if (interviewerRole) {
      // 役割固有のクロージングメッセージを追加
      const roleSpecificClosing = this.getRoleSpecificClosingMessage(interviewerRole.type);
      if (roleSpecificClosing) {
        baseMessage = `${baseMessage} ${roleSpecificClosing}`;
      }
      
      baseMessage = this.adjustMessageForRole(baseMessage, interviewerRole);
    }
    
    return baseMessage;
  }

  /**
   * 役割固有のクロージングメッセージ
   */
  private getRoleSpecificClosingMessage(roleType: InterviewerRoleType): string {
    const closingMap: Record<InterviewerRoleType, string> = {
      'hr': '人事の観点から、あなたの人柄と熱意がよく伝わりました。',
      'ceo': '経営陣として、あなたのビジョンと可能性を感じることができました。',
      'technical_lead': '技術責任者として、あなたの技術力と学習意欲を評価いたします。',
      'team_leader': 'チームリーダーとして、あなたとの協働を楽しみにしています。',
      'peer': '同僚として、あなたと一緒に働けることを期待しています。',
      'senior_manager': '管理職として、あなたの組織への貢献を期待しています。',
      'external': '客観的な視点から、あなたの総合的な能力を高く評価いたします。',
    };

    return closingMap[roleType] || '';
  }

  /**
   * 励ましトリガー生成（役割対応版）
   */
  private generateEncouragementTriggers(
    candidateAnalysis: any, 
    interviewerRole?: InterviewerRole
  ): EncouragementTrigger[] {
    let triggers: EncouragementTrigger[] = [
      {
        condition: 'after_difficult_question',
        message: 'チャレンジングな質問でしたが、よく考えて答えてくださいました。',
        timing: 'immediate',
      },
      {
        condition: 'mid_session',
        message: 'ここまでの回答、とても充実していますね。この調子で続けましょう。',
        timing: 'delayed',
      },
    ];

    // 役割に基づく調整
    if (interviewerRole) {
      triggers = triggers.map(trigger => ({
        ...trigger,
        message: this.adjustMessageForRole(trigger.message, interviewerRole),
      }));

      // 役割固有のトリガーを追加
      const roleSpecificTriggers = this.getRoleSpecificEncouragementTriggers(interviewerRole.type);
      triggers = [...triggers, ...roleSpecificTriggers];
    }

    if (candidateAnalysis.anxietyLevel === 'high') {
      triggers.push({
        condition: 'low_confidence_detected',
        message: '完璧である必要はありません。あなたの率直な思いを聞かせてください。',
        timing: 'immediate',
      });
    }

    return triggers;
  }

  /**
   * 役割固有の励ましトリガー
   */
  private getRoleSpecificEncouragementTriggers(roleType: InterviewerRoleType): EncouragementTrigger[] {
    const triggerMap: Record<InterviewerRoleType, EncouragementTrigger[]> = {
      'hr': [
        {
          condition: 'after_difficult_question',
          message: '人事の観点から、あなたの誠実さが伝わってきます。',
          timing: 'immediate',
        },
      ],
      'ceo': [
        {
          condition: 'mid_session',
          message: '経営陣として、あなたの考え方に興味深いものがあります。',
          timing: 'delayed',
        },
      ],
      'technical_lead': [
        {
          condition: 'after_difficult_question',
          message: '技術的な思考プロセスがよく表れています。',
          timing: 'immediate',
        },
      ],
      'team_leader': [
        {
          condition: 'mid_session',
          message: 'チーム志向の回答が印象的です。',
          timing: 'delayed',
        },
      ],
      'peer': [
        {
          condition: 'after_difficult_question',
          message: '一緒に働きたいと思える誠実な回答でした。',
          timing: 'immediate',
        },
      ],
      'senior_manager': [
        {
          condition: 'mid_session',
          message: '組織への理解が深いことが伝わってきます。',
          timing: 'delayed',
        },
      ],
      'external': [
        {
          condition: 'after_difficult_question',
          message: '客観的に見て、とても良い回答だと思います。',
          timing: 'immediate',
        },
      ],
    };

    return triggerMap[roleType] || [];
  }

  /**
   * 質問テンプレートの初期化
   */
  private initializeQuestionTemplates() {
    // 自己紹介カテゴリ
    this.questionTemplates.set('self-introduction', [
      {
        id: 'self_intro_basic',
        name: '基本的な自己紹介',
        category: 'self-introduction',
        template: 'まずは簡単に自己紹介をお願いします。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'お時間のあるときで、ご自身のペースで自己紹介をお聞かせください。',
          encouragingVersion: 'あなたのことを教えてください。どんなお話でも楽しみにしています。',
        },
      },
      {
        id: 'self_intro_strength',
        name: '強みを含む自己紹介',
        category: 'self-introduction',
        template: 'ご自身の強みを含めて自己紹介をお聞かせください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'ご自身の良いところを含めて、お話しいただけますか。',
          encouragingVersion: 'あなたらしさが伝わる自己紹介をお聞かせください。',
        },
      },
    ]);

    // 経験・スキルカテゴリ
    this.questionTemplates.set('experience-skills', [
      {
        id: 'experience_challenge',
        name: '困難な経験について',
        category: 'experience-skills',
        template: 'これまでの経験で、成長につながった出来事について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '困難', '問題'],
          includeReassurance: true,
          gentleVersion: 'これまでの経験の中で、学びがあった出来事があれば教えてください。',
          encouragingVersion: 'あなたが頑張って取り組んだ経験について、ぜひお聞かせください。',
        },
      },
      {
        id: 'experience_achievement',
        name: '誇れる成果について',
        category: 'experience-skills',
        template: 'これまでで最も誇らしく思う成果について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'これまでで達成できて嬉しかったことがあれば教えてください。',
          encouragingVersion: 'あなたが自信を持てる成果について、ぜひお聞かせください。',
        },
      },
      {
        id: 'experience_specific',
        name: '特定経験についての深掘り',
        category: 'experience-skills',
        template: '{{recentExperience}}について、もう少し詳しくお聞かせいただけますか。特に工夫された点や学びがあれば教えてください。',
        variables: [
          {
            name: 'recentExperience',
            type: 'experience',
            required: false,
          },
        ],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['失敗', '問題'],
          includeReassurance: true,
          gentleVersion: '{{recentExperience}}について、ご自身のペースでお話しください。',
          encouragingVersion: '{{recentExperience}}での貴重な経験について、ぜひお聞かせください。',
        },
      },
      {
        id: 'skill_application',
        name: 'スキル活用について',
        category: 'experience-skills',
        template: '{{mainSkill}}のスキルを活用した具体的な経験があれば教えてください。',
        variables: [
          {
            name: 'mainSkill',
            type: 'skill',
            required: false,
          },
        ],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '{{mainSkill}}について、どのように学ばれたか教えていただけますか。',
          encouragingVersion: '{{mainSkill}}の専門性を活かした経験について、ぜひお聞かせください。',
        },
      },
    ]);

    // 志望動機カテゴリ
    this.questionTemplates.set('motivation', [
      {
        id: 'motivation_company',
        name: '企業志望動機',
        category: 'motivation',
        template: '{{companyName}}を志望された理由を教えてください。',
        variables: [
          {
            name: 'companyName',
            type: 'company',
            required: true,
          },
        ],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '{{companyName}}に興味を持たれたきっかけがあれば教えてください。',
          encouragingVersion: '{{companyName}}のどのような点に魅力を感じられましたか。',
        },
      },
      {
        id: 'motivation_position',
        name: 'ポジション志望動機',
        category: 'motivation',
        template: '{{position}}というポジションに応募された理由を教えてください。',
        variables: [
          {
            name: 'position',
            type: 'position',
            required: true,
          },
        ],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'このポジションでどのようなことにチャレンジしたいですか。',
          encouragingVersion: 'このお仕事のどのような部分に関心をお持ちですか。',
        },
      },
    ]);

    // 問題解決カテゴリ
    this.questionTemplates.set('problem-solving', [
      {
        id: 'problem_solving_approach',
        name: '問題解決アプローチ',
        category: 'problem-solving',
        template: '難しい課題に直面した時の、あなたなりのアプローチ方法を教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['難しい', '困難'],
          includeReassurance: true,
          gentleVersion: '新しい課題に取り組む時に、どのように進めることが多いですか。',
          encouragingVersion: 'あなたなりの課題解決の工夫があれば教えてください。',
        },
      },
      {
        id: 'problem_solving_example',
        name: '具体的な問題解決例',
        category: 'problem-solving',
        template: '具体的な課題を解決した経験について、そのプロセスを教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['問題', '困難'],
          includeReassurance: true,
          gentleVersion: '何かを改善したり、解決したりした経験があれば教えてください。',
          encouragingVersion: '工夫して取り組んだ経験について、お聞かせください。',
        },
      },
    ]);

    // チームワークカテゴリ
    this.questionTemplates.set('teamwork', [
      {
        id: 'teamwork_collaboration',
        name: 'チーム協力経験',
        category: 'teamwork',
        template: 'チームで協力して成果を上げた経験について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'みんなで一緒に取り組んだ経験があれば教えてください。',
          encouragingVersion: 'チームの一員として活動した経験について、お聞かせください。',
        },
      },
      {
        id: 'teamwork_communication',
        name: 'チームコミュニケーション',
        category: 'teamwork',
        template: 'チーム内でのコミュニケーションで大切にしていることを教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: 'チームで働く時に気をつけていることがあれば教えてください。',
          encouragingVersion: 'チームの皆さんとの関係作りで心がけていることはありますか。',
        },
      },
    ]);

    // リーダーシップカテゴリ
    this.questionTemplates.set('leadership', [
      {
        id: 'leadership_experience',
        name: 'リーダーシップ経験',
        category: 'leadership',
        template: 'リーダーとして人をまとめた経験について教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '人をサポートしたり、まとめ役をした経験があれば教えてください。',
          encouragingVersion: 'チームを引っ張ったり、支えたりした経験について、お聞かせください。',
        },
      },
    ]);

    // キャリア目標カテゴリ
    this.questionTemplates.set('career-goals', [
      {
        id: 'career_vision',
        name: '将来のビジョン',
        category: 'career-goals',
        template: '5年後、どのような自分になっていたいですか。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '将来に向けて、どのような成長をしていきたいですか。',
          encouragingVersion: 'あなたの理想の将来像について教えてください。',
        },
      },
    ]);

    // 企業文化適合性カテゴリ
    this.questionTemplates.set('company-culture-fit', [
      {
        id: 'culture_values',
        name: '価値観の適合性',
        category: 'company-culture-fit',
        template: '仕事をする上で大切にしている価値観を教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '働く時に大切に思っていることがあれば教えてください。',
          encouragingVersion: 'あなたが仕事で大事にしていることについて、お聞かせください。',
        },
      },
    ]);

    // 技術的質問カテゴリ
    this.questionTemplates.set('technical', [
      {
        id: 'technical_experience',
        name: '技術的経験',
        category: 'technical',
        template: 'これまでの技術的な経験や取り組みについて教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '技術的なことで学んだり、作ったりした経験があれば教えてください。',
          encouragingVersion: '技術的な分野での経験や興味について、お聞かせください。',
        },
      },
    ]);

    // 行動面接カテゴリ
    this.questionTemplates.set('behavioral', [
      {
        id: 'behavioral_decision',
        name: '意思決定の経験',
        category: 'behavioral',
        template: '重要な決断をした経験について、そのプロセスを教えてください。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: [],
          includeReassurance: true,
          gentleVersion: '何かを決める時に気をつけていることがあれば教えてください。',
          encouragingVersion: '大切な選択をした経験について、お聞かせください。',
        },
      },
    ]);

    // 状況判断カテゴリ
    this.questionTemplates.set('situational', [
      {
        id: 'situational_pressure',
        name: 'プレッシャー下での判断',
        category: 'situational',
        template: '時間やプレッシャーがある状況で、どのように判断を行いますか。',
        variables: [],
        adaptationRules: [],
        safetyConfiguration: {
          avoidTriggerWords: ['プレッシャー', '時間'],
          includeReassurance: true,
          gentleVersion: '忙しい状況でも冷静に判断するコツがあれば教えてください。',
          encouragingVersion: '限られた時間の中で、どのように優先順位をつけていますか。',
        },
      },
    ]);
  }
}