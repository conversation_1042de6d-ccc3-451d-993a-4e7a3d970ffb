/**
 * アダプティブフィードバック生成サービス
 * プロダクト憲法第三条「透明性と信頼」に基づく
 * ユーザーの状態と特性に応じたパーソナライズされたフィードバックを生成
 */
// import { 
//   UserAdaptiveState, 
//   ExperienceLevel, 
//   EmotionalState, 
//   LearningStyle 
// } from '../contexts/UserAdaptiveContext';
import { 
  formatSafeFeedback, 
  getGrowthMessage, 
  getRandomEncouragement,
  FEEDBACK_TONES 
} from '../constants/language';

// 一時的な型定義（管理画面ビルド用）
export type ExperienceLevel = 'beginner' | 'intermediate' | 'advanced';
export type EmotionalState = 'anxious' | 'nervous' | 'calm' | 'confident' | 'excited';
export type LearningStyle = 'visual' | 'auditory' | 'kinesthetic' | 'reading';

export interface UserAdaptiveState {
  experienceLevel: ExperienceLevel;
  emotionalState: EmotionalState;
  learningStyle: LearningStyle;
  personalGoals: any[];
  progressHistory: any[];
  preferences: {
    feedbackPacing: 'immediate' | 'gradual' | 'detailed';
    showProgressIndicators?: boolean;
  };
}

// パフォーマンスデータの型定義
export interface PerformanceData {
  overall: number; // 0-10
  communication: number;
  logicalThinking: number;
  problemSolving: number;
  creativity: number;
  leadership: number;
  teamwork: number;
  adaptability: number;
  weakPoints: string[];
  strengths: string[];
  timeSpent: number; // 秒
  questionCount: number;
  completionRate: number; // 0-1
}

// アダプティブフィードバックの型定義
export interface AdaptiveFeedback {
  approach: 'gentle' | 'balanced' | 'direct';
  tone: keyof typeof FEEDBACK_TONES;
  startWith: 'strengths' | 'overall' | 'improvement';
  pacing: 'immediate' | 'gradual' | 'detailed';
  content: {
    opening: string;
    strengths: string[];
    improvements: string[];
    personalizedTips: string[];
    nextSteps: string[];
    encouragement: string;
  };
  presentation: {
    showScoresImmediately: boolean;
    useVisualAids: boolean;
    includeComparisons: boolean;
    emphasizeProgress: boolean;
  };
}

// 経験レベル別の目標設定
const EXPERIENCE_TARGETS = {
  beginner: {
    communication: 6,
    logicalThinking: 5,
    problemSolving: 5,
    confidence: 4,
  },
  intermediate: {
    communication: 7,
    logicalThinking: 6,
    problemSolving: 6,
    confidence: 6,
  },
  advanced: {
    communication: 8,
    logicalThinking: 7,
    problemSolving: 7,
    confidence: 7,
  },
};

/**
 * ユーザーの状態とパフォーマンスに基づいてアダプティブフィードバックを生成
 */
export const generateAdaptiveFeedback = (
  performance: PerformanceData,
  userState: UserAdaptiveState
): AdaptiveFeedback => {
  const { experienceLevel, emotionalState, learningStyle, preferences } = userState;
  
  // 感情状態と成績に基づくアプローチ決定
  const approach = determineApproach(performance, emotionalState, experienceLevel);
  
  // フィードバックトーンの決定
  const tone = determineTone(performance, emotionalState, approach);
  
  // 開始点の決定
  const startWith = determineStartingPoint(performance, emotionalState, approach);
  
  // ペーシングの決定
  const pacing = determinePacing(preferences.feedbackPacing, emotionalState, learningStyle);
  
  // コンテンツ生成
  const content = generateFeedbackContent(performance, userState, approach, tone);
  
  // プレゼンテーション設定
  const presentation = generatePresentationSettings(userState, performance);
  
  return {
    approach,
    tone,
    startWith,
    pacing,
    content,
    presentation,
  };
};

/**
 * アプローチ方法の決定
 */
const determineApproach = (
  performance: PerformanceData,
  emotionalState: EmotionalState,
  experienceLevel: ExperienceLevel
): AdaptiveFeedback['approach'] => {
  // 低成績 + 不安/緊張 = 優しいアプローチ
  if (performance.overall < 5 && (emotionalState === 'anxious' || emotionalState === 'nervous')) {
    return 'gentle';
  }
  
  // 高成績 + 自信 = 直接的アプローチ
  if (performance.overall >= 7 && emotionalState === 'confident') {
    return 'direct';
  }
  
  // その他はバランス型
  return 'balanced';
};

/**
 * フィードバックトーンの決定
 */
const determineTone = (
  performance: PerformanceData,
  emotionalState: EmotionalState,
  approach: AdaptiveFeedback['approach']
): keyof typeof FEEDBACK_TONES => {
  if (approach === 'gentle' || emotionalState === 'anxious') {
    return 'supportive';
  }
  
  if (performance.overall >= 7) {
    return 'motivational';
  }
  
  return 'constructive';
};

/**
 * 開始点の決定
 */
const determineStartingPoint = (
  performance: PerformanceData,
  emotionalState: EmotionalState,
  approach: AdaptiveFeedback['approach']
): AdaptiveFeedback['startWith'] => {
  if (approach === 'gentle' || emotionalState === 'anxious') {
    return 'strengths';
  }
  
  if (performance.overall >= 6) {
    return 'overall';
  }
  
  return 'strengths'; // 心理的安全性を重視
};

/**
 * ペーシングの決定
 */
const determinePacing = (
  userPreference: UserAdaptiveState['preferences']['feedbackPacing'],
  emotionalState: EmotionalState,
  learningStyle: LearningStyle
): AdaptiveFeedback['pacing'] => {
  // ユーザー設定を優先
  if (userPreference !== 'gradual') {
    return userPreference;
  }
  
  // 不安状態では段階的に
  if (emotionalState === 'anxious' || emotionalState === 'nervous') {
    return 'gradual';
  }
  
  // 学習スタイルに応じて調整
  if (learningStyle === 'reading') {
    return 'detailed';
  }
  
  return 'gradual';
};

/**
 * フィードバックコンテンツの生成
 */
const generateFeedbackContent = (
  performance: PerformanceData,
  userState: UserAdaptiveState,
  approach: AdaptiveFeedback['approach'],
  tone: keyof typeof FEEDBACK_TONES
): AdaptiveFeedback['content'] => {
  const { experienceLevel, learningStyle } = userState;
  
  // 開始メッセージ
  const opening = generateOpeningMessage(performance, approach, experienceLevel);
  
  // 強みの抽出
  const strengths = generateStrengthsMessage(performance, learningStyle);
  
  // 改善点の生成
  const improvements = generateImprovementsMessage(performance, approach, experienceLevel);
  
  // パーソナライズされたアドバイス
  const personalizedTips = generatePersonalizedTips(performance, userState);
  
  // 次のステップ
  const nextSteps = generateNextSteps(performance, userState);
  
  // 励ましのメッセージ
  const encouragement = getRandomEncouragement();
  
  return {
    opening,
    strengths,
    improvements,
    personalizedTips,
    nextSteps,
    encouragement,
  };
};

/**
 * 開始メッセージの生成
 */
const generateOpeningMessage = (
  performance: PerformanceData,
  approach: AdaptiveFeedback['approach'],
  experienceLevel: ExperienceLevel
): string => {
  const baseMessage = getGrowthMessage(experienceLevel);
  
  if (approach === 'gentle') {
    return `${baseMessage} 今回の練習でも新しい発見がありましたね。`;
  }
  
  if (performance.overall >= 7) {
    return `${baseMessage} 素晴らしいパフォーマンスでした！`;
  }
  
  return `${baseMessage} 着実に成長の軌跡が見えています。`;
};

/**
 * 強みメッセージの生成
 */
const generateStrengthsMessage = (
  performance: PerformanceData,
  learningStyle: LearningStyle
): string[] => {
  const strengths: string[] = [];
  
  // 高得点分野を強みとして抽出
  const skillMap = {
    communication: '伝達力',
    logicalThinking: '論理的思考',
    problemSolving: '問題解決',
    creativity: '創造性',
    leadership: 'リーダーシップ',
    teamwork: 'チームワーク',
    adaptability: '適応性',
  };
  
  Object.entries(skillMap).forEach(([skill, label]) => {
    const score = performance[skill as keyof typeof skillMap];
    if (typeof score === 'number' && score >= 6) {
      strengths.push(`${label}において優れた能力を発揮されています`);
    }
  });
  
  // 完了率が高い場合
  if (performance.completionRate >= 0.8) {
    strengths.push('最後まで諦めずに取り組む姿勢が素晴らしいです');
  }
  
  // 学習スタイルに応じた強み
  if (learningStyle === 'visual' && performance.communication >= 6) {
    strengths.push('視覚的な表現力が印象的です');
  }
  
  return strengths.length > 0 ? strengths : ['チャレンジする姿勢そのものが素晴らしい強みです'];
};

/**
 * 改善点メッセージの生成
 */
const generateImprovementsMessage = (
  performance: PerformanceData,
  approach: AdaptiveFeedback['approach'],
  experienceLevel: ExperienceLevel
): string[] => {
  const improvements: string[] = [];
  const targets = EXPERIENCE_TARGETS[experienceLevel];
  
  // 目標未達成の分野を改善点として抽出
  const skillMap = {
    communication: '伝達スキル',
    logicalThinking: '論理的整理',
    problemSolving: '解決アプローチ',
  };
  
  Object.entries(skillMap).forEach(([skill, label]) => {
    const score = performance[skill as keyof typeof skillMap];
    const target = targets[skill as keyof typeof targets];
    if (typeof score === 'number' && typeof target === 'number' && score < target) {
      if (approach === 'gentle') {
        improvements.push(`${label}をさらに磨く機会があります`);
      } else {
        improvements.push(`${label}の向上で更なる成長が期待できます`);
      }
    }
  });
  
  return improvements;
};

/**
 * パーソナライズされたアドバイスの生成
 */
const generatePersonalizedTips = (
  performance: PerformanceData,
  userState: UserAdaptiveState
): string[] => {
  const tips: string[] = [];
  const { learningStyle, experienceLevel, personalGoals } = userState;
  
  // 学習スタイルに応じたアドバイス
  if (learningStyle === 'visual') {
    tips.push('図表やマインドマップを活用すると、より効果的に準備できます');
  } else if (learningStyle === 'auditory') {
    tips.push('声に出して練習することで、さらに上達が期待できます');
  } else if (learningStyle === 'kinesthetic') {
    tips.push('身振り手振りを使った表現練習が効果的です');
  }
  
  // 個人目標に関連したアドバイス
  personalGoals.forEach(goal => {
    if (!goal.isCompleted && goal.progress < 80) {
      tips.push(`「${goal.title}」の達成に向けて、継続的な練習が大きな力になります`);
    }
  });
  
  // 経験レベル別のアドバイス
  if (experienceLevel === 'beginner') {
    tips.push('基礎をしっかり固めることで、自信に繋がります');
  } else if (experienceLevel === 'intermediate') {
    tips.push('応用的な質問への対策を増やすことで、更なるレベルアップが期待できます');
  }
  
  return tips;
};

/**
 * 次のステップの生成
 */
const generateNextSteps = (
  performance: PerformanceData,
  userState: UserAdaptiveState
): string[] => {
  const steps: string[] = [];
  const { experienceLevel, progressHistory } = userState;
  
  // パフォーマンスに基づく次のステップ
  if (performance.overall < 5) {
    steps.push('まずは基礎的な質問から始めて、少しずつ難易度を上げていきましょう');
  } else if (performance.overall < 7) {
    steps.push('応用的な質問にも挑戦して、実践力を高めていきましょう');
  } else {
    steps.push('高度な質問や複数の観点からの質問に挑戦してみましょう');
  }
  
  // 進捗履歴に基づく提案
  if (progressHistory.length > 2) {
    const recentProgress = progressHistory.slice(0, 3);
    const averageScore = recentProgress.reduce((sum, p) => sum + p.overallScore, 0) / recentProgress.length;
    
    if (averageScore > performance.overall) {
      steps.push('前回の成功体験を活かして、同じような準備をしてみましょう');
    }
  }
  
  // 実践的な次のステップ
  steps.push('実際の面接を想定して、時間制限を設けた練習をしてみましょう');
  
  return steps;
};

/**
 * プレゼンテーション設定の生成
 */
const generatePresentationSettings = (
  userState: UserAdaptiveState,
  performance: PerformanceData
): AdaptiveFeedback['presentation'] => {
  const { learningStyle, emotionalState, preferences } = userState;
  
  return {
    showScoresImmediately: emotionalState === 'confident' || preferences.feedbackPacing === 'immediate',
    useVisualAids: learningStyle === 'visual',
    includeComparisons: performance.overall >= 6 && emotionalState !== 'anxious',
    emphasizeProgress: preferences.showProgressIndicators ?? true,
  };
};

/**
 * フィードバック全体を安全な言語で整形
 */
export const formatAdaptiveFeedback = (
  adaptiveFeedback: AdaptiveFeedback
): string => {
  const { content, tone } = adaptiveFeedback;
  
  const sections = [
    content.opening,
    content.strengths.join('。'),
    content.improvements.join('。'),
    content.personalizedTips.join('。'),
    content.nextSteps.join('。'),
    content.encouragement,
  ].filter(Boolean);
  
  const fullContent = sections.join('\n\n');
  
  return formatSafeFeedback(fullContent, tone);
};