/**
 * 履歴書解析サービス
 * PDF/Word形式の履歴書から構造化データを抽出
 */
import { 
  ResumeFile, 
  ParsedResumeContent, 
  ResumeParsingRequest, 
  ResumeParsingResponse,
  SkillInfo,
  WorkExperience,
  Education,
  Certification
} from '../types/candidate-profile';

/**
 * 履歴書解析の設定
 */
interface ParsingConfiguration {
  language: 'ja' | 'en';
  confidenceThreshold: number;
  enableSkillExtraction: boolean;
  enableExperienceExtraction: boolean;
  enableEducationExtraction: boolean;
  enableCertificationExtraction: boolean;
  maxProcessingTime: number; // ミリ秒
}

/**
 * 解析エンジンの種類
 */
type ParsingEngine = 'azure_form_recognizer' | 'tesseract_ocr' | 'mock' | 'custom_nlp';

/**
 * 履歴書解析サービスクラス
 */
export class ResumeParsingService {
  private readonly defaultConfig: ParsingConfiguration = {
    language: 'ja',
    confidenceThreshold: 0.7,
    enableSkillExtraction: true,
    enableExperienceExtraction: true,
    enableEducationExtraction: true,
    enableCertificationExtraction: true,
    maxProcessingTime: 30000, // 30秒
  };

  /**
   * 履歴書ファイルを解析してメタデータを抽出
   */
  async parseResumeFile(
    file: ResumeFile, 
    options: Partial<ParsingConfiguration> = {}
  ): Promise<ResumeParsingResponse> {
    const config = { ...this.defaultConfig, ...options };
    const startTime = Date.now();

    try {
      // ファイル形式の検証
      this.validateFileFormat(file);
      
      // 解析エンジンの選択
      const engine = this.selectParsingEngine(file.fileType);
      
      // ファイル内容の抽出
      const rawText = await this.extractTextFromFile(file, engine);
      
      // 構造化データの生成
      const parsedContent = await this.parseTextContent(rawText, config);
      
      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        parsedContent,
        processingTime,
        confidence: parsedContent.extractionConfidence,
        warnings: this.generateWarnings(parsedContent),
      };
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('履歴書解析エラー:', error);
      
      return {
        success: false,
        errors: [error instanceof Error ? error.message : String(error)],
        processingTime,
        confidence: 0,
      };
    }
  }

  /**
   * ファイル形式の検証
   */
  private validateFileFormat(file: ResumeFile): void {
    const allowedTypes = ['pdf', 'doc', 'docx'];
    if (!allowedTypes.includes(file.fileType)) {
      throw new Error(`サポートされていないファイル形式: ${file.fileType}`);
    }

    // ファイルサイズの制限（10MB）
    const maxSize = 10 * 1024 * 1024;
    if (file.fileSize > maxSize) {
      throw new Error(`ファイルサイズが制限を超えています: ${file.fileSize}バイト`);
    }
  }

  /**
   * 解析エンジンの選択
   */
  private selectParsingEngine(fileType: string): ParsingEngine {
    // 本番環境では Azure Form Recognizer を使用
    if (process.env.NODE_ENV === 'production') {
      return 'azure_form_recognizer';
    }
    
    // 開発環境では mock を使用
    return 'mock';
  }

  /**
   * ファイルからテキストを抽出
   */
  private async extractTextFromFile(file: ResumeFile, engine: ParsingEngine): Promise<string> {
    switch (engine) {
      case 'azure_form_recognizer':
        return await this.extractWithAzure(file);
      case 'tesseract_ocr':
        return await this.extractWithTesseract(file);
      case 'mock':
        return this.generateMockText();
      default:
        throw new Error(`未サポートの解析エンジン: ${engine}`);
    }
  }

  /**
   * Azure Form Recognizer を使用したテキスト抽出
   */
  private async extractWithAzure(file: ResumeFile): Promise<string> {
    // 本番実装では Azure Form Recognizer API を呼び出し
    // 現在はモック実装
    console.log(`Azure Form Recognizer で解析中: ${file.fileName}`);
    await new Promise(resolve => setTimeout(resolve, 2000)); // シミュレート
    return this.generateMockText();
  }

  /**
   * Tesseract OCR を使用したテキスト抽出
   */
  private async extractWithTesseract(file: ResumeFile): Promise<string> {
    // 本番実装では Tesseract.js を使用
    console.log(`Tesseract OCR で解析中: ${file.fileName}`);
    await new Promise(resolve => setTimeout(resolve, 3000)); // シミュレート
    return this.generateMockText();
  }

  /**
   * 開発用のモックテキスト生成
   */
  private generateMockText(): string {
    return `
履歴書

田中 太郎
Email: <EMAIL>
Phone: 090-1234-5678
住所: 東京都渋谷区

【職歴】
2020年4月 - 現在: 株式会社テックイノベーション
ソフトウェアエンジニア
・React/TypeScriptを使用したWebアプリケーション開発
・チーム5名のリーダーとしてプロジェクト管理
・ユーザー体験向上により売上20%向上に貢献

2018年4月 - 2020年3月: 株式会社デジタルソリューション
フロントエンドエンジニア
・Vue.js/JavaScriptでの開発業務
・UI/UXデザインの改善
・社内ツールの開発・保守

【学歴】
2014年4月 - 2018年3月: 東京工業大学 情報工学部
情報科学科 卒業

【スキル】
・プログラミング: JavaScript, TypeScript, Python, Java
・フレームワーク: React, Vue.js, Node.js, Express
・データベース: MySQL, PostgreSQL, MongoDB
・クラウド: AWS, Docker
・その他: Git, CI/CD, アジャイル開発

【資格】
・AWS Certified Solutions Architect (2021年取得)
・基本情報技術者試験 (2017年取得)

【自己PR】
技術への情熱と学習意欲が高く、新しい技術の習得に積極的です。
チームワークを重視し、メンバー間のコミュニケーションを大切にしています。
ユーザー中心の開発を心がけ、常により良い製品作りを目指しています。
`;
  }

  /**
   * テキストコンテンツから構造化データを生成
   */
  private async parseTextContent(
    text: string, 
    config: ParsingConfiguration
  ): Promise<ParsedResumeContent> {
    const parsedContent: ParsedResumeContent = {
      summary: this.extractSummary(text),
      objective: this.extractObjective(text),
      skills: config.enableSkillExtraction ? this.extractSkills(text) : [],
      experience: config.enableExperienceExtraction ? this.extractExperience(text) : [],
      education: config.enableEducationExtraction ? this.extractEducation(text) : [],
      certifications: config.enableCertificationExtraction ? this.extractCertifications(text) : [],
      achievements: this.extractAchievements(text),
      keywords: this.extractKeywords(text),
      languages: this.extractLanguages(text),
      extractionConfidence: this.calculateConfidence(text),
      extractedAt: new Date().toISOString(),
      extractionEngine: 'mock',
    };

    return parsedContent;
  }

  /**
   * 自己PR・サマリーの抽出
   */
  private extractSummary(text: string): string | undefined {
    const summaryMatch = text.match(/【自己PR】\s*([^【]+)/);
    return summaryMatch ? summaryMatch[1].trim() : undefined;
  }

  /**
   * 志望動機・目標の抽出
   */
  private extractObjective(text: string): string | undefined {
    const objectiveMatch = text.match(/【志望動機】\s*([^【]+)/);
    return objectiveMatch ? objectiveMatch[1].trim() : undefined;
  }

  /**
   * スキル情報の抽出
   */
  private extractSkills(text: string): SkillInfo[] {
    const skills: SkillInfo[] = [];
    
    // プログラミング言語の抽出
    const programmingMatch = text.match(/プログラミング[：:]\s*([^\n]+)/);
    if (programmingMatch) {
      const languages = programmingMatch[1].split(/[、,]/).map(s => s.trim());
      languages.forEach(lang => {
        if (lang) {
          skills.push({
            name: lang,
            category: 'technical',
            level: 'intermediate', // デフォルト値
          });
        }
      });
    }

    // フレームワークの抽出
    const frameworkMatch = text.match(/フレームワーク[：:]\s*([^\n]+)/);
    if (frameworkMatch) {
      const frameworks = frameworkMatch[1].split(/[、,]/).map(s => s.trim());
      frameworks.forEach(fw => {
        if (fw) {
          skills.push({
            name: fw,
            category: 'technical',
            level: 'intermediate',
          });
        }
      });
    }

    return skills;
  }

  /**
   * 職歴情報の抽出
   */
  private extractExperience(text: string): WorkExperience[] {
    const experiences: WorkExperience[] = [];
    
    // 簡易的な職歴パターン抽出
    const experiencePattern = /(\d{4}年\d{1,2}月)\s*-\s*([^\n]*?):\s*([^\n]+)\s*([^\n]+)/g;
    let match;
    
    while ((match = experiencePattern.exec(text)) !== null) {
      const [, startDate, endDate, company, position] = match;
      
      experiences.push({
        id: `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        company: company.trim(),
        position: position.trim(),
        startDate: this.parseJapaneseDate(startDate),
        endDate: endDate.includes('現在') ? undefined : this.parseJapaneseDate(endDate),
        isCurrentRole: endDate.includes('現在'),
        responsibilities: [],
        achievements: [],
        skills: [],
      });
    }

    return experiences;
  }

  /**
   * 学歴情報の抽出
   */
  private extractEducation(text: string): Education[] {
    const educations: Education[] = [];
    
    // 学歴パターンの抽出
    const educationPattern = /(\d{4}年\d{1,2}月)\s*-\s*(\d{4}年\d{1,2}月):\s*([^\n]+)\s*([^\n]+)/g;
    let match;
    
    while ((match = educationPattern.exec(text)) !== null) {
      const [, startDate, endDate, institution, degree] = match;
      
      educations.push({
        id: `edu_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        institution: institution.trim(),
        degree: degree.trim(),
        startDate: this.parseJapaneseDate(startDate),
        endDate: this.parseJapaneseDate(endDate),
        isActive: false,
      });
    }

    return educations;
  }

  /**
   * 資格情報の抽出
   */
  private extractCertifications(text: string): Certification[] {
    const certifications: Certification[] = [];
    
    // 資格パターンの抽出
    const certPattern = /・([^(]+)\s*\((\d{4}年)取得\)/g;
    let match;
    
    while ((match = certPattern.exec(text)) !== null) {
      const [, name, year] = match;
      
      certifications.push({
        id: `cert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: name.trim(),
        issuingOrganization: '不明',
        issueDate: this.parseJapaneseYear(year),
      });
    }

    return certifications;
  }

  /**
   * 成果・実績の抽出
   */
  private extractAchievements(text: string): string[] {
    const achievements: string[] = [];
    
    // 数値を含む成果の抽出
    const achievementPattern = /[^。]*(\d+%|売上|向上|改善|削減)[^。]*/g;
    let match;
    
    while ((match = achievementPattern.exec(text)) !== null) {
      achievements.push(match[0].trim());
    }

    return achievements;
  }

  /**
   * キーワードの抽出
   */
  private extractKeywords(text: string): string[] {
    const keywords = new Set<string>();
    
    // 技術キーワード
    const techKeywords = [
      'JavaScript', 'TypeScript', 'Python', 'Java', 'React', 'Vue.js', 'Node.js', 
      'AWS', 'Docker', 'Git', 'SQL', 'MongoDB', 'PostgreSQL', 'CI/CD'
    ];
    
    techKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        keywords.add(keyword);
      }
    });

    return Array.from(keywords);
  }

  /**
   * 言語能力の抽出
   */
  private extractLanguages(text: string): Array<{ name: string; level: 'native' | 'fluent' | 'conversational' | 'basic' }> {
    const languages: Array<{ name: string; level: 'native' | 'fluent' | 'conversational' | 'basic' }> = [];
    
    // 日本語は基本的にネイティブとして追加
    languages.push({ name: '日本語', level: 'native' });
    
    // 英語能力の検出
    if (text.includes('英語') || text.includes('English') || text.includes('TOEIC')) {
      languages.push({ name: '英語', level: 'conversational' });
    }

    return languages;
  }

  /**
   * 解析信頼度の計算
   */
  private calculateConfidence(text: string): number {
    let confidence = 0.5; // ベース信頼度
    
    // テキスト長による信頼度調整
    if (text.length > 500) confidence += 0.2;
    if (text.length > 1000) confidence += 0.1;
    
    // 構造化情報の存在による信頼度向上
    if (text.includes('職歴') || text.includes('経歴')) confidence += 0.1;
    if (text.includes('学歴') || text.includes('教育')) confidence += 0.1;
    if (text.includes('スキル') || text.includes('技術')) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * 警告メッセージの生成
   */
  private generateWarnings(parsedContent: ParsedResumeContent): string[] {
    const warnings: string[] = [];
    
    if (parsedContent.experience.length === 0) {
      warnings.push('職歴情報が検出されませんでした');
    }
    
    if (parsedContent.education.length === 0) {
      warnings.push('学歴情報が検出されませんでした');
    }
    
    if (parsedContent.skills.length === 0) {
      warnings.push('スキル情報が検出されませんでした');
    }
    
    if (parsedContent.extractionConfidence < 0.7) {
      warnings.push('解析精度が低い可能性があります。手動での確認を推奨します');
    }
    
    return warnings;
  }

  /**
   * 日本語形式の日付をISO形式に変換
   */
  private parseJapaneseDate(dateStr: string): string {
    const match = dateStr.match(/(\d{4})年(\d{1,2})月/);
    if (match) {
      const [, year, month] = match;
      return `${year}-${month.padStart(2, '0')}-01`;
    }
    return new Date().toISOString().split('T')[0];
  }

  /**
   * 日本語形式の年をISO形式に変換
   */
  private parseJapaneseYear(yearStr: string): string {
    const match = yearStr.match(/(\d{4})年/);
    if (match) {
      return `${match[1]}-01-01`;
    }
    return new Date().toISOString().split('T')[0];
  }
}

export default ResumeParsingService;