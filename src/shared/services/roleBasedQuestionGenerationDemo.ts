/**
 * 役割別質問生成システムのデモとヘルパー関数
 * 実際の使用例とテスト機能を提供
 */

import { IntelligentQuestionGenerator } from './intelligentQuestionGenerator';
import {
  InterviewerRole,
  InterviewerRoleType,
  InterviewerPersonality,
  QuestionStyle,
} from '../types/interviewer-roles';
import {
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  QuestionCategory,
} from '../types/intelligent-questions';

/**
 * 役割別質問生成のデモ実行クラス
 */
export class RoleBasedQuestionGenerationDemo {
  private questionGenerator: IntelligentQuestionGenerator;

  constructor() {
    this.questionGenerator = new IntelligentQuestionGenerator();
  }

  /**
   * デフォルトの面接官役割を作成
   */
  public createDefaultInterviewerRoles(): Record<InterviewerRoleType, InterviewerRole> {
    return {
      hr: this.createHRRole(),
      ceo: this.createCEORole(),
      technical_lead: this.createTechnicalLeadRole(),
      team_leader: this.createTeamLeaderRole(),
      peer: this.createPeerRole(),
      senior_manager: this.createSeniorManagerRole(),
      external: this.createExternalRole(),
    };
  }

  /**
   * HR役割の作成
   */
  private createHRRole(): InterviewerRole {
    return {
      id: 'hr_default',
      type: 'hr',
      title: '人事担当者',
      description: '企業文化適合性と人物像を重視する人事専門家',
      personality: {
        formality: 'balanced',
        supportiveness: 'encouraging',
        pace: 'normal',
        detailOrientation: 'medium',
        questioningStyle: 'conversational',
        feedbackImmediacy: 'immediate',
        pressureLevel: 'low',
        languageComplexity: 'standard',
        useOfHumor: false,
        empathyLevel: 'high',
      },
      questionStyle: {
        behavioralFocus: 0.7,
        technicalFocus: 0.2,
        visionaryFocus: 0.5,
        culturalFitFocus: 0.9,
        problemSolvingFocus: 0.4,
        leadershipFocus: 0.5,
        teamworkFocus: 0.8,
        preferredCategories: ['company-culture-fit', 'motivation', 'behavioral'],
        difficultyProgression: 'gentle',
        followUpIntensity: 'medium',
        hypotheticalQuestionRatio: 0.2,
        deepDiveTopics: ['価値観', '企業文化', '人間関係'],
        timeAllocation: {
          opening: 5,
          coreQuestions: 20,
          closing: 5,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['企業文化適合性', 'コミュニケーション能力', '人物像'],
        secondaryCriteria: ['成長意欲', '協調性', '誠実性'],
        weightings: {
          '企業文化適合性': 0.4,
          'コミュニケーション能力': 0.3,
          '人物像': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'professional',
        voiceTone: 'warm',
        gestureStyle: 'moderate',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * CEO役割の作成
   */
  private createCEORole(): InterviewerRole {
    return {
      id: 'ceo_default',
      type: 'ceo',
      title: 'CEO・経営陣',
      description: 'ビジョンと戦略的思考を重視する経営幹部',
      personality: {
        formality: 'formal',
        supportiveness: 'neutral',
        pace: 'normal',
        detailOrientation: 'high',
        questioningStyle: 'socratic',
        feedbackImmediacy: 'delayed',
        pressureLevel: 'medium',
        languageComplexity: 'complex',
        useOfHumor: false,
        empathyLevel: 'medium',
      },
      questionStyle: {
        behavioralFocus: 0.5,
        technicalFocus: 0.3,
        visionaryFocus: 0.9,
        culturalFitFocus: 0.6,
        problemSolvingFocus: 0.8,
        leadershipFocus: 0.8,
        teamworkFocus: 0.6,
        preferredCategories: ['leadership', 'career-goals', 'problem-solving'],
        difficultyProgression: 'challenging',
        followUpIntensity: 'high',
        hypotheticalQuestionRatio: 0.4,
        deepDiveTopics: ['ビジョン', '戦略', 'リーダーシップ', '事業理解'],
        timeAllocation: {
          opening: 3,
          coreQuestions: 25,
          closing: 2,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['ビジョナリー思考', 'リーダーシップポテンシャル', '戦略的思考'],
        secondaryCriteria: ['事業理解', '意思決定力', '影響力'],
        weightings: {
          'ビジョナリー思考': 0.4,
          'リーダーシップポテンシャル': 0.3,
          '戦略的思考': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'executive',
        voiceTone: 'authoritative',
        gestureStyle: 'minimal',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * 技術リード役割の作成
   */
  private createTechnicalLeadRole(): InterviewerRole {
    return {
      id: 'tech_lead_default',
      type: 'technical_lead',
      title: '技術責任者',
      description: '技術力と問題解決能力を重視する技術リーダー',
      personality: {
        formality: 'casual',
        supportiveness: 'neutral',
        pace: 'normal',
        detailOrientation: 'high',
        questioningStyle: 'directive',
        feedbackImmediacy: 'immediate',
        pressureLevel: 'medium',
        languageComplexity: 'standard',
        useOfHumor: true,
        empathyLevel: 'medium',
      },
      questionStyle: {
        behavioralFocus: 0.4,
        technicalFocus: 0.9,
        visionaryFocus: 0.3,
        culturalFitFocus: 0.4,
        problemSolvingFocus: 0.8,
        leadershipFocus: 0.5,
        teamworkFocus: 0.6,
        preferredCategories: ['technical', 'problem-solving', 'experience-skills'],
        difficultyProgression: 'adaptive',
        followUpIntensity: 'high',
        hypotheticalQuestionRatio: 0.3,
        deepDiveTopics: ['技術スキル', '実装', 'アーキテクチャ', '学習'],
        timeAllocation: {
          opening: 2,
          coreQuestions: 25,
          closing: 3,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['技術スキル', '問題解決能力', '学習能力'],
        secondaryCriteria: ['実装経験', '技術的コミュニケーション', '品質意識'],
        weightings: {
          '技術スキル': 0.4,
          '問題解決能力': 0.3,
          '学習能力': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'casual',
        voiceTone: 'neutral',
        gestureStyle: 'expressive',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * チームリーダー役割の作成
   */
  private createTeamLeaderRole(): InterviewerRole {
    return {
      id: 'team_leader_default',
      type: 'team_leader',
      title: 'チームリーダー',
      description: 'チームワークと協働を重視する現場リーダー',
      personality: {
        formality: 'balanced',
        supportiveness: 'encouraging',
        pace: 'normal',
        detailOrientation: 'medium',
        questioningStyle: 'conversational',
        feedbackImmediacy: 'immediate',
        pressureLevel: 'low',
        languageComplexity: 'standard',
        useOfHumor: true,
        empathyLevel: 'high',
      },
      questionStyle: {
        behavioralFocus: 0.7,
        technicalFocus: 0.5,
        visionaryFocus: 0.4,
        culturalFitFocus: 0.7,
        problemSolvingFocus: 0.6,
        leadershipFocus: 0.6,
        teamworkFocus: 0.9,
        preferredCategories: ['teamwork', 'behavioral', 'leadership'],
        difficultyProgression: 'adaptive',
        followUpIntensity: 'medium',
        hypotheticalQuestionRatio: 0.3,
        deepDiveTopics: ['チームワーク', '協力', 'コミュニケーション'],
        timeAllocation: {
          opening: 5,
          coreQuestions: 20,
          closing: 5,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['チームワーク', '協調性', 'コミュニケーション能力'],
        secondaryCriteria: ['リーダーシップ', '問題解決', '適応性'],
        weightings: {
          'チームワーク': 0.4,
          '協調性': 0.3,
          'コミュニケーション能力': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'professional',
        voiceTone: 'warm',
        gestureStyle: 'moderate',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * 同僚役割の作成
   */
  private createPeerRole(): InterviewerRole {
    return {
      id: 'peer_default',
      type: 'peer',
      title: '同僚・チームメンバー',
      description: '協働しやすさと実務能力を重視する同僚',
      personality: {
        formality: 'casual',
        supportiveness: 'encouraging',
        pace: 'normal',
        detailOrientation: 'medium',
        questioningStyle: 'conversational',
        feedbackImmediacy: 'immediate',
        pressureLevel: 'low',
        languageComplexity: 'simple',
        useOfHumor: true,
        empathyLevel: 'high',
      },
      questionStyle: {
        behavioralFocus: 0.6,
        technicalFocus: 0.5,
        visionaryFocus: 0.3,
        culturalFitFocus: 0.8,
        problemSolvingFocus: 0.5,
        leadershipFocus: 0.3,
        teamworkFocus: 0.8,
        preferredCategories: ['experience-skills', 'teamwork', 'company-culture-fit'],
        difficultyProgression: 'gentle',
        followUpIntensity: 'low',
        hypotheticalQuestionRatio: 0.2,
        deepDiveTopics: ['協力', '日常業務', '職場の雰囲気'],
        timeAllocation: {
          opening: 7,
          coreQuestions: 18,
          closing: 5,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['協働しやすさ', '実務能力', '人間性'],
        secondaryCriteria: ['コミュニケーション', '適応性', '協調性'],
        weightings: {
          '協働しやすさ': 0.4,
          '実務能力': 0.3,
          '人間性': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'casual',
        voiceTone: 'warm',
        gestureStyle: 'expressive',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * 上級管理職役割の作成
   */
  private createSeniorManagerRole(): InterviewerRole {
    return {
      id: 'senior_mgr_default',
      type: 'senior_manager',
      title: '上級管理職',
      description: '組織運営とマネジメント能力を重視する管理職',
      personality: {
        formality: 'formal',
        supportiveness: 'neutral',
        pace: 'normal',
        detailOrientation: 'high',
        questioningStyle: 'socratic',
        feedbackImmediacy: 'delayed',
        pressureLevel: 'medium',
        languageComplexity: 'complex',
        useOfHumor: false,
        empathyLevel: 'medium',
      },
      questionStyle: {
        behavioralFocus: 0.7,
        technicalFocus: 0.4,
        visionaryFocus: 0.7,
        culturalFitFocus: 0.6,
        problemSolvingFocus: 0.7,
        leadershipFocus: 0.8,
        teamworkFocus: 0.7,
        preferredCategories: ['leadership', 'behavioral', 'problem-solving'],
        difficultyProgression: 'challenging',
        followUpIntensity: 'high',
        hypotheticalQuestionRatio: 0.4,
        deepDiveTopics: ['マネジメント', '組織運営', '意思決定'],
        timeAllocation: {
          opening: 3,
          coreQuestions: 25,
          closing: 2,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['マネジメント能力', '組織貢献', 'リーダーシップ'],
        secondaryCriteria: ['戦略的思考', '意思決定力', '人材育成'],
        weightings: {
          'マネジメント能力': 0.4,
          '組織貢献': 0.3,
          'リーダーシップ': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'executive',
        voiceTone: 'authoritative',
        gestureStyle: 'minimal',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * 外部面接官役割の作成
   */
  private createExternalRole(): InterviewerRole {
    return {
      id: 'external_default',
      type: 'external',
      title: '外部面接官',
      description: '客観的で構造化された評価を行う外部専門家',
      personality: {
        formality: 'formal',
        supportiveness: 'neutral',
        pace: 'normal',
        detailOrientation: 'high',
        questioningStyle: 'directive',
        feedbackImmediacy: 'end_only',
        pressureLevel: 'medium',
        languageComplexity: 'standard',
        useOfHumor: false,
        empathyLevel: 'low',
      },
      questionStyle: {
        behavioralFocus: 0.8,
        technicalFocus: 0.6,
        visionaryFocus: 0.5,
        culturalFitFocus: 0.4,
        problemSolvingFocus: 0.7,
        leadershipFocus: 0.6,
        teamworkFocus: 0.5,
        preferredCategories: ['behavioral', 'situational', 'problem-solving'],
        difficultyProgression: 'linear',
        followUpIntensity: 'high',
        hypotheticalQuestionRatio: 0.5,
        deepDiveTopics: ['行動パターン', '客観的評価', '構造化面接'],
        timeAllocation: {
          opening: 2,
          coreQuestions: 26,
          closing: 2,
        },
      },
      evaluationCriteria: {
        primaryCriteria: ['客観的能力', '行動特性', '総合適性'],
        secondaryCriteria: ['論理的思考', '一貫性', '適応性'],
        weightings: {
          '客観的能力': 0.4,
          '行動特性': 0.3,
          '総合適性': 0.3,
        },
      },
      avatarConfig: {
        appearance: 'professional',
        voiceTone: 'neutral',
        gestureStyle: 'minimal',
      },
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
    };
  }

  /**
   * 役割別質問生成のデモ実行
   */
  public async runRoleBasedDemo(): Promise<Record<InterviewerRoleType, QuestionGenerationResponse>> {
    const roles = this.createDefaultInterviewerRoles();
    const demoRequest = this.createDemoRequest();
    const results: Record<InterviewerRoleType, QuestionGenerationResponse> = {} as any;

    for (const [roleType, role] of Object.entries(roles)) {
      console.log(`Generating questions for role: ${roleType}`);
      
      const response = await this.questionGenerator.generateQuestions(demoRequest, role);
      results[roleType as InterviewerRoleType] = response;
      
      console.log(`Generated ${response.questions.length} questions for ${roleType}`);
    }

    return results;
  }

  /**
   * デモ用のリクエスト作成
   */
  private createDemoRequest(): QuestionGenerationRequest {
    return {
      companyInfo: {
        name: 'テックカンパニー株式会社',
        industry: 'IT・ソフトウェア',
        position: 'フロントエンドエンジニア',
        requirements: ['React', 'TypeScript', 'チームワーク'],
        culture: '成長志向とオープンなコミュニケーション',
        interviewStyle: 'collaborative',
      },
      candidateProfile: {
        experienceLevel: 'mid',
        skills: ['React', 'JavaScript', 'CSS'],
        background: ['スタートアップ経験', 'フロントエンド開発'],
        previousInterviews: 2,
        resumeData: {
          personalInfo: {
            name: '山田太郎',
            currentPosition: 'フロントエンドエンジニア',
            yearsOfExperience: 3,
          },
          workExperience: [
            {
              company: 'ABC スタートアップ',
              position: 'フロントエンドエンジニア',
              duration: '2年',
              achievements: ['UIコンポーネントライブラリ構築', 'パフォーマンス改善'],
            },
          ],
          certifications: ['AWS Certified Cloud Practitioner'],
        },
      },
      questionSettings: {
        totalQuestions: 5,
        categories: ['self-introduction', 'experience-skills', 'technical', 'teamwork', 'motivation'],
        difficulty: 'adaptive',
        estimatedDuration: 30,
        includeWarmup: true,
        includeFollowUp: true,
      },
      adaptiveSettings: {
        personalizeForCandidate: true,
        adjustForAnxiety: true,
        emphasizeGrowth: true,
        avoidNegativeLanguage: true,
      },
    };
  }

  /**
   * 役割別比較レポートの生成
   */
  public generateRoleComparisonReport(
    results: Record<InterviewerRoleType, QuestionGenerationResponse>
  ): string {
    let report = '# 役割別質問生成比較レポート\n\n';

    for (const [roleType, response] of Object.entries(results)) {
      report += `## ${roleType} (${response.questions.length}問)\n\n`;
      
      // 質問の概要
      report += '### 生成された質問:\n';
      response.questions.forEach((question, index) => {
        report += `${index + 1}. [${question.category}] ${question.text}\n`;
      });
      
      // セッション設定
      report += `\n### セッション設定:\n`;
      report += `- 総時間: ${response.sessionConfig.totalDuration}分\n`;
      report += `- 難易度進行: ${response.sessionConfig.difficultyProgression}\n`;
      
      // 支援メッセージ
      report += `\n### オープニングメッセージ:\n`;
      report += `${response.supportiveMessaging.openingMessage}\n\n`;
      
      report += '---\n\n';
    }

    return report;
  }
}

/**
 * 役割別質問生成のヘルパー関数
 */
export function createRoleBasedQuestionGenerator(): RoleBasedQuestionGenerationDemo {
  return new RoleBasedQuestionGenerationDemo();
}

/**
 * デフォルト役割セットの取得
 */
export function getDefaultInterviewerRoles(): Record<InterviewerRoleType, InterviewerRole> {
  const demo = new RoleBasedQuestionGenerationDemo();
  return demo.createDefaultInterviewerRoles();
}

/**
 * 特定の役割タイプのデフォルト設定を取得
 */
export function getDefaultRoleSettings(roleType: InterviewerRoleType): InterviewerRole {
  const roles = getDefaultInterviewerRoles();
  return roles[roleType];
}