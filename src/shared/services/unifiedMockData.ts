/**
 * 統合モックデータサービス
 * 候補者アプリと管理アプリで共有するモックデータ
 */

import type {
  Interview,
  Question,
  Answer,
  Feedback,
  MeetingLink,
  InterviewSummary,
  QuestionGenerationRequest,
  QuestionGenerationResponse,
  MeetingLinkGenerationRequest,
  MeetingLinkGenerationResponse,
  InterviewSessionResponse,
  AnswerSubmissionRequest,
  AnswerSubmissionResponse,
  AudioAnalysisResponse,
  FeedbackListResponse,
  ScoreSet
} from '../types';

// ===============================
// ベースモックデータ
// ===============================

const mockInterviews: Interview[] = [
  {
    id: 'interview_001',
    candidateId: 'candidate_001',
    agentId: 'agent_001',
    companyName: '株式会社テクノロジーソリューション',
    position: 'フロントエンドエンジニア',
    requirements: ['React経験3年以上', 'TypeScript実務経験', 'チーム開発経験'],
    status: 'completed',
    interviewType: 'behavioral',
    estimatedDuration: 900,
    startedAt: '2024-06-14T14:00:00Z',
    completedAt: '2024-06-14T15:30:00Z',
    createdAt: '2024-06-14T10:00:00Z'
  },
  {
    id: 'interview_002',
    candidateId: 'candidate_002',
    agentId: 'agent_001',
    companyName: '株式会社マーケティングラボ',
    position: 'デジタルマーケター',
    requirements: ['Google Analytics経験', 'SNS運用経験', 'データ分析スキル'],
    status: 'completed',
    interviewType: 'mixed',
    estimatedDuration: 1200,
    startedAt: '2024-06-13T15:00:00Z',
    completedAt: '2024-06-13T16:45:00Z',
    createdAt: '2024-06-13T09:00:00Z'
  }
];

const mockQuestions: Question[] = [
  {
    id: 'question_001',
    interviewId: 'interview_001',
    text: 'これまでの経験で最も困難だった技術的課題とその解決方法を教えてください',
    category: '技術経験',
    difficulty: 'medium',
    estimatedTime: 180,
    orderIndex: 1,
    createdAt: '2024-06-14T10:00:00Z'
  },
  {
    id: 'question_002',
    interviewId: 'interview_001',
    text: 'チームプロジェクトでリーダーシップを発揮した経験はありますか？',
    category: 'リーダーシップ',
    difficulty: 'medium',
    estimatedTime: 180,
    orderIndex: 2,
    createdAt: '2024-06-14T10:00:00Z'
  },
  {
    id: 'question_003',
    interviewId: 'interview_001',
    text: '当社で実現したいことは何ですか？',
    category: '志望動機',
    difficulty: 'easy',
    estimatedTime: 120,
    orderIndex: 3,
    createdAt: '2024-06-14T10:00:00Z'
  }
];

const mockAnswers: Answer[] = [
  {
    id: 'answer_001',
    questionId: 'question_001',
    candidateAnswer: 'レガシーシステムのモダナイゼーションプロジェクトで、React移行時に既存のjQueryコードとの互換性問題が発生しました。段階的移行とアダプターパターンを使用して解決しました。',
    transcription: 'レガシーシステムのモダナイゼーションプロジェクトで、React移行時に既存のjQueryコードとの互換性問題が発生しました。段階的移行とアダプターパターンを使用して解決しました。',
    duration: 165,
    answeredAt: '2024-06-14T14:15:00Z'
  },
  {
    id: 'answer_002',
    questionId: 'question_002',
    candidateAnswer: '5人のチームでWebアプリケーション開発を担当し、技術選定からデプロイまでをリードしました。スクラム手法を導入し、定期的な振り返りでチーム効率を30%向上させました。',
    transcription: '5人のチームでWebアプリケーション開発を担当し、技術選定からデプロイまでをリードしました。スクラム手法を導入し、定期的な振り返りでチーム効率を30%向上させました。',
    duration: 185,
    answeredAt: '2024-06-14T14:18:00Z'
  }
];

const mockFeedbacks: Feedback[] = [
  {
    id: 'feedback_001',
    answerId: 'answer_001',
    aiAnalysis: '技術的課題に対する具体的で構造化された回答です。問題識別、解決策、結果が明確に述べられており、技術的な深さも適切です。実際のプロジェクト経験が感じられる内容で、問題解決能力の高さが伺えます。',
    emotionScore: 8.2,
    confidenceScore: 8.5,
    relevanceScore: 9.2,
    clarityScore: 8.8,
    overallRating: 4,
    suggestions: [
      '数値的な成果をより詳しく説明すると説得力が増します',
      'チームメンバーとの協力体制についても言及できるとより良いでしょう'
    ],
    createdAt: '2024-06-14T14:16:00Z'
  },
  {
    id: 'feedback_002',
    answerId: 'answer_002',
    aiAnalysis: 'リーダーシップ経験について具体的な数値と手法を交えて説明されており、非常に優秀な回答です。スクラム導入と効果測定まで言及されており、実践的な経験と成果が明確に示されています。',
    emotionScore: 8.8,
    confidenceScore: 9.1,
    relevanceScore: 9.5,
    clarityScore: 9.0,
    overallRating: 5,
    suggestions: [
      '困難だった点やその克服方法も含めるとより説得力が増します',
      'チームメンバーからのフィードバックがあれば理想的です'
    ],
    createdAt: '2024-06-14T14:19:00Z'
  }
];

const mockMeetingLinks: MeetingLink[] = [
  {
    id: 'link_001',
    interviewId: 'interview_001',
    token: 'mtg_token_abc123',
    url: 'http://localhost:3000/interview-room?token=mtg_token_abc123',
    expiresAt: '2024-06-21T10:00:00Z',
    status: 'used',
    accessedAt: '2024-06-14T14:00:00Z',
    createdAt: '2024-06-14T10:00:00Z',
    candidateName: '田中太郎',
    candidateEmail: '<EMAIL>'
  }
];

// ===============================
// データ生成ヘルパー
// ===============================

const generateQuestions = (
  interviewId: string,
  companyName: string,
  position: string,
  requirements: string[]
): Question[] => {
  const baseQuestions = [
    'まず簡単に自己紹介をお願いします',
    'これまでの経験で最も困難だった課題とその解決方法を教えてください',
    'チームプロジェクトでリーダーシップを発揮した経験はありますか？',
    '技術的な問題をどのように解決しますか？',
    `${companyName}で実現したいことは何ですか？`,
    '5年後のキャリアビジョンを教えてください'
  ];

  // ポジション別追加質問
  const positionSpecificQuestions: Record<string, string[]> = {
    'フロントエンドエンジニア': [
      'Reactの状態管理についてどのような手法を使用していますか？',
      'パフォーマンス最適化で気をつけているポイントは？'
    ],
    'デジタルマーケター': [
      'データ分析結果をどのように施策に活かしますか？',
      'ROI向上のためにどのような取り組みをしましたか？'
    ]
  };

  const additionalQuestions = positionSpecificQuestions[position] || [];
  const allQuestions = [...baseQuestions, ...additionalQuestions];

  return allQuestions.map((text, index) => ({
    id: `${interviewId}_q_${index + 1}`,
    interviewId,
    text,
    category: index === 0 ? '自己紹介' : 
              index <= 2 ? '経験・スキル' : 
              index <= 4 ? '志望動機' : 'キャリア',
    difficulty: index === 0 ? 'easy' as const : 'medium' as const,
    estimatedTime: index === 0 ? 120 : 180,
    orderIndex: index + 1,
    createdAt: new Date().toISOString()
  }));
};

const generateMockFeedback = (answerId: string, answerText: string): Feedback => {
  const scores: ScoreSet = {
    emotion: 7.0 + Math.random() * 2.5,
    confidence: 7.5 + Math.random() * 2.0,
    relevance: 8.0 + Math.random() * 1.8,
    clarity: 7.8 + Math.random() * 2.0
  };

  const overallRating = Math.round((scores.emotion + scores.confidence + scores.relevance + scores.clarity) / 4 / 2);

  return {
    id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    answerId,
    aiAnalysis: '回答は明確で構造化されており、質問に対して適切な内容が含まれています。具体的な経験と成果が述べられており、問題解決能力と技術的な理解の深さが伺えます。',
    emotionScore: scores.emotion,
    confidenceScore: scores.confidence,
    relevanceScore: scores.relevance,
    clarityScore: scores.clarity,
    overallRating: Math.max(1, Math.min(5, overallRating)),
    suggestions: [
      'より具体的な数値や成果を含めると説得力が増します',
      '困難だった点やその克服過程についても詳しく説明できると良いでしょう',
      'チームメンバーとの協力やコミュニケーション面についても触れてみてください'
    ],
    scores,
    createdAt: new Date().toISOString()
  };
};

// ===============================
// 統合モックデータAPIサービス
// ===============================

export class UnifiedMockDataService {
  /**
   * 質問スクリプト生成（管理画面用）
   */
  static async generateQuestionScript(
    request: QuestionGenerationRequest
  ): Promise<QuestionGenerationResponse> {
    await new Promise(resolve => setTimeout(resolve, 1500)); // API遅延シミュレーション

    const interviewId = `interview_${Date.now()}`;
    const questions = generateQuestions(
      interviewId,
      request.companyName,
      request.position,
      request.requirements
    );

    return {
      interviewId,
      questions
    };
  }

  /**
   * ミーティングリンク生成（管理画面用）
   */
  static async generateMeetingLink(
    request: MeetingLinkGenerationRequest
  ): Promise<MeetingLinkGenerationResponse> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    const linkId = `link_${Date.now()}`;
    const token = `mtg_token_${Math.random().toString(36).substr(2, 15)}`;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    return {
      linkId,
      url: `${baseUrl}/interview-room?token=${token}`,
      expiresAt: new Date(Date.now() + (request.expiresInDays || 7) * 24 * 60 * 60 * 1000).toISOString(),
      status: 'active'
    };
  }

  /**
   * 面接セッション開始（候補者用）
   */
  static async startInterviewSession(token: string): Promise<InterviewSessionResponse> {
    await new Promise(resolve => setTimeout(resolve, 800));

    // トークンから面接データを取得（実際はDBクエリ）
    const interview = mockInterviews[0];
    const questions = mockQuestions.filter(q => q.interviewId === interview.id);

    return {
      interviewId: interview.id,
      companyName: interview.companyName,
      position: interview.position,
      estimatedDuration: interview.estimatedDuration,
      questions,
      status: 'in_progress'
    };
  }

  /**
   * 回答送信（候補者用）
   */
  static async submitAnswer(
    request: AnswerSubmissionRequest
  ): Promise<AnswerSubmissionResponse> {
    await new Promise(resolve => setTimeout(resolve, 1200)); // 音声解析時間をシミュレーション

    const answerId = `answer_${Date.now()}`;
    const feedback = generateMockFeedback(answerId, request.textAnswer);

    return {
      answerId,
      transcription: request.textAnswer, // 実際は音声解析結果
      feedback
    };
  }

  /**
   * 音声解析（共通）
   */
  static async analyzeAudio(audioFile: File): Promise<AudioAnalysisResponse> {
    const isSmallFile = audioFile.size <= 1024;
    const delay = isSmallFile ? 100 : 2000;
    await new Promise(resolve => setTimeout(resolve, delay));

    return {
      transcription: '私は3年間フロントエンド開発に携わってきました。特にReactとTypeScriptを中心とした開発経験があり、チーム開発での協力やコードレビューも積極的に行ってきました。',
      confidence: 0.92,
      emotions: {
        happy: 0.7,
        neutral: 0.8,
        surprised: 0.3,
        sad: 0.1,
        angry: 0.05
      },
      speechQuality: {
        clarity: 8.5,
        pace: 7.8,
        volume: 8.2,
        fluency: 8.0
      }
    };
  }

  /**
   * フィードバック一覧取得（管理画面用） - 詳細化版
   */
  static async getFeedbackList(): Promise<FeedbackListResponse> {
    await new Promise(resolve => setTimeout(resolve, 800));

    const detailedInterviewSummaries: InterviewSummary[] = [
      {
        interviewId: 'interview_001',
        candidateName: '田中太郎',
        position: 'フロントエンドエンジニア',
        companyName: '株式会社テクノロジーソリューション',
        completedAt: '2024-06-14T15:30:00Z',
        overallScore: 8.6,
        questionCount: 7,
        duration: 1620, // 27分
        answers: [
          {
            questionText: 'まず簡単に自己紹介をお願いします',
            candidateAnswer: '田中太郎と申します。現在はスタートアップでフロントエンドエンジニアとして働いており、ReactとTypeScriptを主に使用しています。特にUX/UIの改善に情熱を注いでおり、ユーザー体験を向上させることにやりがいを感じています。',
            feedback: {
              id: 'feedback_self_intro',
              answerId: 'answer_self_intro',
              aiAnalysis: '非常に簡潔で明確な自己紹介です。技術スタックと情熱が明確に伝わり、印象の良いスタートでした。具体的な技術名と関心領域が明示されている点が優秀です。',
              emotionScore: 8.5,
              confidenceScore: 8.8,
              relevanceScore: 9.2,
              clarityScore: 9.0,
              overall: 8.9,
              overallRating: 4,
              suggestions: ['学習意欲や成長への姿勢も含めるとさらに魅力的に'],
              createdAt: '2024-06-14T15:05:00Z'
            }
          },
          {
            questionText: 'これまでの経験で最も困難だった技術的課題とその解決方法を教えてください',
            candidateAnswer: 'レガシーシステムのモダナイゼーションプロジェクトで、jQueryベースのシステムをReactに移行する際に、既存のデータ流とReactの状態管理の間で不整合が発生しました。段階的なリファクタリングを計画し、アダプターパターンで一時的なブリッジを作り、最終的に3ヶ月かけて完全移行を完了しました。パフォーマンスも約40%向上しました。',
            feedback: {
              id: 'feedback_technical',
              answerId: 'answer_technical',
              aiAnalysis: '非常に具体的で技術的な深さがある回答です。問題の詳細、解決アプローチ、結果の定量的評価まで含まれており、問題解決能力の高さが伝わります。アダプターパターンの活用や段階的アプローチなど、技術的な判断力も優秀です。',
              emotionScore: 8.8,
              confidenceScore: 9.2,
              relevanceScore: 9.5,
              clarityScore: 9.3,
              overall: 9.2,
              overallRating: 5,
              suggestions: ['チームとの連携方法やコミュニケーション面も言及できると完璧'],
              createdAt: '2024-06-14T15:08:00Z'
            }
          },
          {
            questionText: 'チームプロジェクトでリーダーシップを発揮した経験はありますか？',
            candidateAnswer: 'はい、5人のチームでECサイトのリニューアルプロジェクトでテックリードを担当しました。プロジェクトの技術選定からアーキテクチャ設計、コードレビュープロセスの確立までを主導しました。スクラム手法を導入し、毎日のスタンドアップと週次の振り返りでチームのコミュニケーションを活発化し、結果的に開発速度が30%向上しました。',
            feedback: {
              id: 'feedback_leadership',
              answerId: 'answer_leadership',
              aiAnalysis: 'リーダーシップ経験について非常に具体的で実践的な回答です。技術リーダーシップだけでなく、プロセス改善やチームビルディングの観点からも語られており、マネジメント能力の高さが伝わります。数値で結果を示している点も優秀です。',
              emotionScore: 9.0,
              confidenceScore: 9.5,
              relevanceScore: 9.8,
              clarityScore: 9.2,
              overall: 9.4,
              overallRating: 5,
              suggestions: ['困難だった点やその克服方法も含めるとさらに評価が高まります'],
              createdAt: '2024-06-14T15:12:00Z'
            }
          },
          {
            questionText: '当社で実現したいことは何ですか？',
            candidateAnswer: '当社のプロダクトであるオンラインショッピングプラットフォームのユーザー体験を根本的に改善したいと考えています。特にモバイルファーストのUI/UX設計と、アクセシビリティの向上に力を入れたいです。私の経験を活かして、より多くのユーザーにとって使いやすいサービスを作り上げ、ビジネスの成長に貢献したいと思っています。',
            feedback: {
              id: 'feedback_motivation',
              answerId: 'answer_motivation',
              aiAnalysis: '当社への理解と具体的な貢献意欲が明確に示された優秀な回答です。自身の専門性（UX/UI、アクセシビリティ）と企業のニーズを適切に結びつけており、入社後の活躍イメージが明確です。ビジネスインパクトへの意識も高く評価できます。',
              emotionScore: 8.7,
              confidenceScore: 8.9,
              relevanceScore: 9.6,
              clarityScore: 9.1,
              overall: 9.1,
              overallRating: 5,
              suggestions: ['企業研究の深さや具体的な方法論も言及できるとさらに印象的'],
              createdAt: '2024-06-14T15:15:00Z'
            }
          }
        ]
      },
      {
        interviewId: 'interview_002',
        candidateName: '佐藤花子',
        position: 'デジタルマーケター',
        companyName: '株式会社マーケティングラボ',
        completedAt: '2024-06-13T16:45:00Z',
        overallScore: 7.8,
        questionCount: 5,
        duration: 1320, // 22分
        answers: [
          {
            questionText: 'デジタルマーケティングの経験と得意領域を教えてください',
            candidateAnswer: '現在のBtoB SaaS企業でデジタルマーケティングを担当しており、特にSEOとコンテンツマーケティングを得意としています。Google AnalyticsやSearch Console、HubSpotを使用したデータ分析から、2年間でオーガニック流入を200%向上させました。',
            feedback: {
              id: 'feedback_digital_marketing',
              answerId: 'answer_digital_marketing',
              aiAnalysis: 'デジタルマーケティングの実務経験が具体的なツール名と成果数値で示されており、非常に信頼性の高い回答です。SEOとコンテンツマーケティングという明確な強みと、200%向上という定量的な成果がアピールポイントです。',
              emotionScore: 8.2,
              confidenceScore: 8.6,
              relevanceScore: 9.3,
              clarityScore: 8.8,
              overall: 8.7,
              overallRating: 4,
              suggestions: ['チームでの協業やクロスファンクショナルな取り組みも言及できるとGood'],
              createdAt: '2024-06-13T16:20:00Z'
            }
          },
          {
            questionText: 'データ分析結果をどのように施策に活かしますか？',
            candidateAnswer: 'Google Analyticsとヒートマップツールを組み合わせて、ユーザー行動の定量・定性分析を実施します。CVR改善のためのA/Bテストを月次で実行し、データドリブンな意思決定を心がけています。特にコンバージョンファネルのボトルネックを特定し、優先度をつけて改善施策を実行しています。',
            feedback: {
              id: 'feedback_data_analysis',
              answerId: 'answer_data_analysis',
              aiAnalysis: 'データ分析の実践的な手法と活用方法が体系的に説明されており、高い専門性が伝わります。定量・定性両面のアプローチや、ファネル最適化の観点など、結果にコミットした姿勢が評価できます。',
              emotionScore: 8.0,
              confidenceScore: 8.4,
              relevanceScore: 9.1,
              clarityScore: 8.6,
              overall: 8.5,
              overallRating: 4,
              suggestions: ['具体的な改善事例や数値的インパクトを含めるとさらに有効'],
              createdAt: '2024-06-13T16:25:00Z'
            }
          }
        ]
      }
    ];

    return {
      feedbacks: detailedInterviewSummaries,
      pagination: {
        total: 2,
        page: 1,
        limit: 50
      }
    };
  }

  /**
   * アクティブなミーティングリンク取得（管理画面用）
   */
  static async getActiveMeetingLinks(): Promise<MeetingLink[]> {
    await new Promise(resolve => setTimeout(resolve, 600));
    
    return [
      {
        id: 'link_001',
        interviewId: 'interview_001',
        token: 'mtg_token_abc123',
        url: 'http://localhost:3000/interview-room?token=mtg_token_abc123',
        expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        candidateName: '山田一郎',
        candidateEmail: '<EMAIL>',
        companyInfo: {
          name: '株式会社テクノロジーソリューション',
          position: 'フロントエンドエンジニア',
          requirements: ['React経験3年以上', 'TypeScript実務経験', 'チーム開発経験']
        }
      },
      {
        id: 'link_002',
        interviewId: 'interview_002',
        token: 'mtg_token_xyz789',
        url: 'http://localhost:3000/interview-room?token=mtg_token_xyz789',
        expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        candidateName: '鈴木美咲',
        candidateEmail: '<EMAIL>',
        companyInfo: {
          name: '株式会社マーケティングラボ',
          position: 'デジタルマーケター',
          requirements: ['Google Analytics経験', 'SNS運用経験', 'データ分析スキル']
        }
      }
    ];
  }

  /**
   * レガシーサポート：既存のメソッド名との互換性維持
   */
  static async getBasicQuestions(): Promise<Question[]> {
    const response = await this.generateQuestionScript({
      companyName: 'サンプル企業',
      position: 'エンジニア',
      requirements: ['基本的なスキル'],
      interviewType: 'behavioral',
      estimatedDuration: 900
    });
    return response.questions;
  }

  static async getBasicFeedbacks(interviewId?: string): Promise<Feedback[]> {
    const response = await this.getFeedbackList();
    const allFeedbacks = response.feedbacks.flatMap(summary => 
      summary.answers.map(answer => answer.feedback)
    );
    
    if (interviewId) {
      return allFeedbacks.filter(f => f.answerId.includes(interviewId));
    }
    return allFeedbacks;
  }
}