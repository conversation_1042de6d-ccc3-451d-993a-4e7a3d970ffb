import React from 'react';
import { Box, VStack, Text, useColorModeValue, Badge } from '@chakra-ui/react';
import { CircularProgress, CircularProgressLabel } from '@chakra-ui/react';

// 共通の円形プログレスバーコンポーネント
export interface CircularScoreProps {
  score: number;
  size?: number;
  label?: string;
  color?: string;
  showPercentage?: boolean;
  maxScore?: number;
}

export const CircularScore: React.FC<CircularScoreProps> = ({
  score,
  size = 100,
  label,
  color,
  showPercentage = false,
  maxScore = 100
}) => {
  // スコアに基づいて色を決定
  const getScoreColor = (score: number): string => {
    if (color) return color;
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'green';
    if (percentage >= 60) return 'blue';
    if (percentage >= 40) return 'yellow';
    return 'red';
  };

  const scoreColor = getScoreColor(score);
  const percentage = Math.round((score / maxScore) * 100);

  return (
    <VStack spacing={2}>
      <CircularProgress 
        value={percentage} 
        color={`${scoreColor}.400`}
        size={`${size}px`}
        thickness='8px'
      >
        <CircularProgressLabel
          fontSize={size >= 80 ? 'xl' : 'md'}
          fontWeight="bold"
        >
          {showPercentage ? `${percentage}%` : (
            <VStack spacing={0}>
              <Text fontSize={size >= 80 ? 'xl' : 'md'} fontWeight="bold" color={`${scoreColor}.500`}>
                {score}
              </Text>
              {!showPercentage && (
                <Text fontSize="xs" color="gray.500">/ {maxScore}</Text>
              )}
            </VStack>
          )}
        </CircularProgressLabel>
      </CircularProgress>
      {label && (
        <Text 
          fontSize={{ base: "xs", md: "sm" }} 
          fontWeight="medium" 
          textAlign="center"
          color={useColorModeValue('gray.700', 'gray.300')}
        >
          {label}
        </Text>
      )}
    </VStack>
  );
};

// カスタムSVG円形プログレスバー（より柔軟なスタイリング用）
export interface CustomCircularScoreProps extends CircularScoreProps {
  strokeWidth?: number;
  animationDuration?: number;
}

export const CustomCircularScore: React.FC<CustomCircularScoreProps> = ({
  score,
  size = 100,
  label,
  color,
  maxScore = 100,
  strokeWidth = 6,
  animationDuration = 1
}) => {
  const actualSize = typeof size === 'number' ? size : 100;
  const radius = actualSize / 2 - strokeWidth;
  const circumference = 2 * Math.PI * radius;
  const percentage = (score / maxScore) * 100;
  const offset = circumference - (percentage / 100) * circumference;

  const getScoreColor = (score: number): string => {
    if (color) return color;
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'green';
    if (percentage >= 60) return 'blue';
    if (percentage >= 40) return 'yellow';
    return 'red';
  };

  const scoreColor = getScoreColor(score);
  const strokeColor = useColorModeValue(
    `var(--chakra-colors-${scoreColor}-500)`,
    `var(--chakra-colors-${scoreColor}-400)`
  );
  const trackColor = useColorModeValue(
    'var(--chakra-colors-gray-200)',
    'var(--chakra-colors-gray-700)'
  );
  const textColor = useColorModeValue(`${scoreColor}.600`, `${scoreColor}.400`);

  return (
    <VStack spacing={2}>
      <Box position="relative">
        <svg 
          width={actualSize} 
          height={actualSize}
          style={commonStyles.svgRotate}
        >
          <circle
            cx={actualSize/2}
            cy={actualSize/2}
            r={radius}
            stroke={trackColor}
            strokeWidth={strokeWidth}
            fill="none"
          />
          <circle
            cx={actualSize/2}
            cy={actualSize/2}
            r={radius}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            style={{
              transition: `stroke-dashoffset ${animationDuration}s ease-out`,
              strokeLinecap: 'round'
            }}
          />
        </svg>
        <Box
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          textAlign="center"
        >
          <Text 
            fontSize={actualSize >= 100 ? "2xl" : actualSize >= 80 ? "xl" : "lg"}
            fontWeight="bold" 
            color={textColor}
          >
            {score}
          </Text>
          <Text fontSize={actualSize >= 100 ? "sm" : "xs"} color="gray.500">/ {maxScore}</Text>
        </Box>
      </Box>
      {label && (
        <Text 
          fontSize={{ base: "xs", md: "sm" }} 
          fontWeight="medium" 
          textAlign="center"
          color={useColorModeValue('gray.700', 'gray.300')}
        >
          {label}
        </Text>
      )}
    </VStack>
  );
};

// 共通のプログレスバーのレイアウト
export const commonGridStyles = {
  scoreGrid: {
    columns: { base: 2, md: 3, lg: 6 },
    spacing: { base: 4, md: 6 },
    justifyItems: 'center'
  },
  starFrameworkGrid: {
    columns: { base: 2, md: 3, lg: 5 },
    spacing: { base: 4, md: 6 },
    justifyItems: 'center'
  }
};

// 共通のカラーテーマ
export const scoreColors = {
  communication: 'blue',
  logic: 'green',
  experience: 'purple',
  culture: 'orange',
  technical: 'teal',
  adaptability: 'pink',
  situation: 'blue',
  task: 'green',
  action: 'purple',
  result: 'orange',
  competency: 'red'
} as const;

// 共通のサイズ定義
export const scoreSizes = {
  small: 60,
  medium: 80,
  large: 100,
  xlarge: 120,
  star: 80
} as const;

// 共通のスタイル定義
export const commonStyles = {
  // スクロールバーのスタイル
  scrollbar: {
    '&::-webkit-scrollbar': {
      height: '4px',
      width: '4px',
    },
    '&::-webkit-scrollbar-track': {
      background: 'var(--chakra-colors-gray-100)',
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'var(--chakra-colors-gray-300)',
      borderRadius: '2px',
    },
    _dark: {
      '&::-webkit-scrollbar-track': {
        background: 'var(--chakra-colors-gray-700)',
      },
      '&::-webkit-scrollbar-thumb': {
        background: 'var(--chakra-colors-gray-500)',
      },
    }
  },
  // SVG回転スタイル
  svgRotate: {
    transform: 'rotate(-90deg)'
  },
  // トランジション
  transitions: {
    default: 'all 0.3s ease',
    slow: 'all 1s ease-out',
    fast: 'all 0.2s ease'
  },
  // ビデオスタイル
  video: {
    width: '100%',
    height: '100%',
    objectFit: 'cover' as const,
    borderRadius: '8px',
  }
} as const;

// 共通のテキストスタイル
export const textStyles = {
  heading: { 
    fontSize: { base: "lg", md: "xl" }, 
    fontWeight: "bold" 
  },
  subheading: { 
    fontSize: { base: "md", md: "lg" }, 
    fontWeight: "semibold" 
  },
  body: { 
    fontSize: { base: "sm", md: "md" } 
  },
  caption: { 
    fontSize: { base: "xs", md: "sm" }, 
    color: "gray.600" 
  },
  label: {
    fontSize: "sm",
    fontWeight: "medium"
  }
} as const;

// 共通のスペーシング定義
export const spacing = {
  cardPadding: { base: 4, md: 6 },
  buttonPadding: { px: { base: 6, md: 8 }, py: { base: 3, md: 4 } },
  badgePadding: { px: 3, py: 1 },
  iconBoxSize: { base: 4, md: 5 },
  sectionSpacing: { base: 4, md: 6, lg: 8 }
} as const;

// 共通のフォントサイズ
export const fontSizes = {
  xs: { base: "xs", md: "sm" },
  sm: { base: "sm", md: "md" },
  md: { base: "md", md: "lg" },
  lg: { base: "lg", md: "xl" },
  xl: { base: "xl", md: "2xl" },
  "2xl": { base: "2xl", md: "3xl" },
  "3xl": { base: "3xl", md: "4xl" }
} as const;

// スコアバッジコンポーネント
export interface ScoreBadgeProps {
  score: number;
  maxScore?: number;
  size?: 'sm' | 'md' | 'lg';
  showMax?: boolean;
}

export const ScoreBadge: React.FC<ScoreBadgeProps> = ({ 
  score, 
  maxScore = 100, 
  size = 'md',
  showMax = true 
}) => {
  const percentage = (score / maxScore) * 100;
  let colorScheme = 'red';
  if (percentage >= 80) colorScheme = 'green';
  else if (percentage >= 60) colorScheme = 'blue';
  else if (percentage >= 40) colorScheme = 'yellow';

  const sizeMap = {
    sm: 'xs',
    md: 'sm',
    lg: 'md'
  };

  return (
    <Badge 
      colorScheme={colorScheme}
      fontSize={sizeMap[size]}
      px={spacing.badgePadding.px}
      py={spacing.badgePadding.py}
      borderRadius="full"
      fontWeight="semibold"
    >
      {showMax ? `${score}/${maxScore}` : `${score}%`}
    </Badge>
  );
};