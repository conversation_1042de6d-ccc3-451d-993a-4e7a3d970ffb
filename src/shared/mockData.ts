/**
 * mensetsu-kun モックデータ
 * UI整理・承認フェーズ用の一元化されたモックデータ
 * 
 * このファイルは後で実際のDB・APIと置き換える際の
 * データ構造の参考として使用します。
 */

export interface Question {
  id: string;
  text: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  estimatedTime?: number; // 想定回答時間（秒）
}

export interface Interview {
  id: string;
  candidateName: string;
  position: string;
  companyName: string;
  status: "pending" | "in_progress" | "completed" | "cancelled";
  createdAt: string;
  scheduledAt?: string;
  questions: Question[];
}

export interface Feedback {
  id: string;
  interviewId: string;
  questionId: string;
  candidateAnswer: string;
  aiAnalysis: string;
  emotionScore?: number;
  confidenceScore?: number;
  relevanceScore?: number;
  overallRating: number; // 1-5
  suggestions: string[];
  timestamp: string;
}

export interface AudioData {
  id: string;
  filename: string;
  duration: number;
  transcription: string;
  confidence: number;
}

export interface MeetingLink {
  id: string;
  candidateName: string;
  interviewId: string;
  url: string;
  expiresAt: string;
  createdAt: string;
  status: "active" | "expired" | "used";
}

export interface CompanyInfo {
  id: string;
  name: string;
  industry: string;
  description: string;
  logoUrl?: string;
}

export interface JobPosition {
  id: string;
  title: string;
  department: string;
  level: "junior" | "mid" | "senior" | "executive";
  requirements: string[];
  skills: string[];
}

// ========== モックデータ定義 ==========

export const mockQuestions: Question[] = [
  {
    id: "q1",
    text: "自己紹介をお願いします。経歴と志望動機を含めて2分程度でお話しください。",
    category: "基本情報",
    difficulty: "easy",
    estimatedTime: 120
  },
  {
    id: "q2", 
    text: "これまでの経験で最も困難だった課題と、それをどのように解決したか教えてください。",
    category: "経験・スキル",
    difficulty: "medium",
    estimatedTime: 180
  },
  {
    id: "q3",
    text: "チームで働く際に重視することは何ですか？具体的なエピソードがあれば教えてください。",
    category: "コミュニケーション",
    difficulty: "medium", 
    estimatedTime: 150
  },
  {
    id: "q4",
    text: "5年後のキャリアビジョンを教えてください。この会社でどのような貢献をしたいですか？",
    category: "将来性",
    difficulty: "medium",
    estimatedTime: 180
  },
  {
    id: "q5",
    text: "技術的な課題に直面した時、どのようなアプローチで解決に取り組みますか？",
    category: "技術力",
    difficulty: "hard",
    estimatedTime: 200
  }
];

export const mockInterviews: Interview[] = [
  {
    id: "interview_001",
    candidateName: "田中太郎",
    position: "フロントエンドエンジニア",
    companyName: "株式会社テクノロジー",
    status: "completed",
    createdAt: "2025-06-09T10:00:00Z",
    scheduledAt: "2025-06-09T14:00:00Z",
    questions: mockQuestions.slice(0, 3)
  },
  {
    id: "interview_002", 
    candidateName: "佐藤花子",
    position: "バックエンドエンジニア",
    companyName: "株式会社イノベーション",
    status: "in_progress",
    createdAt: "2025-06-09T11:00:00Z",
    scheduledAt: "2025-06-09T15:00:00Z",
    questions: mockQuestions.slice(1, 4)
  },
  {
    id: "interview_003",
    candidateName: "山田次郎", 
    position: "プロダクトマネージャー",
    companyName: "株式会社デザイン",
    status: "pending",
    createdAt: "2025-06-09T12:00:00Z",
    scheduledAt: "2025-06-09T16:00:00Z", 
    questions: mockQuestions
  }
];

export const mockFeedbacks: Feedback[] = [
  {
    id: "feedback_001",
    interviewId: "interview_001",
    questionId: "q1",
    candidateAnswer: "私は3年間フロントエンド開発に従事し、ReactとTypeScriptを中心に業務を行ってきました。特にユーザビリティ向上に興味があり、御社のプロダクトの品質向上に貢献したいと考えています。",
    aiAnalysis: "自己紹介は簡潔で要点が整理されています。技術スタックと志望動機が明確に述べられており、好印象です。",
    emotionScore: 8.2,
    confidenceScore: 7.5,
    relevanceScore: 9.0,
    overallRating: 4,
    suggestions: [
      "より具体的な成果や数値を含めると説得力が増します",
      "企業研究の深さをアピールできるとさらに良いでしょう"
    ],
    timestamp: "2025-06-09T14:05:00Z"
  },
  {
    id: "feedback_002",
    interviewId: "interview_001", 
    questionId: "q2",
    candidateAnswer: "レガシーシステムのリニューアルプロジェクトで、要件定義が曖昧な状況がありました。ステークホルダーとの定期的な会議を設定し、プロトタイプを作成して認識を合わせることで解決しました。",
    aiAnalysis: "具体的な課題と解決策が述べられており、問題解決能力の高さが伺えます。コミュニケーション能力も評価できます。",
    emotionScore: 7.8,
    confidenceScore: 8.2,
    relevanceScore: 8.7,
    overallRating: 4,
    suggestions: [
      "結果や成果をより詳しく説明すると良いでしょう",
      "チームメンバーとの協力についても触れると完璧です"
    ],
    timestamp: "2025-06-09T14:08:00Z"
  },
  {
    id: "feedback_003",
    interviewId: "interview_002",
    questionId: "q3", 
    candidateAnswer: "チームワークでは透明性を重視します。進捗の共有とリスクの早期発見を心がけており、前職ではスクラム手法を導入してチーム効率を30%向上させました。",
    aiAnalysis: "チームワークに対する考え方が明確で、具体的な成果も示されています。数値での効果測定ができているのは優秀です。",
    emotionScore: 8.5,
    confidenceScore: 8.8,
    relevanceScore: 9.2,
    overallRating: 5,
    suggestions: [
      "他のチームメンバーからの評価も含めると信憑性が高まります"
    ],
    timestamp: "2025-06-09T15:10:00Z"
  }
];

export const mockMeetingLinks: MeetingLink[] = [
  {
    id: "mtg_001",
    candidateName: "田中太郎",
    interviewId: "interview_001", 
    url: "http://localhost:5000/candidate/mtg_001",
    expiresAt: "2025-06-10T14:00:00Z",
    createdAt: "2025-06-09T10:00:00Z",
    status: "used"
  },
  {
    id: "mtg_002",
    candidateName: "佐藤花子",
    interviewId: "interview_002",
    url: "http://localhost:5000/candidate/mtg_002", 
    expiresAt: "2025-06-10T15:00:00Z",
    createdAt: "2025-06-09T11:00:00Z",
    status: "active"
  },
  {
    id: "mtg_003",
    candidateName: "山田次郎",
    interviewId: "interview_003",
    url: "http://localhost:5000/candidate/mtg_003",
    expiresAt: "2025-06-10T16:00:00Z", 
    createdAt: "2025-06-09T12:00:00Z",
    status: "active"
  }
];

export const mockCompanies: CompanyInfo[] = [
  {
    id: "company_001",
    name: "株式会社テクノロジー",
    industry: "IT・ソフトウェア",
    description: "最新技術を活用したWebサービス開発を行っている企業です。",
    logoUrl: "/images/companies/technology-corp.png"
  },
  {
    id: "company_002", 
    name: "株式会社イノベーション",
    industry: "コンサルティング",
    description: "企業のデジタル変革を支援するコンサルティングファームです。"
  },
  {
    id: "company_003",
    name: "株式会社デザイン", 
    industry: "デザイン・クリエイティブ",
    description: "ユーザー体験を重視したプロダクトデザインを手がけています。"
  }
];

export const mockJobPositions: JobPosition[] = [
  {
    id: "position_001",
    title: "フロントエンドエンジニア",
    department: "開発部",
    level: "mid",
    requirements: [
      "React/TypeScriptでの開発経験3年以上",
      "レスポンシブデザインの実装経験",
      "Git/GitHubでのチーム開発経験"
    ],
    skills: ["React", "TypeScript", "HTML/CSS", "JavaScript", "Git"]
  },
  {
    id: "position_002",
    title: "バックエンドエンジニア", 
    department: "開発部",
    level: "mid",
    requirements: [
      "Node.js/Pythonでの開発経験3年以上",
      "データベース設計・運用経験",
      "AWS/GCPでのインフラ構築経験"
    ],
    skills: ["Node.js", "Python", "MySQL", "PostgreSQL", "AWS", "Docker"]
  },
  {
    id: "position_003",
    title: "プロダクトマネージャー",
    department: "企画部", 
    level: "senior",
    requirements: [
      "プロダクト管理経験5年以上",
      "アジャイル開発手法の理解",
      "ステークホルダーマネジメント経験"
    ],
    skills: ["Product Management", "Agile", "Analytics", "User Research"]
  }
];

// ========== データアクセス関数（後でAPI呼び出しに置き換え） ==========

/**
 * 質問データを取得する
 */
export const getQuestions = async (): Promise<Question[]> => {
  // 実際のAPI呼び出しをシミュレート
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockQuestions;
};

/**
 * 特定の質問を取得する
 */
export const getQuestionById = async (id: string): Promise<Question | null> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockQuestions.find(q => q.id === id) || null;
};

/**
 * 面接データを取得する
 */
export const getInterviews = async (): Promise<Interview[]> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  return mockInterviews;
};

/**
 * 特定の面接データを取得する
 */
export const getInterviewById = async (id: string): Promise<Interview | null> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockInterviews.find(i => i.id === id) || null;
};

/**
 * フィードバックデータを取得する
 */
export const getFeedbacks = async (interviewId?: string): Promise<Feedback[]> => {
  await new Promise(resolve => setTimeout(resolve, 600));
  if (interviewId) {
    return mockFeedbacks.filter(f => f.interviewId === interviewId);
  }
  return mockFeedbacks;
};

/**
 * ミーティングリンクを生成する
 */
export const generateMeetingLink = async (candidateName: string): Promise<MeetingLink> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const id = `mtg_${Date.now()}`;
  const link: MeetingLink = {
    id,
    candidateName,
    interviewId: `interview_${Date.now()}`,
    url: `http://localhost:5000/candidate/${id}`,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24時間後
    createdAt: new Date().toISOString(),
    status: "active"
  };
  
  return link;
};

/**
 * 音声解析結果をシミュレート
 */
export const analyzeAudio = async (audioFile: File): Promise<{ text: string; feedback: Feedback }> => {
  await new Promise(resolve => setTimeout(resolve, 2000)); // 解析時間をシミュレート
  
  // モック解析結果
  const transcription = "ありがとうございます。私は○○大学を卒業後、△△会社で3年間フロントエンド開発に携わってきました。特にReactとTypeScriptを使用したSPA開発に従事し、ユーザビリティの向上に取り組んでまいりました。";
  
  const feedback: Feedback = {
    id: `feedback_${Date.now()}`,
    interviewId: "current_interview",
    questionId: "current_question", 
    candidateAnswer: transcription,
    aiAnalysis: "回答は明確で構成されており、技術的な経験が適切に説明されています。具体的な技術スタックと業務内容が述べられており、信頼性が高い回答です。",
    emotionScore: 7.8,
    confidenceScore: 8.1,
    relevanceScore: 8.5,
    overallRating: 4,
    suggestions: [
      "より具体的なプロジェクトの成果や数値があると説得力が増します",
      "技術選択の理由や学習過程についても言及すると良いでしょう"
    ],
    timestamp: new Date().toISOString()
  };
  
  return { text: transcription, feedback };
};

/**
 * 質問生成をシミュレート
 */
export const generateQuestions = async (companyName: string, position: string, requirements?: string[]): Promise<Question[]> => {
  await new Promise(resolve => setTimeout(resolve, 1500)); // AI生成時間をシミュレート
  
  // 職種別のカスタム質問を生成
  const customQuestions: Question[] = [
    {
      id: `custom_q1_${Date.now()}`,
      text: `${companyName}の${position}として、どのようなスキルや経験を活かせると思いますか？`,
      category: "企業適性",
      difficulty: "medium",
      estimatedTime: 180
    },
    {
      id: `custom_q2_${Date.now()}`,
      text: `${position}の業務で最も重要だと考える要素は何ですか？その理由も教えてください。`,
      category: "職種理解",
      difficulty: "medium", 
      estimatedTime: 150
    },
    {
      id: `custom_q3_${Date.now()}`,
      text: "将来的にどのような技術領域に挑戦したいとお考えですか？",
      category: "成長意欲",
      difficulty: "medium",
      estimatedTime: 120
    }
  ];
  
  return [...mockQuestions.slice(0, 2), ...customQuestions];
};

/**
 * 音声合成をシミュレート（実際は音声ファイルを返す）
 */
export const synthesizeText = async (text: string): Promise<Blob> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 実際の実装では音声ファイルを返すが、ここではダミーのBlobを返す
  const dummyAudio = new Blob(["dummy audio data"], { type: "audio/wav" });
  return dummyAudio;
};

/**
 * モックデータのリセット（開発用）
 */
export const resetMockData = (): void => {
  console.log("モックデータをリセットしました");
  // 実装時は必要に応じてlocalStorageなどからクリア
};