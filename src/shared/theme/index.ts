/**
 * 面接君 デザインシステム 2.0
 * UIUX_DESIGN_GUIDLINES.mdに基づくカスタムテーマ定義
 */
import { extendTheme, type ThemeConfig } from "@chakra-ui/react";

const config: ThemeConfig = {
  initialColorMode: "light",
  useSystemColorMode: false,
};

export const mensetsukuTheme = extendTheme({
  config,
  colors: {
    primary: {
      50: "#E6F7FF",
      100: "#B3E0FF",
      200: "#80C9FF",
      300: "#4DB3FF",
      400: "#1A9CFF",
      500: "#2563EB", // メインブルー
      600: "#0052CC",
      700: "#0041A3",
      800: "#00317A",
      900: "#1E3F66",
    },
    support: {
      50: "#F0FDF4",
      100: "#DCFCE7",
      200: "#BBF7D0",
      300: "#86EFAC",
      400: "#4ADE80",
      500: "#16A34A", // サポートグリーン
      600: "#15803D",
      700: "#166534",
      800: "#14532D",
      900: "#14532D",
    },
    caution: {
      50: "#FEF9E7",
      100: "#FEF3C7",
      200: "#FDE68A",
      300: "#FCD34D",
      400: "#FBBF24",
      500: "#F39C12", // 注意イエロー
      600: "#D97706",
      700: "#B45309",
      800: "#92400E",
      900: "#78350F",
    },
    neutral: {
      50: "#F9FAFB",
      100: "#F3F4F6",
      200: "#E5E7EB",
      300: "#D1D5DB",
      400: "#9CA3AF",
      500: "#6B7280",
      600: "#4B5563",
      700: "#374151",
      800: "#1F2937",
      900: "#111827",
    },
  },
  fonts: {
    heading: '"Noto Sans JP", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    body: '"Noto Sans JP", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
  },
  fontSizes: {
    xs: "0.75rem",
    sm: "0.875rem",
    md: "1rem",
    lg: "1.25rem",
    xl: "1.5rem",
    "2xl": "2rem",
    "3xl": "2.5rem",
    "4xl": "3rem",
    "5xl": "3.5rem",
  },
  fontWeights: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeights: {
    normal: "normal",
    none: 1,
    shorter: 1.25,
    short: 1.375,
    base: 1.5,
    tall: 1.625,
    taller: "2",
  },
  letterSpacings: {
    tighter: "-0.05em",
    tight: "-0.025em",
    normal: "0",
    wide: "0.025em",
    wider: "0.05em",
    widest: "0.1em",
  },
  space: {
    px: "1px",
    0.5: "0.125rem",
    1: "0.25rem",
    1.5: "0.375rem",
    2: "0.5rem",
    2.5: "0.625rem",
    3: "0.75rem",
    3.5: "0.875rem",
    4: "1rem",
    5: "1.25rem",
    6: "1.5rem",
    7: "1.75rem",
    8: "2rem",
    9: "2.25rem",
    10: "2.5rem",
    12: "3rem",
    14: "3.5rem",
    16: "4rem",
    20: "5rem",
    24: "6rem",
  },
  sizes: {
    max: "max-content",
    min: "min-content",
    full: "100%",
    xs: "20rem",
    sm: "24rem",
    md: "28rem",
    lg: "32rem",
    xl: "36rem",
    "2xl": "42rem",
    "3xl": "48rem",
    "4xl": "56rem",
    "5xl": "64rem",
    "6xl": "72rem",
    container: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
    },
  },
  radii: {
    none: "0",
    sm: "4px",
    base: "8px",
    md: "8px",
    lg: "16px",
    xl: "24px",
    "2xl": "32px",
    full: "9999px",
  },
  shadows: {
    xs: "0 0 0 1px rgba(0, 0, 0, 0.05)",
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    base: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    xl: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",
    outline: "0 0 0 3px rgba(37, 99, 235, 0.5)",
  },
  transitions: {
    quick: "all 150ms ease-in-out",
    moderate: "all 300ms ease-in-out",
    slow: "all 500ms ease-in-out",
  },
  components: {
    Button: {
      baseStyle: {
        fontWeight: "semibold",
        borderRadius: "lg",
        transition: "all 300ms ease-in-out",
      },
      sizes: {
        sm: {
          fontSize: "sm",
          px: 4,
          py: 2,
        },
        md: {
          fontSize: "md",
          px: 6,
          py: 3,
        },
        lg: {
          fontSize: "lg",
          px: 8,
          py: 4,
        },
      },
      variants: {
        solid: {
          bg: "primary.500",
          color: "white",
          _hover: {
            bg: "primary.600",
            transform: "translateY(-2px)",
            boxShadow: "lg",
          },
          _active: {
            bg: "primary.700",
            transform: "translateY(0)",
          },
        },
        ghost: {
          color: "primary.500",
          _hover: {
            bg: "primary.50",
          },
        },
        outline: {
          borderColor: "primary.500",
          color: "primary.500",
          _hover: {
            bg: "primary.50",
          },
        },
      },
    },
    Heading: {
      baseStyle: {
        fontWeight: "bold",
        lineHeight: "shorter",
      },
    },
    Text: {
      baseStyle: {
        lineHeight: "tall",
      },
    },
    Box: {
      baseStyle: {
        transition: "all 300ms ease-in-out",
      },
    },
  },
  styles: {
    global: {
      body: {
        bg: "neutral.50",
        color: "neutral.900",
        fontSize: "md",
        lineHeight: "tall",
      },
      "*::selection": {
        bg: "primary.100",
        color: "primary.900",
      },
    },
  },
});

// モーショントークン
export const motionTokens = {
  duration: {
    quick: 150,
    moderate: 300,
    slow: 500,
    verySlow: 1000,
  },
  easing: {
    natural: "ease-in-out",
    emphasized: [0.4, 0, 0.2, 1],
    decelerate: [0, 0, 0.2, 1],
    accelerate: [0.4, 0, 1, 1],
  },
};