/**
 * 言語表現マネージャー
 * プロダクト憲法第一条「心理的安全性の絶対的保障」に基づく
 * ネガティブ表現の排除と成長志向の言語への置き換え
 */

// 禁止ワード - 使用を避けるべき表現
export const BANNED_WORDS = [
  'ダメ', '間違い', 'できていない', '不合格', '失敗',
  '悪い', '不十分', '劣っている', '下手', '問題',
  'エラー', 'ミス', '不正解', '間違っている', '駄目'
] as const;

// ポジティブ変換辞書 - ネガティブ表現を成長志向の表現に変換
export const POSITIVE_ALTERNATIVES = {
  '改善点': '成長の機会',
  'エラー': 'もう一度トライするチャンス',
  '不十分': 'さらに良くできるポイント',
  'スコアが低い': '伸びしろがある',
  '間違い': '学習のきっかけ',
  '失敗': '経験値',
  '悪い': '改善できる',
  '問題': 'チャレンジ',
  '不合格': '次回への準備',
  'ミス': '気づき',
  '下手': '練習の余地がある',
  'できていない': '次のステップへ',
  'ダメ': '別のアプローチを'
} as const;

// 励ましのフレーズ - ランダムに選択して使用
export const ENCOURAGEMENT_PHRASES = [
  '素晴らしい挑戦でした！',
  '確実に成長しています',
  '次はもっと上手くいきますよ',
  'あなたの強みが光っていました',
  'とても良い気づきですね',
  'その調子で頑張りましょう',
  '素敵な表現力をお持ちですね',
  '自信を持って進んでください',
  'あなたなら必ずできます',
  '着実に上達していますよ',
  '新しい発見がありましたね',
  'ポジティブな姿勢が素晴らしいです'
] as const;

// 成長サポートメッセージ - 段階的な成長を促す表現
export const GROWTH_MESSAGES = {
  beginner: [
    'まずは基本から一歩ずつ進んでいきましょう',
    '最初は誰でも緊張するものです。大丈夫ですよ',
    '練習を重ねれば必ず上達します'
  ],
  intermediate: [
    'しっかりとした基礎ができていますね',
    'さらなるスキルアップを目指しましょう',
    '応用力も身についてきています'
  ],
  advanced: [
    '高いレベルの実力をお持ちですね',
    'リーダーシップも発揮できそうです',
    '他の方の良いお手本になりますね'
  ]
} as const;

// フィードバックのトーン調整
export const FEEDBACK_TONES = {
  constructive: {
    prefix: 'さらに良くするためのヒント：',
    suffix: 'きっと次回はもっと素晴らしくなりますよ！'
  },
  supportive: {
    prefix: 'あなたの頑張りが伝わってきました。',
    suffix: 'この調子で続けていけば大きな成長が期待できます。'
  },
  motivational: {
    prefix: '素晴らしいパフォーマンスでした！',
    suffix: 'あなたの可能性は無限大です。'
  }
} as const;

/**
 * テキストからネガティブ表現を検出し、ポジティブな表現に変換
 */
export const convertToPositiveLanguage = (text: string): string => {
  let convertedText = text;
  
  // ポジティブ変換辞書を使用して置換
  Object.entries(POSITIVE_ALTERNATIVES).forEach(([negative, positive]) => {
    const regex = new RegExp(negative, 'g');
    convertedText = convertedText.replace(regex, positive);
  });
  
  return convertedText;
};

/**
 * 禁止ワードが含まれているかチェック
 */
export const containsBannedWords = (text: string): boolean => {
  return BANNED_WORDS.some(word => text.includes(word));
};

/**
 * ランダムな励ましフレーズを取得
 */
export const getRandomEncouragement = (): string => {
  const randomIndex = Math.floor(Math.random() * ENCOURAGEMENT_PHRASES.length);
  return ENCOURAGEMENT_PHRASES[randomIndex];
};

/**
 * ユーザーレベルに応じた成長メッセージを取得
 */
export const getGrowthMessage = (level: keyof typeof GROWTH_MESSAGES): string => {
  const messages = GROWTH_MESSAGES[level];
  const randomIndex = Math.floor(Math.random() * messages.length);
  return messages[randomIndex];
};

/**
 * フィードバックを心理的安全性を保ったトーンで整形
 */
export const formatSafeFeedback = (
  content: string,
  tone: keyof typeof FEEDBACK_TONES = 'constructive'
): string => {
  const { prefix, suffix } = FEEDBACK_TONES[tone];
  const safeContent = convertToPositiveLanguage(content);
  
  return `${prefix}\n\n${safeContent}\n\n${suffix}`;
};