/**
 * 認証システム関連の型定義
 */

export interface User {
  id: string;
  email: string;
  role: 'agent' | 'interviewer' | 'candidate';
  // profile: {
  //   name: string;
  //   organization?: string;
  // };
  profile_name: string;
  createdAt?: string;
  lastLoginAt?: string;
  isActive?: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success?: boolean;
  accessToken: string;
  tokenType: 'Bearer';
  expiresIn: number; // 秒
  refreshToken: string;
  user: User;
}

export interface RefreshTokenResponse {
  success: boolean;
  accessToken: string;
  tokenType: 'Bearer';
  expiresIn: number;
  user: User;
}

export interface AuthError {
  error: string;
  message: string;
}

export interface InterviewToken {
  id: string;
  interviewId: string;
  candidateEmail: string;
  usageCount: number;
  maxUsage: number;
  creator: {
    name: string;
    organization?: string;
  };
  metadata?: any;
}

export interface InterviewTokenRequest {
  interviewId: string;
  candidateEmail: string;
  expiryHours?: number;
  maxUsage?: number;
}

export interface InterviewTokenResponse {
  success: boolean;
  token: string;
  tokenId: string;
  expiresAt: string;
  interviewLink: string;
}

export interface InterviewTokenVerification {
  success: boolean;
  interview?: InterviewToken;
  error?: string;
  message?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface ProfileUpdateRequest {
  profileName: string;
  profileOrganization?: string;
}

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
}

// セッション管理用
export interface Session {
  id: string;
  userId: string;
  createdAt: string;
  expiresAt: string;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}

// 認証ログ用
export interface AuthLog {
  id: number;
  userId?: string;
  action: 'login' | 'logout' | 'token_refresh' | 'failed_login';
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  createdAt: string;
}

// API応答の基本型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// トークンペイロード（JWT用）
export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  profileName: string;
  iat: number;
  exp: number;
  jti: string;
}

// 認証設定
export interface AuthConfig {
  apiBaseUrl: string;
  tokenStorageKey: string;
  refreshTokenEndpoint: string;
  loginEndpoint: string;
  logoutEndpoint: string;
  tokenRefreshInterval: number; // ミリ秒
  maxRetryAttempts: number;
}