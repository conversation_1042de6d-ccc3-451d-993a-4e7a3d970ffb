/**
 * 共通型定義 - 面接システム
 * 候補者アプリと管理アプリで共有する型定義
 */

// ===============================
// 基本型定義
// ===============================

export type UserRole = 'agent' | 'candidate' | 'interviewer';

export type InterviewStatus = 'pending' | 'in_progress' | 'completed' | 'expired';

export type InterviewType = 'behavioral' | 'technical' | 'case' | 'mixed';

export type QuestionDifficulty = 'easy' | 'medium' | 'hard';

export type MeetingLinkStatus = 'active' | 'used' | 'expired';

// ===============================
// ユーザー関連
// ===============================

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

// ===============================
// 面接関連
// ===============================

export interface Interview {
  id: string;
  candidateId: string;
  agentId: string;
  companyName: string;
  position: string;
  requirements: string[];
  status: InterviewStatus;
  interviewType: InterviewType;
  estimatedDuration: number; // 秒
  startedAt?: string;
  completedAt?: string;
  createdAt: string;
  
  // リレーション
  questions?: Question[];
  answers?: Answer[];
  feedbacks?: Feedback[];
  meetingLink?: MeetingLink;
}

// ===============================
// 質問関連
// ===============================

export interface Question {
  id: string;
  interviewId: string;
  text: string;
  category?: string;
  difficulty: QuestionDifficulty;
  estimatedTime: number; // 秒
  orderIndex: number;
  createdAt: string;
}

export interface QuestionGenerationRequest {
  companyName: string;
  position: string;
  requirements: string[];
  interviewType: InterviewType;
  estimatedDuration: number;
}

export interface QuestionGenerationResponse {
  interviewId: string;
  questions: Question[];
}

// ===============================
// 回答関連
// ===============================

export interface Answer {
  id: string;
  questionId: string;
  candidateAnswer: string;
  audioFileUrl?: string;
  transcription?: string;
  duration?: number; // 秒
  answeredAt: string;
  
  // リレーション
  feedback?: Feedback;
}

export interface AnswerSubmissionRequest {
  questionId: string;
  textAnswer: string;
  audioFile?: File;
  duration?: number;
}

export interface AnswerSubmissionResponse {
  answerId: string;
  transcription?: string;
  feedback: Feedback;
}

// ===============================
// フィードバック関連
// ===============================

export interface ScoreSet {
  emotion: number;      // 0.00-10.00
  confidence: number;   // 0.00-10.00
  relevance: number;    // 0.00-10.00
  clarity: number;      // 0.00-10.00
}

export interface Feedback {
  id: string;
  answerId: string;
  aiAnalysis: string;
  emotionScore?: number;
  confidenceScore?: number;
  relevanceScore?: number;
  clarityScore?: number;
  overall?: number; // 総合スコア（0-10）
  overallRating: number; // 1-5
  suggestions: string[];
  createdAt: string;
  
  // 計算プロパティ
  scores?: ScoreSet;
}

// ===============================
// ミーティングリンク関連
// ===============================

export interface MeetingLink {
  id: string;
  interviewId: string;
  token: string;
  url: string;
  expiresAt: string;
  status: MeetingLinkStatus;
  accessedAt?: string;
  createdAt: string;
  
  // 追加情報
  candidateName?: string;
  candidateEmail?: string;
  companyInfo?: {
    name: string;
    position: string;
    requirements: string[];
  };
}

export interface MeetingLinkGenerationRequest {
  interviewId: string;
  candidateName: string;
  candidateEmail: string;
  expiresInDays?: number;
}

export interface MeetingLinkGenerationResponse {
  linkId: string;
  url: string;
  expiresAt: string;
  status: MeetingLinkStatus;
}

// ===============================
// 音声解析関連
// ===============================

export interface AudioAnalysisRequest {
  audioFile: File;
  language?: string;
}

export interface EmotionScores {
  happy: number;
  neutral: number;
  surprised: number;
  sad?: number;
  angry?: number;
  fearful?: number;
}

export interface SpeechQuality {
  clarity: number;
  pace: number;
  volume: number;
  fluency?: number;
}

export interface AudioAnalysisResponse {
  transcription: string;
  confidence: number;
  emotions: EmotionScores;
  speechQuality: SpeechQuality;
}

// ===============================
// API レスポンス型
// ===============================

export interface InterviewSessionResponse {
  interviewId: string;
  companyName: string;
  position: string;
  estimatedDuration: number;
  questions: Question[];
  status: InterviewStatus;
}

export interface InterviewCompletionResponse {
  interviewId: string;
  completedAt: string;
  totalDuration: number;
  overallScore: number;
  message: string;
}

export interface FeedbackListResponse {
  feedbacks: InterviewSummary[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
}

export interface InterviewSummary {
  interviewId: string;
  candidateName: string;
  position: string;
  companyName: string;
  completedAt: string;
  overallScore: number;
  questionCount: number;
  duration: number;
  answers: AnswerWithFeedback[];
}

export interface AnswerWithFeedback {
  questionText: string;
  candidateAnswer: string;
  feedback: Feedback;
}

// ===============================
// エラー型
// ===============================

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ApiErrorResponse {
  error: ApiError;
}

// ===============================
// ページネーション
// ===============================

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResponse {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// ===============================
// フィルター・検索
// ===============================

export interface FeedbackFilters {
  agentId?: string;
  status?: InterviewStatus;
  dateFrom?: string;
  dateTo?: string;
  position?: string;
  companyName?: string;
}

export interface InterviewFilters {
  candidateId?: string;
  agentId?: string;
  status?: InterviewStatus[];
  interviewType?: InterviewType[];
  dateFrom?: string;
  dateTo?: string;
}

// ===============================
// 設定・構成
// ===============================

export interface InterviewConfig {
  maxDuration: number; // 秒
  maxQuestions: number;
  allowRetry: boolean;
  autoSave: boolean;
  audioRequired: boolean;
  reminderEmails: boolean;
}

export interface AgentConfig {
  id: string;
  name: string;
  email: string;
  defaultInterviewConfig: InterviewConfig;
  notificationSettings: {
    emailOnCompletion: boolean;
    emailOnExpiration: boolean;
    dailySummary: boolean;
  };
}

// ===============================
// 統計・レポート
// ===============================

export interface InterviewStats {
  totalInterviews: number;
  completedInterviews: number;
  averageScore: number;
  averageDuration: number;
  topCategories: Array<{
    category: string;
    count: number;
    averageScore: number;
  }>;
}

export interface CandidatePerformance {
  candidateId: string;
  candidateName: string;
  interviewCount: number;
  averageScore: number;
  bestCategory: string;
  improvementAreas: string[];
  progressTrend: Array<{
    date: string;
    score: number;
  }>;
}