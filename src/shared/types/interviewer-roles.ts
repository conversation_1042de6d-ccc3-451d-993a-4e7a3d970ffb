/**
 * 面接官役割・ステップ設定の型定義
 * 役割別の質問スタイルと評価基準の管理
 */

// ===== 面接官役割 =====

/**
 * 面接官の役割タイプ
 */
export type InterviewerRoleType = 
  | 'hr'              // 人事
  | 'ceo'             // 社長・CEO
  | 'team_leader'     // チームリーダー・部門長
  | 'technical_lead'  // 技術責任者・技術リーダー
  | 'peer'            // 同僚・メンバー
  | 'senior_manager'  // 上級管理職
  | 'external';       // 外部面接官（コンサル等）

/**
 * 面接官のパーソナリティ特性
 */
export interface InterviewerPersonality {
  // コミュニケーションスタイル
  formality: 'casual' | 'formal' | 'balanced';
  supportiveness: 'encouraging' | 'neutral' | 'challenging';
  pace: 'slow' | 'normal' | 'fast';
  detailOrientation: 'high' | 'medium' | 'low';
  
  // 面接での振る舞い
  questioningStyle: 'directive' | 'conversational' | 'socratic';
  feedbackImmediacy: 'immediate' | 'delayed' | 'end_only';
  pressureLevel: 'low' | 'medium' | 'high';
  
  // 言語・表現特性
  languageComplexity: 'simple' | 'standard' | 'complex';
  useOfHumor: boolean;
  empathyLevel: 'low' | 'medium' | 'high';
}

/**
 * 質問スタイルの設定
 */
export interface QuestionStyle {
  // 質問カテゴリの重視度 (0-1の重み)
  behavioralFocus: number;      // 行動面接（STAR法等）
  technicalFocus: number;       // 技術・専門知識
  visionaryFocus: number;       // ビジョン・価値観
  culturalFitFocus: number;     // 企業文化適合性
  problemSolvingFocus: number;  // 問題解決能力
  leadershipFocus: number;      // リーダーシップ
  teamworkFocus: number;        // チームワーク・協調性
  
  // 質問の特性
  preferredCategories: string[]; // 優先する質問カテゴリ
  difficultyProgression: 'linear' | 'adaptive' | 'challenging' | 'gentle';
  followUpIntensity: 'low' | 'medium' | 'high';
  hypotheticalQuestionRatio: number; // 仮定質問の比率 (0-1)
  
  // 深掘りの傾向
  deepDiveTopics: string[];     // 深掘りしたいトピック
  timeAllocation: {
    opening: number;            // オープニング時間（分）
    coreQuestions: number;      // メイン質問時間（分）
    closing: number;            // クロージング時間（分）
  };
}

/**
 * 面接官役割の完全定義
 */
export interface InterviewerRole {
  id: string;
  type: InterviewerRoleType;
  title: string;                // 表示名
  description: string;          // 役割の説明
  
  // 役割の特性
  personality: InterviewerPersonality;
  questionStyle: QuestionStyle;
  
  // 評価項目・基準
  evaluationCriteria: {
    primaryCriteria: string[];   // 主要評価項目
    secondaryCriteria: string[]; // 副次評価項目
    weightings: Record<string, number>; // 各項目の重み
  };
  
  // アバター・UI設定
  avatarConfig: {
    appearance: 'professional' | 'casual' | 'executive';
    voiceTone: 'warm' | 'neutral' | 'authoritative';
    gestureStyle: 'minimal' | 'moderate' | 'expressive';
  };
  
  // 使用統計
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// ===== 面接ステップ・フロー =====

/**
 * 面接ステップの種類
 */
export type InterviewStepType = 
  | 'screening'       // 書類選考後の初回面接
  | 'first_round'     // 1次面接
  | 'second_round'    // 2次面接
  | 'technical'       // 技術面接
  | 'cultural_fit'    // カルチャーフィット面接
  | 'final'           // 最終面接
  | 'executive'       // 役員面接
  | 'informal';       // カジュアル面談

/**
 * 面接ステップの詳細設定
 */
export interface InterviewStep {
  id: string;
  type: InterviewStepType;
  name: string;
  description: string;
  order: number;                // ステップの順序
  
  // 時間設定
  duration: number;             // 面接時間（分）
  preparationTime?: number;     // 準備時間（分）
  
  // 担当面接官
  interviewerRole: InterviewerRole;
  coInterviewers?: InterviewerRole[]; // 複数面接官の場合
  
  // 質問・評価設定
  questionCategories: string[];
  evaluationCriteria: string[];
  passingScore: number;         // 合格基準スコア
  
  // フロー制御
  isRequired: boolean;          // 必須ステップかどうか
  prerequisites?: string[];     // 前提条件（前のステップID）
  skipConditions?: {
    rule: string;
    condition: any;
  }[];
  
  // ステップ固有設定
  stepSpecificSettings: {
    allowRetake: boolean;       // 再受験可能か
    feedbackTiming: 'immediate' | 'step_end' | 'process_end';
    recordingEnabled: boolean;  // 録画・録音するか
  };
}

/**
 * 完全な面接プロセス設定
 */
export interface InterviewProcess {
  id: string;
  name: string;
  description: string;
  companyId: string;
  positionId?: string;
  
  // ステップ構成
  steps: InterviewStep[];
  totalEstimatedDuration: number; // 全体所要時間（分）
  
  // プロセス設定
  processSettings: {
    allowStepSkipping: boolean;
    requireAllStepsCompletion: boolean;
    maxRetakeAttempts: number;
    cooldownPeriod?: number;    // 再挑戦までの待機期間（時間）
  };
  
  // 通知・フィードバック
  notificationSettings: {
    sendStepCompletionNotification: boolean;
    sendProcessCompletionNotification: boolean;
    sendFailureNotification: boolean;
  };
  
  // メタデータ
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  version: number;              // バージョン管理
  isActive: boolean;
  
  // 使用統計
  usageStats: {
    totalCandidates: number;
    completionRate: number;
    averageScore: number;
    stepDropoffRates: Record<string, number>;
  };
}

// ===== 役割別テンプレート =====

/**
 * 事前定義された面接官役割テンプレート
 */
export interface RoleTemplate {
  role: InterviewerRoleType;
  template: Omit<InterviewerRole, 'id' | 'usageCount' | 'createdAt' | 'updatedAt'>;
}

/**
 * 業界・職種別のカスタマイズ設定
 */
export interface IndustryRoleCustomization {
  industry: string;             // 業界名
  jobFunction: string;          // 職種・職能
  roleCustomizations: {
    roleType: InterviewerRoleType;
    customizations: {
      questionStyle: Partial<QuestionStyle>;
      evaluationCriteria: Partial<InterviewerRole['evaluationCriteria']>;
      personalityAdjustments: Partial<InterviewerPersonality>;
    };
  }[];
}

// ===== API・サービス関連 =====

/**
 * 面接官役割作成リクエスト
 */
export interface CreateInterviewerRoleRequest {
  type: InterviewerRoleType;
  title: string;
  description: string;
  personality: Partial<InterviewerPersonality>;
  questionStyle: Partial<QuestionStyle>;
  evaluationCriteria: {
    primaryCriteria: string[];
    secondaryCriteria?: string[];
    weightings?: Record<string, number>;
  };
  avatarConfig?: Partial<InterviewerRole['avatarConfig']>;
}

/**
 * 面接プロセス作成リクエスト
 */
export interface CreateInterviewProcessRequest {
  name: string;
  description: string;
  companyId: string;
  positionId?: string;
  steps: Array<{
    type: InterviewStepType;
    name: string;
    description: string;
    duration: number;
    interviewerRoleId: string;
    questionCategories: string[];
    isRequired: boolean;
  }>;
  processSettings?: Partial<InterviewProcess['processSettings']>;
}

/**
 * 役割選択のサジェスト情報
 */
export interface RoleSuggestion {
  role: InterviewerRole;
  matchScore: number;           // マッチ度 (0-1)
  reasons: string[];            // 推薦理由
  usageStats: {
    totalUsage: number;
    successRate: number;
    avgCandidateRating: number;
  };
}

/**
 * 面接プロセス実行状況
 */
export interface InterviewProcessExecution {
  id: string;
  processId: string;
  candidateProfileId: string;
  currentStepId: string;
  currentStepIndex: number;
  
  // 進捗状況
  status: 'not_started' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  startedAt?: string;
  completedAt?: string;
  
  // ステップ実行結果
  stepResults: {
    stepId: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
    score?: number;
    feedback?: string;
    startedAt?: string;
    completedAt?: string;
    duration?: number;
    interviewerFeedback?: string;
  }[];
  
  // 全体結果
  overallScore?: number;
  overallFeedback?: string;
  recommendation: 'hire' | 'maybe' | 'no_hire' | 'pending';
  
  // メタデータ
  createdAt: string;
  updatedAt: string;
}

export default InterviewerRole;