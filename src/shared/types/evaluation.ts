/**
 * 評価システムの型定義
 * 面接における詳細な評価基準とスコアリング
 */

export interface EvaluationCriterion {
  id: string;
  name: string;
  description: string;
  weight: number;              // 重み（0-100%）
  maxScore: number;            // 最大スコア
  scoreType: 'numeric' | 'rating' | 'boolean' | 'text';
  
  // スコアリング設定
  scoreOptions?: {
    min: number;
    max: number;
    step: number;
  };
  
  // 評価基準の詳細
  rubric: {
    excellent: {
      score: number;
      description: string;
      examples?: string[];
    };
    good: {
      score: number;
      description: string;
      examples?: string[];
    };
    satisfactory: {
      score: number;
      description: string;
      examples?: string[];
    };
    needsImprovement: {
      score: number;
      description: string;
      examples?: string[];
    };
  };
  
  // フィードバック設定
  feedbackTemplates: {
    positive: string[];
    constructive: string[];
    specific: string[];
  };
  
  // 質問との関連
  relatedQuestionCategories: string[];
  isRequired: boolean;
  category: 'technical' | 'communication' | 'cultural_fit' | 'leadership' | 'custom';
}

export interface EvaluationMatrix {
  id: string;
  stepId: string;
  criteria: EvaluationCriterion[];
  
  // マトリックス設定
  totalWeight: number;         // 全体の重み（100%チェック用）
  passingThreshold: number;    // 合格閾値（%）
  
  // 集計設定
  aggregationMethod: 'weighted_average' | 'simple_average' | 'custom';
  customAggregationFormula?: string;
  
  // スコア正規化
  normalization: {
    enabled: boolean;
    method: 'z_score' | 'min_max' | 'percentile';
  };
}

export interface RecordingSettings {
  id: string;
  stepId: string;
  
  // 基本録画設定
  videoEnabled: boolean;
  audioEnabled: boolean;
  screenRecordingEnabled: boolean;
  
  // 品質設定
  videoQuality: '720p' | '1080p' | '480p';
  audioQuality: 'high' | 'medium' | 'low';
  frameRate: 30 | 60 | 24;
  
  // 保存設定
  retentionPeriod: number;     // 保存期間（日）
  autoDeleteAfterPeriod: boolean;
  compressionEnabled: boolean;
  
  // プライバシー設定
  privacySettings: {
    requireConsent: boolean;
    consentText: string;
    allowCandidateDownload: boolean;
    allowCandidateDelete: boolean;
    maskSensitiveInfo: boolean;
  };
  
  // アクセス制御
  accessControl: {
    viewableByRoles: string[];
    downloadableByRoles: string[];
    editableByRoles: string[];
    shareable: boolean;
  };
  
  // 通知設定
  notifications: {
    notifyOnRecordingStart: boolean;
    notifyOnRecordingEnd: boolean;
    notifyOnAccessAttempt: boolean;
  };
}

export interface StepSpecificConfiguration {
  id: string;
  stepId: string;
  stepType: string;
  
  // リトライ設定
  retrySettings: {
    allowRetry: boolean;
    maxRetryAttempts: number;
    retryInterval: number;       // 分
    retryConditions: ('score_below_threshold' | 'technical_failure' | 'candidate_request')[];
    resetOnRetry: boolean;       // 再試行時に前回スコアをリセット
  };
  
  // 依存関係設定
  dependencies: {
    prerequisiteSteps: string[];
    conditionalLogic: {
      rule: string;              // JavaScript式
      description: string;
    }[];
    skipConditions: {
      condition: string;
      reason: string;
    }[];
  };
  
  // 通知設定
  notificationSettings: {
    stepStartNotification: {
      enabled: boolean;
      template: string;
      recipients: ('candidate' | 'interviewer' | 'hr' | 'manager')[];
    };
    stepCompletionNotification: {
      enabled: boolean;
      template: string;
      recipients: ('candidate' | 'interviewer' | 'hr' | 'manager')[];
    };
    reminderNotifications: {
      enabled: boolean;
      intervals: number[];       // 分
      template: string;
    };
  };
  
  // タイムアウト設定
  timeoutSettings: {
    sessionTimeout: number;      // 分
    warningTime: number;         // 分
    autoSubmitOnTimeout: boolean;
    allowExtension: boolean;
    maxExtensionTime: number;    // 分
  };
  
  // カスタム設定
  customSettings: {
    [key: string]: any;
  };
}

export interface QuestionCategoryConfiguration {
  id: string;
  stepId: string;
  name: string;
  description: string;
  
  // 質問設定
  questionSettings: {
    minQuestions: number;
    maxQuestions: number;
    difficultyDistribution: {
      easy: number;              // %
      medium: number;            // %
      hard: number;              // %
    };
    timeAllocation: number;      // 分
  };
  
  // カテゴリ固有設定
  categoryType: 'behavioral' | 'technical' | 'situational' | 'case_study' | 'custom';
  tags: string[];
  
  // AI生成設定
  aiGenerationSettings: {
    enabled: boolean;
    style: 'formal' | 'conversational' | 'challenging';
    adaptToPreviousAnswers: boolean;
    includeFollowUpQuestions: boolean;
  };
  
  // 評価との関連
  linkedEvaluationCriteria: string[];
  weight: number;              // カテゴリの重み
}

// 集約型：完全なステップ設定
export interface CompleteStepConfiguration {
  stepId: string;
  evaluationMatrix: EvaluationMatrix;
  recordingSettings: RecordingSettings;
  specificConfiguration: StepSpecificConfiguration;
  questionCategories: QuestionCategoryConfiguration[];
}

// API リクエスト型
export interface UpdateEvaluationCriteriaRequest {
  stepId: string;
  criteria: EvaluationCriterion[];
}

export interface UpdateRecordingSettingsRequest {
  stepId: string;
  settings: RecordingSettings;
}

export interface UpdateStepConfigurationRequest {
  stepId: string;
  configuration: StepSpecificConfiguration;
}