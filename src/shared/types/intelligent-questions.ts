/**
 * インテリジェント質問生成システムの型定義
 * プロダクト憲法第一条（心理的安全性の絶対的保障）に基づく
 */

// ===== コア型定義 =====

/**
 * 質問生成リクエスト
 */
export interface QuestionGenerationRequest {
  // 企業情報
  companyInfo: {
    name: string;
    industry: string;
    position: string;
    requirements: string[];
    culture?: string;
    interviewStyle?: string;
  };
  
  // 面接官役割情報（オプション）
  interviewerRole?: {
    roleType: string; // InterviewerRoleTypeと互換性を保つ
    personality?: any; // InterviewerPersonalityと互換性を保つ
    questionStyle?: any; // QuestionStyleと互換性を保つ
  };
  
  // 候補者情報
  candidateProfile?: {
    experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
    skills: string[];
    background: string[];
    previousInterviews?: number;
    // 履歴書データからの詳細情報
    resumeData?: {
      personalInfo?: {
        name?: string;
        currentPosition?: string;
        yearsOfExperience?: number;
      };
      workExperience?: Array<{
        company: string;
        position: string;
        duration: string;
        achievements?: string[];
      }>;
      education?: Array<{
        institution: string;
        degree: string;
        field?: string;
      }>;
      certifications?: string[];
      projects?: Array<{
        name: string;
        description: string;
        technologies?: string[];
      }>;
      languages?: Array<{
        language: string;
        proficiency: string;
      }>;
    };
  };
  
  // 質問設定
  questionSettings: {
    totalQuestions: number;
    categories: QuestionCategory[];
    difficulty: 'easy' | 'medium' | 'hard' | 'adaptive';
    estimatedDuration: number; // 分
    includeWarmup: boolean;
    includeFollowUp: boolean;
  };
  
  // 適応的設定
  adaptiveSettings?: {
    personalizeForCandidate: boolean;
    adjustForAnxiety: boolean;
    emphasizeGrowth: boolean;
    avoidNegativeLanguage: boolean;
  };
}

/**
 * 質問カテゴリ
 */
export type QuestionCategory = 
  | 'self-introduction'
  | 'experience-skills'
  | 'motivation'
  | 'problem-solving'
  | 'teamwork'
  | 'leadership'
  | 'career-goals'
  | 'company-culture-fit'
  | 'technical'
  | 'behavioral'
  | 'situational';

/**
 * 生成された質問
 */
export interface GeneratedQuestion {
  id: string;
  text: string;
  category: QuestionCategory;
  intent: QuestionIntent;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedAnswerTime: number; // 秒
  followUpQuestions?: string[];
  
  // 評価観点
  evaluationCriteria: EvaluationCriterion[];
  
  // 心理的安全性配慮
  supportiveElements: {
    encouragementPrefix?: string;
    clarificationNote?: string;
    reassuranceMessage?: string;
  };
  
  // メタデータ
  metadata: {
    generatedAt: string;
    version: string;
    source: 'ai' | 'template' | 'custom';
    adaptationReason?: string;
  };
}

/**
 * 質問意図
 */
export interface QuestionIntent {
  primary: string;        // 主要な評価目的
  secondary?: string[];   // 副次的な評価観点
  skillAssessment: string[]; // 評価対象スキル
  personalityTraits: string[]; // 評価対象の性格特性
}

/**
 * 評価基準
 */
export interface EvaluationCriterion {
  name: string;
  description: string;
  weight: number; // 0-1
  scoringGuideline: {
    excellent: string;
    good: string;
    acceptable: string;
    needsImprovement: string;
  };
}

/**
 * 質問生成レスポンス
 */
export interface QuestionGenerationResponse {
  requestId: string;
  questions: GeneratedQuestion[];
  sessionConfig: {
    totalDuration: number;
    breakPoints: number[]; // 休憩タイミング（質問番号）
    adaptiveFlow: boolean;
    difficultyProgression: 'linear' | 'adaptive' | 'plateau';
  };
  
  // 心理的安全性メッセージ
  supportiveMessaging: {
    openingMessage: string;
    transitionMessages: string[];
    closingMessage: string;
    encouragementTriggers: EncouragementTrigger[];
  };
  
  // メタデータ
  metadata: {
    generatedAt: string;
    processingTime: number;
    adaptationApplied: string[];
    qualityScore: number; // 0-1
  };
}

/**
 * 励ましトリガー
 */
export interface EncouragementTrigger {
  condition: 'after_difficult_question' | 'mid_session' | 'low_confidence_detected' | 'time_pressure';
  message: string;
  timing: 'immediate' | 'delayed' | 'next_question';
}

// ===== アダプティブ機能関連 =====

/**
 * 候補者状態
 */
export interface CandidateState {
  currentStress: number; // 1-10
  confidence: number; // 1-10
  engagement: number; // 1-10
  comprehension: number; // 1-10
  responseQuality: number; // 1-10
  timeUtilization: number; // 回答時間の効率性 0-1
}

/**
 * 適応的調整
 */
export interface AdaptiveAdjustment {
  type: 'difficulty' | 'pacing' | 'support' | 'encouragement';
  reason: string;
  adjustment: string;
  appliedAt: string;
}

/**
 * セッション分析
 */
export interface SessionAnalysis {
  questionPerformance: {
    questionId: string;
    responseTime: number;
    qualityScore: number;
    confidenceLevel: number;
    needsImprovement: boolean;
  }[];
  
  overallMetrics: {
    averageResponseTime: number;
    consistencyScore: number;
    improvementTrend: 'improving' | 'stable' | 'declining';
    recommendedNextLevel: QuestionCategory[];
  };
  
  adaptiveRecommendations: {
    nextSessionDifficulty: 'easy' | 'medium' | 'hard';
    focusAreas: string[];
    encouragementNeeds: boolean;
    practiceRecommendations: string[];
  };
}

// ===== 質問テンプレート =====

/**
 * 質問テンプレート
 */
export interface QuestionTemplate {
  id: string;
  name: string;
  category: QuestionCategory;
  template: string; // プレースホルダー付きテンプレート
  variables: TemplateVariable[];
  adaptationRules: AdaptationRule[];
  
  // 心理的安全性設定
  safetyConfiguration: {
    avoidTriggerWords: string[];
    includeReassurance: boolean;
    gentleVersion?: string;
    encouragingVersion?: string;
  };
}

/**
 * テンプレート変数
 */
export interface TemplateVariable {
  name: string;
  type: 'company' | 'position' | 'skill' | 'experience' | 'custom';
  required: boolean;
  defaultValue?: string;
  validationRules?: string[];
}

/**
 * 適応ルール
 */
export interface AdaptationRule {
  condition: string;
  modification: string;
  priority: number;
  description: string;
}

// ===== 質問プール管理 =====

/**
 * 質問プール
 */
export interface QuestionPool {
  id: string;
  name: string;
  description: string;
  industry?: string;
  position?: string;
  templates: QuestionTemplate[];
  
  metadata: {
    createdAt: string;
    updatedAt: string;
    version: string;
    usage: number;
    effectivenessScore: number; // 0-1
  };
}

/**
 * 質問有効性メトリクス
 */
export interface QuestionEffectiveness {
  questionId: string;
  usage: {
    totalUsed: number;
    successfulSessions: number;
    averageResponseQuality: number;
  };
  
  candidateFeedback: {
    clarity: number; // 1-5
    fairness: number; // 1-5
    relevance: number; // 1-5
    difficulty: number; // 1-5
  };
  
  outcomes: {
    discriminatingPower: number; // 候補者の差別化能力
    predictiveValidity: number; // 実際の性能との相関
    biasAssessment: number; // バイアス評価スコア
  };
}