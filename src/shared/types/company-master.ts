/**
 * 企業マスター管理システムの型定義
 * 永続データとしての企業情報・職種・質問テンプレート管理
 */

// ===== 企業マスター =====

/**
 * 企業マスター情報（永続データ）
 */
export interface CompanyMaster {
  id: string;
  name: string;
  industry: string;
  description: string;
  culture: string;
  businessDescription?: string;
  vision?: string;
  mission?: string;
  values?: string[];
  interviewStyle: 'casual' | 'formal' | 'hybrid';
  size: 'startup' | 'sme' | 'large' | 'enterprise';
  location?: string[];
  benefits?: string[];
  workEnvironment?: {
    remoteWork: boolean;
    flexTime: boolean;
    averageOvertimeHours?: number;
  };
  website?: string;
  logoUrl?: string;
  
  // メタデータ
  createdAt: string;
  updatedAt: string;
  createdBy: string; // エージェントID
  lastUsed: string;
  usageCount: number;
  
  // 関連データ
  positions: PositionMaster[];
  questionTemplates: QuestionTemplate[];
}

/**
 * 職種マスター情報
 */
export interface PositionMaster {
  id: string;
  companyId: string;
  title: string;
  department: string;
  level: 'entry' | 'mid' | 'senior' | 'executive' | 'c-level';
  requirements: string[];
  preferredSkills: string[];
  salaryRange?: {
    min: number;
    max: number;
    currency: string;
  };
  
  // 面接設定
  expectedDuration: number; // 分
  difficulty: 'easy' | 'medium' | 'hard';
  focusAreas: string[]; // 重点評価領域
  
  // メタデータ
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  usageCount: number;
}

/**
 * 質問テンプレート（企業×職種別）
 */
export interface QuestionTemplate {
  id: string;
  companyId: string;
  positionId?: string; // null = 企業共通テンプレート
  name: string;
  description: string;
  
  // 質問設定
  questions: TemplateQuestion[];
  totalEstimatedTime: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'adaptive';
  
  // プロダクト憲法準拠設定
  psychologicalSafety: {
    enabled: boolean;
    anxietyReduction: boolean;
    encouragementLevel: 'low' | 'medium' | 'high';
    personalizedMessaging: boolean;
  };
  
  // メタデータ
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  usageCount: number;
  lastUsed: string;
  rating?: number; // 1-5点、使用後のエージェント評価
  tags: string[];
}

/**
 * テンプレート内の質問定義
 */
export interface TemplateQuestion {
  id: string;
  order: number;
  text: string;
  category: string;
  intent: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedAnswerTime: number;
  
  // 評価設定
  evaluationCriteria: string[];
  scoringWeight: number; // 0-1
  
  // 心理的安全性配慮
  supportiveElements: {
    encouragementPrefix?: string;
    clarificationNote?: string;
    reassuranceMessage?: string;
  };
  
  // フォローアップ
  followUpQuestions: string[];
  adaptiveOptions: {
    easyVariant?: string;
    hardVariant?: string;
  };
}

// ===== 個別面談リンク（期限付きデータ） =====

/**
 * 個別面談リンク情報
 */
export interface InterviewLink {
  id: string;
  
  // マスターデータ参照
  companyMasterId: string;
  positionMasterId: string;
  questionTemplateId?: string;
  
  // 候補者情報
  candidateName: string;
  candidateEmail: string;
  candidateProfile?: {
    experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
    skills: string[];
    background: string[];
  };
  
  // エージェント個別設定
  customSettings: {
    additionalQuestions: CustomQuestion[];
    focusAreas: string[];
    specialInstructions: string;
    timeAllocation: Record<string, number>; // カテゴリ別時間配分
  };
  
  // リンク管理
  status: 'active' | 'completed' | 'expired' | 'cancelled';
  expiresAt: string;
  accessUrl: string;
  
  // 結果データ（期限後削除対象）
  interviewResults?: InterviewResults;
  
  // メタデータ
  createdAt: string;
  createdBy: string; // エージェントID
  completedAt?: string;
}

/**
 * エージェントが個別に追加する質問
 */
export interface CustomQuestion {
  id: string;
  text: string;
  intent: string;
  insertAfterQuestionId?: string; // どの質問の後に挿入するか
  category: string;
  estimatedTime: number;
  isRequired: boolean;
}

/**
 * 面談結果データ（期限後削除）
 */
export interface InterviewResults {
  sessionId: string;
  startedAt: string;
  completedAt: string;
  duration: number;
  
  // 回答データ
  responses: {
    questionId: string;
    answer: string;
    responseTime: number;
    confidenceLevel: number;
    audioFileUrl?: string;
  }[];
  
  // 分析結果
  analysis: {
    overallScore: number;
    categoryScores: Record<string, number>;
    strengths: string[];
    improvementAreas: string[];
    recommendations: string[];
  };
  
  // エージェント評価
  agentFeedback?: {
    rating: number; // 1-5
    notes: string;
    followUpActions: string[];
  };
}

// ===== サジェスト・検索用 =====

/**
 * 企業検索・サジェスト結果
 */
export interface CompanySuggestion {
  id: string;
  name: string;
  industry: string;
  matchScore: number; // 0-1、入力との類似度
  lastUsed: string;
  usageCount: number;
}

/**
 * 職種検索・サジェスト結果
 */
export interface PositionSuggestion {
  id: string;
  companyId: string;
  title: string;
  department: string;
  level: string;
  matchScore: number;
  lastUsed: string;
  usageCount: number;
}

// ===== データライフサイクル管理 =====

/**
 * データ削除ポリシー設定
 */
export interface DataRetentionPolicy {
  // 面談結果の保持期間（日数）
  interviewResultsRetentionDays: number;
  
  // 個別リンクの保持期間（完了後）
  linkRetentionDaysAfterCompletion: number;
  
  // 候補者個人情報の保持期間
  candidateDataRetentionDays: number;
  
  // マスターデータの削除条件
  masterDataCleanupPolicy: {
    unusedCompanyDays: number; // 使用されない企業の削除期間
    unusedPositionDays: number; // 使用されない職種の削除期間
    lowRatedTemplateThreshold: number; // 低評価テンプレートの削除閾値
  };
}

/**
 * データ削除対象の特定結果
 */
export interface DeletionCandidate {
  type: 'interviewResult' | 'link' | 'candidateData' | 'unusedMaster';
  id: string;
  createdAt: string;
  lastUsed: string;
  reason: string;
  estimatedDataSize: number; // KB
}

// ===== API レスポンス型 =====

export interface CompanyMasterResponse {
  companies: CompanyMaster[];
  total: number;
  page: number;
  limit: number;
}

export interface SuggestionResponse {
  companies: CompanySuggestion[];
  positions: PositionSuggestion[];
  templates: QuestionTemplate[];
}

export interface DataCleanupResponse {
  deletionCandidates: DeletionCandidate[];
  totalDataSize: number;
  estimatedSavings: number;
  autoDeleteEnabled: boolean;
}