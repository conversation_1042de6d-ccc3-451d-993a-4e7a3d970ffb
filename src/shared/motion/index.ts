/**
 * 面接君 モーションシステム
 * Framer Motionを使用したアニメーションプリセット
 */
import { Variants } from "framer-motion";
import { motionTokens } from "../theme";

// 基本的なアニメーションバリアント
export const fadeIn: Variants = {
  initial: { 
    opacity: 0, 
    y: 10 
  },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.natural,
    }
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: {
      duration: motionTokens.duration.quick / 1000,
    }
  }
};

export const slideIn: Variants = {
  initial: { 
    x: -20, 
    opacity: 0 
  },
  animate: { 
    x: 0, 
    opacity: 1,
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.decelerate,
    }
  },
  exit: {
    x: 20,
    opacity: 0,
    transition: {
      duration: motionTokens.duration.quick / 1000,
    }
  }
};

export const scaleIn: Variants = {
  initial: { 
    scale: 0.95, 
    opacity: 0 
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.emphasized,
    }
  },
  exit: {
    scale: 0.95,
    opacity: 0,
    transition: {
      duration: motionTokens.duration.quick / 1000,
    }
  }
};

// パルスアニメーション（常時繰り返し）
export const pulse: Variants = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      repeatType: "loop",
      ease: motionTokens.easing.natural,
    }
  }
};

// 呼吸アニメーション（深呼吸プロンプト用）
export const breathe: Variants = {
  inhale: {
    scale: 1.2,
    transition: {
      duration: 4,
      ease: motionTokens.easing.natural,
    }
  },
  exhale: {
    scale: 1,
    transition: {
      duration: 4,
      ease: motionTokens.easing.natural,
    }
  }
};

// ステージごとのアニメーション（フィードバック表示用）
export const staggerChildren: Variants = {
  initial: {
    opacity: 0,
  },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    }
  }
};

export const staggerItem: Variants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.decelerate,
    }
  }
};

// ホバー・タップインタラクション
export const buttonMotion = {
  whileHover: { 
    scale: 1.05,
    transition: {
      duration: motionTokens.duration.quick / 1000,
    }
  },
  whileTap: { 
    scale: 0.95,
    transition: {
      duration: motionTokens.duration.quick / 1000,
    }
  },
};

// カードホバーエフェクト
export const cardHover = {
  rest: {
    scale: 1,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
  },
  hover: {
    scale: 1.02,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.emphasized,
    }
  }
};

// アバターの表情変化アニメーション
export const avatarExpression = {
  neutral: {
    y: 0,
    rotate: 0,
  },
  encouraging: {
    y: [-5, 0, -5],
    rotate: [-5, 5, -5],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "loop" as const,
      ease: motionTokens.easing.natural,
    }
  },
  patient: {
    y: [0, -2, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      repeatType: "loop" as const,
      ease: motionTokens.easing.natural,
    }
  }
};

// プログレスバーアニメーション
export const progressBar: Variants = {
  initial: {
    scaleX: 0,
    transformOrigin: "left",
  },
  animate: (progress: number) => ({
    scaleX: progress,
    transition: {
      duration: motionTokens.duration.slow / 1000,
      ease: motionTokens.easing.decelerate,
    }
  })
};

// 成功・エラーフィードバック
export const successBounce = {
  initial: { scale: 0 },
  animate: {
    scale: [0, 1.2, 1],
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      times: [0, 0.6, 1],
      ease: motionTokens.easing.emphasized,
    }
  }
};

export const shake = {
  animate: {
    x: [-10, 10, -10, 10, 0],
    transition: {
      duration: motionTokens.duration.moderate / 1000,
      ease: motionTokens.easing.natural,
    }
  }
};