const express = require('express');

const router = express.Router();

router.post('/signal', (req, res) => {
  try {
    res.json({
      status: 'connected',
      id: `mtg-${Date.now()}`,
      candidateName: '山田 太郎'
    });
  } catch (error) {
    console.error('シグナリングエラー:', error);
    res.status(500).json({ error: 'シグナリングに失敗しました' });
  }
});

router.post('/orchestrate', async (req, res) => {
  try {
    const response = {
      questions: [
        { id: '1', text: '自己紹介をしてください' },
        { id: '2', text: 'なぜ当社を志望しましたか？' },
        { id: '3', text: '5年後のキャリアプランを教えてください' }
      ],
      feedback: 'ご応募ありがとうございました。'
    };
    console.log('オーケストレーション応答:', response);
    res.json(response);
  } catch (error) {
    console.error('オーケストレーションエラー:', error);
    res.status(500).json({ error: '質問の取得に失敗しました' });
  }
});

module.exports = router;
