const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const sdk = require('microsoft-cognitiveservices-speech-sdk');

const router = express.Router();

const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const upload = multer({
  dest: uploadDir,
  limits: { fileSize: 10 * 1024 * 1024 }
});

router.get('/synthesize', async (req, res) => {
  let synthesizer = null;
  try {
    const text = req.query.text;
    if (!text) {
      return res.status(400).json({ error: 'テキストが指定されていません' });
    }

    console.log('音声合成リクエスト:', { text });

    const speechConfig = sdk.SpeechConfig.fromSubscription(
      process.env.AZURE_SPEECH_KEY,
      process.env.AZURE_SPEECH_REGION
    );
    speechConfig.speechSynthesisVoiceName = 'ja-JP-NanamiNeural';
    speechConfig.speechSynthesisOutputFormat = sdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm;

    const ssml = `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="ja-JP">
        <voice name="ja-JP-NanamiNeural">
          <prosody rate="0%" pitch="0%">
            ${text}
          </prosody>
        </voice>
      </speak>
    `;

    synthesizer = new sdk.SpeechSynthesizer(speechConfig);

    const audioResult = await new Promise((resolve, reject) => {
      synthesizer.speakSsmlAsync(
        ssml,
        result => {
          if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
            console.log('音声合成成功:', {
              audioDataSize: result.audioData.byteLength,
              audioDuration: result.audioDuration,
              resultId: result.resultId
            });
            resolve(result);
          } else {
            console.error('音声合成失敗:', {
              reason: result.reason,
              errorDetails: result.errorDetails,
              resultId: result.resultId
            });
            reject(new Error(`音声合成に失敗しました: ${result.reason}`));
          }
        },
        error => {
          console.error('音声合成エラー:', error);
          reject(error);
        }
      );
    });

    if (!audioResult.audioData || audioResult.audioData.byteLength === 0) {
      throw new Error('音声データが生成されませんでした');
    }

    res.set('Content-Type', 'audio/wav');
    res.set('Content-Length', audioResult.audioData.byteLength);
    res.send(Buffer.from(audioResult.audioData));
  } catch (error) {
    console.error('音声合成エラー:', error);
    res.status(500).json({
      error: '音声合成中にエラーが発生しました',
      details: error.message
    });
  } finally {
    if (synthesizer) {
      synthesizer.close();
    }
  }
});

router.post('/analyze', upload.single('audio'), async (req, res) => {
  let recognizer = null;
  let pushStream = null;
  try {
    if (!req.file) {
      console.error('音声ファイルがアップロードされていません');
      return res.status(400).json({ error: '音声ファイルがアップロードされていません' });
    }

    console.log('音声ファイルを受信:', {
      path: req.file.path,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      filename: req.file.filename
    });

    if (!fs.existsSync(req.file.path)) {
      console.error('音声ファイルが見つかりません:', req.file.path);
      return res.status(400).json({ error: '音声ファイルが見つかりません' });
    }

    const stats = fs.statSync(req.file.path);
    console.log('音声ファイルのサイズ:', stats.size, 'bytes');

    if (stats.size === 0) {
      console.error('音声ファイルが空です');
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ error: '音声ファイルが空です' });
    }

    const audioData = fs.readFileSync(req.file.path);
    console.log('音声ファイルの読み込み完了:', audioData.length, 'bytes');

    const dumpPath = req.file.path + '.dump.wav';
    fs.writeFileSync(dumpPath, audioData);
    console.log('📝 音声ファイルをダンプしました:', dumpPath);

    pushStream = sdk.AudioInputStream.createPushStream();
    pushStream.write(audioData);
    pushStream.close();

    const audioConfig = sdk.AudioConfig.fromStreamInput(pushStream);
    const speechConfig = sdk.SpeechConfig.fromSubscription(
      process.env.AZURE_SPEECH_KEY,
      process.env.AZURE_SPEECH_REGION
    );

    speechConfig.speechRecognitionLanguage = 'ja-JP';
    speechConfig.outputFormat = sdk.OutputFormat.Detailed;

    speechConfig.setProperty(
      sdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,
      '10000'
    );
    speechConfig.setProperty(
      sdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,
      '2000'
    );

    speechConfig.setProperty(
      sdk.PropertyId.SpeechServiceConnection_EnableAudioLogging,
      'true'
    );
    speechConfig.setProperty(
      sdk.PropertyId.SpeechServiceConnection_EnableAudioBuffering,
      'true'
    );

    recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);

    console.log('音声認識を開始します...');

    const result = await new Promise((resolve, reject) => {
      recognizer.recognizeOnceAsync(
        result => {
          console.log('音声認識結果:', {
            reason: result.reason,
            text: result.text,
            confidence: result.confidence,
            duration: result.duration,
            offset: result.offset
          });

          if (result.reason === sdk.ResultReason.NoMatch || !result.text.trim()) {
            resolve(null);
          } else if (result.reason === sdk.ResultReason.RecognizedSpeech) {
            resolve(result);
          } else {
            reject(new Error(`音声認識に失敗しました: ${result.reason}`));
          }
        },
        error => {
          console.error('音声認識エラー:', {
            message: error.message,
            code: error.code,
            details: error.details
          });
          reject(new Error(`音声認識エラー: ${error.message}`));
        }
      );
    });

    if (result === null) {
      fs.unlinkSync(req.file.path);
      return res.status(200).json({
        error: '声が拾えていないようです。マイクの前でゆっくりはっきりお話しください。'
      });
    }

    console.log('音声認識が完了しました');

    fs.unlinkSync(req.file.path);
    console.log('一時ファイルを削除しました');

    res.json({
      text: result.text,
      confidence: result.confidence,
      feedback: {
        id: Date.now(),
        text: `「${result.text}」について、もう少し詳しくお聞かせいただけますか？`
      }
    });
  } catch (error) {
    console.error('音声解析エラーの詳細:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      details: error.details
    });

    if (recognizer) {
      recognizer.close();
    }
    if (pushStream) {
      pushStream.close();
    }
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
      console.log('エラー発生時に一時ファイルを削除しました');
    }

    res.status(500).json({
      error: '音声解析中にエラーが発生しました',
      details: error.message,
      code: error.code,
      stack: error.stack
    });
  }
});

module.exports = router;
