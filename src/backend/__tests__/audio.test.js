const request = require('supertest');
const express = require('express');
const audioRoutes = require('../routes/audio');

const app = express();
app.use(express.json());
app.use('/', audioRoutes);

describe('audio routes', () => {
  test('GET /synthesize without text returns 400', async () => {
    const res = await request(app).get('/synthesize');
    expect(res.status).toBe(400);
  });

  test('POST /analyze without file returns 400', async () => {
    const res = await request(app)
      .post('/analyze')
      .attach('audio', Buffer.alloc(0), 'empty.wav');
    expect(res.status).toBe(400);
  });
});
