const request = require('supertest');
const express = require('express');
const orchestrationRoutes = require('../routes/orchestration');

const app = express();
app.use(express.json());
app.use('/', orchestrationRoutes);

describe('orchestration routes', () => {
  test('POST /signal returns connection status', async () => {
    const res = await request(app).post('/signal');
    expect(res.status).toBe(200);
    expect(res.body.status).toBe('connected');
  });

  test('POST /orchestrate returns questions array', async () => {
    const res = await request(app).post('/orchestrate');
    expect(res.status).toBe(200);
    expect(Array.isArray(res.body.questions)).toBe(true);
  });
});
