const request = require('supertest');
const express = require('express');
const cookieParser = require('cookie-parser');
const authRoutes = require('../routes/auth');

const app = express();
app.use(express.json());
app.use(cookieParser());
app.use('/', authRoutes);

describe('auth routes', () => {
  test('POST /login without credentials returns 400', async () => {
    const res = await request(app).post('/login').send({});
    expect(res.status).toBe(400);
  });
});
