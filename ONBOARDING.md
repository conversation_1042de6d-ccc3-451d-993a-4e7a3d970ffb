# エンジニア向けオンボーディングガイド

## 🎯 このドキュメントの目的

新しくプロジェクトに参加するエンジニアが、最短時間で開発に着手できるようにするためのガイドです。

## 📝 プロジェクト理解のチェックリスト

### Day 1: 環境構築とプロジェクト理解

- [ ] README.mdを読んでプロジェクトの概要を理解
- [ ] 開発環境のセットアップ完了
- [ ] 各サービス（candidate, management, FastAPI API）の起動確認
- [ ] デモアカウントでの動作確認

### Day 2: コードベース理解

- [ ] アーキテクチャ図の理解
- [ ] 主要なデータフローの理解
- [ ] コーディング規約（CODING_GUIDELINES.md）の確認
- [ ] 主要コンポーネントの役割理解

### Day 3: 開発着手

- [ ] 開発用ブランチの作成
- [ ] 簡単なバグ修正または機能追加の実施
- [ ] プルリクエストの作成

## 🔑 重要な概念

### 1. システムの登場人物

| 役割 | 説明 | 使用する画面 |
|------|------|------------|
| エージェント | 人材紹介会社の担当者 | management画面 |
| 求職者 | 面接練習を行う候補者 | candidate画面 |
| 管理者 | システム管理者 | 管理API直接アクセス |

### 2. データフロー

```mermaid
graph LR
    A[エージェント] -->|面接リンク生成| B[Management画面]
    B -->|リンク送付| C[求職者]
    C -->|アクセス| D[Candidate画面]
    D -->|面接実施| E[3Dアバター/AI]
    E -->|結果保存| F[Backend]
    F -->|結果表示| B
```

### 3. 主要な技術概念

#### Azure Speech Service
- 音声認識と音声合成を提供
- 3Dアバターのレンダリング
- WebRTC経由でストリーミング

#### WebRTC
- ブラウザとAzureサービス間のリアルタイム通信
- ICEサーバー経由での接続確立
- 音声・映像のストリーミング

## 🛠️ 開発環境詳細

### 必須ツール

```bash
# Node.jsバージョン確認
node --version  # v18.x以上

# Pythonバージョン確認
python --version  # 3.11以上

# pnpmバージョン確認（推奨）
pnpm --version  # v9.x以上

# npmバージョン確認（pnpm未使用の場合）
npm --version   # v8.x以上

# Dockerバージョン確認
docker --version
docker-compose --version
```

### 依存関係のインストール

このプロジェクトは **pnpm workspaces** を使用しています。

```bash
# 1. pnpmをインストール（未インストールの場合）
npm install -g pnpm

# 2. プロジェクトルートで全ての依存関係をインストール
pnpm install

# 3. 各サービスの起動確認
npm run dev:all
```

### VSCode推奨拡張機能

- ESLint
- Prettier
- TypeScript React code snippets
- Azure Account
- Docker

### 環境変数設定

```bash
# .envファイルの作成
NEXT_PUBLIC_AZURE_SPEECH_KEY=xxx      # Azure Portalから取得
NEXT_PUBLIC_AZURE_SPEECH_REGION=xxx   # 例: japaneast
```

## 📂 プロジェクト構造詳細

### src/candidate (求職者向けフロントエンド)

```
candidate/
├── components/         # UIコンポーネント
│   ├── Avatar.tsx     # 3Dアバター表示の中核
│   ├── InterviewRoom.tsx  # 面接ルーム全体の管理
│   └── HeaderNavigation.tsx  # 共通ヘッダー
├── lib/               # ビジネスロジック
│   ├── unified-data.ts  # 統一データサービス
│   └── advancedPracticeData.ts  # 練習用データ
├── pages/             # Next.jsページ
│   ├── test-data.tsx  # リンクアクセスページ
│   ├── interview-prep.tsx  # 面接準備画面
│   └── focused-interview.tsx  # 実際の面接画面
└── theme/             # UIテーマ設定
```

### src/management (エージェント向け管理画面)

```
management/
├── components/
│   ├── MeetingLinkGenerator.tsx  # リンク生成
│   ├── QuestionScriptGenerator.tsx  # 質問作成
│   └── FeedbackViewer.tsx  # 結果表示
└── lib/
    └── agentData.ts  # エージェントデータ管理
```

### src/fastapi (APIサーバー)

```
fastapi/
├── app/
│   ├── api/           # エンドポイント定義
│   ├── core/          # 設定・ミドルウェア
│   └── services/      # ビジネスロジック
├── Dockerfile
└── requirements.txt
```

## 🔧 よく使うコマンド

### 開発用コマンド

```bash
# Docker サービス起動
cd infra
docker-compose up -d


# フロントエンド起動
npm run dev:candidate    # http://localhost:3003
npm run dev:management   # http://localhost:3001


# FastAPI サーバー (ローカル実行例)
cd ../src/fastapi
uvicorn app.main:app --reload --port 8080
```

### デバッグ用コマンド

```bash
# TypeScriptの型チェック
npm run type-check

# ESLintチェック
npm run lint

# 依存関係の確認
npm ls

# ポート使用状況確認（Mac/Linux）
lsof -i :3001  # management
lsof -i :8080  # FastAPI
lsof -i :3003  # candidate

```

## 🎮 デモアカウント

### エージェントアカウント
- Email: <EMAIL>
- Password: password123

### テスト用リンクID
- link_001: 基本的な面接練習
- link_002: 高度な面接練習

## 🐛 よくあるトラブルと解決方法

### 1. ポートが既に使用されている

```bash
# プロセスを確認して終了
lsof -ti:3000 | xargs kill -9
```

### 2. Azure認証エラー

- `.env`ファイルの認証情報を確認
- Azure Portalでキーが有効か確認
- リージョン設定が正しいか確認

### 3. WebRTC接続エラー

- ブラウザコンソールでエラーログ確認
- ChromeまたはEdgeを使用（Safariは非推奨）
- HTTPSで接続しているか確認

### 4. TypeScriptエラー

```bash
# 型定義を再生成
npm run generate-types

# node_modulesをクリーンインストール
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

## 📚 さらに学ぶ

### 必読ドキュメント

1. [CODING_GUIDELINES.md](./CODING_GUIDELINES.md) - コーディング規約
2. [UIUX_DESIGN_GUIDLINES.md](./UIUX_DESIGN_GUIDLINES.md) - UI/UXガイドライン
3. [CLAUDE.md](./CLAUDE.md) - 技術的な詳細仕様

### 外部リソース

- [Azure Speech Service ドキュメント](https://docs.microsoft.com/azure/cognitive-services/speech-service/)
- [WebRTC API](https://developer.mozilla.org/docs/Web/API/WebRTC_API)
- [Next.js ドキュメント](https://nextjs.org/docs)
- [Chakra UI](https://chakra-ui.com/)

## 💬 質問・サポート

### Slackチャンネル
- #mensetsu-kun-dev: 開発関連の質問
- #mensetsu-kun-general: 一般的な質問

### ドキュメント更新

このドキュメントに追加すべき情報があれば、PRを作成してください。

---

**Welcome to the team! 🎉**

何か不明な点があれば、遠慮なく質問してください。