# 面接くん (Mensetsu-kun) - AI面接練習プラットフォーム

## ⚠️ 重要: プロジェクト構造変更履歴

### Vercelビルド問題対応履歴

**問題**: モノレポ環境でのTypeScript共有モジュール解決エラー
- エラー: `Cannot find module '@mensetsu-kun/shared/services/resumeParsingService'`
- ローカルビルドは成功するがVercelで失敗する状況が継続

**試行した解決策**:
1. `transpilePackages` 設定追加 → 失敗
2. TypeScript path aliases (`@shared/*`) → 失敗  
3. ローカルnpmパッケージ化 (`file:` プロトコル) → 失敗
4. `tsconfig.json` extends統合 → 失敗 (TS5083エラー)

**最終解決策**: pnpm workspaces への移行 (2024-06-16実装) ✅ **成功**

### ロールバック情報

**Vercel問題発生前の安定コミット**:
- コミット: `b021c66` - "fix: Add transpilePackages for shared modules in management app"
- 日時: 問題発生前の最後の安定状態

**構造変更前の最後のコミット**:
- コミット: `6a4a157` - "fix: tsconfig.json extendsをルートに統合し、@mensetsu-kun/sharedパス解決を確立"
- 説明: pnpm workspaces移行前の npm + file: プロトコルを使用した状態

### 🎉 解決済み - Vercelビルド成功

**解決コミット**: `7b169b8` - "feat: pnpm workspacesへの移行とVercel設定の更新"

**成功要因**:
- pnpm workspaces による確実な依存関係管理
- `workspace:^1.0.0` プロトコルでの共有パッケージ参照
- Vercel推奨のモノレポ構成への完全準拠
- `@mensetsu-kun/shared` パッケージの正常認識

**ビルド結果**: ✅ TypeScript compilation成功 → ✅ 全ページ静的生成成功 → ✅ デプロイ完了

```bash
# 参考: 過去のロールバック手順（現在は不要）
git checkout b021c66  # Vercel問題発生前に戻す
# または  
git checkout 6a4a157  # pnpm移行前に戻す
```

## 📋 プロジェクト概要

**面接くん**は、求職者と人材エージェントをつなぐAI駆動の面接練習プラットフォームです。3Dアバターとの対話を通じて、実際の面接に近い体験を提供し、求職者のスキル向上を支援します。

### 主要機能

1. **エージェント向け機能**
   - 面接リンクの生成・管理
   - 質問スクリプトの作成
   - 候補者の面接結果・フィードバック閲覧
   - 企業情報に基づいたカスタム面接の設定

2. **求職者向け機能**
   - 3Dアバターとの対話型面接練習
   - リアルタイム音声認識・合成
   - 段階的な練習モード（基礎練習→実践練習）
   - 詳細なフィードバックレポート
   - 面接スキルの可視化

3. **技術的特徴**
   - Azure Speech Serviceによる自然な音声対話
   - WebRTCベースのリアルタイム通信
   - レスポンシブデザイン（モバイル対応）
   - アクセシビリティ対応

## 🚀 クイックスタート

### 前提条件

- Node.js 18.x以上
- **pnpm 9.x以上** (推奨: pnpm 10.x)
- Docker & Docker Compose
- Azure Speech Serviceのアカウント

### 環境構築

1. **リポジトリのクローン**
```bash
git clone https://github.com/Mafumo-Inc/mensetsu-kun.git
cd mensetsu-kun

# 依存関係をインストール
pnpm install
```

2. **環境変数の設定**
```bash
# プロジェクトルートに.envファイルを作成
cp .env.example .env

# 以下の値を設定
NEXT_PUBLIC_AZURE_SPEECH_KEY=your_azure_speech_key
NEXT_PUBLIC_AZURE_SPEECH_REGION=your_azure_speech_region
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
OPENAI_API_KEY=your_openai_api_key
GOOGLE_API_KEY=your_google_api_key
```

3. **pnpmのインストール (未インストールの場合)**
```bash
# pnpmをグローバルインストール
npm install -g pnpm

# バージョン確認
pnpm --version
```

4. **依存関係のインストール**
```bash
# pnpm workspacesで全体の依存関係をインストール
pnpm install
```

5. **テスト用パッケージの追加**
```bash
# backendディレクトリでJestとSupertestをインストール
cd src/backend
pnpm add -D jest supertest
```

### オフライン開発時の依存取得

1. オンライン環境で必要なパッケージを事前に取得
   ```bash
   pnpm fetch
   ```
   これにより `~/.pnpm-store` に依存が保存されます。オフライン環境へコピー後、
   以下のように `--offline` オプションでインストールできます。

   ```bash
   pnpm install --offline
   ```

2. もしくは Verdaccio などのローカル npm レジストリを用意し、`.npmrc` に
   `registry=http://localhost:4873` を設定した上で `pnpm install` を実行します。

5. **Dockerコンテナの起動**
```bash
cd infra
docker-compose up -d
```

6. **開発サーバーの起動**

#### フロントエンドのみ（推奨）
```bash
# 求職者画面と管理画面のみ起動（モックデータで動作）
pnpm dev:frontend   # 求職者画面 (http://localhost:3000)
pnpm dev:management # 管理画面 (http://localhost:3001)
```

#### FastAPI バックエンドを含めた開発
```bash
# すべてのサービスを起動
pnpm dev:all

# または個別に起動
pnpm dev:backend    # バックエンド (http://localhost:8080)
pnpm dev:frontend   # 求職者画面 (http://localhost:3000)
pnpm dev:management # 管理画面 (http://localhost:3001)

# pnpm workspaces経由での個別起動も可能
pnpm --filter mensetsu-kun-management dev  # 管理画面のみ
pnpm --filter mensetsu-kun-candidate dev   # 求職者画面のみ
```

## 🏗️ アーキテクチャ

```
mensetsu-kun/
├── src/
│   ├── candidate/     # 求職者向けフロントエンド (Next.js + TypeScript)
│   ├── management/    # エージェント向け管理画面 (Next.js + TypeScript)
│   ├── fastapi/      # APIサーバー (FastAPI + Python 3.11)
│   │   └── app/      # エンドポイント・サービス実装
│   └── shared/        # 共通ライブラリ・型定義 (@mensetsu-kun/shared package)
├── infra/            # Docker構成ファイル
└── docs/             # ドキュメント類
```

### ⚠️ 開発モードについて

**重要**: 現在は**フロントエンド開発に集中**しており、ほとんどの機能は**モックデータ**で動作します。

#### 認証システムの状態
- **バックエンド認証**: JWT認証システムが実装済み（`src/fastapi/app/api/endpoints/auth.py`）
- **影響範囲**: `demo-login`ページのみがバックエンドを使用
- **その他のページ**: すべてモックデータで動作

#### 開発方法の選択
1. **モックのみで開発**（推奨）
   - バックエンドサーバーを起動せずに開発
   - `demo-login`以外のページで作業
   - 高速な開発サイクル

2. **バックエンドを含めた開発**
   - `pnpm dev:backend`でバックエンドサーバーを起動
   - `demo-login`経由でのログインが可能
   - 実際のJWT認証フローをテスト可能

### 技術スタック

| カテゴリ | 技術 |
|---------|------|
| フロントエンド | Next.js 14, TypeScript, React 18 |
| UIライブラリ | Chakra UI, Framer Motion |
| バックエンド | FastAPI (Python 3.11) |
| 音声/アバター | Azure Speech Service, WebRTC |
| インフラ | Docker, Docker Compose |
| 開発ツール | ESLint, Prettier, TypeScript |

## 📱 主要な画面フロー

### 🔐 デモ・開発環境アクセス
**開発状況確認**: `http://localhost:3000/demo-login`
- **エージェント・面接官ログイン**: 管理画面へのアクセス
  - デモアカウント（バックエンド起動時のみ）:
    - エージェント: `<EMAIL>` / `demo123`
    - 面接官: `<EMAIL>` / `demo123`
- **候補者テスト機能**: 各種リンク状態のテスト（モックで動作）
- **開発者向け**: 全機能の動作確認

### 📋 エージェント側フロー（管理画面）
1. **ログイン** → 管理ダッシュボード（`http://localhost:3001`）
2. **統合リンク作成ワークフロー**:
   - 企業資料アップロード → AI分析 → 面接設定 → 質問生成 → リンク発行
3. **面接設定**: 
   - シングルステージ面接（7つの面接官役割から選択）
   - マルチステージ面接（8種類のステップを組み合わせ）
4. **結果管理**: 候補者の面接結果・フィードバック閲覧

### 🧑‍💼 候補者側フロー（求職者画面）
1. **エージェントリンクアクセス** → トークン認証
2. **面接準備**:
   - 環境チェック（カメラ・マイク・ネットワーク）
   - 目標設定・企業情報確認
3. **面接実施** → 3Dアバターとの対話
   - Azure Avatar Serviceによるリアルタイム面接官
   - 音声認識・感情分析・進捗可視化
4. **結果確認** → 詳細フィードバックレポート

### 🔧 開発・テスト機能
- **有効/期限切れ/無効リンクのテスト**: demo-loginから各状況をシミュレート
- **面接データプレビュー**: エージェント設定データの確認

## 🔧 開発ガイド

### コーディング規約

詳細は[CODING_GUIDELINES.md](./CODING_GUIDELINES.md)を参照してください。

- **命名規則**: 
  - コンポーネント: PascalCase
  - 関数/変数: camelCase
  - 定数: UPPER_SNAKE_CASE
- **TypeScript**: 厳密な型定義を使用
- **非同期処理**: async/awaitパターンを推奨
- **エラーハンドリング**: try/catchで適切に処理

### コンポーネント構成

```typescript
// 例: 求職者向けコンポーネント
src/candidate/components/
├── Avatar.tsx              # 3Dアバター表示
├── InterviewRoom.tsx       # 面接ルーム全体
├── QuestionDisplay.tsx     # 質問表示
├── AnswerInput.tsx         # 回答入力（音声/テキスト）
├── FeedbackDisplay.tsx     # フィードバック表示
└── HeaderNavigation.tsx    # 共通ヘッダー
```

### API構成

```javascript
// FastAPI backend endpoints (抜粋)
POST /api/auth/login                 # ログイン
POST /api/auth/logout                # ログアウト
POST /api/auth/refresh               # トークン更新
GET  /api/health/health              # ヘルスチェック
POST /api/interviews/join            # 面接セッション参加
POST /api/interviews/{id}/start      # 面接開始
GET  /api/interviews/{id}/feedback   # フィードバック取得
```

## 🧪 テスト

```bash
# ユニットテスト
pnpm test

# E2Eテスト
pnpm test:e2e

# カバレッジレポート
pnpm test:coverage
```

## 📦 ビルド・デプロイ

```bash
# プロダクションビルド
pnpm build:all

# 個別ビルド
pnpm build:candidate
pnpm build:management
pnpm build:backend

# Dockerイメージビルド
docker-compose build
```

## 🐛 トラブルシューティング

### よくある問題

1. **アバターが表示されない**
   - Azure Speech Serviceの認証情報を確認
   - WebRTC接続状態をブラウザコンソールで確認
   - ネットワーク設定（ファイアウォール等）を確認

2. **音声認識が動作しない**
   - ブラウザの音声権限を確認
   - HTTPSで接続しているか確認
   - マイクデバイスの接続を確認

3. **ビルドエラー**
   - Node.jsバージョンを確認（18.x以上）
   - `node_modules`を削除して再インストール
   - TypeScriptの型エラーを確認

## 🔄 最新の開発状況 (2025年6月14日更新)

### 完了した改善
✅ **コード統一・整理**
- CODING_GUIDELINES.mdに基づく一貫性のあるコード記法を適用
- 全コンポーネントでCommonStyles.tsxを使用した統一されたスタイリング
- TypeScriptの型安全性強化とエラー修正

✅ **データ管理の統合**
- 全てのモックデータを統一APIサービス層に集約 (`src/candidate/data/mockApiData.ts`)
- 重複するデータソースを削除し、保守性を向上
- InterviewReportApi, InterviewDataApi クラスによる型安全なデータアクセス

✅ **UI/UXの改善**
- STAR+C評価セクションの横並びレイアウト実装
- フィードバック表示の100px円形スコアで統一
- レスポンシブ対応とモバイル最適化
- フィラーワードやフレーマーモーション非推奨警告を修正

### データ構造概要

**統合APIサービス** (`src/candidate/services/api.ts`)
```typescript
// 面接評価レポート取得
InterviewReportApi.getComprehensiveReport(candidateId, interviewId)

// STAR+C分析例取得  
InterviewReportApi.getSTARExamples(candidateId, questionIds)

// 候補者プロファイル取得
InterviewReportApi.getCandidateProfile(candidateId)

// 面接質問取得
InterviewDataApi.getQuestions(scenarioId, candidateProfile)
```

**モックデータ管理** (`src/candidate/data/mockApiData.ts`)
- 基本面接質問・フィードバック (`generateMockBasicQuestions`, `generateMockBasicFeedbacks`)
- 包括的面接レポート (`generateMockComprehensiveReport`)
- 業界別高度質問 (`generateMockAdvancedQuestions`)
- 候補者プロファイル (`generateMockCandidateProfile`)
- 音声解析・合成シミュレーション (`analyzeAudio`, `synthesizeText`)
- サンプル会話ログ (`communication.json`) - デモ用の会話履歴

**型定義** (`src/candidate/types/evaluation.ts`)
- ComprehensiveInterviewReport: 包括的面接評価レポート
- STARCEvaluation: STAR+C分析フレームワーク
- InterviewEvaluationFramework: 7つの評価軸（コミュニケーション、論理思考など）
- AudioAnalysisResult: 音声・感情分析結果

### テスト状況
- ✅ TypeScript型チェック: エラーなし
- ✅ Next.js本番ビルド: 成功
- ✅ 全コンポーネントの統合API移行: 完了
- ✅ UI一貫性チェック: STAR+C横並び、100px円形スコア適用済み

## 🎨 フロントエンド開発タスク (2025年6月15日追加)

### 現状の課題
フロントエンド（`src/candidate/`）の品質向上と保守性改善のため、以下の作業が必要です：

### Phase 1: 基盤整備（優先度: 高）
- [x] **環境変数・設定値の一元管理**（✅ 2025/06/15 完了）
  - config/index.tsによる設定統合
  - ロギングシステムの導入
  
- [x] **エラーハンドリングの標準化**（✅ 2025/06/15 完了）
  - 共通エラーハンドリングフックの作成
  - ユーザーフレンドリーなエラーメッセージ
  - エラー境界（Error Boundary）の実装
  
- [x] **型定義の強化（Phase 1）**（✅ 2025/06/15 完了）
  - Azure Speech SDKの型定義追加
  - 主要コンポーネントのany型排除
  - TypeScript型安全性の向上

- [x] **新しい統合ワークフローの設計と実装**（✅ 2025/06/15 完了）
  - 心理的安全性ベースの言語表現管理
  - マイクロモーメント最適化（LastBreathPrompt.tsx）
  - アダプティブUI（UserAdaptiveContext.tsx）
  - パーソナライゼーション機能（adaptiveFeedback.ts）
  - フロー理論実装（SessionGoals.tsx）

### Phase 2: コンポーネント整理（優先度: 中）
- [ ] **フィードバック表示コンポーネントの整理**
  - 重複する実装の統合
  - 再利用可能なコンポーネント化
  
- [ ] **InterviewRoomコンポーネントの責務分離**
  - ビジネスロジックのカスタムフックへの移行
  - UIコンポーネントの細分化
  - 状態管理の最適化

### Phase 3: UX改善（優先度: 中）
- [ ] **面接準備画面の統合**
  - interview-prep.tsxとinterview-preparation.tsxの統合
  - 共通ロジックの抽出
  
- [ ] **アクセシビリティ改善**
  - キーボードナビゲーションの強化
  - スクリーンリーダー対応
  - WCAG 2.1 AA準拠

### Phase 4: パフォーマンス最適化（優先度: 低）
- [ ] **コード分割とLazy Loading**
  - 動的インポートの活用
  - バンドルサイズの削減
  
- [ ] **レンダリング最適化**
  - React.memoの適切な使用
  - useMemoとuseCallbackの最適化
  
- [ ] **未使用ファイル・コンポーネントの削除**
  - pages/archive/ディレクトリの削除
  - 未使用importの整理

### 進捗管理
- 完了タスク: 5/14 (36%)
- 最終更新: 2025/06/15
- 新規実装: インテリジェント質問生成システム完了、心理的安全性ベースの統合ワークフロー完了

## 🧠 インテリジェント質問生成システム (2025年6月15日実装完了)

### 概要
プロダクト憲法の3原則に基づく革新的なAI質問生成システムを実装。企業情報と候補者プロファイルから最適化された面接質問を自動生成します。

### 🎯 主要機能

**AI質問生成エンジン**
- 企業文化・ポジション・要件に基づく動的質問生成
- 候補者の経験レベル・スキル・背景への適応調整
- STAR+C手法やコンピテンシー評価に対応
- 質問カテゴリ別のインテリジェント分類

**心理的安全性の実装** (プロダクト憲法第一条)
- 励ましメッセージの自動生成
- 不安軽減のための配慮機能
- ネガティブ表現の自動回避
- 緊張緩和のためのウォームアップ質問

**個人の尊重と成長支援** (プロダクト憲法第二条)
- 候補者プロファイルベースの個別化
- 経験レベル適応調整
- 成長機会を強調する質問設計
- フォローアップ質問による深掘り支援

**透明性と公正性** (プロダクト憲法第三条)
- 質問生成プロセスの完全可視化
- 評価基準の明確化
- アルゴリズムの透明性確保
- バイアス検出・軽減機能

### 🎨 UI/UXの大幅改善

**Before（2025/06/15以前）**
- 基本的なHTMLフォーム
- スタイルなしの「非常に微妙」な状態
- 機能的だが魅力に欠けるデザイン

**After（完全リデザイン後）**
- プロフェッショナルで洗練されたUI
- プロダクト憲法3原則の視覚化
- Framer Motionアニメーション
- レスポンシブ対応の2カラムレイアウト
- カラーコード化されたセクション
- 統一されたデザイン言語

### 🏗️ 技術アーキテクチャ

**ファイル構成**
```
src/
├── shared/
│   ├── types/intelligent-questions.ts           # 共通型定義
│   └── services/intelligentQuestionGenerator.ts # 共有サービス層
├── management/
│   ├── components/IntelligentQuestionGenerator.tsx # UIコンポーネント
│   ├── pages/question-generator.tsx             # ページルート
│   ├── pages/_app.tsx                          # Chakra UIプロバイダー
│   ├── pages/_document.tsx                     # HTML基盤設定
│   └── services/intelligentQuestionGenerator.ts # 管理画面用サービス
└── candidate/
    ├── services/intelligentInterviewService.ts  # 候補者側統合
    └── hooks/useIntelligentInterview.ts         # React Hook API
```

**主要型定義**
```typescript
interface QuestionGenerationRequest {
  companyInfo: CompanyInfo;
  candidateProfile: CandidateProfile;
  questionSettings: QuestionSettings;
  adaptiveSettings: AdaptiveSettings;
}

interface GeneratedQuestion {
  id: string;
  text: string;
  category: QuestionCategory;
  intent: QuestionIntent;
  difficulty: 'easy' | 'medium' | 'hard';
  supportiveElements: {
    encouragementPrefix?: string;
    clarificationNote?: string;
    reassuranceMessage?: string;
  };
  followUpQuestions?: string[];
  estimatedAnswerTime: number;
}
```

### 🚀 実装成果

**コード品質**
- TypeScript完全対応（4,200行以上の新規実装）
- 型安全性の確保
- モジュラー設計による高い保守性
- 豊富なコメント・ドキュメント

**ユーザーエクスペリエンス**
- 直感的で使いやすいインターフェース
- 心理的安全性を重視したUI設計
- プロダクト憲法の可視化
- リアルタイムフィードバック

**技術的改善**
- Chakra UIプロバイダー設定の完了
- react-iconsパッケージ統合
- TypeScript構文エラーの完全解決
- ビルドエラーの修正

### 📊 プルリクエスト

**PR #5**: [feat: インテリジェント質問生成システムの実装とUI完全リデザイン](https://github.com/Mafumo-Inc/mensetsu-kun/pull/5)
- **ステータス**: レビュー待ち
- **変更ファイル**: 12ファイル
- **追加行数**: 4,200行以上
- **削除行数**: 427行

### 🎯 次のステップ
1. PR #5のレビュー・マージ ✅ 完了 (2025年6月16日)
2. 企業情報管理機能の追加 ✅ 完了
3. 複数資料・テキスト入力対応 ✅ 完了
4. 質問意図の追加・編集・削除機能 ✅ 完了
5. **Phase 1: 求職者プロファイル機能** ✅ 完了 (2025年6月17日)
6. **Phase 2: 実装予定機能の開発** 🚧 進行中 (2025年6月17日〜)

### 🚧 現在の開発優先順位
#### 高優先度（即座に実装）
1. **質問意図の個別編集機能** - 編集ボタン、編集フォーム、インライン編集
2. **統合ワークフローでのプロファイル連携** - CandidateProfileInput統合
3. **サーバー側トークン認証システム** - 現在はクライアント側のみ

#### 中優先度（次期実装）
4. **マルチステージ面接の詳細編集UI** - ステップ別設定、評価基準、録画設定
5. **リアルタイムAI分析** - 表情・声のトーン解析（現在はモック）
6. **テンプレート管理機能** - バックエンド永続化、編集、削除、バージョン管理

## 🚀 完全デプロイ成功！TypeScriptエラー解決の軌跡 (2025年6月17日)

### 🎉 デプロイ成功の証明
**✅ Vercelビルド・デプロイ完全成功** (2025年6月17日 01:21)
```
[01:21:29.998] ✓ Compiled successfully
[01:21:32.099] ✓ Generating static pages (5/5)  
[01:21:56.058] Build Completed in /vercel/output [1m]
[01:21:57.940] Deployment completed
```

### 🔧 解決したTypeScriptエラーの全履歴

**エラー解決の軌跡** - 9つの複雑なTypeScriptエラーを段階的に解決：

1. **PersonalInfo型不整合**: `currentPosition`プロパティ不足 → 型定義拡張
2. **PersonalInfo importエラー**: CandidateProfileInputでのimport不足 → import追加
3. **SkillInfo category必須**: スキル作成時の`category`プロパティ不足 → デフォルト値設定
4. **CandidateProfile必須プロパティ**: メタデータフィールド不足 → 必須フィールド追加
5. **generateEnhancedQuestions引数不足**: `interviewConfig`パラメータ不足 → 型定義拡張
6. **JSXコンポーネント認識エラー**: React.FC vs 関数宣言の問題 → 'use client'追加 + 関数宣言化
7. **Framer Motion transition型**: 文字列vs Transitionオブジェクト → オブジェクト形式に修正
8. **career?プロパティundefined**: オプショナルプロパティのnullチェック不足 → 存在チェック追加
9. **重複関数実装**: 同名メソッドの重複定義 → 古い定義削除
10. **leadershipVariant型不足**: styleAdjustments型定義不足 → 型定義追加

### 🛠️ 技術的解決手法

**モノレポ・TypeScript環境での課題**
- pnpm workspaces環境でのTypeScript型解決
- Next.js 13+ の'use client'ディレクティブ要件
- Chakra UI + Framer Motionの型互換性
- 共有パッケージ(@mensetsu-kun/shared)の型参照

**解決パターン**
- **型定義拡張**: 不足プロパティの段階的追加
- **null安全性**: オプショナルプロパティの適切なハンドリング
- **コンポーネント型安全性**: React.FC → 関数宣言への統一
- **重複排除**: 重複メソッド定義の段階的削除

### 📊 改修規模
- **修正対象ファイル**: 10+ ファイル
- **解決したTypeScriptエラー**: 10件
- **コミット数**: 8コミット
- **作業時間**: 約3時間の集中デバッグ

### 💡 得られた知見

**TypeScript + Next.js + monorepo環境**
- 型定義の段階的拡張アプローチの有効性
- 'use client'ディレクティブの適切な配置
- Framer Motion型システムとの正しい連携
- pnpm workspaces環境でのパッケージ参照

**エラー解決戦略**
- 各エラーを独立して解決し、新たなエラーを段階的に対処
- ビルドログの詳細な分析による根本原因特定
- 型安全性を保ちながらの実装修正

## 🏢 企業マスター・データ管理システム (2025年6月16日追加)

### 概要
実用的な面接管理システムとして、企業情報・職種・質問テンプレートの永続管理と、個別面談データの期限付き管理を分離したデータアーキテクチャを実装。

### 🎯 データ設計の基本思想

**永続データ（企業マスター）**
- 企業基本情報・職種・質問テンプレート
- エージェントの資産として蓄積・再利用
- サジェスト機能で効率的な入力支援

**期限付きデータ（個別面談）**
- 候補者個人情報・面談結果・分析データ
- 期限後自動削除でプライバシー保護
- リンクごとのカスタマイズ設定

### 🔄 統合ワークフロー

```
1. PDF アップロード → 企業情報抽出
2. 企業名入力 → サジェスト表示 → 既存選択 or 新規作成
3. 職種入力 → サジェスト表示 → 既存選択 or 新規作成
4. 質問テンプレート選択（過去生成済み）or 新規AI生成
5. エージェント個別調整（追加質問、重点評価項目）
6. テンプレート保存 + リンク発行
7. 面談実施 → 結果保存（期限付き）
8. データライフサイクル管理（自動削除）
```

### 📊 主要コンポーネント

**企業マスター管理**
- `CompanyMasterService`: 企業・職種・テンプレートの CRUD 操作
- `WorkflowIntegrationService`: 質問生成とマスター管理の統合
- サジェスト検索とマッチングアルゴリズム
- 使用履歴ベースの優先順位付け

**質問テンプレート管理**
- AI生成質問の自動テンプレート化
- 企業×職種別のテンプレート分類
- プロダクト憲法準拠設定の保持
- 使用実績とエージェント評価の蓄積

**データライフサイクル管理**
- 面談結果の自動削除（デフォルト90日）
- 候補者個人情報の期限管理（365日）
- 未使用マスターデータのクリーンアップ
- GDPR準拠のデータ保護ポリシー

### 🛡️ プライバシー・セキュリティ

**データ分離原則**
```typescript
// 永続データ（期限なし）
CompanyMaster {
  name, industry, culture, positions[], questionTemplates[]
}

// 期限付きデータ（自動削除）
InterviewLink {
  companyMasterId, // 参照のみ
  candidateName, candidateEmail, // 個人情報
  interviewResults, // 面談結果
  expiresAt // 削除期限
}
```

**自動削除ポリシー**
- 面談結果: 90日後自動削除
- 個別リンク: 完了後30日で削除
- 候補者個人情報: 365日後削除
- マスターデータ: 使用履歴ベースの保持

### 💡 運用上のメリット

**エージェント効率化**
- 企業情報の再入力不要
- 過去の質問テンプレート再利用
- サジェスト機能による入力支援
- ワンクリックでの類似企業設定

**データ品質向上**
- 企業情報の統一・標準化
- 重複データの自動統合
- 使用実績による品質向上
- テンプレートの評価・改善サイクル

**コンプライアンス対応**
- 個人情報の期限管理
- GDPR Right to be Forgotten 対応
- データ最小化原則の実装
- 監査ログの自動生成

## 👤 求職者プロファイル・面接官役割システム (2025年6月16日追加)

### 概要
AI面接官がより個人化された面接体験を提供するため、求職者の履歴書・職務経歴書の解析機能と面接官の役割・ステップ別設定機能を統合。

### 🎯 求職者プロファイル機能

**履歴書・職務経歴書の統合**
- PDF/Word形式の履歴書アップロード・解析
- 個人情報・スキル・経験の自動抽出
- AI面接官の事前準備・個別化質問生成
- 経験レベル・業界背景に基づく適応調整

**データ構造**
```typescript
interface CandidateProfile {
  personalInfo: { name, email, location };
  resume: { 
    fileId, fileName, uploadedAt,
    parsedContent: {
      summary, skills, experience, education, achievements 
    }
  };
  experience: {
    level: 'entry' | 'mid' | 'senior' | 'executive';
    totalYears, currentRole, previousRoles, skills
  };
}
```

### 🎭 面接官役割・ステップ設定

**面接官役割の定義**
- **HR（人事）**: 会社概要、基本適性、文化適合性重視
- **CEO（社長）**: ビジョン、長期目標、リーダーシップ重視  
- **Team Leader（チームリーダー）**: 技術スキル、協調性、実務経験重視
- **Technical Lead（技術責任者）**: 専門技術、アーキテクチャ、問題解決重視

**役割別特性**
```typescript
interface InterviewerRole {
  personality: {
    formality: 'casual' | 'formal' | 'balanced';
    supportiveness: 'encouraging' | 'challenging';
    pace: 'slow' | 'normal' | 'fast';
  };
  questionStyle: {
    behavioralFocus: number;  // 行動面接重視度
    technicalFocus: number;   // 技術面接重視度
    visionaryFocus: number;   // ビジョン質問重視度
  };
}
```

### 🔄 統合ワークフロー（改訂版）

```
1. 求職者プロファイル作成
   - 履歴書アップロード → 自動解析
   - 個人情報・経験データ抽出

2. 企業・職種選択
   - 既存企業サジェスト or 新規作成
   - 企業情報との照合・差分検出

3. 面接設定
   - 面接官役割選択（HR/CEO/Team Leader等）
   - 面接ステップ設定（1次/2次/最終面接）
   - 役割別評価基準の自動設定

4. AI面接官準備
   - 候補者プロファイル + 面接官役割の統合
   - 個別化質問の動的生成
   - 役割に応じた話し方・評価軸の調整

5. 面接実施
   - 個人の経験を踏まえた深掘り質問
   - 役割別の面接スタイル実現
   - リアルタイム適応調整

6. 結果分析・フィードバック
   - 個人背景を考慮した改善提案
   - 面接官視点に基づく評価
   - 次ステップの面接準備
```

### 📊 データ期限管理の拡張

**柔軟な期限設定**
- 基本期限: 各リンクの設定期限
- エージェント更新: リンク期限の延長可能
- 企業別期限: 複数企業練習時の最長期限を適用
- 候補者プロファイル: より長期の保持（求職活動期間中）

**期限計算ロジック**
```typescript
// リンク期限 = max(基本期限, エージェント更新期限, 企業別最長期限)
interface LinkExpirationLogic {
  baseExpiration: Date;           // 基本設定期限
  agentUpdatedExpiration?: Date;  // エージェント延長期限  
  longestCompanyExpiration?: Date; // 他企業の最長期限
  candidateProfileExpiration: Date; // プロファイル期限
  
  // 実際の期限 = これらの最大値
  effectiveExpiration: Date;
}
```

### 💡 Phase 1 実装計画

**優先度: 高**
1. CandidateProfile型定義とデータベース設計
2. 履歴書アップロード・解析サービス実装
3. DocumentUploaderコンポーネントの拡張
4. 個人化質問生成ロジックの統合

**優先度: 中**  
5. InterviewerRole型定義とテンプレート作成
6. 面接官役割選択UIコンポーネント
7. 役割別質問生成・評価システム
8. データ期限管理の拡張実装

### 🛡️ プライバシー・セキュリティの強化

**個人情報保護**
- 履歴書の暗号化保存
- アクセスログの詳細記録
- 同意管理システムの実装
- データ匿名化オプション

**期限管理の精密化**
- 候補者プロファイル: 求職活動期間（通常6ヶ月）
- 面接結果: 企業別設定（30-365日）
- リンク有効期限: 柔軟な延長システム
- 自動削除の事前通知機能

## 🛠️ バックエンド開発タスク (2025年6月15日追加)

### 現状の課題
バックエンドは`src/fastapi/`ディレクトリのFastAPI実装に統一されました。今後の課題は次の通りです：
- データベース（すべてハードコード）
- 認証・認可システム
- セッション管理
- WebSocketサポート（WebSocketは依存関係にあるが未使用）
- エラーログ・監視システム

### Phase 1: 基盤整備（優先度: 高）
- [ ] **Azure Avatar API統合**
  - Azure Avatar Service の完全実装
  - WebRTC接続の安定化とエラーハンドリング強化
  - Avatar, LiveChatAvatar, AdaptiveAvatarコンポーネントの統合
  - リアルタイム音声・映像ストリーミングの最適化

- [ ] **PostgreSQLデータベース導入**
  - SQLAlchemy ORMの設定
  - 基本的なスキーマ設計（users, interviews, feedbacks）
  - マイグレーション環境の構築
  
- [ ] **認証システムの実装**
  - JWT認証の実装
  - ロールベースアクセス制御（候補者/エージェント/面接官）
  - セッション管理
  
- [ ] **エラーハンドリング改善**
  - 統一的なエラーレスポンス形式
  - ログシステムの導入（Winston/Pino）
  - Sentryなどの監視ツール統合

### Phase 2: 機能拡張（優先度: 中）
- [ ] **WebSocketリアルタイム通信**
  - WebSocketの本格実装
  - 面接進捗のリアルタイム同期
  - WebRTCシグナリングサーバー
  
- [ ] **ファイルストレージ改善**
  - Azure Storage統合
  - 音声ファイルの永続化
  - CDN配信の検討
  
- [ ] **APIドキュメント整備**
  - OpenAPI/Swagger仕様書作成
  - APIバージョニング戦略

### Phase 3: 高度な機能（優先度: 中〜低）
- [ ] **AI分析エンジン統合**
  - Azure Cognitive Services完全統合
  - カスタム評価モデルの実装
  - バッチ処理システム
  
- [ ] **レポート生成システム**
  - PDF/Excel出力機能
  - 定期レポートの自動生成
  - メール通知システム

### Phase 4: スケーラビリティ（優先度: 低）
- [ ] **マイクロサービス化**
  - APIゲートウェイ
  - サービス分割（auth, interview, analytics）
  - メッセージキューの導入
  
- [ ] **パフォーマンス最適化**
  - Redisキャッシュ導入
  - データベースインデックス最適化
  - 負荷分散の実装

### 開発環境の改善
- [ ] **テスト環境の構築**
  - 単体テスト・統合テストの整備
  - CI/CDパイプラインの構築
  - ステージング環境の用意

## 📚 関連ドキュメント

- [CLAUDE.md](./CLAUDE.md) - Claude Code向け技術仕様
- [CODING_GUIDELINES.md](./CODING_GUIDELINES.md) - コーディング規約
- [UIUX_DESIGN_GUIDLINES.md](./UIUX_DESIGN_GUIDLINES.md) - UI/UXガイドライン
- [IMPROVEMENT_PLAN_V4.md](./IMPROVEMENT_PLAN_V4.md) - システム改善計画

## 🤝 コントリビューション

1. Featureブランチを作成 (`git checkout -b feature/amazing-feature`)
2. 変更をコミット (`git commit -m 'Add some amazing feature'`)
3. ブランチをプッシュ (`git push origin feature/amazing-feature`)
4. プルリクエストを作成

## 📄 ライセンス

本プロジェクトは商用ライセンスです。詳細はMafumo Inc.にお問い合わせください。

## 📞 サポート

- 技術的な質問: [GitHub Issues](https://github.com/Mafumo-Inc/mensetsu-kun/issues)
- ビジネスに関するお問い合わせ: <EMAIL># test
