# dependencies
node_modules/
.pnp
.pnp.js

# testing
coverage/

# next.js
.next/
out/

# production
build/
dist/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# uploads
uploads/

# IDE
.idea/
.vscode/

src/fastapi/data/
src/fastapi/.mplconfig/*
.cursor/*
*.tsbuildinfo

./candidates/*
candidates/