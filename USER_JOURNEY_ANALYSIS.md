# Management App User Journey Analysis

## Current State Analysis

### Tab Structure (in index.tsx)

The management app has 6 main tabs:

1. **ダッシュボード (Dashboard)**
   - Contains: `AgentDashboard` component
   - Quick Actions: Links to other tabs

2. **リンク作成 (Link Creation)**
   - Contains: `IntegratedLinkWorkflow` component
   - This is the main workflow for creating interview links

3. **リンク管理 (Link Management)**
   - Contains: `LinkManager` component
   - For managing existing links

4. **分析結果 (Analysis Results)**
   - Contains: `FeedbackViewer` component
   - For viewing interview feedback

5. **質問管理 (Question Management)**
   - Contains: `InterviewIntentConfig` and `QuestionScriptGenerator` components
   - For managing interview questions

6. **資料管理 (Document Management)**
   - Contains: `DocumentUploader` component
   - For uploading company documents

### Current User Journey for Creating Interview Links

When an HR agent accesses "/" and creates an interview link, they go through the **IntegratedLinkWorkflow** component, which has 4 steps:

1. **Step 1: Document Upload & Company Selection**
   - Upload PDF/documents
   - Enter company name (with suggestions)
   - Enter position name (with suggestions)
   - Option to create new company if not found

2. **Step 2: Intelligent Question Generation**
   - Choose between enhanced mode (intelligent) or legacy mode
   - Can select from existing templates
   - AI generates questions based on uploaded documents

3. **Step 3: Question Confirmation & Editing**
   - Review generated questions
   - Edit intents if needed
   - Option to save as template

4. **Step 4: Link Settings & Generation**
   - Enter candidate name and email
   - Set expiration date
   - Add agent notes
   - Generate the link

## Missing Features & Integration Issues

### 1. Interviewer Role Selection (7 Role Types)

**Status**: ❌ NOT INTEGRATED in main workflow

The 7 interviewer roles are defined in `/src/shared/types/interviewer-roles.ts`:
- HR (人事)
- CEO (社長・CEO)
- Team Leader (チームリーダー・部門長)
- Technical Lead (技術責任者・技術リーダー)
- Peer (同僚・メンバー)
- Senior Manager (上級管理職)
- External (外部面接官)

**Where it's used**:
- ✅ Imported in `MeetingLinkGenerator.tsx`
- ✅ Used within an accordion panel (collapsed by default)
- ❌ NOT used in `IntegratedLinkWorkflow.tsx` (the main link creation flow)

**Problem**: Users going through the main "リンク作成" tab workflow never see the role selection UI.

### 2. Multi-Stage Interview Setup

**Status**: ❌ NOT INTEGRATED anywhere

The `MultiStageInterviewSetup` component exists but is:
- ❌ Not imported in `index.tsx`
- ❌ Not used in `IntegratedLinkWorkflow`
- ❌ Not accessible through any UI path

### 3. Role-Based Features in MeetingLinkGenerator

**Status**: ⚠️ PARTIALLY AVAILABLE but not in main flow

The `MeetingLinkGenerator` component has:
- ✅ InterviewerRoleSelector integration
- ✅ CandidateProfileInput integration
- ⚠️ But it's in the "質問管理" tab, not the main link creation flow
- ⚠️ Features are behind a toggle switch (disabled by default)

### 4. Intelligent Question Generation

**Status**: ✅ AVAILABLE in main flow

The intelligent question generation is properly integrated:
- ✅ Available in Step 2 of `IntegratedLinkWorkflow`
- ✅ Toggle between enhanced/legacy mode
- ✅ Shows generated questions with psychological safety considerations

## Root Cause Analysis

The main issue is that there are **TWO SEPARATE** link generation workflows:

1. **Main Workflow**: `IntegratedLinkWorkflow` (in "リンク作成" tab)
   - This is what users see when creating links
   - Missing: Role selection, multi-stage setup

2. **Secondary Workflow**: `MeetingLinkGenerator` (in "質問管理" tab)
   - Has role selection features
   - But users don't naturally navigate here for link creation

## Recommendations

### Option 1: Enhance IntegratedLinkWorkflow
Add the missing features to the main workflow:
- Add InterviewerRoleSelector to Step 1 or 2
- Add option for multi-stage interview setup
- Integrate candidate profile features

### Option 2: Consolidate Workflows
- Remove duplication between IntegratedLinkWorkflow and MeetingLinkGenerator
- Create a single, comprehensive link creation workflow
- Ensure all features are accessible

### Option 3: Progressive Enhancement
- Keep basic flow simple
- Add "Advanced Options" section in IntegratedLinkWorkflow
- Include role selection, multi-stage setup as optional features

## Quick Fix Suggestions

To make the 7 role types immediately visible:

1. **Add to Step 1 of IntegratedLinkWorkflow**:
   ```tsx
   // After company/position selection
   <InterviewerRoleSelector
     onRoleSelected={setSelectedRole}
     allowCustomization={true}
   />
   ```

2. **Or add a new step** between current Step 1 and 2:
   - Step 1.5: "面接官役割選択" (Interviewer Role Selection)

3. **Make it visible in the UI**:
   - Remove the accordion/toggle that hides these features
   - Make role selection a required or prominently displayed option