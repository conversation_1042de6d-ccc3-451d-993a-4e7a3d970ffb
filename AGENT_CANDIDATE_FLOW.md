# エージェント-求職者連携フロー設計書

## システム概要

### 非同期型面接システムの利点
- **時間の自由度**: 求職者は任意のタイミングで面接を受験可能
- **場所の制約なし**: リモート環境での面接実施
- **複数企業対応**: 1つのプラットフォームで複数企業の面接を管理
- **エージェント効率化**: リアルタイム監視不要、結果確認は後日

---

## 完全フロー設計

### Phase 1: 面接準備（エージェント側）

```mermaid
sequenceDiagram
    participant Agent as エージェント
    participant System as システム
    participant Email as メール送信
    participant DB as データベース

    Agent->>System: 1. ログイン・認証
    Agent->>System: 2. 企業情報入力
    Note over Agent,System: 企業名、ポジション、要件
    System->>DB: 3. 面接設定保存
    Agent->>System: 4. 質問スクリプト生成
    System->>DB: 5. 質問データ保存
    Agent->>System: 6. ミーティングリンク生成
    System->>DB: 7. リンク情報保存
    System->>Email: 8. 候補者にリンク送信
    System->>Agent: 9. リンク発行完了通知
```

**エージェント操作手順:**
1. 管理画面にログイン
2. 「新規面接作成」から企業情報入力
3. AI質問生成で面接スクリプト作成
4. 候補者情報入力してミーティングリンク発行
5. 自動メール送信 or 手動でリンク共有

### Phase 2: 面接実施（求職者側）

```mermaid
sequenceDiagram
    participant Candidate as 求職者
    participant Browser as ブラウザ
    participant System as システム
    participant AI as AI分析エンジン
    participant DB as データベース

    Candidate->>Browser: 1. ミーティングリンクアクセス
    Browser->>System: 2. トークン認証
    System->>DB: 3. 面接データ取得
    System->>Candidate: 4. 面接画面表示
    Candidate->>System: 5. 面接開始
    
    loop 各質問について
        System->>Candidate: 質問表示
        Candidate->>System: 音声 + テキスト回答
        System->>AI: リアルタイム音声解析
        AI->>System: 分析結果返却
        System->>DB: 回答・分析結果保存
        System->>Candidate: 即座にフィードバック表示
    end
    
    Candidate->>System: 6. 面接完了
    System->>DB: 7. 完了ステータス更新
    System->>Candidate: 8. 完了画面・感謝メッセージ
```

**求職者体験フロー:**
1. メールのリンクをクリック
2. ブラウザで面接画面が開く
3. 企業情報・ポジション確認
4. デバイスチェック（マイク・カメラ）
5. 質問に順次回答（音声録音 + テキスト）
6. 即座にAIフィードバック確認
7. 面接完了・お疲れ様画面

### Phase 3: 結果確認（エージェント側）

```mermaid
sequenceDiagram
    participant System as システム
    participant Agent as エージェント
    participant Email as メール通知
    participant Client as クライアント企業

    System->>Email: 1. 面接完了通知送信
    Agent->>System: 2. 管理画面アクセス
    System->>Agent: 3. フィードバック一覧表示
    Agent->>System: 4. 詳細レポート閲覧
    Agent->>System: 5. PDF/Excel エクスポート
    Agent->>Client: 6. 結果共有・提案
```

**エージェント確認手順:**
1. 面接完了のメール通知受信
2. 管理画面でフィードバック一覧確認
3. 個別の詳細分析レポート閲覧
4. レポートのPDF/Excel出力
5. クライアント企業への結果共有

---

## 技術実装設計

### 1. トークンベース認証システム

#### ミーティングリンク構造
```
https://mensetsu-kun.com/interview?token=eyJ0eXAiOiJKV1QiLCJhbGci...

トークン内容:
{
  "interviewId": "uuid",
  "candidateEmail": "<EMAIL>", 
  "companyName": "株式会社テクノロジー",
  "expiresAt": "2024-06-21T10:00:00Z",
  "permissions": ["answer", "view_feedback"]
}
```

#### セキュリティ対策
- トークン有効期限: デフォルト7日
- 1回限りアクセス or 複数回許可（設定可能）
- IPアドレス制限（オプション）
- ブラウザセッション管理

### 2. 面接セッション管理

#### 状態遷移
```typescript
type InterviewStatus = 
  | 'created'     // 面接作成済み
  | 'link_sent'   // リンク送信済み  
  | 'accessed'    // 候補者アクセス済み
  | 'in_progress' // 面接実施中
  | 'completed'   // 完了
  | 'expired'     // 期限切れ
  | 'cancelled';  // キャンセル

type QuestionStatus = 
  | 'pending'     // 未回答
  | 'answered'    // 回答済み
  | 'skipped';    // スキップ
```

#### プログレス管理
```typescript
interface InterviewProgress {
  totalQuestions: number;
  answeredQuestions: number;
  currentQuestionIndex: number;
  estimatedTimeRemaining: number; // 秒
  startedAt: string;
  lastActivityAt: string;
}
```

### 3. リアルタイムフィードバック

#### AI分析パイプライン
```typescript
// 音声解析フロー
const analyzeAnswerPipeline = async (
  audioFile: File, 
  textAnswer: string,
  questionContext: Question
): Promise<InstantFeedback> => {
  
  // 1. 音声転写（Azure Speech to Text）
  const transcription = await speechToText(audioFile);
  
  // 2. 感情分析（Azure Text Analytics）
  const emotions = await analyzeEmotions(transcription);
  
  // 3. 回答評価（GPT-4 + 企業固有プロンプト）
  const evaluation = await evaluateAnswer(
    transcription, 
    textAnswer, 
    questionContext
  );
  
  // 4. スコア算出
  const scores = calculateScores(emotions, evaluation);
  
  // 5. 即座にフィードバック生成
  return generateInstantFeedback(scores, evaluation);
};
```

### 4. データ永続化戦略

#### ローカルストレージ活用
```typescript
// 面接中断・再開サポート
interface SessionStorage {
  interviewId: string;
  currentQuestionIndex: number;
  answers: Answer[];
  startTime: string;
  autoSaveInterval: number;
}

// 5秒毎に自動保存
setInterval(() => {
  saveToLocalStorage(getCurrentState());
}, 5000);
```

---

## ユーザー体験設計

### 1. エージェント用管理画面の改善

#### ダッシュボード機能拡張
```typescript
interface AgentDashboard {
  // 進行中の面接
  activeInterviews: {
    id: string;
    candidateName: string;
    companyName: string;
    status: InterviewStatus;
    progress: number; // 0-100%
    timeRemaining: string;
  }[];
  
  // 完了済み面接
  completedInterviews: {
    id: string;
    candidateName: string;
    overallScore: number;
    completedAt: string;
    reportReady: boolean;
  }[];
  
  // 統計情報
  statistics: {
    totalInterviews: number;
    averageScore: number;
    completionRate: number;
    averageDuration: number;
  };
}
```

#### リアルタイム通知システム
```typescript
// WebSocket または Server-Sent Events
interface AgentNotification {
  type: 'interview_started' | 'interview_completed' | 'link_expired';
  interviewId: string;
  candidateName: string;
  timestamp: string;
  message: string;
}
```

### 2. 候補者体験の最適化

#### アクセシビリティ対応
- 音声入力が困難な場合のテキストのみモード
- 視覚障害者向けスクリーンリーダー対応
- 多言語サポート（日本語・英語）

#### デバイス互換性
- スマートフォン対応（縦画面最適化）
- タブレット対応
- 低スペックPCでの動作保証

#### 中断・再開機能
```typescript
interface InterruptionHandling {
  // ネットワーク切断対応
  autoReconnect: boolean;
  maxReconnectAttempts: number;
  
  // 面接中断対応  
  allowPause: boolean;
  maxPauseDuration: number; // 分
  
  // ローカル保存
  autoSaveAnswers: boolean;
  saveInterval: number; // 秒
}
```

---

## エラーハンドリング・例外処理

### 1. 一般的なエラーケース

#### トークン関連エラー
```typescript
interface TokenError {
  code: 'TOKEN_EXPIRED' | 'TOKEN_INVALID' | 'TOKEN_USED';
  message: string;
  action: 'contact_agent' | 'request_new_link' | 'try_again';
}
```

#### ネットワークエラー
```typescript
interface NetworkError {
  code: 'CONNECTION_LOST' | 'UPLOAD_FAILED' | 'TIMEOUT';
  message: string;
  retryable: boolean;
  retryAfter?: number; // 秒
}
```

### 2. 復旧フロー

#### 自動復旧
- ネットワーク再接続時の自動同期
- 未送信回答の自動アップロード
- セッション復元機能

#### 手動復旧
- エージェントによるトークン再発行
- 面接データの手動復旧
- サポート担当者によるデータ修復

---

## メール通知テンプレート

### 1. 候補者向けメール

#### 面接招待メール
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>面接のご案内 - 面接くん</title>
</head>
<body>
    <h2>{{candidateName}}様</h2>
    
    <p>{{companyName}}の{{position}}ポジションの面接にご応募いただき、ありがとうございます。</p>
    
    <p>AI面接システム「面接くん」を使用した面接を実施いたします。</p>
    
    <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">
        <h3>面接詳細</h3>
        <ul>
            <li>企業名: {{companyName}}</li>
            <li>ポジション: {{position}}</li>
            <li>予想所要時間: {{estimatedDuration}}分</li>
            <li>有効期限: {{expiresAt}}</li>
        </ul>
    </div>
    
    <p><strong>面接開始リンク:</strong></p>
    <p><a href="{{interviewUrl}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none;">面接を開始する</a></p>
    
    <h3>準備事項</h3>
    <ul>
        <li>安定したインターネット接続</li>
        <li>マイク・スピーカー（またはヘッドセット）</li>
        <li>静かな環境</li>
        <li>Chrome、Firefox、Safari等の最新ブラウザ</li>
    </ul>
    
    <p>ご質問がございましたら、担当エージェントまでお気軽にお問い合わせください。</p>
    
    <p>{{agentName}}<br>{{agentEmail}}</p>
</body>
</html>
```

### 2. エージェント向け通知

#### 面接完了通知
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>面接完了通知 - 面接くん</title>
</head>
<body>
    <h2>面接完了のお知らせ</h2>
    
    <p>{{candidateName}}様の面接が完了しました。</p>
    
    <div style="background: #e8f5e8; padding: 15px; margin: 15px 0;">
        <h3>面接結果サマリー</h3>
        <ul>
            <li>候補者: {{candidateName}}</li>
            <li>企業: {{companyName}}</li>
            <li>ポジション: {{position}}</li>
            <li>実施時間: {{actualDuration}}分</li>
            <li>総合スコア: {{overallScore}}/10</li>
            <li>回答完了率: {{completionRate}}%</li>
        </ul>
    </div>
    
    <p><a href="{{dashboardUrl}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none;">詳細レポートを確認する</a></p>
    
    <h3>次のステップ</h3>
    <ol>
        <li>管理画面で詳細分析レポートを確認</li>
        <li>必要に応じてPDF/Excelレポートをダウンロード</li>
        <li>クライアント企業への結果共有</li>
    </ol>
</body>
</html>
```

---

## パフォーマンス最適化

### 1. フロントエンド最適化

#### 音声処理最適化
```typescript
// 音声圧縮・最適化
const optimizeAudioFile = async (file: File): Promise<File> => {
  // 1. ファイルサイズ制限
  if (file.size > 10 * 1024 * 1024) { // 10MB
    throw new Error('音声ファイルが大きすぎます');
  }
  
  // 2. 形式変換（WebM → MP3）
  const compressed = await compressAudio(file, {
    bitrate: 128, // kbps
    sampleRate: 44100,
    channels: 1 // モノラル
  });
  
  return compressed;
};

// プログレッシブアップロード
const uploadWithProgress = async (
  file: File,
  onProgress: (progress: number) => void
): Promise<void> => {
  const chunkSize = 1024 * 1024; // 1MB chunks
  const totalChunks = Math.ceil(file.size / chunkSize);
  
  for (let i = 0; i < totalChunks; i++) {
    const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
    await uploadChunk(chunk, i);
    onProgress((i + 1) / totalChunks * 100);
  }
};
```

### 2. バックエンド最適化

#### データベースインデックス
```sql
-- パフォーマンス重要クエリ用インデックス
CREATE INDEX idx_interviews_agent_status ON interviews(agent_id, status);
CREATE INDEX idx_answers_interview_created ON answers(interview_id, answered_at);
CREATE INDEX idx_feedbacks_overall_rating ON feedbacks(overall_rating);
CREATE INDEX idx_meeting_links_token_status ON meeting_links(token, status);
```

#### キャッシュ戦略
```typescript
// Redis キャッシュ活用
interface CacheStrategy {
  // 面接データ（5分間キャッシュ）
  interviewData: TTL_5_MINUTES;
  
  // AI分析結果（1時間キャッシュ）
  analysisResults: TTL_1_HOUR;
  
  // 統計データ（30分キャッシュ）
  statistics: TTL_30_MINUTES;
}
```

---

## セキュリティ・プライバシー

### 1. データ保護
- 音声ファイルの暗号化保存
- 個人情報の仮名化処理
- GDPR準拠のデータ削除機能

### 2. アクセス制御
- ロールベース権限管理
- API レート制限
- 不正アクセス検知

### 3. 監査ログ
```typescript
interface AuditLog {
  timestamp: string;
  userId: string;
  action: 'login' | 'interview_access' | 'data_export' | 'data_delete';
  resource: string;
  ipAddress: string;
  userAgent: string;
  result: 'success' | 'failure';
}
```

この連携フロー設計により、エージェントと求職者双方にとって効率的で使いやすい非同期面接システムが実現できます。