services:
  backend:
    build:
      context: ../src/fastapi
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - ../src/fastapi:/app
      - ../src/fastapi/data:/app/data
    env_file:
      - ../.env
    environment:
      - DATABASE_URL=**************************************/mensetsu_db
      - REDIS_URL=redis://redis:6379
      - CHROMA_URL=http://chroma:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    networks:
      - app-network
  voicevox:
    image: voicevox/voicevox_engine:latest
    ports:
      - "50021:50021"
    volumes:
      - voicevox_data:/root/.local/share/voicevox-engine
    networks:
      - app-network


volumes:
  voicevox_data:

networks:
  app-network:
    driver: bridge
