#!/usr/bin/env bash
set -e

echo "🚀 Mensetsu-kun セットアップを開始します..."

# 環境変数の設定
if [ -f .env.example ] && [ ! -f .env ]; then
  echo "📄 .env.example から .env を作成中..."
  cp .env.example .env
fi

# pnpmのインストール確認
if ! command -v pnpm >/dev/null 2>&1; then
  echo "📦 pnpm をインストール中..."
  npm install -g pnpm@latest
else
  echo "✅ pnpm は既にインストールされています ($(pnpm --version))"
fi

# pnpm workspacesで依存関係をインストール
echo "📦 依存関係をインストール中..."
pnpm install

# Dockerの確認（オプション）
if command -v docker >/dev/null 2>&1; then
  echo "✅ Docker は既にインストールされています"
else
  echo "⚠️  Docker が見つかりません。インフラ機能は利用できません"
fi

echo "✅ セットアップ完了！"
echo ""
echo "▶ 開発サーバーの起動:"
echo "  pnpm dev:all        # すべてのサービスを起動"
echo "  pnpm dev:frontend   # 求職者画面のみ"
echo "  pnpm dev:management # 管理画面のみ"
echo "  pnpm dev:backend    # バックエンドのみ"