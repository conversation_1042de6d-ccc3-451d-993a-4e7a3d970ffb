# API仕様書 - 面接くん

## アーキテクチャ概要

### システム構成
- **候補者アプリ**: 面接受験用フロントエンド
- **管理アプリ**: エージェント用管理画面
- **バックエンドAPI**: 共通データベースアクセス層
- **音声解析AI**: Azure Speech Services統合

### データフロー（非同期型）
1. エージェント → 質問スクリプト生成・ミーティングリンク発行
2. 候補者 → リンクアクセス・面接実施（任意タイミング）
3. システム → 回答データ・AI分析結果をDB保存
4. エージェント → 結果確認（面接完了後）

---

## データベーススキーマ

### 1. Users (ユーザー管理)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  role ENUM('agent', 'candidate', 'interviewer') NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. Interviews (面接セッション)
```sql
CREATE TABLE interviews (
  id UUID PRIMARY KEY,
  candidate_id UUID REFERENCES users(id),
  agent_id UUID REFERENCES users(id),
  company_name VARCHAR(255),
  position VARCHAR(255),
  requirements TEXT[],
  status ENUM('pending', 'in_progress', 'completed', 'expired') DEFAULT 'pending',
  interview_type ENUM('behavioral', 'technical', 'case', 'mixed') DEFAULT 'behavioral',
  estimated_duration INTEGER DEFAULT 900, -- 秒
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. Questions (質問管理)
```sql
CREATE TABLE questions (
  id UUID PRIMARY KEY,
  interview_id UUID REFERENCES interviews(id),
  text TEXT NOT NULL,
  category VARCHAR(100),
  difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
  estimated_time INTEGER DEFAULT 180, -- 秒
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 4. Answers (回答データ)
```sql
CREATE TABLE answers (
  id UUID PRIMARY KEY,
  question_id UUID REFERENCES questions(id),
  candidate_answer TEXT,
  audio_file_url VARCHAR(500),
  transcription TEXT,
  duration INTEGER, -- 秒
  answered_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Feedbacks (AI分析結果)
```sql
CREATE TABLE feedbacks (
  id UUID PRIMARY KEY,
  answer_id UUID REFERENCES answers(id),
  ai_analysis TEXT NOT NULL,
  emotion_score DECIMAL(3,2), -- 0.00-10.00
  confidence_score DECIMAL(3,2),
  relevance_score DECIMAL(3,2),
  clarity_score DECIMAL(3,2),
  overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
  suggestions TEXT[],
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 6. Meeting_Links (ミーティングリンク管理)
```sql
CREATE TABLE meeting_links (
  id UUID PRIMARY KEY,
  interview_id UUID REFERENCES interviews(id),
  token VARCHAR(255) UNIQUE NOT NULL,
  url VARCHAR(500) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  status ENUM('active', 'used', 'expired') DEFAULT 'active',
  accessed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

---

## API エンドポイント

### 管理アプリ用API

#### 1. 質問スクリプト生成
```http
POST /api/management/question-scripts
Content-Type: application/json

{
  "companyName": "株式会社テクノロジー",
  "position": "フロントエンドエンジニア",
  "requirements": ["React経験3年以上", "TypeScript実務経験"],
  "interviewType": "behavioral",
  "estimatedDuration": 900
}

Response:
{
  "interviewId": "uuid",
  "questions": [
    {
      "id": "uuid",
      "text": "これまでの経験で最も困難だった課題は？",
      "category": "経験・スキル",
      "difficulty": "medium",
      "estimatedTime": 180,
      "orderIndex": 1
    }
  ]
}
```

#### 2. ミーティングリンク生成
```http
POST /api/management/meeting-links
Content-Type: application/json

{
  "interviewId": "uuid",
  "candidateName": "田中太郎",
  "candidateEmail": "<EMAIL>",
  "expiresInDays": 7
}

Response:
{
  "linkId": "uuid",
  "url": "https://mensetsu-kun.com/interview?token=abc123",
  "expiresAt": "2024-06-21T10:00:00Z",
  "status": "active"
}
```

#### 3. フィードバック一覧取得
```http
GET /api/management/feedbacks?agentId={agentId}&status=completed&limit=50

Response:
{
  "feedbacks": [
    {
      "interviewId": "uuid",
      "candidateName": "田中太郎",
      "position": "フロントエンドエンジニア",
      "completedAt": "2024-06-14T15:30:00Z",
      "overallScore": 8.2,
      "questionCount": 5,
      "answers": [
        {
          "questionText": "困難だった課題について",
          "candidateAnswer": "レガシーシステムの刷新で...",
          "feedback": {
            "aiAnalysis": "具体的で構造化された回答",
            "scores": {
              "emotion": 8.2,
              "confidence": 7.5,
              "relevance": 9.0,
              "clarity": 8.3
            },
            "overallRating": 4,
            "suggestions": ["より具体的な数値があると良い"]
          }
        }
      ]
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 50
  }
}
```

### 候補者アプリ用API

#### 1. 面接セッション開始
```http
GET /api/candidate/interview?token={meetingToken}

Response:
{
  "interviewId": "uuid",
  "companyName": "株式会社テクノロジー",
  "position": "フロントエンドエンジニア",
  "estimatedDuration": 900,
  "questions": [
    {
      "id": "uuid",
      "text": "自己紹介をお願いします",
      "category": "基本情報",
      "estimatedTime": 180,
      "orderIndex": 1
    }
  ],
  "status": "pending"
}
```

#### 2. 回答送信
```http
POST /api/candidate/answers
Content-Type: multipart/form-data

{
  "questionId": "uuid",
  "textAnswer": "私は3年間フロントエンド開発に...",
  "audioFile": File, // 音声ファイル（オプション）
  "duration": 120
}

Response:
{
  "answerId": "uuid",
  "transcription": "私は3年間フロントエンド開発に...",
  "feedback": {
    "aiAnalysis": "明確で構造化された回答です",
    "scores": {
      "emotion": 8.2,
      "confidence": 7.5,
      "relevance": 9.0,
      "clarity": 8.3
    },
    "overallRating": 4,
    "suggestions": ["具体的な成果を含めると更に良い"]
  }
}
```

#### 3. 面接完了
```http
POST /api/candidate/interview/{interviewId}/complete

Response:
{
  "interviewId": "uuid",
  "completedAt": "2024-06-14T15:30:00Z",
  "totalDuration": 720,
  "overallScore": 8.2,
"message": "面接が完了しました。結果はエージェントに共有されます。"
}
```

#### 4. 候補者詳細取得
```http
GET /api/candidate/profile/{candidateId}

Response:
{
  "id": "candidate-001",
  "type": "中途",
  "position": "ソフトウェアエンジニア",
  "industry": "IT",
  "experienceLevel": "mid",
  "yearsOfExperience": 5,
  "previousIndustry": "IT",
  "targetIndustry": "IT",
  "specializations": ["React", "TypeScript", "Node.js"]
}
```

### 共通API

#### 1. 面接データ取得
```http
GET /api/shared/interview/{interviewId}

Response:
{
  "id": "uuid",
  "candidateName": "田中太郎",
  "companyName": "株式会社テクノロジー",
  "position": "フロントエンドエンジニア",
  "status": "completed",
  "questions": [...],
  "answers": [...],
  "feedbacks": [...],
  "timestamps": {
    "created": "2024-06-14T10:00:00Z",
    "started": "2024-06-14T14:00:00Z",
    "completed": "2024-06-14T15:30:00Z"
  }
}
```

---

## 音声解析API

### Azure Speech Services統合
```http
POST /api/audio/analyze
Content-Type: multipart/form-data

{
  "audioFile": File,
  "language": "ja-JP"
}

Response:
{
  "transcription": "転写されたテキスト",
  "confidence": 0.95,
  "emotions": {
    "happy": 0.8,
    "neutral": 0.6,
    "surprised": 0.3
  },
  "speechQuality": {
    "clarity": 8.5,
    "pace": 7.8,
    "volume": 8.0
  }
}
```

---

## エラーハンドリング

### HTTPステータスコード
- `200` - 成功
- `201` - 作成成功
- `400` - リクエストエラー
- `401` - 認証エラー
- `403` - 権限エラー
- `404` - リソース未発見
- `409` - 競合エラー
- `500` - サーバーエラー

### エラーレスポンス形式
```json
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "無効なミーティングトークンです",
    "details": {
      "token": "abc123",
      "reason": "expired"
    }
  }
}
```

---

## セキュリティ

### 認証・認可
- エージェント: JWT認証
- 候補者: ミーティングトークン認証（一時的）
- API Key: 内部API間通信

### データ保護
- 音声ファイル: 暗号化保存
- 個人情報: GDPR/個人情報保護法準拠
- トークン: 有効期限付き（デフォルト7日）

---

## 非同期処理

### 音声解析フロー
1. 候補者 → 音声ファイルアップロード
2. サーバー → バックグラウンドで音声解析実行
3. 結果 → データベース保存
4. エージェント → 完了後に結果確認

### 通知システム
- エージェント: 面接完了時にメール通知
- 候補者: 面接リンク送信時にメール通知