import { readFile } from 'node:fs/promises';
import ts from './src/candidate/node_modules/typescript/lib/typescript.js';

export async function resolve(specifier, context, defaultResolve) {
  if (specifier.endsWith('.ts') || specifier.endsWith('.tsx')) {
    return { url: new URL(specifier, context.parentURL).href, shortCircuit: true };
  }
  return defaultResolve(specifier, context, defaultResolve);
}

export async function load(url, context, defaultLoad) {
  if (url.endsWith('.ts') || url.endsWith('.tsx')) {
    const source = await readFile(new URL(url), 'utf8');
    const { outputText } = ts.transpileModule(source, {
      compilerOptions: {
        module: ts.ModuleKind.ES2022,
        jsx: ts.JsxEmit.React,
        target: ts.ScriptTarget.ES2022
      },
      fileName: url
    });
    return { format: 'module', source: outputText, shortCircuit: true };
  }
  return defaultLoad(url, context, defaultLoad);
}
