# 面接官側練習機能 基盤設計書

## 機能概要

### 目的
面接官（企業の採用担当者・人事・現場責任者）がAI面接システムを活用して自身の面接スキルを向上させるための練習機能。

### 対象ユーザー
- **企業の面接官**: 人事担当者、採用マネージャー、技術責任者
- **エージェント**: 面接官向けトレーニングサービス提供者
- **新任面接官**: 面接経験の浅い社員

---

## システム構成

### 1. 三者連携システム

```mermaid
graph TB
    subgraph "エージェント"
        A1[質問設計]
        A2[シナリオ作成]
        A3[面接官管理]
        A4[練習結果分析]
    end

    subgraph "面接官"
        I1[練習申込]
        I2[模擬面接実施]
        I3[フィードバック確認]
        I4[スキル向上]
    end

    subgraph "AIシステム"
        AI1[候補者役AI]
        AI2[面接分析AI]
        AI3[質問評価AI]
        AI4[改善提案AI]
    end

    A1 --> AI1
    A2 --> I2
    I2 --> AI2
    AI2 --> I3
    AI3 --> A4
    AI4 --> I4
```

### 2. 既存システムとの統合

#### 候補者システムの流用
- **質問管理システム**: 同一のQuestion型・API
- **音声解析エンジン**: 面接官の音声分析に活用
- **フィードバックシステム**: 評価ロジックの応用
- **UI/UXコンポーネント**: 共通デザインシステム

#### 新規機能
- **AI候補者エンジン**: 候補者役を演じるAI
- **面接官評価システム**: 質問技術・コミュニケーション分析
- **シナリオ管理**: 業界・職種別練習シナリオ

---

## 機能設計

### 1. 面接官向けダッシュボード

#### 練習メニュー
```typescript
interface InterviewerPracticeMenu {
  // 基本練習
  basicScenarios: {
    id: string;
    title: string;
    description: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    duration: number; // 分
    skills: string[]; // ['質問技術', 'アイスブレイク', '深掘り']
  }[];

  // 業界特化練習
  industryScenarios: {
    industry: string;
    positions: string[];
    scenarios: PracticeScenario[];
  }[];

  // カスタム練習
  customScenarios: {
    companySpecific: boolean;
    questionTypes: string[];
    candidateProfiles: CandidateProfile[];
  };
}
```

#### 進捗管理
```typescript
interface InterviewerProgress {
  totalPractices: number;
  skillScores: {
    questionTechnique: number;      // 質問技術 (0-10)
    activeListening: number;       // 傾聴力
    followUpSkill: number;         // 深掘り技術
    timeManagement: number;        // 時間管理
    biasAwareness: number;         // バイアス認識
    legalCompliance: number;       // 法的配慮
  };
  weakAreas: string[];
  improvementSuggestions: string[];
  nextRecommendedPractice: string;
}
```

### 2. AI候補者システム

#### 候補者ペルソナ生成
```typescript
interface AICandidatePersona {
  // 基本プロファイル
  profile: {
    name: string;
    age: number;
    background: string;
    experience: string;
    personality: 'confident' | 'nervous' | 'talkative' | 'reserved' | 'analytical';
    strengths: string[];
    weaknesses: string[];
  };

  // 回答スタイル
  responseStyle: {
    verbosity: 'concise' | 'detailed' | 'rambling';
    confidence: number; // 0-1
    honesty: number;    // 0-1 (課題を隠すかどうか)
    preparation: number; // 0-1 (準備不足の表現)
  };

  // シナリオ固有設定
  scenarioSettings: {
    hasRelevantExperience: boolean;
    jobFitScore: number; // 0-1
    motivationLevel: number; // 0-1
    challengingAspects: string[]; // 面接官が苦労する要素
  };
}
```

#### AI回答生成エンジン
```typescript
interface AICandidateEngine {
  generateResponse(
    question: string,
    persona: AICandidatePersona,
    context: InterviewContext
  ): Promise<{
    textResponse: string;
    audioResponse?: Blob;
    behavioralCues: {
      pauseLength: number;
      speechSpeed: number;
      confidenceLevel: number;
      eyeContact: number; // 将来のビデオ対応
    };
    hiddenThoughts: string; // 面接官向けヒント
  }>;

  // 動的難易度調整
  adjustDifficulty(
    interviewerSkillLevel: number,
    currentPerformance: number
  ): AICandidatePersona;

  // 特殊シナリオ対応
  triggerChallengingBehavior(
    scenario: 'evasive' | 'overconfident' | 'unprepared' | 'aggressive'
  ): void;
}
```

### 3. 面接官評価システム

#### 質問分析
```typescript
interface QuestionAnalysis {
  // 質問タイプ分類
  questionType: 'open' | 'closed' | 'leading' | 'hypothetical' | 'behavioral';
  
  // 質問品質評価
  quality: {
    clarity: number;        // 0-10 明確性
    relevance: number;      // 0-10 関連性
    legality: number;       // 0-10 法的適切性
    bias: number;          // 0-10 バイアスの少なさ
    depth: number;         // 0-10 深掘り度
  };

  // 改善提案
  suggestions: {
    betterPhrasing?: string;
    followUpQuestions?: string[];
    avoidanceReasons?: string[];
  };

  // ベストプラクティス
  bestPractice: {
    category: string;
    example: string;
    reasoning: string;
  };
}
```

#### 面接フロー分析
```typescript
interface InterviewFlowAnalysis {
  // 時間管理
  timeManagement: {
    totalDuration: number;
    questionDistribution: Record<string, number>;
    rushingIndicators: boolean;
    appropriatePacing: number; // 0-10
  };

  // 構造化面接度
  structure: {
    hasIntroduction: boolean;
    followsPlannedQuestions: boolean;
    consistentDepth: boolean;
    appropriateClosing: boolean;
    score: number; // 0-10
  };

  // 候補者体験
  candidateExperience: {
    waitTimeAfterAnswers: number[];
    interruptionCount: number;
    encouragementGiven: boolean;
    professionalTone: number; // 0-10
  };
}
```

### 4. 学習コンテンツ

#### スキル別トレーニング
```typescript
interface SkillTrainingModule {
  skill: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  
  content: {
    theory: {
      concepts: string[];
      examples: string[];
      commonMistakes: string[];
    };
    
    practice: {
      scenarios: PracticeScenario[];
      exercises: Exercise[];
      assessments: Assessment[];
    };
    
    resources: {
      articles: string[];
      videos: string[];
      templates: string[];
    };
  };
}
```

---

## 実装フェーズ

### Phase 1: 基盤構築（1-2ヶ月）

#### 1.1 データベース拡張
```sql
-- 面接官管理
CREATE TABLE interviewers (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  company_id UUID,
  department VARCHAR(100),
  experience_level ENUM('beginner', 'intermediate', 'advanced'),
  specializations TEXT[],
  created_at TIMESTAMP DEFAULT NOW()
);

-- 練習セッション
CREATE TABLE practice_sessions (
  id UUID PRIMARY KEY,
  interviewer_id UUID REFERENCES interviewers(id),
  scenario_id UUID,
  ai_candidate_persona JSONB,
  status ENUM('scheduled', 'in_progress', 'completed'),
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 面接官評価
CREATE TABLE interviewer_evaluations (
  id UUID PRIMARY KEY,
  session_id UUID REFERENCES practice_sessions(id),
  question_analysis JSONB,
  flow_analysis JSONB,
  overall_score DECIMAL(3,2),
  improvement_areas TEXT[],
  strengths TEXT[],
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 1.2 AI候補者エンジン開発
- OpenAI GPT-4/Claude活用のペルソナベース回答生成
- 音声合成による自然な回答音声生成
- 難易度調整アルゴリズム実装

#### 1.3 基本UI実装
- 面接官ダッシュボード
- 練習セッション画面
- 基本的な評価表示

### Phase 2: 高度機能実装（2-3ヶ月）

#### 2.1 高度分析機能
- リアルタイム質問分析
- 音声分析（トーン・ペース・感情）
- バイアス検出システム

#### 2.2 学習コンテンツ
- インタラクティブトレーニングモジュール
- ベストプラクティス集
- 業界別シナリオライブラリ

#### 2.3 統合機能
- エージェント-面接官-候補者三者連携
- カスタムシナリオ作成ツール
- 詳細レポート生成

### Phase 3: 高度機能・最適化（1-2ヶ月）

#### 3.1 ビデオ対応
- ビデオ面接シミュレーション
- 表情・ジェスチャー分析
- アイコンタクト評価

#### 3.2 チーム機能
- 複数面接官による協働面接練習
- 組織全体のスキル分析
- 面接官認定システム

---

## 技術実装詳細

### 1. AI候補者実装

#### GPT-4 Prompt Engineering
```typescript
const generateCandidatePrompt = (
  question: string,
  persona: AICandidatePersona,
  context: InterviewContext
): string => {
  return `
あなたは就職面接を受けている候補者です。以下の設定で回答してください：

【候補者プロファイル】
- 名前: ${persona.profile.name}
- 年齢: ${persona.profile.age}
- 背景: ${persona.profile.background}
- 性格: ${persona.profile.personality}
- 経験レベル: ${persona.profile.experience}

【回答スタイル】
- 詳細度: ${persona.responseStyle.verbosity}
- 自信度: ${persona.responseStyle.confidence * 100}%
- 準備度: ${persona.responseStyle.preparation * 100}%

【質問】
${question}

【指示】
1. この候補者になりきって自然に回答してください
2. 回答は1-3分程度の長さにしてください
3. 適切な敬語を使用してください
4. 性格や経験レベルに応じた回答をしてください
5. 時々適度な「えーっと」や「そうですね」を含めて自然さを演出してください

回答:
`;
};
```

#### 音声合成実装
```typescript
class AICandidateVoice {
  private speechConfig: SpeechConfig;
  
  constructor() {
    this.speechConfig = SpeechConfig.fromSubscription(
      process.env.AZURE_SPEECH_KEY!,
      process.env.AZURE_SPEECH_REGION!
    );
  }

  async generateSpeech(
    text: string,
    persona: AICandidatePersona
  ): Promise<Blob> {
    // ペルソナに基づく音声設定
    const voiceSettings = this.getVoiceSettings(persona);
    
    this.speechConfig.speechSynthesisVoiceName = voiceSettings.voice;
    this.speechConfig.speechSynthesisOutputFormat = 
      SpeechSynthesisOutputFormat.Audio24Khz96KBitRateMonoMp3;

    const synthesizer = new SpeechSynthesizer(this.speechConfig);
    
    // SSML で感情・ペース調整
    const ssmlText = this.generateSSML(text, voiceSettings);
    
    return new Promise((resolve, reject) => {
      synthesizer.speakSsmlAsync(
        ssmlText,
        result => {
          const audioBlob = new Blob([result.audioData], { type: 'audio/mp3' });
          resolve(audioBlob);
        },
        error => reject(error)
      );
    });
  }

  private getVoiceSettings(persona: AICandidatePersona) {
    // 年齢・性格に基づく音声選択
    const baseVoice = persona.profile.age < 30 ? 'ja-JP-NanamiNeural' : 'ja-JP-KeitaNeural';
    
    return {
      voice: baseVoice,
      rate: persona.responseStyle.confidence > 0.7 ? '1.1' : '0.9',
      pitch: persona.profile.personality === 'nervous' ? '-5%' : '+0%'
    };
  }

  private generateSSML(text: string, settings: any): string {
    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="ja-JP">
        <voice name="${settings.voice}">
          <prosody rate="${settings.rate}" pitch="${settings.pitch}">
            ${text}
          </prosody>
        </voice>
      </speak>
    `;
  }
}
```

### 2. 質問分析エンジン

#### リアルタイム分析
```typescript
class QuestionAnalyzer {
  async analyzeQuestion(
    questionText: string,
    context: InterviewContext
  ): Promise<QuestionAnalysis> {
    
    // 1. 質問タイプ分類
    const questionType = await this.classifyQuestionType(questionText);
    
    // 2. 法的適切性チェック
    const legalityCheck = await this.checkLegality(questionText);
    
    // 3. バイアス検出
    const biasAnalysis = await this.detectBias(questionText);
    
    // 4. 質問品質評価
    const qualityScores = await this.evaluateQuality(questionText, context);
    
    // 5. 改善提案生成
    const suggestions = await this.generateSuggestions(
      questionText, 
      questionType, 
      qualityScores
    );

    return {
      questionType,
      quality: qualityScores,
      suggestions,
      bestPractice: await this.getBestPractice(questionType)
    };
  }

  private async checkLegality(question: string): Promise<number> {
    // 日本の雇用法規に基づく違法質問検出
    const illegalPatterns = [
      /家族構成|結婚|出産|育児/,
      /政治|宗教|思想|信条/,
      /出身地|本籍|国籍/,
      /借金|ローン|年収|家計/
    ];

    const violations = illegalPatterns.filter(pattern => pattern.test(question));
    return Math.max(0, 10 - violations.length * 3);
  }

  private async detectBias(question: string): Promise<number> {
    // AI による潜在的バイアス検出
    const prompt = `
以下の面接質問に含まれる潜在的なバイアスを0-10点で評価してください（10点が最もバイアスが少ない）：

質問: "${question}"

評価観点:
- 性別バイアス
- 年齢バイアス  
- 学歴バイアス
- 外見バイアス
- 文化的バイアス

点数（0-10）:
`;

    // GPT-4 による分析実行
    const result = await this.callGPT4(prompt);
    return parseFloat(result) || 5;
  }
}
```

---

## UI/UX設計

### 1. 面接官練習画面

#### レイアウト構成
```typescript
interface InterviewerPracticeLayout {
  // メイン画面
  mainArea: {
    aiCandidateVideo: ReactNode;    // AI候補者の映像
    interviewerNotes: ReactNode;    // 面接官メモ
    questionSuggestions: ReactNode; // リアルタイム質問提案
  };

  // サイドパネル
  sidePanel: {
    candidateProfile: ReactNode;    // 候補者プロファイル
    timeTracker: ReactNode;         // 時間管理
    realTimeFeedback: ReactNode;    // リアルタイム評価
    emergencyHelp: ReactNode;       // 困ったときのヘルプ
  };

  // ボトムバー
  bottomBar: {
    recordingStatus: ReactNode;     // 録画状態
    practiceControls: ReactNode;    // 練習制御
    endSession: ReactNode;          // セッション終了
  };
}
```

#### リアルタイムフィードバック表示
```tsx
const RealTimeFeedback: React.FC = () => {
  return (
    <VStack spacing={4} align="stretch">
      <Heading size="sm">リアルタイム評価</Heading>
      
      {/* 質問品質 */}
      <Box p={3} bg="blue.50" borderRadius="md">
        <HStack justify="space-between">
          <Text fontSize="sm">質問の明確性</Text>
          <Badge colorScheme="green">8.5/10</Badge>
        </HStack>
        <Progress value={85} colorScheme="green" size="sm" mt={2} />
      </Box>

      {/* 時間管理 */}
      <Box p={3} bg="yellow.50" borderRadius="md">
        <HStack justify="space-between">
          <Text fontSize="sm">時間配分</Text>
          <Badge colorScheme="yellow">注意</Badge>
        </HStack>
        <Text fontSize="xs" color="yellow.700" mt={1}>
          この質問に時間をかけすぎています
        </Text>
      </Box>

      {/* 次の質問提案 */}
      <Box p={3} bg="purple.50" borderRadius="md">
        <Text fontSize="sm" fontWeight="bold" mb={2}>
          おすすめ深掘り質問
        </Text>
        <Text fontSize="xs">
          「その経験から何を学びましたか？」
        </Text>
      </Box>
    </VStack>
  );
};
```

### 2. 練習結果レポート

#### 総合評価画面
```tsx
const PracticeResultReport: React.FC = () => {
  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        
        {/* 総合スコア */}
        <Card>
          <CardBody>
            <HStack spacing={8} justify="center">
              <VStack>
                <Text fontSize="sm" color="gray.600">総合スコア</Text>
                <Text fontSize="4xl" fontWeight="bold" color="blue.600">
                  8.2/10
                </Text>
              </VStack>
              <Divider orientation="vertical" height="60px" />
              <VStack align="start" spacing={2}>
                <HStack>
                  <Text fontSize="sm">質問技術:</Text>
                  <Badge colorScheme="green">優秀</Badge>
                </HStack>
                <HStack>
                  <Text fontSize="sm">時間管理:</Text>
                  <Badge colorScheme="yellow">改善の余地</Badge>
                </HStack>
                <HStack>
                  <Text fontSize="sm">法的配慮:</Text>
                  <Badge colorScheme="green">適切</Badge>
                </HStack>
              </VStack>
            </HStack>
          </CardBody>
        </Card>

        {/* 詳細分析 */}
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <QuestionAnalysisCard />
          <FlowAnalysisCard />
          <ImprovementSuggestions />
          <NextStepsRecommendation />
        </SimpleGrid>

      </VStack>
    </Container>
  );
};
```

---

## 効果測定・KPI

### 1. 面接官スキル向上指標
- **質問品質スコア**: 練習前後の質問評価向上
- **法的適切性**: 不適切質問の減少率
- **時間管理**: 予定時間内の面接完了率
- **候補者体験**: AI候補者による面接体験評価

### 2. 組織レベル指標
- **面接品質標準化**: 組織内面接官のスコア分散減少
- **採用精度向上**: 面接評価と実際のパフォーマンス相関
- **法的リスク軽減**: 不適切質問による問題発生件数

### 3. システム利用指標
- **練習頻度**: 面接官あたりの月間練習回数
- **継続率**: 3ヶ月以上の継続利用率
- **満足度**: 面接官・エージェントの満足度調査

---

この面接官側練習機能により、AI面接システムは求職者・エージェント・面接官の三者すべてにメリットを提供する包括的なソリューションとなります。既存の技術基盤を最大限活用しながら、面接の質向上という新たな価値を創出できます。