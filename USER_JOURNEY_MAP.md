# 面接くん 管理画面 - 完全ユーザージャーニーマップ

## 📊 概要

**対象**: 人材エージェント（面接リンク作成者）  
**目的**: 候補者向けの面接練習リンクを作成する  
**最終更新**: 2025年6月17日（実装検証完了版）  

## 🎯 主要なユーザーフロー

## 📋 管理者側フロー（エージェント・面接官）
### フロー A: 統合リンク作成ワークフロー（主要機能）
### フロー B: ミーティングリンク生成（簡易版）
### フロー C: 候補者プロファイル作成（専用ページ）

## 🧑‍💼 候補者側フロー（求職者）
### フロー D: エージェントリンク経由面接（主要フロー）
### フロー E: 開発・テスト用候補者機能
### フロー F: 直接トークンアクセス

---

## 📍 共通スタート地点

### 管理画面アクセス
**URL**: `http://localhost:3001/` （開発環境）  
**画面**: ダッシュボード（管理画面 index.tsx）

### 主要タブ構成
1. **ダッシュボード** - AgentDashboard、クイックアクション
2. **リンク作成** - IntegratedLinkWorkflow（統合ワークフロー）
3. **リンク管理** - LinkManager（発行済みリンク管理）
4. **分析結果** - FeedbackViewer（候補者分析結果）
5. **質問管理** - QuestionScriptGenerator、QuestionList
6. **資料管理** - DocumentUploader（企業情報アップロード）

---

## 🚀 フロー A: 統合リンク作成ワークフロー（IntegratedLinkWorkflow）

### アクセス方法
- **タブ**: 「リンク作成」をクリック
- **ファイル**: `src/management/components/IntegratedLinkWorkflow.tsx`
- **機能**: PDF→AI分析→質問生成→リンク発行の統合フロー

### Step 0: 資料アップロード・企業選択 📄

#### 0-1. 企業情報の入力方法（3つの選択肢）

**方法1: PDFアップロード → 自動抽出**
1. **「企業資料をアップロード」エリアを使用**
   - ドラッグ&ドロップまたは「ファイルを選択」
   - PDF, DOC, DOCX形式に対応

2. **AIによる企業情報抽出**
   - 自動的に企業名、業界、職種を抽出 **⚠️ 実装予定：実際のPDF解析（現在はモック）**
   - 「✓ 抽出完了」の表示を確認

**方法2: 企業名から検索・選択**
1. **企業名入力フィールドに入力**
   - 既存の企業データベースから検索 **⚠️ 実装予定：企業マスターDB（現在はモック）**
   - 候補リストから選択 **⚠️ 実装予定：インテリジェント検索機能**

**方法3: 手動入力**
1. **「手動で企業情報を入力」オプション**
   - 企業名、業界、ポジション名を手動入力
   - 新規企業として登録

#### 0-2. 次のステップへ
- **ボタン**: 「次へ：面接設定」
- **条件**: 企業情報が設定済み（アップロード、選択、または手動入力完了）
- **遷移先**: Step 1（面接プロセス設定）

### Step 1: 面接プロセス設定 ⚙️

#### 1-1. 面接モード選択
**重要**: このステップで面接の基本構造を決定

**A. シングルステージ面接（デフォルト）**
- **トグルスイッチ**: 「マルチステージ面接」がOFF状態
- **表示コンポーネント**: `InterviewerRoleSelector`
- **選択肢**: 7つの面接官役割
  - 人事（HR）
  - CEO・社長
  - チームリーダー
  - 技術責任者
  - 同僚・メンバー
  - 上級管理職
  - 外部面接官

**B. マルチステージ面接**
- **トグルスイッチ**: 「マルチステージ面接」をONにする
- **表示コンポーネント**: `MultiStageInterviewSetup`
- **機能**:
  - プロセス名設定
  - 対象ポジション設定
  - 面接ステップの追加（8種類から選択）
    - 書類選考面接
    - 1次面接
    - 2次面接
    - 技術面接
    - カルチャーフィット面接
    - 最終面接
    - 役員面接
    - カジュアル面談
  - ステップごとの詳細設定（時間、合格基準、録画設定など）**⚠️ 実装予定：詳細編集UI**

#### 1-2. 設定確認
- **条件**: 面接官役割選択 または マルチステージプロセス作成完了
- **表示**: 緑色の確認カード「面接設定完了」
- **内容**: 選択された役割またはプロセスの詳細表示

#### 1-3. 次のステップへ
- **ボタン**: 「次へ：質問生成」
- **条件**: 面接設定が完了している
- **遷移先**: Step 2（AI 質問意図生成）

### Step 2: AI 質問意図生成 🤖

#### 2-1. 既存テンプレート選択（オプション）
**条件**: 同一企業で過去に作成されたテンプレートが存在する場合
- **表示**: 緑色のテンプレート選択カード
- **機能**: 過去のテンプレート再利用 **⚠️ 実装予定：実際のテンプレート読み込み機能**
- **選択肢**: 最大3つの既存テンプレートを表示 **⚠️ 実装予定：企業マスターDB統合**

#### 2-2. AI質問生成の開始
- **ボタン**: 「AI 質問意図生成開始」
- **処理**: アップロードされた資料をAIが解析 **⚠️ 実装予定：実際のAI解析エンジン（現在はモック）**
- **表示**: プログレスバーと「AI が資料を解析中...」メッセージ
- **結果**: 質問意図リストの自動生成 **⚠️ 実装予定：実際のAI品質評価システム**

#### 2-3. 次のステップへ
- **条件**: AI生成が完了
- **自動遷移**: Step 3（質問意図確認・編集）

### Step 3: 質問意図確認・編集 ✏️

#### 3-1. 生成結果の確認
- **表示**: 「AI が X つの質問意図を生成しました」アラート
- **適用設定**: 選択された面接設定の確認表示
  - シングルステージ: 面接官役割の詳細
  - マルチステージ: プロセス名、ステップ数、予想時間

#### 3-2. インテリジェント質問の表示（enhancedMode時）
**条件**: プロダクト憲法準拠モードが有効な場合
- **表示**: 「生成された質問（プロダクト憲法準拠）」セクション
- **内容**: 詳細な質問リスト（最大3問をプレビュー表示）
- **要素**: 質問カテゴリ、難易度、励まし要素、推定回答時間

#### 3-3. 質問意図の編集
**各質問意図カードの構成**:
- **優先度バッジ**: 重要/通常/補助
- **カテゴリ表示**: 質問のカテゴリー分類
- **編集済みマーク**: 手動編集された場合の表示
- **信頼度**: AI生成の信頼度パーセンテージ
- **削除ボタン**: 不要な質問意図の削除 ✅
- **質問意図**: メインの質問内容
- **説明**: 質問の詳細説明
- **AI指示**: AIエージェントへの具体的指示
- **想定時間**: 質問の推定所要時間

**⚠️ 実装予定**: 個別質問意図の編集機能（編集ボタン、編集フォーム、インライン編集）

#### 3-4. テンプレート保存（オプション）
**条件**: enhancedMode && 生成された質問 && 企業ID設定済み
- **表示**: 黄色のテンプレート保存カード
- **機能**: 生成された質問をテンプレートとして保存 **⚠️ 実装予定：バックエンド永続化**
- **設定**: テンプレート名の入力
- **ボタン**: 「保存する」「保存しない」

**⚠️ 実装予定**: テンプレート管理機能（編集、削除、バージョン管理、エージェント間共有）

#### 3-5. 次のステップへ
- **ボタン**: 「次へ：リンク設定」
- **条件**: 質問意図が1つ以上存在
- **重要**: 修正済み - ボタンは正常に Step 4 に遷移

### Step 4: リンク設定・発行 🔗

#### 4-1. 候補者情報入力
- **候補者名** (必須): テキスト入力
- **候補者メールアドレス** (任意): Email入力
- **リンクの有効期限**: セレクトボックス
  - 1日後
  - 3日後
  - 7日後（推奨）
  - 14日後
  - 30日後

#### 4-2. エージェントメモ
- **ラベル**: 「エージェントメモ（候補者向け）」
- **機能**: 候補者へのアドバイスやメッセージ入力
- **形式**: テキストエリア（3行）

#### 4-3. 生成設定の確認
**設定サマリーの表示**:
- 質問意図数
- 想定面接時間
- 参考資料ファイル数
- 使用テンプレート（該当時）
- 企業マスター情報（該当時）

#### 4-4. リンク発行
- **ボタン**: 「面接リンクを発行」
- **処理**: ローディング状態「リンク生成中...」
- **完了**: 生成されたリンクの表示
- **後処理**: 成功時は自動的にリンク管理画面に遷移

---

## 🔗 フロー B: ミーティングリンク生成（MeetingLinkGenerator）

### アクセス方法
- **注意**: 現在のタブ構成からは直接アクセス不可
- **ファイル**: `src/management/components/MeetingLinkGenerator.tsx`
- **用途**: より簡易な面接リンク作成

### 主要機能

#### B-1. 基本設定
- **候補者名**: 必須入力
- **面接タイトル**: 任意入力
- **有効期限**: 1時間〜30日の選択

#### B-2. プロファイル連携設定（実装済み）
**プロファイル連携トグル**:
- **ラベル**: 「候補者プロファイル連携」
- **機能**: ONにするとアコーディオンが展開
- **コンポーネント**: `CandidateProfileInput` (simplified版)

**プロファイル入力機能**:
- **候補者名**: 必須
- **現在の職位**: 任意
- **経験年数**: 数値入力
- **主要スキル**: タグ形式での追加・削除
- **ボタン**: 「プロファイル設定」「クリア」

#### B-3. 面接官役割設定
- **コンポーネント**: `InterviewerRoleSelector`
- **機能**: 7つの役割から選択・カスタマイズ可能

#### B-4. リンク生成
- **ボタン**: 「面接リンクを生成」
- **結果**: 候補者向けリンクの発行

---

## 🧑‍💼 フロー C: 候補者プロファイル作成（専用ページ）

### アクセス方法
**重要**: 現在、直接的なナビゲーションが不足
- **ページ**: `/candidate-profile-creation`
- **現在のアクセス方法**: 
  1. 質問管理タブ → QuestionScriptGenerator
  2. 「プロファイルベース質問生成」タブ
  3. 「新しいプロファイルを作成」ボタン

### Step 1: 基本情報入力

#### C-1-1. 候補者基本情報
- **氏名**: 必須
- **メールアドレス**: 必須
- **電話番号**: 任意
- **居住地**: 任意
- **国籍**: 任意
- **生年月日**: 任意

#### C-1-2. 職歴情報
- **現在の職位**: 任意
- **経験年数**: 数値入力
- **キャリア**: 複数の職歴を追加可能

### Step 2: 履歴書アップロード・解析

#### C-2-1. ファイルアップロード
- **対応形式**: PDF, DOC, DOCX
- **機能**: ドラッグ&ドロップ対応
- **処理**: AI による履歴書解析 **⚠️ 実装予定：実際の履歴書解析エンジン（現在はモック）**

#### C-2-2. 解析結果表示
- **抽出情報**: 職歴、スキル、学歴等 **⚠️ 実装予定：AI による自動抽出**
- **確認・編集**: 解析結果の修正可能

### Step 3: 求職活動設定

#### C-3-1. 希望条件
- **希望職種**: 選択式
- **希望業界**: 選択式
- **希望年収**: 範囲選択
- **勤務地希望**: 地域選択

#### C-3-2. 面接設定
- **コミュニケーションスタイル**: balanced/formal/casual
- **フィードバック希望**: constructive/direct/encouraging
- **面接ペース**: normal/fast/slow

### Step 4: 確認・保存

#### C-4-1. プロファイル確認
- **サマリー表示**: 全入力内容の確認
- **編集機能**: 各セクションの修正可能

#### C-4-2. 保存・完了
- **ボタン**: 「プロファイルを保存」
- **完了**: データベースへの保存完了

---

## 🎛️ 各種設定・管理機能

### リンク管理（LinkManager）
- **アクセス**: リンク管理タブ
- **機能**: 発行済みリンクの確認・編集・延長・削除 **⚠️ 実装予定：詳細管理機能（現在は基本表示のみ）**

### 分析結果確認（FeedbackViewer）
- **アクセス**: 分析結果タブ
- **機能**: 候補者の面接結果・フィードバック閲覧

### 質問管理
- **InterviewIntentConfig**: AI面接官意図設定
- **QuestionScriptGenerator**: 質問スクリプト生成
- **QuestionList**: 生成された質問一覧

### 資料管理（DocumentUploader）
- **アクセス**: 資料管理タブ
- **機能**: 企業情報・求人票のアップロード

---

## ⚠️ 重要な注意事項・既知の問題

### 修正済み問題
1. **Step 3→4 ナビゲーションバグ**: 修正完了
   - 問題: 「次へ：リンク設定」ボタンが Step 4 に遷移しなかった
   - 原因: `setCurrentStep(3)` になっていた
   - 修正: `setCurrentStep(4)` に修正済み

### アクセシビリティの問題
1. **候補者プロファイル作成ページの発見性**
   - 問題: 直接的なナビゲーションが不足
   - 現在: 質問管理 → QuestionScriptGenerator 経由でのみアクセス可能
   - 改善提案: メインダッシュボードからの直接リンク追加

2. **フロー間の整合性**
   - 統合ワークフロー: 高機能、複雑
   - ミーティングリンク生成: 簡易、直感的
   - プロファイル作成: 完全機能、独立

### 機能の重複
1. **プロファイル入力機能**
   - IntegratedLinkWorkflow: 基本的な候補者情報のみ **⚠️ 実装予定：プロファイル連携機能**
   - MeetingLinkGenerator: CandidateProfileInput（簡易版）✅
   - 専用ページ: 完全なプロファイル作成機能 ✅

2. **面接官役割選択**
   - IntegratedLinkWorkflow: InterviewerRoleSelector
   - MeetingLinkGenerator: 同じコンポーネント
   - 統一されているが、アクセス方法が異なる

---

## 📈 推奨改善事項

### 1. ナビゲーション改善
- 候補者プロファイル作成ページへの直接アクセス追加
- 各フローの使い分けガイドライン明確化

### 2. フロー統合
- 統合ワークフローでのプロファイル入力機能強化
- または各フローの役割分担明確化

### 3. ユーザビリティ向上
- 進行状況の視覚的改善
- エラーハンドリングの強化
- 保存状態の明確化

---

## 🔗 関連ファイル・コンポーネント

### 主要コンポーネント
- `IntegratedLinkWorkflow.tsx` - 統合ワークフロー
- `MeetingLinkGenerator.tsx` - 簡易リンク生成
- `CandidateProfileInput.tsx` - プロファイル入力
- `InterviewerRoleSelector.tsx` - 面接官役割選択
- `MultiStageInterviewSetup.tsx` - マルチステージ設定

### ページファイル
- `src/management/pages/index.tsx` - メインダッシュボード
- `src/management/pages/candidate-profile-creation.tsx` - プロファイル作成

### 型定義
- `src/shared/types/candidate-profile.ts` - プロファイル型
- `src/shared/types/interviewer-roles.ts` - 面接官役割型

### サービス
- `src/shared/services/roleBasedQuestionGenerationDemo.ts` - 役割別質問生成
- `src/shared/services/intelligentQuestionGenerator.ts` - AI質問生成

---

## 🧑‍💼 候補者側ユーザージャーニー

### 共通エントリーポイント

#### デモ・開発確認用アクセス
**URL**: `http://localhost:3000/` または `http://localhost:3000/demo-login`  
**目的**: 開発状況確認・テスト機能へのアクセス  
**ファイル**: `src/candidate/pages/demo-login.tsx`

#### 認証システム構成
- **ルートアクセス**: `/` → 自動的に `/demo-login` にリダイレクト
- **3つの認証タブ**: エージェント・面接官ログイン、候補者テスト機能
- **実装状況**: ✅ 完全実装済み

---

## 🎯 フロー D: エージェントリンク経由面接（主要フロー）

### D-1. エージェントからリンク受信
**前提**: エージェントが管理画面で面接リンクを作成・送信済み

#### D-1-1. リンクアクセス
- **形式**: `http://localhost:3000/interview-prep?linkId=xxx&scenarioId=xxx&status=active`
- **パラメータ**:
  - `linkId`: 面接リンクID
  - `scenarioId`: 面接シナリオID（オプション）
  - `status`: リンクステータス（active/expired/invalid）

#### D-1-2. リンク状態別の処理
**A. 有効なリンク（status=active）**:
- **ページ**: `/interview-prep` 
- **表示**: トークン入力画面
- **機能**: エージェント設定情報の表示（企業情報、メモ等）

**B. 期限切れリンク（status=expired）**:
- **表示**: 期限切れメッセージ
- **案内**: エージェントへの連絡方法
- **対応**: 新しいリンクの再発行依頼

**C. 無効リンク（status=invalid）**:
- **表示**: 無効リンクエラー
- **案内**: 正しいリンクの確認要請

### D-2. トークン認証・面接準備

#### D-2-1. トークン入力
- **画面**: `/interview-prep` のトークン入力フォーム
- **デモトークン**: `demo-token-123`（開発用）
- **検証**: クライアント側でのトークン照合 **⚠️ 実装予定：サーバー側認証**

#### D-2-2. 面接準備プロセス
**ステップ1: 環境チェック**
- **カメラ**: 動作確認・映像品質チェック
- **マイク**: 音声入力テスト・レベル調整
- **ネットワーク**: 接続安定性確認
- **環境**: 静かな場所での実施確認

**ステップ2: 目標設定**
- **面接の目的**: 練習目標の明確化
- **改善点**: 前回からの改善ポイント
- **重点項目**: 今回注力したいスキル

**ステップ3: 企業・職種情報確認**
- **企業情報**: エージェントが設定した企業詳細
- **職種詳細**: ポジション要件・期待スキル
- **エージェントメモ**: 個別アドバイス・注意点

**ステップ4: 最終準備**
- **設定確認**: 全項目のチェック完了確認
- **心構え**: 面接に向けたメンタル準備
- **開始**: 実際の面接セッションへ移行

### D-3. 面接実施

#### D-3-1. 面接ルーム開始
- **遷移**: `/focused-interview`（エージェントリンク専用）
- **3Dアバター**: Azure Avatar Service による面接官
- **機能**: 
  - リアルタイム音声認識・応答
  - 感情分析フィードバック
  - 進行状況可視化
  - 一時停止・再開機能

#### D-3-2. 面接進行
**質問フェーズ**:
- **AI質問**: エージェントが設定した質問意図に基づく
- **音声入力**: 音声回答（テキスト入力バックアップ）
- **リアルタイム分析**: コミュニケーション評価

**フィードバックフェーズ**:
- **即時フィードバック**: 回答後の簡潔なアドバイス
- **励まし要素**: 心理的安全性を重視したサポート
- **改善提案**: 具体的な改善ポイント提示

### D-4. 結果・分析確認

#### D-4-1. 面接終了・結果表示
- **画面**: `/interview-result`
- **総合評価**: スコア・レーダーチャート
- **詳細分析**: 
  - コミュニケーションスキル
  - 回答内容の適切性
  - 表情・声のトーン分析
  - 改善推奨ポイント

#### D-4-2. レポート機能
- **エージェント共有**: 結果のエージェント送信 **⚠️ 実装予定：自動共有機能**
- **進捗追跡**: 過去の面接との比較
- **次回への提案**: 継続改善プラン

---

## 🔧 フロー E: 開発・テスト用候補者機能

### E-1. アクセス方法
**URL**: `http://localhost:3000/demo-login` → 「候補者テスト機能」タブ  
**目的**: 開発者・QA向けの機能テスト・状況確認

### E-2. テスト機能一覧

#### E-2-1. リンク状態テスト
**有効リンクテスト**:
- **ボタン**: 「有効リンクをテスト」
- **遷移**: `/interview-prep?linkId=valid_link_123&status=active`
- **期待動作**: 正常なトークン入力画面表示

**期限切れリンクテスト**:
- **ボタン**: 「期限切れリンクをテスト」
- **遷移**: `/interview-prep?linkId=expired_link_456&status=expired`
- **期待動作**: 期限切れエラーメッセージ表示

**無効リンクテスト**:
- **ボタン**: 「無効リンクをテスト」
- **遷移**: `/interview-prep?linkId=invalid_link_999&status=invalid`
- **期待動作**: 無効リンクエラーメッセージ表示

#### E-2-2. データプレビューテスト
**面接データプレビュー**:
- **ボタン**: 「プレビューページを表示」
- **遷移**: `/test-data`
- **機能**: エージェント設定データの確認・開発用プレビュー

---

## 🔑 フロー F: 直接トークンアクセス

### F-1. アクセス方法
**前提**: 候補者が既にトークン情報を保有している場合
- **画面**: `/candidate-waiting`（アカウント保有者向け）
- **画面**: `/interview-token?token=xxx`（直接トークンアクセス）

### F-2. 直接アクセスフロー
#### F-2-1. トークン入力
- **入力**: 直接トークン入力フィールド
- **検証**: トークンの有効性確認
- **遷移**: 有効な場合は面接準備画面へ

#### F-2-2. 面接実施
- **フロー**: フロー D-3 と同様の面接プロセス
- **違い**: エージェント設定情報が制限される場合あり

---

## 🔐 認証・アクセス制御

### 認証システムの現状

#### 実装済み機能 ✅
1. **demo-login ページ**: 3つのロール（エージェント/面接官/候補者）対応
2. **テストアカウント**: `src/candidate/lib/testAccounts.ts` で定義
3. **ロール別リダイレクト**: 
   - エージェント → 管理画面（`http://localhost:3001`）
   - 面接官 → `/interviewer-dashboard` **⚠️ 実装予定**
   - 候補者 → テスト機能のみ

#### 候補者の主要アクセスパターン
1. **リンクベースアクセス** (本来の設計): エージェントからのリンク経由
2. **開発・テスト用アクセス**: demo-login 経由
3. **直接トークンアクセス**: トークン直接入力

#### セキュリティ・認証の課題 **⚠️ 実装予定**
- **クライアント側認証**: 現在はlocalStorage/sessionStorageのみ
- **トークン検証**: サーバー側での厳密な検証が必要
- **セッション管理**: 適切な有効期限・無効化機能
- **アクセス制御**: ページレベルでの認可制御

---

## 🎛️ 候補者関連のコンポーネント・ページ

### 主要ページ
- **`demo-login.tsx`** - 認証・テスト機能エントリーポイント ✅
- **`interview-prep.tsx`** - リンクベース面接準備 ✅
- **`interview-token.tsx`** - 直接トークンアクセス ✅
- **`focused-interview.tsx`** - エージェントリンク専用面接 ✅
- **`interview-room.tsx`** - 汎用面接ルーム ✅
- **`interview-result.tsx`** - 結果表示・分析 ✅
- **`candidate-waiting.tsx`** - エージェントリンク待機 ✅
- **`test-data.tsx`** - 開発用データプレビュー ✅

### サポートコンポーネント
- **`AccessibilityHelper.tsx`** - アクセシビリティ支援 ✅
- **`CandidateAuthGuard.tsx`** - 認証ガード **⚠️ 現在未使用**

### 設定・データ
- **`testAccounts.ts`** - テストアカウント定義 ✅
- **`agentLinkData.ts`** - エージェントリンクモックデータ ✅
- **`config.ts`** - 候補者アプリ設定 ✅

---

## 🚧 実装予定機能一覧

### 管理者側（エージェント・面接官）- 高優先度
1. **質問意図の個別編集機能** - 編集ボタン、編集フォーム、インライン編集
2. **実際のAI解析エンジン** - PDF解析、履歴書解析、質問生成（現在はモック実装）
3. **統合ワークフローでのプロファイル連携** - CandidateProfileInput統合
4. **企業マスターデータベース** - 永続化、検索機能、使用統計

### 管理者側（エージェント・面接官）- 中優先度
5. **マルチステージ面接の詳細編集UI** - ステップ別設定、評価基準、録画設定
6. **テンプレート管理機能** - バックエンド永続化、編集、削除、バージョン管理
7. **リンク管理の詳細機能** - 有効期限管理、ステータス更新、分析機能
8. **インテリジェント検索機能** - あいまい検索、ランキング、サジェスト

### 候補者側 - 高優先度
9. **サーバー側トークン認証** - 現在はクライアント側のみ
10. **面接官ダッシュボード** - `/interviewer-dashboard` 実装
11. **結果自動共有機能** - エージェントへの結果送信
12. **リアルタイムAI分析** - 表情・声のトーン解析（現在はモック）

### 候補者側 - 中優先度
13. **適切なセッション管理** - 有効期限・無効化機能
14. **進捗追跡システム** - 過去面接との比較分析
15. **アクセシビリティ強化** - より詳細な支援機能
16. **認証ガード機能** - CandidateAuthGuard の活用

### 共通・低優先度
17. **AI品質スコアリングシステム** - 実際の信頼度計算、品質評価
18. **企業情報変更の自動検出** - 差分分析、変更提案
19. **心理的安全性の適応制御** - リアルタイム調整、個人化メッセージ
20. **エージェント間テンプレート共有** - 権限管理、共有設定

---

**最終更新**: 2025年6月17日  
**検証状況**: 管理者側・候補者側全フロー実装確認済み・未実装機能特定完了  
**重要バグ**: Step 3→4 ナビゲーション修正完了  
**認証システム**: demo-login から開発状況確認可能 ✅