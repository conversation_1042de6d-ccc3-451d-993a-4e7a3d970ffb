{"name": "men<PERSON>u-kun", "version": "0.1.0", "description": "Stub server for AI 3D Avatar Interview Tool", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:management\"", "dev:backend": "cd src/fastapi && uvicorn app.main:app --reload --host 0.0.0.0 --port 8080", "dev:frontend": "cd src/candidate && npm run dev", "dev:management": "cd src/management && npm run dev", "stop": "kill-port 3001 8080 3003", "install:all": "npm install && cd src/candidate && npm install && cd ../management && npm install", "build": "cd src/candidate && npm install && npm run build", "build:all": "cd src/candidate && npm run build && cd ../management && npm run build", "build:candidate": "cd src/candidate && npm run build", "build:management": "cd src/management && npm run build", "type-check": "cd src/candidate && npx tsc --noEmit && cd ../management && npx tsc --noEmit", "vercel-build": "cd src/candidate && npm install && npm run build", "test": "node --test"}, "devDependencies": {"concurrently": "^8.2.2", "kill-port": "^2.0.1", "tsx": "^4"}, "dependencies": {"framer-motion": "^12.16.0", "microsoft-cognitiveservices-speech-sdk": "^1.44.0", "react-intersection-observer": "^9.16.0", "zustand": "^5.0.5"}}