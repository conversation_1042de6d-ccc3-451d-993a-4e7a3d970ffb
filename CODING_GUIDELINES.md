# Coding Guidelines

## 目次
1. [コーディングスタイル](#コーディングスタイル)
   1. [命名規則](#命名規則)
   2. [フォーマット](#フォーマット)
   3. [コメント](#コメント)
2. [設計原則とベストプラクティス](#設計原則とベストプラクティス)
   1. [ディレクトリ構成](#ディレクトリ構成)
   2. [コンポーネント設計](#コンポーネント設計)
   3. [状態管理](#状態管理)
   4. [エラーハンドリング](#エラーハンドリング)
   5. [非同期処理](#非同期処理)
   6. [依存関係管理](#依存関係管理)
3. [バージョン管理](#バージョン管理)

---

## コーディングスタイル

### 命名規則
- **変数・関数**: `camelCase`
- **定数**: `UPPER_SNAKE_CASE`
- **クラス・コンポーネント**: `PascalCase`
- **ファイル・ディレクトリ**: `kebab-case`

```ts
// 👍 Good
const hourlyRate = 1000;
function calculatePayroll() {}
class PayrollService {}

// 👎 Bad
const Hourly_rate = 1000;
function calculate_payroll() {}
class payroll_service {}
```

### フォーマット
- インデントは **2スペース**
- 1行の長さは **100文字以内**
- ダブルクオート `""` を使用
- 自動整形には **Prettier** を用いる

```json
// .prettierrc
{
  "singleQuote": false,
  "printWidth": 100,
  "tabWidth": 2
}
```

### コメント
- 関数・クラスには **JSDoc/TSDoc** 形式で記述
- 複雑な処理にはインラインコメントを付与

```ts
/**
 * 勤怠集計を計算する
 * @param punches 打刻データ一覧
 * @returns 集計結果
 */
function computeSummary(punches: Punch[]): Summary {
  // 休憩時間を含めて計算
  return {} as Summary;
}
```

## 設計原則とベストプラクティス

### ディレクトリ構成
```
client/
  src/
    components/
    hooks/
    pages/
    lib/
server/
shared/
```
各機能を `components`, `pages`, `services` などのディレクトリに配置します。

### コンポーネント設計
- **単一責任の原則** を守る
- 行数が増え再利用できそうな場合は分割する
- Props は `camelCase` で命名

```tsx
// 👍 Good
function UserCard({ userName }: { userName: string }) {
  return <div>{userName}</div>;
}

// 👎 Bad
function user_card(props) {
  return <div>{props.username}</div>;
}
```

### 状態管理
- 画面固有の状態は React の `useState`
- 複数コンポーネントで共有する場合は Context か外部ライブラリを利用

### エラーハンドリング
- API 呼び出しは `try/catch` で包む
- ユーザー向けメッセージとログ用メッセージを分ける

### 非同期処理
- `async/await` を基本とし、必ずエラーハンドリングを行う

```ts
// 👍 Good
try {
  const data = await fetchData();
} catch (e) {
  console.error(e);
}
```

### 依存関係管理

このプロジェクトは **pnpm workspaces** を使用したモノレポ構成です。

#### インストール・セットアップ
```bash
# pnpmのインストール（未インストールの場合）
npm install -g pnpm

# 依存関係のインストール
pnpm install
```

#### 共有モジュールのインポート
```ts
// 👍 Good - pnpm workspaceパッケージ名を使用
import { ResumeParsingService } from '@mensetsu-kun/shared/services/resumeParsingService';
import type { CandidateProfile } from '@mensetsu-kun/shared/types/candidate-profile';

// 👎 Bad - 相対パスは使用しない
import { ResumeParsingService } from '../../shared/services/resumeParsingService';
```

#### ワークスペース構成
- `src/candidate/` - 求職者向けアプリ
- `src/management/` - 管理者向けアプリ  
- `src/shared/` - 共有ライブラリ（`@mensetsu-kun/shared`パッケージ）
- `src/backend/` - APIサーバー

#### 開発コマンド
```bash
# 全アプリの開発サーバー起動
npm run dev:all

# 個別アプリの開発（pnpm workspaces）
pnpm --filter mensetsu-kun-candidate dev
pnpm --filter mensetsu-kun-management dev

# 個別アプリのビルド
pnpm --filter mensetsu-kun-management build
```

## バージョン管理

### Git コミットメッセージ
- [Conventional Commits](https://www.conventionalcommits.org/ja/v1.0.0/) を採用
- 例: `feat: add payroll calculation`, `fix: handle null value`
